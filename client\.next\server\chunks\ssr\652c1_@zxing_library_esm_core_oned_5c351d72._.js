module.exports = {

"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/OneDReader.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2008 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/BitArray.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/DecodeHintType.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultMetadataType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ResultMetadataType.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ResultPoint.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NotFoundException.js [app-ssr] (ecmascript)");
;
;
;
;
;
/**
 * Encapsulates functionality and implementation that is common to all families
 * of one-dimensional barcodes.
 *
 * <AUTHOR> (Daniel Switkin)
 * <AUTHOR> Owen
 */ var OneDReader = function() {
    function OneDReader() {}
    /*
    @Override
    public Result decode(BinaryBitmap image) throws NotFoundException, FormatException {
      return decode(image, null);
    }
    */ // Note that we don't try rotation without the try harder flag, even if rotation was supported.
    // @Override
    OneDReader.prototype.decode = function(image, hints) {
        try {
            return this.doDecode(image, hints);
        } catch (nfe) {
            var tryHarder = hints && hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].TRY_HARDER) === true;
            if (tryHarder && image.isRotateSupported()) {
                var rotatedImage = image.rotateCounterClockwise();
                var result = this.doDecode(rotatedImage, hints);
                // Record that we found it rotated 90 degrees CCW / 270 degrees CW
                var metadata = result.getResultMetadata();
                var orientation_1 = 270;
                if (metadata !== null && metadata.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultMetadataType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].ORIENTATION) === true) {
                    // But if we found it reversed in doDecode(), add in that result here:
                    orientation_1 = orientation_1 + metadata.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultMetadataType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].ORIENTATION) % 360;
                }
                result.putMetadata(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultMetadataType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].ORIENTATION, orientation_1);
                // Update result points
                var points = result.getResultPoints();
                if (points !== null) {
                    var height = rotatedImage.getHeight();
                    for(var i = 0; i < points.length; i++){
                        points[i] = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](height - points[i].getY() - 1, points[i].getX());
                    }
                }
                return result;
            } else {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
            }
        }
    };
    // @Override
    OneDReader.prototype.reset = function() {
    // do nothing
    };
    /**
     * We're going to examine rows from the middle outward, searching alternately above and below the
     * middle, and farther out each time. rowStep is the number of rows between each successive
     * attempt above and below the middle. So we'd scan row middle, then middle - rowStep, then
     * middle + rowStep, then middle - (2 * rowStep), etc.
     * rowStep is bigger as the image is taller, but is always at least 1. We've somewhat arbitrarily
     * decided that moving up and down by about 1/16 of the image is pretty good; we try more of the
     * image if "trying harder".
     *
     * @param image The image to decode
     * @param hints Any hints that were requested
     * @return The contents of the decoded barcode
     * @throws NotFoundException Any spontaneous errors which occur
     */ OneDReader.prototype.doDecode = function(image, hints) {
        var width = image.getWidth();
        var height = image.getHeight();
        var row = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](width);
        var tryHarder = hints && hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].TRY_HARDER) === true;
        var rowStep = Math.max(1, height >> (tryHarder ? 8 : 5));
        var maxLines;
        if (tryHarder) {
            maxLines = height; // Look at the whole image, not just the center
        } else {
            maxLines = 15; // 15 rows spaced 1/32 apart is roughly the middle half of the image
        }
        var middle = Math.trunc(height / 2);
        for(var x = 0; x < maxLines; x++){
            // Scanning from the middle out. Determine which row we're looking at next:
            var rowStepsAboveOrBelow = Math.trunc((x + 1) / 2);
            var isAbove = (x & 0x01) === 0; // i.e. is x even?
            var rowNumber = middle + rowStep * (isAbove ? rowStepsAboveOrBelow : -rowStepsAboveOrBelow);
            if (rowNumber < 0 || rowNumber >= height) {
                break;
            }
            // Estimate black point for this row and load it:
            try {
                row = image.getBlackRow(rowNumber, row);
            } catch (ignored) {
                continue;
            }
            var _loop_1 = function(attempt) {
                if (attempt === 1) {
                    row.reverse(); // reverse the row and continue
                    // This means we will only ever draw result points *once* in the life of this method
                    // since we want to avoid drawing the wrong points after flipping the row, and,
                    // don't want to clutter with noise from every single row scan -- just the scans
                    // that start on the center line.
                    if (hints && hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].NEED_RESULT_POINT_CALLBACK) === true) {
                        var newHints_1 = new Map();
                        hints.forEach(function(hint, key) {
                            return newHints_1.set(key, hint);
                        });
                        newHints_1.delete(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].NEED_RESULT_POINT_CALLBACK);
                        hints = newHints_1;
                    }
                }
                try {
                    // Look for a barcode
                    var result = this_1.decodeRow(rowNumber, row, hints);
                    // We found our barcode
                    if (attempt === 1) {
                        // But it was upside down, so note that
                        result.putMetadata(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultMetadataType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].ORIENTATION, 180);
                        // And remember to flip the result points horizontally.
                        var points = result.getResultPoints();
                        if (points !== null) {
                            points[0] = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](width - points[0].getX() - 1, points[0].getY());
                            points[1] = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](width - points[1].getX() - 1, points[1].getY());
                        }
                    }
                    return {
                        value: result
                    };
                } catch (re) {
                // continue -- just couldn't decode this row
                }
            };
            var this_1 = this;
            // While we have the image data in a BitArray, it's fairly cheap to reverse it in place to
            // handle decoding upside down barcodes.
            for(var attempt = 0; attempt < 2; attempt++){
                var state_1 = _loop_1(attempt);
                if (typeof state_1 === "object") return state_1.value;
            }
        }
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
    };
    /**
     * Records the size of successive runs of white and black pixels in a row, starting at a given point.
     * The values are recorded in the given array, and the number of runs recorded is equal to the size
     * of the array. If the row starts on a white pixel at the given start point, then the first count
     * recorded is the run of white pixels starting from that point; likewise it is the count of a run
     * of black pixels if the row begin on a black pixels at that point.
     *
     * @param row row to count from
     * @param start offset into row to start at
     * @param counters array into which to record counts
     * @throws NotFoundException if counters cannot be filled entirely from row before running out
     *  of pixels
     */ OneDReader.recordPattern = function(row, start, counters) {
        var numCounters = counters.length;
        for(var index = 0; index < numCounters; index++)counters[index] = 0;
        var end = row.getSize();
        if (start >= end) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        var isWhite = !row.get(start);
        var counterPosition = 0;
        var i = start;
        while(i < end){
            if (row.get(i) !== isWhite) {
                counters[counterPosition]++;
            } else {
                if (++counterPosition === numCounters) {
                    break;
                } else {
                    counters[counterPosition] = 1;
                    isWhite = !isWhite;
                }
            }
            i++;
        }
        // If we read fully the last section of pixels and filled up our counters -- or filled
        // the last counter but ran off the side of the image, OK. Otherwise, a problem.
        if (!(counterPosition === numCounters || counterPosition === numCounters - 1 && i === end)) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
    };
    OneDReader.recordPatternInReverse = function(row, start, counters) {
        // This could be more efficient I guess
        var numTransitionsLeft = counters.length;
        var last = row.get(start);
        while(start > 0 && numTransitionsLeft >= 0){
            if (row.get(--start) !== last) {
                numTransitionsLeft--;
                last = !last;
            }
        }
        if (numTransitionsLeft >= 0) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        OneDReader.recordPattern(row, start + 1, counters);
    };
    /**
     * Determines how closely a set of observed counts of runs of black/white values matches a given
     * target pattern. This is reported as the ratio of the total variance from the expected pattern
     * proportions across all pattern elements, to the length of the pattern.
     *
     * @param counters observed counters
     * @param pattern expected pattern
     * @param maxIndividualVariance The most any counter can differ before we give up
     * @return ratio of total variance between counters and pattern compared to total pattern size
     */ OneDReader.patternMatchVariance = function(counters, pattern, maxIndividualVariance) {
        var numCounters = counters.length;
        var total = 0;
        var patternLength = 0;
        for(var i = 0; i < numCounters; i++){
            total += counters[i];
            patternLength += pattern[i];
        }
        if (total < patternLength) {
            // If we don't even have one pixel per unit of bar width, assume this is too small
            // to reliably match, so fail:
            return Number.POSITIVE_INFINITY;
        }
        var unitBarWidth = total / patternLength;
        maxIndividualVariance *= unitBarWidth;
        var totalVariance = 0.0;
        for(var x = 0; x < numCounters; x++){
            var counter = counters[x];
            var scaledPattern = pattern[x] * unitBarWidth;
            var variance = counter > scaledPattern ? counter - scaledPattern : scaledPattern - counter;
            if (variance > maxIndividualVariance) {
                return Number.POSITIVE_INFINITY;
            }
            totalVariance += variance;
        }
        return totalVariance / total;
    };
    return OneDReader;
}();
const __TURBOPACK__default__export__ = OneDReader;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/Code128Reader.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2008 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
/*namespace com.google.zxing.oned {*/ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/BarcodeFormat.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ChecksumException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ChecksumException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/DecodeHintType.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/FormatException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NotFoundException.js [app-ssr] (ecmascript)");
// import Reader from '../Reader';
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Result.js [app-ssr] (ecmascript)");
// import ResultMetadataType from '../ResultMetadataType';
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ResultPoint.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/OneDReader.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
;
;
;
;
;
;
/**
 * <p>Decodes Code 128 barcodes.</p>
 *
 * <AUTHOR> Owen
 */ var Code128Reader = function(_super) {
    __extends(Code128Reader, _super);
    function Code128Reader() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    Code128Reader.findStartPattern = function(row) {
        var width = row.getSize();
        var rowOffset = row.getNextSet(0);
        var counterPosition = 0;
        var counters = Int32Array.from([
            0,
            0,
            0,
            0,
            0,
            0
        ]);
        var patternStart = rowOffset;
        var isWhite = false;
        var patternLength = 6;
        for(var i = rowOffset; i < width; i++){
            if (row.get(i) !== isWhite) {
                counters[counterPosition]++;
            } else {
                if (counterPosition === patternLength - 1) {
                    var bestVariance = Code128Reader.MAX_AVG_VARIANCE;
                    var bestMatch = -1;
                    for(var startCode = Code128Reader.CODE_START_A; startCode <= Code128Reader.CODE_START_C; startCode++){
                        var variance = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].patternMatchVariance(counters, Code128Reader.CODE_PATTERNS[startCode], Code128Reader.MAX_INDIVIDUAL_VARIANCE);
                        if (variance < bestVariance) {
                            bestVariance = variance;
                            bestMatch = startCode;
                        }
                    }
                    // Look for whitespace before start pattern, >= 50% of width of start pattern
                    if (bestMatch >= 0 && row.isRange(Math.max(0, patternStart - (i - patternStart) / 2), patternStart, false)) {
                        return Int32Array.from([
                            patternStart,
                            i,
                            bestMatch
                        ]);
                    }
                    patternStart += counters[0] + counters[1];
                    counters = counters.slice(2, counters.length);
                    counters[counterPosition - 1] = 0;
                    counters[counterPosition] = 0;
                    counterPosition--;
                } else {
                    counterPosition++;
                }
                counters[counterPosition] = 1;
                isWhite = !isWhite;
            }
        }
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
    };
    Code128Reader.decodeCode = function(row, counters, rowOffset) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].recordPattern(row, rowOffset, counters);
        var bestVariance = Code128Reader.MAX_AVG_VARIANCE; // worst variance we'll accept
        var bestMatch = -1;
        for(var d = 0; d < Code128Reader.CODE_PATTERNS.length; d++){
            var pattern = Code128Reader.CODE_PATTERNS[d];
            var variance = this.patternMatchVariance(counters, pattern, Code128Reader.MAX_INDIVIDUAL_VARIANCE);
            if (variance < bestVariance) {
                bestVariance = variance;
                bestMatch = d;
            }
        }
        // TODO We're overlooking the fact that the STOP pattern has 7 values, not 6.
        if (bestMatch >= 0) {
            return bestMatch;
        } else {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
    };
    Code128Reader.prototype.decodeRow = function(rowNumber, row, hints) {
        var convertFNC1 = hints && hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].ASSUME_GS1) === true;
        var startPatternInfo = Code128Reader.findStartPattern(row);
        var startCode = startPatternInfo[2];
        var currentRawCodesIndex = 0;
        var rawCodes = new Uint8Array(20);
        rawCodes[currentRawCodesIndex++] = startCode;
        var codeSet;
        switch(startCode){
            case Code128Reader.CODE_START_A:
                codeSet = Code128Reader.CODE_CODE_A;
                break;
            case Code128Reader.CODE_START_B:
                codeSet = Code128Reader.CODE_CODE_B;
                break;
            case Code128Reader.CODE_START_C:
                codeSet = Code128Reader.CODE_CODE_C;
                break;
            default:
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        var done = false;
        var isNextShifted = false;
        var result = '';
        var lastStart = startPatternInfo[0];
        var nextStart = startPatternInfo[1];
        var counters = Int32Array.from([
            0,
            0,
            0,
            0,
            0,
            0
        ]);
        var lastCode = 0;
        var code = 0;
        var checksumTotal = startCode;
        var multiplier = 0;
        var lastCharacterWasPrintable = true;
        var upperMode = false;
        var shiftUpperMode = false;
        while(!done){
            var unshift = isNextShifted;
            isNextShifted = false;
            // Save off last code
            lastCode = code;
            // Decode another code from image
            code = Code128Reader.decodeCode(row, counters, nextStart);
            rawCodes[currentRawCodesIndex++] = code;
            // Remember whether the last code was printable or not (excluding CODE_STOP)
            if (code !== Code128Reader.CODE_STOP) {
                lastCharacterWasPrintable = true;
            }
            // Add to checksum computation (if not CODE_STOP of course)
            if (code !== Code128Reader.CODE_STOP) {
                multiplier++;
                checksumTotal += multiplier * code;
            }
            // Advance to where the next code will to start
            lastStart = nextStart;
            nextStart += counters.reduce(function(previous, current) {
                return previous + current;
            }, 0);
            // Take care of illegal start codes
            switch(code){
                case Code128Reader.CODE_START_A:
                case Code128Reader.CODE_START_B:
                case Code128Reader.CODE_START_C:
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
            }
            switch(codeSet){
                case Code128Reader.CODE_CODE_A:
                    if (code < 64) {
                        if (shiftUpperMode === upperMode) {
                            result += String.fromCharCode(' '.charCodeAt(0) + code);
                        } else {
                            result += String.fromCharCode(' '.charCodeAt(0) + code + 128);
                        }
                        shiftUpperMode = false;
                    } else if (code < 96) {
                        if (shiftUpperMode === upperMode) {
                            result += String.fromCharCode(code - 64);
                        } else {
                            result += String.fromCharCode(code + 64);
                        }
                        shiftUpperMode = false;
                    } else {
                        // Don't let CODE_STOP, which always appears, affect whether whether we think the last
                        // code was printable or not.
                        if (code !== Code128Reader.CODE_STOP) {
                            lastCharacterWasPrintable = false;
                        }
                        switch(code){
                            case Code128Reader.CODE_FNC_1:
                                if (convertFNC1) {
                                    if (result.length === 0) {
                                        // GS1 specification 5.4.3.7. and 5.4.6.4. If the first char after the start code
                                        // is FNC1 then this is GS1-128. We add the symbology identifier.
                                        result += ']C1';
                                    } else {
                                        // GS1 specification 5.4.7.5. Every subsequent FNC1 is returned as ASCII 29 (GS)
                                        result += String.fromCharCode(29);
                                    }
                                }
                                break;
                            case Code128Reader.CODE_FNC_2:
                            case Code128Reader.CODE_FNC_3:
                                break;
                            case Code128Reader.CODE_FNC_4_A:
                                if (!upperMode && shiftUpperMode) {
                                    upperMode = true;
                                    shiftUpperMode = false;
                                } else if (upperMode && shiftUpperMode) {
                                    upperMode = false;
                                    shiftUpperMode = false;
                                } else {
                                    shiftUpperMode = true;
                                }
                                break;
                            case Code128Reader.CODE_SHIFT:
                                isNextShifted = true;
                                codeSet = Code128Reader.CODE_CODE_B;
                                break;
                            case Code128Reader.CODE_CODE_B:
                                codeSet = Code128Reader.CODE_CODE_B;
                                break;
                            case Code128Reader.CODE_CODE_C:
                                codeSet = Code128Reader.CODE_CODE_C;
                                break;
                            case Code128Reader.CODE_STOP:
                                done = true;
                                break;
                        }
                    }
                    break;
                case Code128Reader.CODE_CODE_B:
                    if (code < 96) {
                        if (shiftUpperMode === upperMode) {
                            result += String.fromCharCode(' '.charCodeAt(0) + code);
                        } else {
                            result += String.fromCharCode(' '.charCodeAt(0) + code + 128);
                        }
                        shiftUpperMode = false;
                    } else {
                        if (code !== Code128Reader.CODE_STOP) {
                            lastCharacterWasPrintable = false;
                        }
                        switch(code){
                            case Code128Reader.CODE_FNC_1:
                                if (convertFNC1) {
                                    if (result.length === 0) {
                                        // GS1 specification 5.4.3.7. and 5.4.6.4. If the first char after the start code
                                        // is FNC1 then this is GS1-128. We add the symbology identifier.
                                        result += ']C1';
                                    } else {
                                        // GS1 specification 5.4.7.5. Every subsequent FNC1 is returned as ASCII 29 (GS)
                                        result += String.fromCharCode(29);
                                    }
                                }
                                break;
                            case Code128Reader.CODE_FNC_2:
                            case Code128Reader.CODE_FNC_3:
                                break;
                            case Code128Reader.CODE_FNC_4_B:
                                if (!upperMode && shiftUpperMode) {
                                    upperMode = true;
                                    shiftUpperMode = false;
                                } else if (upperMode && shiftUpperMode) {
                                    upperMode = false;
                                    shiftUpperMode = false;
                                } else {
                                    shiftUpperMode = true;
                                }
                                break;
                            case Code128Reader.CODE_SHIFT:
                                isNextShifted = true;
                                codeSet = Code128Reader.CODE_CODE_A;
                                break;
                            case Code128Reader.CODE_CODE_A:
                                codeSet = Code128Reader.CODE_CODE_A;
                                break;
                            case Code128Reader.CODE_CODE_C:
                                codeSet = Code128Reader.CODE_CODE_C;
                                break;
                            case Code128Reader.CODE_STOP:
                                done = true;
                                break;
                        }
                    }
                    break;
                case Code128Reader.CODE_CODE_C:
                    if (code < 100) {
                        if (code < 10) {
                            result += '0';
                        }
                        result += code;
                    } else {
                        if (code !== Code128Reader.CODE_STOP) {
                            lastCharacterWasPrintable = false;
                        }
                        switch(code){
                            case Code128Reader.CODE_FNC_1:
                                if (convertFNC1) {
                                    if (result.length === 0) {
                                        // GS1 specification 5.4.3.7. and 5.4.6.4. If the first char after the start code
                                        // is FNC1 then this is GS1-128. We add the symbology identifier.
                                        result += ']C1';
                                    } else {
                                        // GS1 specification 5.4.7.5. Every subsequent FNC1 is returned as ASCII 29 (GS)
                                        result += String.fromCharCode(29);
                                    }
                                }
                                break;
                            case Code128Reader.CODE_CODE_A:
                                codeSet = Code128Reader.CODE_CODE_A;
                                break;
                            case Code128Reader.CODE_CODE_B:
                                codeSet = Code128Reader.CODE_CODE_B;
                                break;
                            case Code128Reader.CODE_STOP:
                                done = true;
                                break;
                        }
                    }
                    break;
            }
            // Unshift back to another code set if we were shifted
            if (unshift) {
                codeSet = codeSet === Code128Reader.CODE_CODE_A ? Code128Reader.CODE_CODE_B : Code128Reader.CODE_CODE_A;
            }
        }
        var lastPatternSize = nextStart - lastStart;
        // Check for ample whitespace following pattern, but, to do this we first need to remember that
        // we fudged decoding CODE_STOP since it actually has 7 bars, not 6. There is a black bar left
        // to read off. Would be slightly better to properly read. Here we just skip it:
        nextStart = row.getNextUnset(nextStart);
        if (!row.isRange(nextStart, Math.min(row.getSize(), nextStart + (nextStart - lastStart) / 2), false)) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        // Pull out from sum the value of the penultimate check code
        checksumTotal -= multiplier * lastCode;
        // lastCode is the checksum then:
        if (checksumTotal % 103 !== lastCode) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ChecksumException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        // Need to pull out the check digits from string
        var resultLength = result.length;
        if (resultLength === 0) {
            // false positive
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        // Only bother if the result had at least one character, and if the checksum digit happened to
        // be a printable character. If it was just interpreted as a control code, nothing to remove.
        if (resultLength > 0 && lastCharacterWasPrintable) {
            if (codeSet === Code128Reader.CODE_CODE_C) {
                result = result.substring(0, resultLength - 2);
            } else {
                result = result.substring(0, resultLength - 1);
            }
        }
        var left = (startPatternInfo[1] + startPatternInfo[0]) / 2.0;
        var right = lastStart + lastPatternSize / 2.0;
        var rawCodesSize = rawCodes.length;
        var rawBytes = new Uint8Array(rawCodesSize);
        for(var i = 0; i < rawCodesSize; i++){
            rawBytes[i] = rawCodes[i];
        }
        var points = [
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](left, rowNumber),
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](right, rowNumber)
        ];
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](result, rawBytes, 0, points, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].CODE_128, new Date().getTime());
    };
    Code128Reader.CODE_PATTERNS = [
        Int32Array.from([
            2,
            1,
            2,
            2,
            2,
            2
        ]),
        Int32Array.from([
            2,
            2,
            2,
            1,
            2,
            2
        ]),
        Int32Array.from([
            2,
            2,
            2,
            2,
            2,
            1
        ]),
        Int32Array.from([
            1,
            2,
            1,
            2,
            2,
            3
        ]),
        Int32Array.from([
            1,
            2,
            1,
            3,
            2,
            2
        ]),
        Int32Array.from([
            1,
            3,
            1,
            2,
            2,
            2
        ]),
        Int32Array.from([
            1,
            2,
            2,
            2,
            1,
            3
        ]),
        Int32Array.from([
            1,
            2,
            2,
            3,
            1,
            2
        ]),
        Int32Array.from([
            1,
            3,
            2,
            2,
            1,
            2
        ]),
        Int32Array.from([
            2,
            2,
            1,
            2,
            1,
            3
        ]),
        Int32Array.from([
            2,
            2,
            1,
            3,
            1,
            2
        ]),
        Int32Array.from([
            2,
            3,
            1,
            2,
            1,
            2
        ]),
        Int32Array.from([
            1,
            1,
            2,
            2,
            3,
            2
        ]),
        Int32Array.from([
            1,
            2,
            2,
            1,
            3,
            2
        ]),
        Int32Array.from([
            1,
            2,
            2,
            2,
            3,
            1
        ]),
        Int32Array.from([
            1,
            1,
            3,
            2,
            2,
            2
        ]),
        Int32Array.from([
            1,
            2,
            3,
            1,
            2,
            2
        ]),
        Int32Array.from([
            1,
            2,
            3,
            2,
            2,
            1
        ]),
        Int32Array.from([
            2,
            2,
            3,
            2,
            1,
            1
        ]),
        Int32Array.from([
            2,
            2,
            1,
            1,
            3,
            2
        ]),
        Int32Array.from([
            2,
            2,
            1,
            2,
            3,
            1
        ]),
        Int32Array.from([
            2,
            1,
            3,
            2,
            1,
            2
        ]),
        Int32Array.from([
            2,
            2,
            3,
            1,
            1,
            2
        ]),
        Int32Array.from([
            3,
            1,
            2,
            1,
            3,
            1
        ]),
        Int32Array.from([
            3,
            1,
            1,
            2,
            2,
            2
        ]),
        Int32Array.from([
            3,
            2,
            1,
            1,
            2,
            2
        ]),
        Int32Array.from([
            3,
            2,
            1,
            2,
            2,
            1
        ]),
        Int32Array.from([
            3,
            1,
            2,
            2,
            1,
            2
        ]),
        Int32Array.from([
            3,
            2,
            2,
            1,
            1,
            2
        ]),
        Int32Array.from([
            3,
            2,
            2,
            2,
            1,
            1
        ]),
        Int32Array.from([
            2,
            1,
            2,
            1,
            2,
            3
        ]),
        Int32Array.from([
            2,
            1,
            2,
            3,
            2,
            1
        ]),
        Int32Array.from([
            2,
            3,
            2,
            1,
            2,
            1
        ]),
        Int32Array.from([
            1,
            1,
            1,
            3,
            2,
            3
        ]),
        Int32Array.from([
            1,
            3,
            1,
            1,
            2,
            3
        ]),
        Int32Array.from([
            1,
            3,
            1,
            3,
            2,
            1
        ]),
        Int32Array.from([
            1,
            1,
            2,
            3,
            1,
            3
        ]),
        Int32Array.from([
            1,
            3,
            2,
            1,
            1,
            3
        ]),
        Int32Array.from([
            1,
            3,
            2,
            3,
            1,
            1
        ]),
        Int32Array.from([
            2,
            1,
            1,
            3,
            1,
            3
        ]),
        Int32Array.from([
            2,
            3,
            1,
            1,
            1,
            3
        ]),
        Int32Array.from([
            2,
            3,
            1,
            3,
            1,
            1
        ]),
        Int32Array.from([
            1,
            1,
            2,
            1,
            3,
            3
        ]),
        Int32Array.from([
            1,
            1,
            2,
            3,
            3,
            1
        ]),
        Int32Array.from([
            1,
            3,
            2,
            1,
            3,
            1
        ]),
        Int32Array.from([
            1,
            1,
            3,
            1,
            2,
            3
        ]),
        Int32Array.from([
            1,
            1,
            3,
            3,
            2,
            1
        ]),
        Int32Array.from([
            1,
            3,
            3,
            1,
            2,
            1
        ]),
        Int32Array.from([
            3,
            1,
            3,
            1,
            2,
            1
        ]),
        Int32Array.from([
            2,
            1,
            1,
            3,
            3,
            1
        ]),
        Int32Array.from([
            2,
            3,
            1,
            1,
            3,
            1
        ]),
        Int32Array.from([
            2,
            1,
            3,
            1,
            1,
            3
        ]),
        Int32Array.from([
            2,
            1,
            3,
            3,
            1,
            1
        ]),
        Int32Array.from([
            2,
            1,
            3,
            1,
            3,
            1
        ]),
        Int32Array.from([
            3,
            1,
            1,
            1,
            2,
            3
        ]),
        Int32Array.from([
            3,
            1,
            1,
            3,
            2,
            1
        ]),
        Int32Array.from([
            3,
            3,
            1,
            1,
            2,
            1
        ]),
        Int32Array.from([
            3,
            1,
            2,
            1,
            1,
            3
        ]),
        Int32Array.from([
            3,
            1,
            2,
            3,
            1,
            1
        ]),
        Int32Array.from([
            3,
            3,
            2,
            1,
            1,
            1
        ]),
        Int32Array.from([
            3,
            1,
            4,
            1,
            1,
            1
        ]),
        Int32Array.from([
            2,
            2,
            1,
            4,
            1,
            1
        ]),
        Int32Array.from([
            4,
            3,
            1,
            1,
            1,
            1
        ]),
        Int32Array.from([
            1,
            1,
            1,
            2,
            2,
            4
        ]),
        Int32Array.from([
            1,
            1,
            1,
            4,
            2,
            2
        ]),
        Int32Array.from([
            1,
            2,
            1,
            1,
            2,
            4
        ]),
        Int32Array.from([
            1,
            2,
            1,
            4,
            2,
            1
        ]),
        Int32Array.from([
            1,
            4,
            1,
            1,
            2,
            2
        ]),
        Int32Array.from([
            1,
            4,
            1,
            2,
            2,
            1
        ]),
        Int32Array.from([
            1,
            1,
            2,
            2,
            1,
            4
        ]),
        Int32Array.from([
            1,
            1,
            2,
            4,
            1,
            2
        ]),
        Int32Array.from([
            1,
            2,
            2,
            1,
            1,
            4
        ]),
        Int32Array.from([
            1,
            2,
            2,
            4,
            1,
            1
        ]),
        Int32Array.from([
            1,
            4,
            2,
            1,
            1,
            2
        ]),
        Int32Array.from([
            1,
            4,
            2,
            2,
            1,
            1
        ]),
        Int32Array.from([
            2,
            4,
            1,
            2,
            1,
            1
        ]),
        Int32Array.from([
            2,
            2,
            1,
            1,
            1,
            4
        ]),
        Int32Array.from([
            4,
            1,
            3,
            1,
            1,
            1
        ]),
        Int32Array.from([
            2,
            4,
            1,
            1,
            1,
            2
        ]),
        Int32Array.from([
            1,
            3,
            4,
            1,
            1,
            1
        ]),
        Int32Array.from([
            1,
            1,
            1,
            2,
            4,
            2
        ]),
        Int32Array.from([
            1,
            2,
            1,
            1,
            4,
            2
        ]),
        Int32Array.from([
            1,
            2,
            1,
            2,
            4,
            1
        ]),
        Int32Array.from([
            1,
            1,
            4,
            2,
            1,
            2
        ]),
        Int32Array.from([
            1,
            2,
            4,
            1,
            1,
            2
        ]),
        Int32Array.from([
            1,
            2,
            4,
            2,
            1,
            1
        ]),
        Int32Array.from([
            4,
            1,
            1,
            2,
            1,
            2
        ]),
        Int32Array.from([
            4,
            2,
            1,
            1,
            1,
            2
        ]),
        Int32Array.from([
            4,
            2,
            1,
            2,
            1,
            1
        ]),
        Int32Array.from([
            2,
            1,
            2,
            1,
            4,
            1
        ]),
        Int32Array.from([
            2,
            1,
            4,
            1,
            2,
            1
        ]),
        Int32Array.from([
            4,
            1,
            2,
            1,
            2,
            1
        ]),
        Int32Array.from([
            1,
            1,
            1,
            1,
            4,
            3
        ]),
        Int32Array.from([
            1,
            1,
            1,
            3,
            4,
            1
        ]),
        Int32Array.from([
            1,
            3,
            1,
            1,
            4,
            1
        ]),
        Int32Array.from([
            1,
            1,
            4,
            1,
            1,
            3
        ]),
        Int32Array.from([
            1,
            1,
            4,
            3,
            1,
            1
        ]),
        Int32Array.from([
            4,
            1,
            1,
            1,
            1,
            3
        ]),
        Int32Array.from([
            4,
            1,
            1,
            3,
            1,
            1
        ]),
        Int32Array.from([
            1,
            1,
            3,
            1,
            4,
            1
        ]),
        Int32Array.from([
            1,
            1,
            4,
            1,
            3,
            1
        ]),
        Int32Array.from([
            3,
            1,
            1,
            1,
            4,
            1
        ]),
        Int32Array.from([
            4,
            1,
            1,
            1,
            3,
            1
        ]),
        Int32Array.from([
            2,
            1,
            1,
            4,
            1,
            2
        ]),
        Int32Array.from([
            2,
            1,
            1,
            2,
            1,
            4
        ]),
        Int32Array.from([
            2,
            1,
            1,
            2,
            3,
            2
        ]),
        Int32Array.from([
            2,
            3,
            3,
            1,
            1,
            1,
            2
        ])
    ];
    Code128Reader.MAX_AVG_VARIANCE = 0.25;
    Code128Reader.MAX_INDIVIDUAL_VARIANCE = 0.7;
    Code128Reader.CODE_SHIFT = 98;
    Code128Reader.CODE_CODE_C = 99;
    Code128Reader.CODE_CODE_B = 100;
    Code128Reader.CODE_CODE_A = 101;
    Code128Reader.CODE_FNC_1 = 102;
    Code128Reader.CODE_FNC_2 = 97;
    Code128Reader.CODE_FNC_3 = 96;
    Code128Reader.CODE_FNC_4_A = 101;
    Code128Reader.CODE_FNC_4_B = 100;
    Code128Reader.CODE_START_A = 103;
    Code128Reader.CODE_START_B = 104;
    Code128Reader.CODE_START_C = 105;
    Code128Reader.CODE_STOP = 106;
    return Code128Reader;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = Code128Reader;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/Code39Reader.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2008 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
/*namespace com.google.zxing.oned {*/ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/BarcodeFormat.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ChecksumException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ChecksumException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/FormatException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NotFoundException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/OneDReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Result.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ResultPoint.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
;
;
;
;
;
/**
 * <p>Decodes Code 39 barcodes. Supports "Full ASCII Code 39" if USE_CODE_39_EXTENDED_MODE is set.</p>
 *
 * <AUTHOR> Owen
 * @see Code93Reader
 */ var Code39Reader = function(_super) {
    __extends(Code39Reader, _super);
    /**
     * Creates a reader that assumes all encoded data is data, and does not treat the final
     * character as a check digit. It will not decoded "extended Code 39" sequences.
     */ // public Code39Reader() {
    //   this(false);
    // }
    /**
     * Creates a reader that can be configured to check the last character as a check digit.
     * It will not decoded "extended Code 39" sequences.
     *
     * @param usingCheckDigit if true, treat the last data character as a check digit, not
     * data, and verify that the checksum passes.
     */ // public Code39Reader(boolean usingCheckDigit) {
    //   this(usingCheckDigit, false);
    // }
    /**
     * Creates a reader that can be configured to check the last character as a check digit,
     * or optionally attempt to decode "extended Code 39" sequences that are used to encode
     * the full ASCII character set.
     *
     * @param usingCheckDigit if true, treat the last data character as a check digit, not
     * data, and verify that the checksum passes.
     * @param extendedMode if true, will attempt to decode extended Code 39 sequences in the
     * text.
     */ function Code39Reader(usingCheckDigit, extendedMode) {
        if (usingCheckDigit === void 0) {
            usingCheckDigit = false;
        }
        if (extendedMode === void 0) {
            extendedMode = false;
        }
        var _this = _super.call(this) || this;
        _this.usingCheckDigit = usingCheckDigit;
        _this.extendedMode = extendedMode;
        _this.decodeRowResult = '';
        _this.counters = new Int32Array(9);
        return _this;
    }
    Code39Reader.prototype.decodeRow = function(rowNumber, row, hints) {
        var e_1, _a, e_2, _b;
        var theCounters = this.counters;
        theCounters.fill(0);
        this.decodeRowResult = '';
        var start = Code39Reader.findAsteriskPattern(row, theCounters);
        // Read off white space
        var nextStart = row.getNextSet(start[1]);
        var end = row.getSize();
        var decodedChar;
        var lastStart;
        do {
            Code39Reader.recordPattern(row, nextStart, theCounters);
            var pattern = Code39Reader.toNarrowWidePattern(theCounters);
            if (pattern < 0) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
            }
            decodedChar = Code39Reader.patternToChar(pattern);
            this.decodeRowResult += decodedChar;
            lastStart = nextStart;
            try {
                for(var theCounters_1 = (e_1 = void 0, __values(theCounters)), theCounters_1_1 = theCounters_1.next(); !theCounters_1_1.done; theCounters_1_1 = theCounters_1.next()){
                    var counter = theCounters_1_1.value;
                    nextStart += counter;
                }
            } catch (e_1_1) {
                e_1 = {
                    error: e_1_1
                };
            } finally{
                try {
                    if (theCounters_1_1 && !theCounters_1_1.done && (_a = theCounters_1.return)) _a.call(theCounters_1);
                } finally{
                    if (e_1) throw e_1.error;
                }
            }
            // Read off white space
            nextStart = row.getNextSet(nextStart);
        }while (decodedChar !== '*')
        this.decodeRowResult = this.decodeRowResult.substring(0, this.decodeRowResult.length - 1); // remove asterisk
        // Look for whitespace after pattern:
        var lastPatternSize = 0;
        try {
            for(var theCounters_2 = __values(theCounters), theCounters_2_1 = theCounters_2.next(); !theCounters_2_1.done; theCounters_2_1 = theCounters_2.next()){
                var counter = theCounters_2_1.value;
                lastPatternSize += counter;
            }
        } catch (e_2_1) {
            e_2 = {
                error: e_2_1
            };
        } finally{
            try {
                if (theCounters_2_1 && !theCounters_2_1.done && (_b = theCounters_2.return)) _b.call(theCounters_2);
            } finally{
                if (e_2) throw e_2.error;
            }
        }
        var whiteSpaceAfterEnd = nextStart - lastStart - lastPatternSize;
        // If 50% of last pattern size, following last pattern, is not whitespace, fail
        // (but if it's whitespace to the very end of the image, that's OK)
        if (nextStart !== end && whiteSpaceAfterEnd * 2 < lastPatternSize) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        if (this.usingCheckDigit) {
            var max = this.decodeRowResult.length - 1;
            var total = 0;
            for(var i = 0; i < max; i++){
                total += Code39Reader.ALPHABET_STRING.indexOf(this.decodeRowResult.charAt(i));
            }
            if (this.decodeRowResult.charAt(max) !== Code39Reader.ALPHABET_STRING.charAt(total % 43)) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ChecksumException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
            }
            this.decodeRowResult = this.decodeRowResult.substring(0, max);
        }
        if (this.decodeRowResult.length === 0) {
            // false positive
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        var resultString;
        if (this.extendedMode) {
            resultString = Code39Reader.decodeExtended(this.decodeRowResult);
        } else {
            resultString = this.decodeRowResult;
        }
        var left = (start[1] + start[0]) / 2.0;
        var right = lastStart + lastPatternSize / 2.0;
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](resultString, null, 0, [
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](left, rowNumber),
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](right, rowNumber)
        ], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].CODE_39, new Date().getTime());
    };
    Code39Reader.findAsteriskPattern = function(row, counters) {
        var width = row.getSize();
        var rowOffset = row.getNextSet(0);
        var counterPosition = 0;
        var patternStart = rowOffset;
        var isWhite = false;
        var patternLength = counters.length;
        for(var i = rowOffset; i < width; i++){
            if (row.get(i) !== isWhite) {
                counters[counterPosition]++;
            } else {
                if (counterPosition === patternLength - 1) {
                    // Look for whitespace before start pattern, >= 50% of width of start pattern
                    if (this.toNarrowWidePattern(counters) === Code39Reader.ASTERISK_ENCODING && row.isRange(Math.max(0, patternStart - Math.floor((i - patternStart) / 2)), patternStart, false)) {
                        return [
                            patternStart,
                            i
                        ];
                    }
                    patternStart += counters[0] + counters[1];
                    counters.copyWithin(0, 2, 2 + counterPosition - 1);
                    counters[counterPosition - 1] = 0;
                    counters[counterPosition] = 0;
                    counterPosition--;
                } else {
                    counterPosition++;
                }
                counters[counterPosition] = 1;
                isWhite = !isWhite;
            }
        }
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
    };
    // For efficiency, returns -1 on failure. Not throwing here saved as many as 700 exceptions
    // per image when using some of our blackbox images.
    Code39Reader.toNarrowWidePattern = function(counters) {
        var e_3, _a;
        var numCounters = counters.length;
        var maxNarrowCounter = 0;
        var wideCounters;
        do {
            var minCounter = 0x7fffffff;
            try {
                for(var counters_1 = (e_3 = void 0, __values(counters)), counters_1_1 = counters_1.next(); !counters_1_1.done; counters_1_1 = counters_1.next()){
                    var counter = counters_1_1.value;
                    if (counter < minCounter && counter > maxNarrowCounter) {
                        minCounter = counter;
                    }
                }
            } catch (e_3_1) {
                e_3 = {
                    error: e_3_1
                };
            } finally{
                try {
                    if (counters_1_1 && !counters_1_1.done && (_a = counters_1.return)) _a.call(counters_1);
                } finally{
                    if (e_3) throw e_3.error;
                }
            }
            maxNarrowCounter = minCounter;
            wideCounters = 0;
            var totalWideCountersWidth = 0;
            var pattern = 0;
            for(var i = 0; i < numCounters; i++){
                var counter = counters[i];
                if (counter > maxNarrowCounter) {
                    pattern |= 1 << numCounters - 1 - i;
                    wideCounters++;
                    totalWideCountersWidth += counter;
                }
            }
            if (wideCounters === 3) {
                // Found 3 wide counters, but are they close enough in width?
                // We can perform a cheap, conservative check to see if any individual
                // counter is more than 1.5 times the average:
                for(var i = 0; i < numCounters && wideCounters > 0; i++){
                    var counter = counters[i];
                    if (counter > maxNarrowCounter) {
                        wideCounters--;
                        // totalWideCountersWidth = 3 * average, so this checks if counter >= 3/2 * average
                        if (counter * 2 >= totalWideCountersWidth) {
                            return -1;
                        }
                    }
                }
                return pattern;
            }
        }while (wideCounters > 3)
        return -1;
    };
    Code39Reader.patternToChar = function(pattern) {
        for(var i = 0; i < Code39Reader.CHARACTER_ENCODINGS.length; i++){
            if (Code39Reader.CHARACTER_ENCODINGS[i] === pattern) {
                return Code39Reader.ALPHABET_STRING.charAt(i);
            }
        }
        if (pattern === Code39Reader.ASTERISK_ENCODING) {
            return '*';
        }
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
    };
    Code39Reader.decodeExtended = function(encoded) {
        var length = encoded.length;
        var decoded = '';
        for(var i = 0; i < length; i++){
            var c = encoded.charAt(i);
            if (c === '+' || c === '$' || c === '%' || c === '/') {
                var next = encoded.charAt(i + 1);
                var decodedChar = '\0';
                switch(c){
                    case '+':
                        // +A to +Z map to a to z
                        if (next >= 'A' && next <= 'Z') {
                            decodedChar = String.fromCharCode(next.charCodeAt(0) + 32);
                        } else {
                            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                        }
                        break;
                    case '$':
                        // $A to $Z map to control codes SH to SB
                        if (next >= 'A' && next <= 'Z') {
                            decodedChar = String.fromCharCode(next.charCodeAt(0) - 64);
                        } else {
                            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                        }
                        break;
                    case '%':
                        // %A to %E map to control codes ESC to US
                        if (next >= 'A' && next <= 'E') {
                            decodedChar = String.fromCharCode(next.charCodeAt(0) - 38);
                        } else if (next >= 'F' && next <= 'J') {
                            decodedChar = String.fromCharCode(next.charCodeAt(0) - 11);
                        } else if (next >= 'K' && next <= 'O') {
                            decodedChar = String.fromCharCode(next.charCodeAt(0) + 16);
                        } else if (next >= 'P' && next <= 'T') {
                            decodedChar = String.fromCharCode(next.charCodeAt(0) + 43);
                        } else if (next === 'U') {
                            decodedChar = '\0';
                        } else if (next === 'V') {
                            decodedChar = '@';
                        } else if (next === 'W') {
                            decodedChar = '`';
                        } else if (next === 'X' || next === 'Y' || next === 'Z') {
                            decodedChar = '\x7f';
                        } else {
                            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                        }
                        break;
                    case '/':
                        // /A to /O map to ! to , and /Z maps to :
                        if (next >= 'A' && next <= 'O') {
                            decodedChar = String.fromCharCode(next.charCodeAt(0) - 32);
                        } else if (next === 'Z') {
                            decodedChar = ':';
                        } else {
                            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                        }
                        break;
                }
                decoded += decodedChar;
                // bump up i again since we read two characters
                i++;
            } else {
                decoded += c;
            }
        }
        return decoded;
    };
    Code39Reader.ALPHABET_STRING = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%';
    /**
     * These represent the encodings of characters, as patterns of wide and narrow bars.
     * The 9 least-significant bits of each int correspond to the pattern of wide and narrow,
     * with 1s representing "wide" and 0s representing narrow.
     */ Code39Reader.CHARACTER_ENCODINGS = [
        0x034,
        0x121,
        0x061,
        0x160,
        0x031,
        0x130,
        0x070,
        0x025,
        0x124,
        0x064,
        0x109,
        0x049,
        0x148,
        0x019,
        0x118,
        0x058,
        0x00D,
        0x10C,
        0x04C,
        0x01C,
        0x103,
        0x043,
        0x142,
        0x013,
        0x112,
        0x052,
        0x007,
        0x106,
        0x046,
        0x016,
        0x181,
        0x0C1,
        0x1C0,
        0x091,
        0x190,
        0x0D0,
        0x085,
        0x184,
        0x0C4,
        0x0A8,
        0x0A2,
        0x08A,
        0x02A // /-%
    ];
    Code39Reader.ASTERISK_ENCODING = 0x094;
    return Code39Reader;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = Code39Reader;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/Code93Reader.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2010 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
/*namespace com.google.zxing.oned {*/ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/BarcodeFormat.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ChecksumException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ChecksumException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/FormatException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NotFoundException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/OneDReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Result.js [app-ssr] (ecmascript)");
//import com.google.zxing.ResultMetadataType;
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ResultPoint.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
;
;
;
;
;
/**
 * <p>Decodes Code 93 barcodes.</p>
 *
 * <AUTHOR> Owen
 * @see Code39Reader
 */ var Code93Reader = function(_super) {
    __extends(Code93Reader, _super);
    //public Code93Reader() {
    //  decodeRowResult = new StringBuilder(20);
    //  counters = new int[6];
    //}
    function Code93Reader() {
        var _this = _super.call(this) || this;
        _this.decodeRowResult = '';
        _this.counters = new Int32Array(6);
        return _this;
    }
    Code93Reader.prototype.decodeRow = function(rowNumber, row, hints) {
        var e_1, _a, e_2, _b;
        var start = this.findAsteriskPattern(row);
        // Read off white space
        var nextStart = row.getNextSet(start[1]);
        var end = row.getSize();
        var theCounters = this.counters;
        theCounters.fill(0);
        this.decodeRowResult = '';
        var decodedChar;
        var lastStart;
        do {
            Code93Reader.recordPattern(row, nextStart, theCounters);
            var pattern = this.toPattern(theCounters);
            if (pattern < 0) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
            }
            decodedChar = this.patternToChar(pattern);
            this.decodeRowResult += decodedChar;
            lastStart = nextStart;
            try {
                for(var theCounters_1 = (e_1 = void 0, __values(theCounters)), theCounters_1_1 = theCounters_1.next(); !theCounters_1_1.done; theCounters_1_1 = theCounters_1.next()){
                    var counter = theCounters_1_1.value;
                    nextStart += counter;
                }
            } catch (e_1_1) {
                e_1 = {
                    error: e_1_1
                };
            } finally{
                try {
                    if (theCounters_1_1 && !theCounters_1_1.done && (_a = theCounters_1.return)) _a.call(theCounters_1);
                } finally{
                    if (e_1) throw e_1.error;
                }
            }
            // Read off white space
            nextStart = row.getNextSet(nextStart);
        }while (decodedChar !== '*')
        this.decodeRowResult = this.decodeRowResult.substring(0, this.decodeRowResult.length - 1); // remove asterisk
        var lastPatternSize = 0;
        try {
            for(var theCounters_2 = __values(theCounters), theCounters_2_1 = theCounters_2.next(); !theCounters_2_1.done; theCounters_2_1 = theCounters_2.next()){
                var counter = theCounters_2_1.value;
                lastPatternSize += counter;
            }
        } catch (e_2_1) {
            e_2 = {
                error: e_2_1
            };
        } finally{
            try {
                if (theCounters_2_1 && !theCounters_2_1.done && (_b = theCounters_2.return)) _b.call(theCounters_2);
            } finally{
                if (e_2) throw e_2.error;
            }
        }
        // Should be at least one more black module
        if (nextStart === end || !row.get(nextStart)) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        if (this.decodeRowResult.length < 2) {
            // false positive -- need at least 2 checksum digits
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        this.checkChecksums(this.decodeRowResult);
        // Remove checksum digits
        this.decodeRowResult = this.decodeRowResult.substring(0, this.decodeRowResult.length - 2);
        var resultString = this.decodeExtended(this.decodeRowResult);
        var left = (start[1] + start[0]) / 2.0;
        var right = lastStart + lastPatternSize / 2.0;
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](resultString, null, 0, [
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](left, rowNumber),
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](right, rowNumber)
        ], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].CODE_93, new Date().getTime());
    };
    Code93Reader.prototype.findAsteriskPattern = function(row) {
        var width = row.getSize();
        var rowOffset = row.getNextSet(0);
        this.counters.fill(0);
        var theCounters = this.counters;
        var patternStart = rowOffset;
        var isWhite = false;
        var patternLength = theCounters.length;
        var counterPosition = 0;
        for(var i = rowOffset; i < width; i++){
            if (row.get(i) !== isWhite) {
                theCounters[counterPosition]++;
            } else {
                if (counterPosition === patternLength - 1) {
                    if (this.toPattern(theCounters) === Code93Reader.ASTERISK_ENCODING) {
                        return new Int32Array([
                            patternStart,
                            i
                        ]);
                    }
                    patternStart += theCounters[0] + theCounters[1];
                    theCounters.copyWithin(0, 2, 2 + counterPosition - 1);
                    theCounters[counterPosition - 1] = 0;
                    theCounters[counterPosition] = 0;
                    counterPosition--;
                } else {
                    counterPosition++;
                }
                theCounters[counterPosition] = 1;
                isWhite = !isWhite;
            }
        }
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
    };
    Code93Reader.prototype.toPattern = function(counters) {
        var e_3, _a;
        var sum = 0;
        try {
            for(var counters_1 = __values(counters), counters_1_1 = counters_1.next(); !counters_1_1.done; counters_1_1 = counters_1.next()){
                var counter = counters_1_1.value;
                sum += counter;
            }
        } catch (e_3_1) {
            e_3 = {
                error: e_3_1
            };
        } finally{
            try {
                if (counters_1_1 && !counters_1_1.done && (_a = counters_1.return)) _a.call(counters_1);
            } finally{
                if (e_3) throw e_3.error;
            }
        }
        var pattern = 0;
        var max = counters.length;
        for(var i = 0; i < max; i++){
            var scaled = Math.round(counters[i] * 9.0 / sum);
            if (scaled < 1 || scaled > 4) {
                return -1;
            }
            if ((i & 0x01) === 0) {
                for(var j = 0; j < scaled; j++){
                    pattern = pattern << 1 | 0x01;
                }
            } else {
                pattern <<= scaled;
            }
        }
        return pattern;
    };
    Code93Reader.prototype.patternToChar = function(pattern) {
        for(var i = 0; i < Code93Reader.CHARACTER_ENCODINGS.length; i++){
            if (Code93Reader.CHARACTER_ENCODINGS[i] === pattern) {
                return Code93Reader.ALPHABET_STRING.charAt(i);
            }
        }
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
    };
    Code93Reader.prototype.decodeExtended = function(encoded) {
        var length = encoded.length;
        var decoded = '';
        for(var i = 0; i < length; i++){
            var c = encoded.charAt(i);
            if (c >= 'a' && c <= 'd') {
                if (i >= length - 1) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                }
                var next = encoded.charAt(i + 1);
                var decodedChar = '\0';
                switch(c){
                    case 'd':
                        // +A to +Z map to a to z
                        if (next >= 'A' && next <= 'Z') {
                            decodedChar = String.fromCharCode(next.charCodeAt(0) + 32);
                        } else {
                            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                        }
                        break;
                    case 'a':
                        // $A to $Z map to control codes SH to SB
                        if (next >= 'A' && next <= 'Z') {
                            decodedChar = String.fromCharCode(next.charCodeAt(0) - 64);
                        } else {
                            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                        }
                        break;
                    case 'b':
                        if (next >= 'A' && next <= 'E') {
                            // %A to %E map to control codes ESC to USep
                            decodedChar = String.fromCharCode(next.charCodeAt(0) - 38);
                        } else if (next >= 'F' && next <= 'J') {
                            // %F to %J map to ; < = > ?
                            decodedChar = String.fromCharCode(next.charCodeAt(0) - 11);
                        } else if (next >= 'K' && next <= 'O') {
                            // %K to %O map to [ \ ] ^ _
                            decodedChar = String.fromCharCode(next.charCodeAt(0) + 16);
                        } else if (next >= 'P' && next <= 'T') {
                            // %P to %T map to { | } ~ DEL
                            decodedChar = String.fromCharCode(next.charCodeAt(0) + 43);
                        } else if (next === 'U') {
                            // %U map to NUL
                            decodedChar = '\0';
                        } else if (next === 'V') {
                            // %V map to @
                            decodedChar = '@';
                        } else if (next === 'W') {
                            // %W map to `
                            decodedChar = '`';
                        } else if (next >= 'X' && next <= 'Z') {
                            // %X to %Z all map to DEL (127)
                            decodedChar = String.fromCharCode(127);
                        } else {
                            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                        }
                        break;
                    case 'c':
                        // /A to /O map to ! to , and /Z maps to :
                        if (next >= 'A' && next <= 'O') {
                            decodedChar = String.fromCharCode(next.charCodeAt(0) - 32);
                        } else if (next === 'Z') {
                            decodedChar = ':';
                        } else {
                            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                        }
                        break;
                }
                decoded += decodedChar;
                // bump up i again since we read two characters
                i++;
            } else {
                decoded += c;
            }
        }
        return decoded;
    };
    Code93Reader.prototype.checkChecksums = function(result) {
        var length = result.length;
        this.checkOneChecksum(result, length - 2, 20);
        this.checkOneChecksum(result, length - 1, 15);
    };
    Code93Reader.prototype.checkOneChecksum = function(result, checkPosition, weightMax) {
        var weight = 1;
        var total = 0;
        for(var i = checkPosition - 1; i >= 0; i--){
            total += weight * Code93Reader.ALPHABET_STRING.indexOf(result.charAt(i));
            if (++weight > weightMax) {
                weight = 1;
            }
        }
        if (result.charAt(checkPosition) !== Code93Reader.ALPHABET_STRING[total % 47]) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ChecksumException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
        }
    };
    // Note that 'abcd' are dummy characters in place of control characters.
    Code93Reader.ALPHABET_STRING = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%abcd*";
    /**
     * These represent the encodings of characters, as patterns of wide and narrow bars.
     * The 9 least-significant bits of each int correspond to the pattern of wide and narrow.
     */ Code93Reader.CHARACTER_ENCODINGS = [
        0x114,
        0x148,
        0x144,
        0x142,
        0x128,
        0x124,
        0x122,
        0x150,
        0x112,
        0x10A,
        0x1A8,
        0x1A4,
        0x1A2,
        0x194,
        0x192,
        0x18A,
        0x168,
        0x164,
        0x162,
        0x134,
        0x11A,
        0x158,
        0x14C,
        0x146,
        0x12C,
        0x116,
        0x1B4,
        0x1B2,
        0x1AC,
        0x1A6,
        0x196,
        0x19A,
        0x16C,
        0x166,
        0x136,
        0x13A,
        0x12E,
        0x1D4,
        0x1D2,
        0x1CA,
        0x16E,
        0x176,
        0x1AE,
        0x126,
        0x1DA,
        0x1D6,
        0x132,
        0x15E
    ];
    Code93Reader.ASTERISK_ENCODING = Code93Reader.CHARACTER_ENCODINGS[47];
    return Code93Reader;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = Code93Reader;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/ITFReader.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2008 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
/*namespace com.google.zxing.oned {*/ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/BarcodeFormat.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/DecodeHintType.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/FormatException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NotFoundException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Result.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ResultPoint.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/StringBuilder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/System.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/OneDReader.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
;
;
;
;
;
;
;
/**
 * <p>Decodes ITF barcodes.</p>
 *
 * <AUTHOR>
 */ var ITFReader = function(_super) {
    __extends(ITFReader, _super);
    function ITFReader() {
        // private static W = 3; // Pixel width of a 3x wide line
        // private static w = 2; // Pixel width of a 2x wide line
        // private static N = 1; // Pixed width of a narrow line
        var _this = _super !== null && _super.apply(this, arguments) || this;
        // Stores the actual narrow line width of the image being decoded.
        _this.narrowLineWidth = -1;
        return _this;
    }
    // See ITFWriter.PATTERNS
    /*
  
    /!**
     * Patterns of Wide / Narrow lines to indicate each digit
     *!/
    */ ITFReader.prototype.decodeRow = function(rowNumber, row, hints) {
        var e_1, _a;
        // Find out where the Middle section (payload) starts & ends
        var startRange = this.decodeStart(row);
        var endRange = this.decodeEnd(row);
        var result = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        ITFReader.decodeMiddle(row, startRange[1], endRange[0], result);
        var resultString = result.toString();
        var allowedLengths = null;
        if (hints != null) {
            allowedLengths = hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].ALLOWED_LENGTHS);
        }
        if (allowedLengths == null) {
            allowedLengths = ITFReader.DEFAULT_ALLOWED_LENGTHS;
        }
        // To avoid false positives with 2D barcodes (and other patterns), make
        // an assumption that the decoded string must be a 'standard' length if it's short
        var length = resultString.length;
        var lengthOK = false;
        var maxAllowedLength = 0;
        try {
            for(var allowedLengths_1 = __values(allowedLengths), allowedLengths_1_1 = allowedLengths_1.next(); !allowedLengths_1_1.done; allowedLengths_1_1 = allowedLengths_1.next()){
                var value = allowedLengths_1_1.value;
                if (length === value) {
                    lengthOK = true;
                    break;
                }
                if (value > maxAllowedLength) {
                    maxAllowedLength = value;
                }
            }
        } catch (e_1_1) {
            e_1 = {
                error: e_1_1
            };
        } finally{
            try {
                if (allowedLengths_1_1 && !allowedLengths_1_1.done && (_a = allowedLengths_1.return)) _a.call(allowedLengths_1);
            } finally{
                if (e_1) throw e_1.error;
            }
        }
        if (!lengthOK && length > maxAllowedLength) {
            lengthOK = true;
        }
        if (!lengthOK) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        var points = [
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](startRange[1], rowNumber),
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](endRange[0], rowNumber)
        ];
        var resultReturn = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](resultString, null, 0, points, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].ITF, new Date().getTime());
        return resultReturn;
    };
    /*
    /!**
     * @param row          row of black/white values to search
     * @param payloadStart offset of start pattern
     * @param resultString {@link StringBuilder} to append decoded chars to
     * @throws NotFoundException if decoding could not complete successfully
     *!/*/ ITFReader.decodeMiddle = function(row, payloadStart, payloadEnd, resultString) {
        // Digits are interleaved in pairs - 5 black lines for one digit, and the
        // 5
        // interleaved white lines for the second digit.
        // Therefore, need to scan 10 lines and then
        // split these into two arrays
        var counterDigitPair = new Int32Array(10); // 10
        var counterBlack = new Int32Array(5); // 5
        var counterWhite = new Int32Array(5); // 5
        counterDigitPair.fill(0);
        counterBlack.fill(0);
        counterWhite.fill(0);
        while(payloadStart < payloadEnd){
            // Get 10 runs of black/white.
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].recordPattern(row, payloadStart, counterDigitPair);
            // Split them into each array
            for(var k = 0; k < 5; k++){
                var twoK = 2 * k;
                counterBlack[k] = counterDigitPair[twoK];
                counterWhite[k] = counterDigitPair[twoK + 1];
            }
            var bestMatch = ITFReader.decodeDigit(counterBlack);
            resultString.append(bestMatch.toString());
            bestMatch = this.decodeDigit(counterWhite);
            resultString.append(bestMatch.toString());
            counterDigitPair.forEach(function(counterDigit) {
                payloadStart += counterDigit;
            });
        }
    };
    /*/!**
     * Identify where the start of the middle / payload section starts.
     *
     * @param row row of black/white values to search
     * @return Array, containing index of start of 'start block' and end of
     *         'start block'
     *!/*/ ITFReader.prototype.decodeStart = function(row) {
        var endStart = ITFReader.skipWhiteSpace(row);
        var startPattern = ITFReader.findGuardPattern(row, endStart, ITFReader.START_PATTERN);
        // Determine the width of a narrow line in pixels. We can do this by
        // getting the width of the start pattern and dividing by 4 because its
        // made up of 4 narrow lines.
        this.narrowLineWidth = (startPattern[1] - startPattern[0]) / 4;
        this.validateQuietZone(row, startPattern[0]);
        return startPattern;
    };
    /*/!**
     * The start & end patterns must be pre/post fixed by a quiet zone. This
     * zone must be at least 10 times the width of a narrow line.  Scan back until
     * we either get to the start of the barcode or match the necessary number of
     * quiet zone pixels.
     *
     * Note: Its assumed the row is reversed when using this method to find
     * quiet zone after the end pattern.
     *
     * ref: http://www.barcode-1.net/i25code.html
     *
     * @param row bit array representing the scanned barcode.
     * @param startPattern index into row of the start or end pattern.
     * @throws NotFoundException if the quiet zone cannot be found
     *!/*/ ITFReader.prototype.validateQuietZone = function(row, startPattern) {
        var quietCount = this.narrowLineWidth * 10; // expect to find this many pixels of quiet zone
        // if there are not so many pixel at all let's try as many as possible
        quietCount = quietCount < startPattern ? quietCount : startPattern;
        for(var i = startPattern - 1; quietCount > 0 && i >= 0; i--){
            if (row.get(i)) {
                break;
            }
            quietCount--;
        }
        if (quietCount !== 0) {
            // Unable to find the necessary number of quiet zone pixels.
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
    };
    /*
    /!**
     * Skip all whitespace until we get to the first black line.
     *
     * @param row row of black/white values to search
     * @return index of the first black line.
     * @throws NotFoundException Throws exception if no black lines are found in the row
     *!/*/ ITFReader.skipWhiteSpace = function(row) {
        var width = row.getSize();
        var endStart = row.getNextSet(0);
        if (endStart === width) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        return endStart;
    };
    /*/!**
     * Identify where the end of the middle / payload section ends.
     *
     * @param row row of black/white values to search
     * @return Array, containing index of start of 'end block' and end of 'end
     *         block'
     *!/*/ ITFReader.prototype.decodeEnd = function(row) {
        // For convenience, reverse the row and then
        // search from 'the start' for the end block
        row.reverse();
        try {
            var endStart = ITFReader.skipWhiteSpace(row);
            var endPattern = void 0;
            try {
                endPattern = ITFReader.findGuardPattern(row, endStart, ITFReader.END_PATTERN_REVERSED[0]);
            } catch (error) {
                if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]) {
                    endPattern = ITFReader.findGuardPattern(row, endStart, ITFReader.END_PATTERN_REVERSED[1]);
                }
            }
            // The start & end patterns must be pre/post fixed by a quiet zone. This
            // zone must be at least 10 times the width of a narrow line.
            // ref: http://www.barcode-1.net/i25code.html
            this.validateQuietZone(row, endPattern[0]);
            // Now recalculate the indices of where the 'endblock' starts & stops to
            // accommodate
            // the reversed nature of the search
            var temp = endPattern[0];
            endPattern[0] = row.getSize() - endPattern[1];
            endPattern[1] = row.getSize() - temp;
            return endPattern;
        } finally{
            // Put the row back the right way.
            row.reverse();
        }
    };
    /*
    /!**
     * @param row       row of black/white values to search
     * @param rowOffset position to start search
     * @param pattern   pattern of counts of number of black and white pixels that are
     *                  being searched for as a pattern
     * @return start/end horizontal offset of guard pattern, as an array of two
     *         ints
     * @throws NotFoundException if pattern is not found
     *!/*/ ITFReader.findGuardPattern = function(row, rowOffset, pattern) {
        var patternLength = pattern.length;
        var counters = new Int32Array(patternLength);
        var width = row.getSize();
        var isWhite = false;
        var counterPosition = 0;
        var patternStart = rowOffset;
        counters.fill(0);
        for(var x = rowOffset; x < width; x++){
            if (row.get(x) !== isWhite) {
                counters[counterPosition]++;
            } else {
                if (counterPosition === patternLength - 1) {
                    if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].patternMatchVariance(counters, pattern, ITFReader.MAX_INDIVIDUAL_VARIANCE) < ITFReader.MAX_AVG_VARIANCE) {
                        return [
                            patternStart,
                            x
                        ];
                    }
                    patternStart += counters[0] + counters[1];
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].arraycopy(counters, 2, counters, 0, counterPosition - 1);
                    counters[counterPosition - 1] = 0;
                    counters[counterPosition] = 0;
                    counterPosition--;
                } else {
                    counterPosition++;
                }
                counters[counterPosition] = 1;
                isWhite = !isWhite;
            }
        }
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
    };
    /*/!**
     * Attempts to decode a sequence of ITF black/white lines into single
     * digit.
     *
     * @param counters the counts of runs of observed black/white/black/... values
     * @return The decoded digit
     * @throws NotFoundException if digit cannot be decoded
     *!/*/ ITFReader.decodeDigit = function(counters) {
        var bestVariance = ITFReader.MAX_AVG_VARIANCE; // worst variance we'll accept
        var bestMatch = -1;
        var max = ITFReader.PATTERNS.length;
        for(var i = 0; i < max; i++){
            var pattern = ITFReader.PATTERNS[i];
            var variance = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].patternMatchVariance(counters, pattern, ITFReader.MAX_INDIVIDUAL_VARIANCE);
            if (variance < bestVariance) {
                bestVariance = variance;
                bestMatch = i;
            } else if (variance === bestVariance) {
                // if we find a second 'best match' with the same variance, we can not reliably report to have a suitable match
                bestMatch = -1;
            }
        }
        if (bestMatch >= 0) {
            return bestMatch % 10;
        } else {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
    };
    ITFReader.PATTERNS = [
        Int32Array.from([
            1,
            1,
            2,
            2,
            1
        ]),
        Int32Array.from([
            2,
            1,
            1,
            1,
            2
        ]),
        Int32Array.from([
            1,
            2,
            1,
            1,
            2
        ]),
        Int32Array.from([
            2,
            2,
            1,
            1,
            1
        ]),
        Int32Array.from([
            1,
            1,
            2,
            1,
            2
        ]),
        Int32Array.from([
            2,
            1,
            2,
            1,
            1
        ]),
        Int32Array.from([
            1,
            2,
            2,
            1,
            1
        ]),
        Int32Array.from([
            1,
            1,
            1,
            2,
            2
        ]),
        Int32Array.from([
            2,
            1,
            1,
            2,
            1
        ]),
        Int32Array.from([
            1,
            2,
            1,
            2,
            1
        ]),
        Int32Array.from([
            1,
            1,
            3,
            3,
            1
        ]),
        Int32Array.from([
            3,
            1,
            1,
            1,
            3
        ]),
        Int32Array.from([
            1,
            3,
            1,
            1,
            3
        ]),
        Int32Array.from([
            3,
            3,
            1,
            1,
            1
        ]),
        Int32Array.from([
            1,
            1,
            3,
            1,
            3
        ]),
        Int32Array.from([
            3,
            1,
            3,
            1,
            1
        ]),
        Int32Array.from([
            1,
            3,
            3,
            1,
            1
        ]),
        Int32Array.from([
            1,
            1,
            1,
            3,
            3
        ]),
        Int32Array.from([
            3,
            1,
            1,
            3,
            1
        ]),
        Int32Array.from([
            1,
            3,
            1,
            3,
            1
        ]) // 9
    ];
    ITFReader.MAX_AVG_VARIANCE = 0.38;
    ITFReader.MAX_INDIVIDUAL_VARIANCE = 0.5;
    /* /!** Valid ITF lengths. Anything longer than the largest value is also allowed. *!/*/ ITFReader.DEFAULT_ALLOWED_LENGTHS = [
        6,
        8,
        10,
        12,
        14
    ];
    /*/!**
     * Start/end guard pattern.
     *
     * Note: The end pattern is reversed because the row is reversed before
     * searching for the END_PATTERN
     *!/*/ ITFReader.START_PATTERN = Int32Array.from([
        1,
        1,
        1,
        1
    ]);
    ITFReader.END_PATTERN_REVERSED = [
        Int32Array.from([
            1,
            1,
            2
        ]),
        Int32Array.from([
            1,
            1,
            3
        ]) // 3x
    ];
    return ITFReader;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = ITFReader;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/AbstractUPCEANReader.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2008 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/OneDReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NotFoundException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/FormatException.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
;
/**
 * <p>Encapsulates functionality and implementation that is common to UPC and EAN families
 * of one-dimensional barcodes.</p>
 *
 * <AUTHOR> (Daniel Switkin)
 * <AUTHOR> Owen
 * <AUTHOR> (Alasdair Mackintosh)
 */ var AbstractUPCEANReader = function(_super) {
    __extends(AbstractUPCEANReader, _super);
    function AbstractUPCEANReader() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.decodeRowStringBuffer = '';
        return _this;
    }
    // private final UPCEANExtensionSupport extensionReader;
    // private final EANManufacturerOrgSupport eanManSupport;
    /*
    protected UPCEANReader() {
        decodeRowStringBuffer = new StringBuilder(20);
        extensionReader = new UPCEANExtensionSupport();
        eanManSupport = new EANManufacturerOrgSupport();
    }
    */ AbstractUPCEANReader.findStartGuardPattern = function(row) {
        var foundStart = false;
        var startRange;
        var nextStart = 0;
        var counters = Int32Array.from([
            0,
            0,
            0
        ]);
        while(!foundStart){
            counters = Int32Array.from([
                0,
                0,
                0
            ]);
            startRange = AbstractUPCEANReader.findGuardPattern(row, nextStart, false, this.START_END_PATTERN, counters);
            var start = startRange[0];
            nextStart = startRange[1];
            var quietStart = start - (nextStart - start);
            if (quietStart >= 0) {
                foundStart = row.isRange(quietStart, start, false);
            }
        }
        return startRange;
    };
    AbstractUPCEANReader.checkChecksum = function(s) {
        return AbstractUPCEANReader.checkStandardUPCEANChecksum(s);
    };
    AbstractUPCEANReader.checkStandardUPCEANChecksum = function(s) {
        var length = s.length;
        if (length === 0) return false;
        var check = parseInt(s.charAt(length - 1), 10);
        return AbstractUPCEANReader.getStandardUPCEANChecksum(s.substring(0, length - 1)) === check;
    };
    AbstractUPCEANReader.getStandardUPCEANChecksum = function(s) {
        var length = s.length;
        var sum = 0;
        for(var i = length - 1; i >= 0; i -= 2){
            var digit = s.charAt(i).charCodeAt(0) - '0'.charCodeAt(0);
            if (digit < 0 || digit > 9) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
            }
            sum += digit;
        }
        sum *= 3;
        for(var i = length - 2; i >= 0; i -= 2){
            var digit = s.charAt(i).charCodeAt(0) - '0'.charCodeAt(0);
            if (digit < 0 || digit > 9) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
            }
            sum += digit;
        }
        return (1000 - sum) % 10;
    };
    AbstractUPCEANReader.decodeEnd = function(row, endStart) {
        return AbstractUPCEANReader.findGuardPattern(row, endStart, false, AbstractUPCEANReader.START_END_PATTERN, new Int32Array(AbstractUPCEANReader.START_END_PATTERN.length).fill(0));
    };
    /**
     * @throws NotFoundException
     */ AbstractUPCEANReader.findGuardPatternWithoutCounters = function(row, rowOffset, whiteFirst, pattern) {
        return this.findGuardPattern(row, rowOffset, whiteFirst, pattern, new Int32Array(pattern.length));
    };
    /**
     * @param row row of black/white values to search
     * @param rowOffset position to start search
     * @param whiteFirst if true, indicates that the pattern specifies white/black/white/...
     * pixel counts, otherwise, it is interpreted as black/white/black/...
     * @param pattern pattern of counts of number of black and white pixels that are being
     * searched for as a pattern
     * @param counters array of counters, as long as pattern, to re-use
     * @return start/end horizontal offset of guard pattern, as an array of two ints
     * @throws NotFoundException if pattern is not found
     */ AbstractUPCEANReader.findGuardPattern = function(row, rowOffset, whiteFirst, pattern, counters) {
        var width = row.getSize();
        rowOffset = whiteFirst ? row.getNextUnset(rowOffset) : row.getNextSet(rowOffset);
        var counterPosition = 0;
        var patternStart = rowOffset;
        var patternLength = pattern.length;
        var isWhite = whiteFirst;
        for(var x = rowOffset; x < width; x++){
            if (row.get(x) !== isWhite) {
                counters[counterPosition]++;
            } else {
                if (counterPosition === patternLength - 1) {
                    if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].patternMatchVariance(counters, pattern, AbstractUPCEANReader.MAX_INDIVIDUAL_VARIANCE) < AbstractUPCEANReader.MAX_AVG_VARIANCE) {
                        return Int32Array.from([
                            patternStart,
                            x
                        ]);
                    }
                    patternStart += counters[0] + counters[1];
                    var slice = counters.slice(2, counters.length);
                    for(var i = 0; i < counterPosition - 1; i++){
                        counters[i] = slice[i];
                    }
                    counters[counterPosition - 1] = 0;
                    counters[counterPosition] = 0;
                    counterPosition--;
                } else {
                    counterPosition++;
                }
                counters[counterPosition] = 1;
                isWhite = !isWhite;
            }
        }
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
    };
    AbstractUPCEANReader.decodeDigit = function(row, counters, rowOffset, patterns) {
        this.recordPattern(row, rowOffset, counters);
        var bestVariance = this.MAX_AVG_VARIANCE;
        var bestMatch = -1;
        var max = patterns.length;
        for(var i = 0; i < max; i++){
            var pattern = patterns[i];
            var variance = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].patternMatchVariance(counters, pattern, AbstractUPCEANReader.MAX_INDIVIDUAL_VARIANCE);
            if (variance < bestVariance) {
                bestVariance = variance;
                bestMatch = i;
            }
        }
        if (bestMatch >= 0) {
            return bestMatch;
        } else {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
    };
    // These two values are critical for determining how permissive the decoding will be.
    // We've arrived at these values through a lot of trial and error. Setting them any higher
    // lets false positives creep in quickly.
    AbstractUPCEANReader.MAX_AVG_VARIANCE = 0.48;
    AbstractUPCEANReader.MAX_INDIVIDUAL_VARIANCE = 0.7;
    /**
     * Start/end guard pattern.
     */ AbstractUPCEANReader.START_END_PATTERN = Int32Array.from([
        1,
        1,
        1
    ]);
    /**
     * Pattern marking the middle of a UPC/EAN pattern, separating the two halves.
     */ AbstractUPCEANReader.MIDDLE_PATTERN = Int32Array.from([
        1,
        1,
        1,
        1,
        1
    ]);
    /**
     * end guard pattern.
     */ AbstractUPCEANReader.END_PATTERN = Int32Array.from([
        1,
        1,
        1,
        1,
        1,
        1
    ]);
    /**
     * "Odd", or "L" patterns used to encode UPC/EAN digits.
     */ AbstractUPCEANReader.L_PATTERNS = [
        Int32Array.from([
            3,
            2,
            1,
            1
        ]),
        Int32Array.from([
            2,
            2,
            2,
            1
        ]),
        Int32Array.from([
            2,
            1,
            2,
            2
        ]),
        Int32Array.from([
            1,
            4,
            1,
            1
        ]),
        Int32Array.from([
            1,
            1,
            3,
            2
        ]),
        Int32Array.from([
            1,
            2,
            3,
            1
        ]),
        Int32Array.from([
            1,
            1,
            1,
            4
        ]),
        Int32Array.from([
            1,
            3,
            1,
            2
        ]),
        Int32Array.from([
            1,
            2,
            1,
            3
        ]),
        Int32Array.from([
            3,
            1,
            1,
            2
        ])
    ];
    return AbstractUPCEANReader;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = AbstractUPCEANReader;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/UPCEANExtension5Support.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright (C) 2010 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/BarcodeFormat.js [app-ssr] (ecmascript)");
// import UPCEANReader from './UPCEANReader';
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$AbstractUPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/AbstractUPCEANReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Result.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ResultPoint.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultMetadataType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ResultMetadataType.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NotFoundException.js [app-ssr] (ecmascript)");
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
;
;
;
;
/**
 * @see UPCEANExtension2Support
 */ var UPCEANExtension5Support = function() {
    function UPCEANExtension5Support() {
        this.CHECK_DIGIT_ENCODINGS = [
            0x18,
            0x14,
            0x12,
            0x11,
            0x0C,
            0x06,
            0x03,
            0x0A,
            0x09,
            0x05
        ];
        this.decodeMiddleCounters = Int32Array.from([
            0,
            0,
            0,
            0
        ]);
        this.decodeRowStringBuffer = '';
    }
    UPCEANExtension5Support.prototype.decodeRow = function(rowNumber, row, extensionStartRange) {
        var result = this.decodeRowStringBuffer;
        var end = this.decodeMiddle(row, extensionStartRange, result);
        var resultString = result.toString();
        var extensionData = UPCEANExtension5Support.parseExtensionString(resultString);
        var resultPoints = [
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]((extensionStartRange[0] + extensionStartRange[1]) / 2.0, rowNumber),
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](end, rowNumber)
        ];
        var extensionResult = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](resultString, null, 0, resultPoints, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].UPC_EAN_EXTENSION, new Date().getTime());
        if (extensionData != null) {
            extensionResult.putAllMetadata(extensionData);
        }
        return extensionResult;
    };
    UPCEANExtension5Support.prototype.decodeMiddle = function(row, startRange, resultString) {
        var e_1, _a;
        var counters = this.decodeMiddleCounters;
        counters[0] = 0;
        counters[1] = 0;
        counters[2] = 0;
        counters[3] = 0;
        var end = row.getSize();
        var rowOffset = startRange[1];
        var lgPatternFound = 0;
        for(var x = 0; x < 5 && rowOffset < end; x++){
            var bestMatch = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$AbstractUPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].decodeDigit(row, counters, rowOffset, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$AbstractUPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].L_AND_G_PATTERNS);
            resultString += String.fromCharCode('0'.charCodeAt(0) + bestMatch % 10);
            try {
                for(var counters_1 = (e_1 = void 0, __values(counters)), counters_1_1 = counters_1.next(); !counters_1_1.done; counters_1_1 = counters_1.next()){
                    var counter = counters_1_1.value;
                    rowOffset += counter;
                }
            } catch (e_1_1) {
                e_1 = {
                    error: e_1_1
                };
            } finally{
                try {
                    if (counters_1_1 && !counters_1_1.done && (_a = counters_1.return)) _a.call(counters_1);
                } finally{
                    if (e_1) throw e_1.error;
                }
            }
            if (bestMatch >= 10) {
                lgPatternFound |= 1 << 4 - x;
            }
            if (x !== 4) {
                // Read off separator if not last
                rowOffset = row.getNextSet(rowOffset);
                rowOffset = row.getNextUnset(rowOffset);
            }
        }
        if (resultString.length !== 5) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        var checkDigit = this.determineCheckDigit(lgPatternFound);
        if (UPCEANExtension5Support.extensionChecksum(resultString.toString()) !== checkDigit) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        return rowOffset;
    };
    UPCEANExtension5Support.extensionChecksum = function(s) {
        var length = s.length;
        var sum = 0;
        for(var i = length - 2; i >= 0; i -= 2){
            sum += s.charAt(i).charCodeAt(0) - '0'.charCodeAt(0);
        }
        sum *= 3;
        for(var i = length - 1; i >= 0; i -= 2){
            sum += s.charAt(i).charCodeAt(0) - '0'.charCodeAt(0);
        }
        sum *= 3;
        return sum % 10;
    };
    UPCEANExtension5Support.prototype.determineCheckDigit = function(lgPatternFound) {
        for(var d = 0; d < 10; d++){
            if (lgPatternFound === this.CHECK_DIGIT_ENCODINGS[d]) {
                return d;
            }
        }
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
    };
    UPCEANExtension5Support.parseExtensionString = function(raw) {
        if (raw.length !== 5) {
            return null;
        }
        var value = UPCEANExtension5Support.parseExtension5String(raw);
        if (value == null) {
            return null;
        }
        return new Map([
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultMetadataType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].SUGGESTED_PRICE,
                value
            ]
        ]);
    };
    UPCEANExtension5Support.parseExtension5String = function(raw) {
        var currency;
        switch(raw.charAt(0)){
            case '0':
                currency = '£';
                break;
            case '5':
                currency = '$';
                break;
            case '9':
                // Reference: http://www.jollytech.com
                switch(raw){
                    case '90000':
                        // No suggested retail price
                        return null;
                    case '99991':
                        // Complementary
                        return '0.00';
                    case '99990':
                        return 'Used';
                }
                // Otherwise... unknown currency?
                currency = '';
                break;
            default:
                currency = '';
                break;
        }
        var rawAmount = parseInt(raw.substring(1));
        var unitsString = (rawAmount / 100).toString();
        var hundredths = rawAmount % 100;
        var hundredthsString = hundredths < 10 ? '0' + hundredths : hundredths.toString(); // fixme
        return currency + unitsString + '.' + hundredthsString;
    };
    return UPCEANExtension5Support;
}();
const __TURBOPACK__default__export__ = UPCEANExtension5Support;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/UPCEANExtension2Support.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright (C) 2012 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/BarcodeFormat.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$AbstractUPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/AbstractUPCEANReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Result.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ResultPoint.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultMetadataType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ResultMetadataType.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NotFoundException.js [app-ssr] (ecmascript)");
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
;
;
;
;
/**
 * @see UPCEANExtension5Support
 */ var UPCEANExtension2Support = function() {
    function UPCEANExtension2Support() {
        this.decodeMiddleCounters = Int32Array.from([
            0,
            0,
            0,
            0
        ]);
        this.decodeRowStringBuffer = '';
    }
    UPCEANExtension2Support.prototype.decodeRow = function(rowNumber, row, extensionStartRange) {
        var result = this.decodeRowStringBuffer;
        var end = this.decodeMiddle(row, extensionStartRange, result);
        var resultString = result.toString();
        var extensionData = UPCEANExtension2Support.parseExtensionString(resultString);
        var resultPoints = [
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]((extensionStartRange[0] + extensionStartRange[1]) / 2.0, rowNumber),
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](end, rowNumber)
        ];
        var extensionResult = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](resultString, null, 0, resultPoints, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].UPC_EAN_EXTENSION, new Date().getTime());
        if (extensionData != null) {
            extensionResult.putAllMetadata(extensionData);
        }
        return extensionResult;
    };
    UPCEANExtension2Support.prototype.decodeMiddle = function(row, startRange, resultString) {
        var e_1, _a;
        var counters = this.decodeMiddleCounters;
        counters[0] = 0;
        counters[1] = 0;
        counters[2] = 0;
        counters[3] = 0;
        var end = row.getSize();
        var rowOffset = startRange[1];
        var checkParity = 0;
        for(var x = 0; x < 2 && rowOffset < end; x++){
            var bestMatch = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$AbstractUPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].decodeDigit(row, counters, rowOffset, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$AbstractUPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].L_AND_G_PATTERNS);
            resultString += String.fromCharCode('0'.charCodeAt(0) + bestMatch % 10);
            try {
                for(var counters_1 = (e_1 = void 0, __values(counters)), counters_1_1 = counters_1.next(); !counters_1_1.done; counters_1_1 = counters_1.next()){
                    var counter = counters_1_1.value;
                    rowOffset += counter;
                }
            } catch (e_1_1) {
                e_1 = {
                    error: e_1_1
                };
            } finally{
                try {
                    if (counters_1_1 && !counters_1_1.done && (_a = counters_1.return)) _a.call(counters_1);
                } finally{
                    if (e_1) throw e_1.error;
                }
            }
            if (bestMatch >= 10) {
                checkParity |= 1 << 1 - x;
            }
            if (x !== 1) {
                // Read off separator if not last
                rowOffset = row.getNextSet(rowOffset);
                rowOffset = row.getNextUnset(rowOffset);
            }
        }
        if (resultString.length !== 2) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        if (parseInt(resultString.toString()) % 4 !== checkParity) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        return rowOffset;
    };
    UPCEANExtension2Support.parseExtensionString = function(raw) {
        if (raw.length !== 2) {
            return null;
        }
        return new Map([
            [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultMetadataType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].ISSUE_NUMBER,
                parseInt(raw)
            ]
        ]);
    };
    return UPCEANExtension2Support;
}();
const __TURBOPACK__default__export__ = UPCEANExtension2Support;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/UPCEANExtensionSupport.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright (C) 2010 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$AbstractUPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/AbstractUPCEANReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEANExtension5Support$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/UPCEANExtension5Support.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEANExtension2Support$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/UPCEANExtension2Support.js [app-ssr] (ecmascript)");
;
;
;
var UPCEANExtensionSupport = function() {
    function UPCEANExtensionSupport() {}
    UPCEANExtensionSupport.decodeRow = function(rowNumber, row, rowOffset) {
        var extensionStartRange = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$AbstractUPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].findGuardPattern(row, rowOffset, false, this.EXTENSION_START_PATTERN, new Int32Array(this.EXTENSION_START_PATTERN.length).fill(0));
        try {
            // return null;
            var fiveSupport = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEANExtension5Support$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
            return fiveSupport.decodeRow(rowNumber, row, extensionStartRange);
        } catch (err) {
            // return null;
            var twoSupport = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEANExtension2Support$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
            return twoSupport.decodeRow(rowNumber, row, extensionStartRange);
        }
    };
    UPCEANExtensionSupport.EXTENSION_START_PATTERN = Int32Array.from([
        1,
        1,
        2
    ]);
    return UPCEANExtensionSupport;
}();
const __TURBOPACK__default__export__ = UPCEANExtensionSupport;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/UPCEANReader.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2008 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/BarcodeFormat.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/DecodeHintType.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Result.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultMetadataType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ResultMetadataType.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ResultPoint.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEANExtensionSupport$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/UPCEANExtensionSupport.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$AbstractUPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/AbstractUPCEANReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NotFoundException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/FormatException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ChecksumException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ChecksumException.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
;
;
;
;
;
;
;
;
/**
 * <p>Encapsulates functionality and implementation that is common to UPC and EAN families
 * of one-dimensional barcodes.</p>
 *
 * <AUTHOR> (Daniel Switkin)
 * <AUTHOR> Owen
 * <AUTHOR> (Alasdair Mackintosh)
 */ var UPCEANReader = function(_super) {
    __extends(UPCEANReader, _super);
    function UPCEANReader() {
        var _this = _super.call(this) || this;
        _this.decodeRowStringBuffer = '';
        UPCEANReader.L_AND_G_PATTERNS = UPCEANReader.L_PATTERNS.map(function(arr) {
            return Int32Array.from(arr);
        });
        for(var i = 10; i < 20; i++){
            var widths = UPCEANReader.L_PATTERNS[i - 10];
            var reversedWidths = new Int32Array(widths.length);
            for(var j = 0; j < widths.length; j++){
                reversedWidths[j] = widths[widths.length - j - 1];
            }
            UPCEANReader.L_AND_G_PATTERNS[i] = reversedWidths;
        }
        return _this;
    }
    UPCEANReader.prototype.decodeRow = function(rowNumber, row, hints) {
        var startGuardRange = UPCEANReader.findStartGuardPattern(row);
        var resultPointCallback = hints == null ? null : hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].NEED_RESULT_POINT_CALLBACK);
        if (resultPointCallback != null) {
            var resultPoint_1 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]((startGuardRange[0] + startGuardRange[1]) / 2.0, rowNumber);
            resultPointCallback.foundPossibleResultPoint(resultPoint_1);
        }
        var budello = this.decodeMiddle(row, startGuardRange, this.decodeRowStringBuffer);
        var endStart = budello.rowOffset;
        var result = budello.resultString;
        if (resultPointCallback != null) {
            var resultPoint_2 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](endStart, rowNumber);
            resultPointCallback.foundPossibleResultPoint(resultPoint_2);
        }
        var endRange = UPCEANReader.decodeEnd(row, endStart);
        if (resultPointCallback != null) {
            var resultPoint_3 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]((endRange[0] + endRange[1]) / 2.0, rowNumber);
            resultPointCallback.foundPossibleResultPoint(resultPoint_3);
        }
        // Make sure there is a quiet zone at least as big as the end pattern after the barcode. The
        // spec might want more whitespace, but in practice this is the maximum we can count on.
        var end = endRange[1];
        var quietEnd = end + (end - endRange[0]);
        if (quietEnd >= row.getSize() || !row.isRange(end, quietEnd, false)) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        var resultString = result.toString();
        // UPC/EAN should never be less than 8 chars anyway
        if (resultString.length < 8) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        if (!UPCEANReader.checkChecksum(resultString)) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ChecksumException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        var left = (startGuardRange[1] + startGuardRange[0]) / 2.0;
        var right = (endRange[1] + endRange[0]) / 2.0;
        var format = this.getBarcodeFormat();
        var resultPoint = [
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](left, rowNumber),
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](right, rowNumber)
        ];
        var decodeResult = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](resultString, null, 0, resultPoint, format, new Date().getTime());
        var extensionLength = 0;
        try {
            var extensionResult = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEANExtensionSupport$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].decodeRow(rowNumber, row, endRange[1]);
            decodeResult.putMetadata(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultMetadataType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].UPC_EAN_EXTENSION, extensionResult.getText());
            decodeResult.putAllMetadata(extensionResult.getResultMetadata());
            decodeResult.addResultPoints(extensionResult.getResultPoints());
            extensionLength = extensionResult.getText().length;
        } catch (err) {}
        var allowedExtensions = hints == null ? null : hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].ALLOWED_EAN_EXTENSIONS);
        if (allowedExtensions != null) {
            var valid = false;
            for(var length_1 in allowedExtensions){
                if (extensionLength.toString() === length_1) {
                    valid = true;
                    break;
                }
            }
            if (!valid) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
            }
        }
        if (format === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].EAN_13 || format === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].UPC_A) {
        // let countryID = eanManSupport.lookupContryIdentifier(resultString); todo
        // if (countryID != null) {
        //     decodeResult.putMetadata(ResultMetadataType.POSSIBLE_COUNTRY, countryID);
        // }
        }
        return decodeResult;
    };
    UPCEANReader.checkChecksum = function(s) {
        return UPCEANReader.checkStandardUPCEANChecksum(s);
    };
    UPCEANReader.checkStandardUPCEANChecksum = function(s) {
        var length = s.length;
        if (length === 0) return false;
        var check = parseInt(s.charAt(length - 1), 10);
        return UPCEANReader.getStandardUPCEANChecksum(s.substring(0, length - 1)) === check;
    };
    UPCEANReader.getStandardUPCEANChecksum = function(s) {
        var length = s.length;
        var sum = 0;
        for(var i = length - 1; i >= 0; i -= 2){
            var digit = s.charAt(i).charCodeAt(0) - '0'.charCodeAt(0);
            if (digit < 0 || digit > 9) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
            }
            sum += digit;
        }
        sum *= 3;
        for(var i = length - 2; i >= 0; i -= 2){
            var digit = s.charAt(i).charCodeAt(0) - '0'.charCodeAt(0);
            if (digit < 0 || digit > 9) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
            }
            sum += digit;
        }
        return (1000 - sum) % 10;
    };
    UPCEANReader.decodeEnd = function(row, endStart) {
        return UPCEANReader.findGuardPattern(row, endStart, false, UPCEANReader.START_END_PATTERN, new Int32Array(UPCEANReader.START_END_PATTERN.length).fill(0));
    };
    return UPCEANReader;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$AbstractUPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = UPCEANReader;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/EAN13Reader.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2008 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/BarcodeFormat.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/UPCEANReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NotFoundException.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
;
/**
 * <p>Implements decoding of the EAN-13 format.</p>
 *
 * <AUTHOR> (Daniel Switkin)
 * <AUTHOR> Owen
 * <AUTHOR> (Alasdair Mackintosh)
 */ var EAN13Reader = function(_super) {
    __extends(EAN13Reader, _super);
    function EAN13Reader() {
        var _this = _super.call(this) || this;
        _this.decodeMiddleCounters = Int32Array.from([
            0,
            0,
            0,
            0
        ]);
        return _this;
    }
    EAN13Reader.prototype.decodeMiddle = function(row, startRange, resultString) {
        var e_1, _a, e_2, _b;
        var counters = this.decodeMiddleCounters;
        counters[0] = 0;
        counters[1] = 0;
        counters[2] = 0;
        counters[3] = 0;
        var end = row.getSize();
        var rowOffset = startRange[1];
        var lgPatternFound = 0;
        for(var x = 0; x < 6 && rowOffset < end; x++){
            var bestMatch = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].decodeDigit(row, counters, rowOffset, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].L_AND_G_PATTERNS);
            resultString += String.fromCharCode('0'.charCodeAt(0) + bestMatch % 10);
            try {
                for(var counters_1 = (e_1 = void 0, __values(counters)), counters_1_1 = counters_1.next(); !counters_1_1.done; counters_1_1 = counters_1.next()){
                    var counter = counters_1_1.value;
                    rowOffset += counter;
                }
            } catch (e_1_1) {
                e_1 = {
                    error: e_1_1
                };
            } finally{
                try {
                    if (counters_1_1 && !counters_1_1.done && (_a = counters_1.return)) _a.call(counters_1);
                } finally{
                    if (e_1) throw e_1.error;
                }
            }
            if (bestMatch >= 10) {
                lgPatternFound |= 1 << 5 - x;
            }
        }
        resultString = EAN13Reader.determineFirstDigit(resultString, lgPatternFound);
        var middleRange = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].findGuardPattern(row, rowOffset, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].MIDDLE_PATTERN, new Int32Array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].MIDDLE_PATTERN.length).fill(0));
        rowOffset = middleRange[1];
        for(var x = 0; x < 6 && rowOffset < end; x++){
            var bestMatch = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].decodeDigit(row, counters, rowOffset, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].L_PATTERNS);
            resultString += String.fromCharCode('0'.charCodeAt(0) + bestMatch);
            try {
                for(var counters_2 = (e_2 = void 0, __values(counters)), counters_2_1 = counters_2.next(); !counters_2_1.done; counters_2_1 = counters_2.next()){
                    var counter = counters_2_1.value;
                    rowOffset += counter;
                }
            } catch (e_2_1) {
                e_2 = {
                    error: e_2_1
                };
            } finally{
                try {
                    if (counters_2_1 && !counters_2_1.done && (_b = counters_2.return)) _b.call(counters_2);
                } finally{
                    if (e_2) throw e_2.error;
                }
            }
        }
        return {
            rowOffset: rowOffset,
            resultString: resultString
        };
    };
    EAN13Reader.prototype.getBarcodeFormat = function() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].EAN_13;
    };
    EAN13Reader.determineFirstDigit = function(resultString, lgPatternFound) {
        for(var d = 0; d < 10; d++){
            if (lgPatternFound === this.FIRST_DIGIT_ENCODINGS[d]) {
                resultString = String.fromCharCode('0'.charCodeAt(0) + d) + resultString;
                return resultString;
            }
        }
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
    };
    EAN13Reader.FIRST_DIGIT_ENCODINGS = [
        0x00,
        0x0B,
        0x0D,
        0xE,
        0x13,
        0x19,
        0x1C,
        0x15,
        0x16,
        0x1A
    ];
    return EAN13Reader;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = EAN13Reader;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/EAN8Reader.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2008 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/BarcodeFormat.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/UPCEANReader.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
/**
 * <p>Implements decoding of the EAN-8 format.</p>
 *
 * <AUTHOR> Owen
 */ var EAN8Reader = function(_super) {
    __extends(EAN8Reader, _super);
    function EAN8Reader() {
        var _this = _super.call(this) || this;
        _this.decodeMiddleCounters = Int32Array.from([
            0,
            0,
            0,
            0
        ]);
        return _this;
    }
    EAN8Reader.prototype.decodeMiddle = function(row, startRange, resultString) {
        var e_1, _a, e_2, _b;
        var counters = this.decodeMiddleCounters;
        counters[0] = 0;
        counters[1] = 0;
        counters[2] = 0;
        counters[3] = 0;
        var end = row.getSize();
        var rowOffset = startRange[1];
        for(var x = 0; x < 4 && rowOffset < end; x++){
            var bestMatch = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].decodeDigit(row, counters, rowOffset, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].L_PATTERNS);
            resultString += String.fromCharCode('0'.charCodeAt(0) + bestMatch);
            try {
                for(var counters_1 = (e_1 = void 0, __values(counters)), counters_1_1 = counters_1.next(); !counters_1_1.done; counters_1_1 = counters_1.next()){
                    var counter = counters_1_1.value;
                    rowOffset += counter;
                }
            } catch (e_1_1) {
                e_1 = {
                    error: e_1_1
                };
            } finally{
                try {
                    if (counters_1_1 && !counters_1_1.done && (_a = counters_1.return)) _a.call(counters_1);
                } finally{
                    if (e_1) throw e_1.error;
                }
            }
        }
        var middleRange = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].findGuardPattern(row, rowOffset, true, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].MIDDLE_PATTERN, new Int32Array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].MIDDLE_PATTERN.length).fill(0));
        rowOffset = middleRange[1];
        for(var x = 0; x < 4 && rowOffset < end; x++){
            var bestMatch = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].decodeDigit(row, counters, rowOffset, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].L_PATTERNS);
            resultString += String.fromCharCode('0'.charCodeAt(0) + bestMatch);
            try {
                for(var counters_2 = (e_2 = void 0, __values(counters)), counters_2_1 = counters_2.next(); !counters_2_1.done; counters_2_1 = counters_2.next()){
                    var counter = counters_2_1.value;
                    rowOffset += counter;
                }
            } catch (e_2_1) {
                e_2 = {
                    error: e_2_1
                };
            } finally{
                try {
                    if (counters_2_1 && !counters_2_1.done && (_b = counters_2.return)) _b.call(counters_2);
                } finally{
                    if (e_2) throw e_2.error;
                }
            }
        }
        return {
            rowOffset: rowOffset,
            resultString: resultString
        };
    };
    EAN8Reader.prototype.getBarcodeFormat = function() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].EAN_8;
    };
    return EAN8Reader;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = EAN8Reader;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/UPCAReader.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2008 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
/*namespace com.google.zxing.oned {*/ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/BarcodeFormat.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Result.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NotFoundException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$EAN13Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/EAN13Reader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/UPCEANReader.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
;
;
;
/**
 * Encapsulates functionality and implementation that is common to all families
 * of one-dimensional barcodes.
 *
 * <AUTHOR> (Daniel Switkin)
 * <AUTHOR> Owen
 * <AUTHOR> (Sam Rudloff)
 *
 * @source https://github.com/zxing/zxing/blob/3c96923276dd5785d58eb970b6ba3f80d36a9505/core/src/main/java/com/google/zxing/oned/UPCAReader.java
 *
 * @experimental
 */ var UPCAReader = function(_super) {
    __extends(UPCAReader, _super);
    function UPCAReader() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.ean13Reader = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$EAN13Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        return _this;
    }
    // @Override
    UPCAReader.prototype.getBarcodeFormat = function() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].UPC_A;
    };
    // Note that we don't try rotation without the try harder flag, even if rotation was supported.
    // @Override
    UPCAReader.prototype.decode = function(image, hints) {
        return this.maybeReturnResult(this.ean13Reader.decode(image));
    };
    // @Override
    UPCAReader.prototype.decodeRow = function(rowNumber, row, hints) {
        return this.maybeReturnResult(this.ean13Reader.decodeRow(rowNumber, row, hints));
    };
    // @Override
    UPCAReader.prototype.decodeMiddle = function(row, startRange, resultString) {
        return this.ean13Reader.decodeMiddle(row, startRange, resultString);
    };
    UPCAReader.prototype.maybeReturnResult = function(result) {
        var text = result.getText();
        if (text.charAt(0) === '0') {
            var upcaResult = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](text.substring(1), null, null, result.getResultPoints(), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].UPC_A);
            if (result.getResultMetadata() != null) {
                upcaResult.putAllMetadata(result.getResultMetadata());
            }
            return upcaResult;
        } else {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
    };
    UPCAReader.prototype.reset = function() {
        this.ean13Reader.reset();
    };
    return UPCAReader;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = UPCAReader;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/UPCEReader.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2008 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/UPCEANReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/StringBuilder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NotFoundException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/BarcodeFormat.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
;
;
// package com.google.zxing.oned;
// import com.google.zxing.BarcodeFormat;
// import com.google.zxing.FormatException;
// import com.google.zxing.NotFoundException;
// import com.google.zxing.common.BitArray;
/**
 * <p>Implements decoding of the UPC-E format.</p>
 * <p><a href="http://www.barcodeisland.com/upce.phtml">This</a> is a great reference for
 * UPC-E information.</p>
 *
 * <AUTHOR> Owen
 *
 * @source https://github.com/zxing/zxing/blob/3c96923276dd5785d58eb970b6ba3f80d36a9505/core/src/main/java/com/google/zxing/oned/UPCEReader.java
 *
 * @experimental
 */ var UPCEReader = function(_super) {
    __extends(UPCEReader, _super);
    function UPCEReader() {
        var _this = _super.call(this) || this;
        _this.decodeMiddleCounters = new Int32Array(4);
        return _this;
    }
    /**
     * @throws NotFoundException
     */ // @Override
    UPCEReader.prototype.decodeMiddle = function(row, startRange, result) {
        var e_1, _a;
        var counters = this.decodeMiddleCounters.map(function(x) {
            return x;
        });
        counters[0] = 0;
        counters[1] = 0;
        counters[2] = 0;
        counters[3] = 0;
        var end = row.getSize();
        var rowOffset = startRange[1];
        var lgPatternFound = 0;
        for(var x = 0; x < 6 && rowOffset < end; x++){
            var bestMatch = UPCEReader.decodeDigit(row, counters, rowOffset, UPCEReader.L_AND_G_PATTERNS);
            result += String.fromCharCode('0'.charCodeAt(0) + bestMatch % 10);
            try {
                for(var counters_1 = (e_1 = void 0, __values(counters)), counters_1_1 = counters_1.next(); !counters_1_1.done; counters_1_1 = counters_1.next()){
                    var counter = counters_1_1.value;
                    rowOffset += counter;
                }
            } catch (e_1_1) {
                e_1 = {
                    error: e_1_1
                };
            } finally{
                try {
                    if (counters_1_1 && !counters_1_1.done && (_a = counters_1.return)) _a.call(counters_1);
                } finally{
                    if (e_1) throw e_1.error;
                }
            }
            if (bestMatch >= 10) {
                lgPatternFound |= 1 << 5 - x;
            }
        }
        UPCEReader.determineNumSysAndCheckDigit(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](result), lgPatternFound);
        return rowOffset;
    };
    /**
     * @throws NotFoundException
     */ // @Override
    UPCEReader.prototype.decodeEnd = function(row, endStart) {
        return UPCEReader.findGuardPatternWithoutCounters(row, endStart, true, UPCEReader.MIDDLE_END_PATTERN);
    };
    /**
     * @throws FormatException
     */ // @Override
    UPCEReader.prototype.checkChecksum = function(s) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].checkChecksum(UPCEReader.convertUPCEtoUPCA(s));
    };
    /**
     * @throws NotFoundException
     */ UPCEReader.determineNumSysAndCheckDigit = function(resultString, lgPatternFound) {
        for(var numSys = 0; numSys <= 1; numSys++){
            for(var d = 0; d < 10; d++){
                if (lgPatternFound === this.NUMSYS_AND_CHECK_DIGIT_PATTERNS[numSys][d]) {
                    resultString.insert(0, '0' + numSys);
                    resultString.append('0' + d);
                    return;
                }
            }
        }
        throw __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getNotFoundInstance();
    };
    // @Override
    UPCEReader.prototype.getBarcodeFormat = function() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].UPC_E;
    };
    /**
     * Expands a UPC-E value back into its full, equivalent UPC-A code value.
     *
     * @param upce UPC-E code as string of digits
     * @return equivalent UPC-A code as string of digits
     */ UPCEReader.convertUPCEtoUPCA = function(upce) {
        // the following line is equivalent to upce.getChars(1, 7, upceChars, 0);
        var upceChars = upce.slice(1, 7).split('').map(function(x) {
            return x.charCodeAt(0);
        });
        var result = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        result.append(upce.charAt(0));
        var lastChar = upceChars[5];
        switch(lastChar){
            case 0:
            case 1:
            case 2:
                result.appendChars(upceChars, 0, 2);
                result.append(lastChar);
                result.append('0000');
                result.appendChars(upceChars, 2, 3);
                break;
            case 3:
                result.appendChars(upceChars, 0, 3);
                result.append('00000');
                result.appendChars(upceChars, 3, 2);
                break;
            case 4:
                result.appendChars(upceChars, 0, 4);
                result.append('00000');
                result.append(upceChars[4]);
                break;
            default:
                result.appendChars(upceChars, 0, 5);
                result.append('0000');
                result.append(lastChar);
                break;
        }
        // Only append check digit in conversion if supplied
        if (upce.length >= 8) {
            result.append(upce.charAt(7));
        }
        return result.toString();
    };
    /**
     * The pattern that marks the middle, and end, of a UPC-E pattern.
     * There is no "second half" to a UPC-E barcode.
     */ UPCEReader.MIDDLE_END_PATTERN = Int32Array.from([
        1,
        1,
        1,
        1,
        1,
        1
    ]);
    // For an UPC-E barcode, the final digit is represented by the parities used
    // to encode the middle six digits, according to the table below.
    //
    //                Parity of next 6 digits
    //    Digit   0     1     2     3     4     5
    //       0    Even   Even  Even Odd  Odd   Odd
    //       1    Even   Even  Odd  Even Odd   Odd
    //       2    Even   Even  Odd  Odd  Even  Odd
    //       3    Even   Even  Odd  Odd  Odd   Even
    //       4    Even   Odd   Even Even Odd   Odd
    //       5    Even   Odd   Odd  Even Even  Odd
    //       6    Even   Odd   Odd  Odd  Even  Even
    //       7    Even   Odd   Even Odd  Even  Odd
    //       8    Even   Odd   Even Odd  Odd   Even
    //       9    Even   Odd   Odd  Even Odd   Even
    //
    // The encoding is represented by the following array, which is a bit pattern
    // using Odd = 0 and Even = 1. For example, 5 is represented by:
    //
    //              Odd Even Even Odd Odd Even
    // in binary:
    //                0    1    1   0   0    1   == 0x19
    //
    /**
     * See {@link #L_AND_G_PATTERNS}; these values similarly represent patterns of
     * even-odd parity encodings of digits that imply both the number system (0 or 1)
     * used, and the check digit.
     */ UPCEReader.NUMSYS_AND_CHECK_DIGIT_PATTERNS = [
        Int32Array.from([
            0x38,
            0x34,
            0x32,
            0x31,
            0x2C,
            0x26,
            0x23,
            0x2A,
            0x29,
            0x25
        ]),
        Int32Array.from([
            0x07,
            0x0B,
            0x0D,
            0x0E,
            0x13,
            0x19,
            0x1C,
            0x15,
            0x16,
            0x1
        ])
    ];
    return UPCEReader;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = UPCEReader;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/MultiFormatUPCEANReader.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2008 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/BarcodeFormat.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/DecodeHintType.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Result.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/OneDReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$EAN13Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/EAN13Reader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$EAN8Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/EAN8Reader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCAReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/UPCAReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NotFoundException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/UPCEReader.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
;
;
;
;
;
;
;
/**
 * <p>A reader that can read all available UPC/EAN formats. If a caller wants to try to
 * read all such formats, it is most efficient to use this implementation rather than invoke
 * individual readers.</p>
 *
 * <AUTHOR> Owen
 */ var MultiFormatUPCEANReader = function(_super) {
    __extends(MultiFormatUPCEANReader, _super);
    function MultiFormatUPCEANReader(hints) {
        var _this = _super.call(this) || this;
        var possibleFormats = hints == null ? null : hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].POSSIBLE_FORMATS);
        var readers = [];
        if (possibleFormats != null) {
            if (possibleFormats.indexOf(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].EAN_13) > -1) {
                readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$EAN13Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
            }
            if (possibleFormats.indexOf(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].UPC_A) > -1) {
                readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCAReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
            }
            if (possibleFormats.indexOf(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].EAN_8) > -1) {
                readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$EAN8Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
            }
            if (possibleFormats.indexOf(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].UPC_E) > -1) {
                readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
            }
        }
        if (readers.length === 0) {
            readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$EAN13Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
            readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCAReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
            readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$EAN8Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
            readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$UPCEReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
        }
        _this.readers = readers;
        return _this;
    }
    MultiFormatUPCEANReader.prototype.decodeRow = function(rowNumber, row, hints) {
        var e_1, _a;
        try {
            for(var _b = __values(this.readers), _c = _b.next(); !_c.done; _c = _b.next()){
                var reader = _c.value;
                try {
                    // const result: Result = reader.decodeRow(rowNumber, row, startGuardPattern, hints);
                    var result = reader.decodeRow(rowNumber, row, hints);
                    // Special case: a 12-digit code encoded in UPC-A is identical to a "0"
                    // followed by those 12 digits encoded as EAN-13. Each will recognize such a code,
                    // UPC-A as a 12-digit string and EAN-13 as a 13-digit string starting with "0".
                    // Individually these are correct and their readers will both read such a code
                    // and correctly call it EAN-13, or UPC-A, respectively.
                    //
                    // In this case, if we've been looking for both types, we'd like to call it
                    // a UPC-A code. But for efficiency we only run the EAN-13 decoder to also read
                    // UPC-A. So we special case it here, and convert an EAN-13 result to a UPC-A
                    // result if appropriate.
                    //
                    // But, don't return UPC-A if UPC-A was not a requested format!
                    var ean13MayBeUPCA = result.getBarcodeFormat() === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].EAN_13 && result.getText().charAt(0) === '0';
                    // @SuppressWarnings("unchecked")
                    var possibleFormats = hints == null ? null : hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].POSSIBLE_FORMATS);
                    var canReturnUPCA = possibleFormats == null || possibleFormats.includes(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].UPC_A);
                    if (ean13MayBeUPCA && canReturnUPCA) {
                        var rawBytes = result.getRawBytes();
                        // Transfer the metadata across
                        var resultUPCA = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](result.getText().substring(1), rawBytes, rawBytes ? rawBytes.length : null, result.getResultPoints(), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].UPC_A);
                        resultUPCA.putAllMetadata(result.getResultMetadata());
                        return resultUPCA;
                    }
                    return result;
                } catch (err) {
                // continue;
                }
            }
        } catch (e_1_1) {
            e_1 = {
                error: e_1_1
            };
        } finally{
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            } finally{
                if (e_1) throw e_1.error;
            }
        }
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
    };
    MultiFormatUPCEANReader.prototype.reset = function() {
        var e_2, _a;
        try {
            for(var _b = __values(this.readers), _c = _b.next(); !_c.done; _c = _b.next()){
                var reader = _c.value;
                reader.reset();
            }
        } catch (e_2_1) {
            e_2 = {
                error: e_2_1
            };
        } finally{
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            } finally{
                if (e_2) throw e_2.error;
            }
        }
    };
    return MultiFormatUPCEANReader;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = MultiFormatUPCEANReader;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/CodaBarReader.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2008 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
/*namespace com.google.zxing.oned {*/ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/BarcodeFormat.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NotFoundException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/OneDReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Result.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ResultPoint.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
;
;
;
/**
 * <p>Decodes CodaBar barcodes. </p>
 *
 * <AUTHOR> @dodobelieve
 * @see CodaBarReader
 */ var CodaBarReader = function(_super) {
    __extends(CodaBarReader, _super);
    function CodaBarReader() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.CODA_BAR_CHAR_SET = {
            nnnnnww: '0',
            nnnnwwn: '1',
            nnnwnnw: '2',
            wwnnnnn: '3',
            nnwnnwn: '4',
            wnnnnwn: '5',
            nwnnnnw: '6',
            nwnnwnn: '7',
            nwwnnnn: '8',
            wnnwnnn: '9',
            nnnwwnn: '-',
            nnwwnnn: '$',
            wnnnwnw: ':',
            wnwnnnw: '/',
            wnwnwnn: '.',
            nnwwwww: '+',
            nnwwnwn: 'A',
            nwnwnnw: 'B',
            nnnwnww: 'C',
            nnnwwwn: 'D'
        };
        return _this;
    }
    CodaBarReader.prototype.decodeRow = function(rowNumber, row, hints) {
        var validRowData = this.getValidRowData(row);
        if (!validRowData) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        var retStr = this.codaBarDecodeRow(validRowData.row);
        if (!retStr) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](retStr, null, 0, [
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](validRowData.left, rowNumber),
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](validRowData.right, rowNumber)
        ], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].CODABAR, new Date().getTime());
    };
    /**
     * converts bit array to valid data array(lengths of black bits and white bits)
     * @param row bit array to convert
     */ CodaBarReader.prototype.getValidRowData = function(row) {
        var booleanArr = row.toArray();
        var startIndex = booleanArr.indexOf(true);
        if (startIndex === -1) return null;
        var lastIndex = booleanArr.lastIndexOf(true);
        if (lastIndex <= startIndex) return null;
        booleanArr = booleanArr.slice(startIndex, lastIndex + 1);
        var result = [];
        var lastBit = booleanArr[0];
        var bitLength = 1;
        for(var i = 1; i < booleanArr.length; i++){
            if (booleanArr[i] === lastBit) {
                bitLength++;
            } else {
                lastBit = booleanArr[i];
                result.push(bitLength);
                bitLength = 1;
            }
        }
        result.push(bitLength);
        // CodaBar code data valid
        if (result.length < 23 && (result.length + 1) % 8 !== 0) return null;
        return {
            row: result,
            left: startIndex,
            right: lastIndex
        };
    };
    /**
     * decode codabar code
     * @param row row to cecode
     */ CodaBarReader.prototype.codaBarDecodeRow = function(row) {
        var code = [];
        var barThreshold = Math.ceil(row.reduce(function(pre, item) {
            return (pre + item) / 2;
        }, 0));
        // Read one encoded character at a time.
        while(row.length > 0){
            var seg = row.splice(0, 8).splice(0, 7);
            var key = seg.map(function(len) {
                return len < barThreshold ? 'n' : 'w';
            }).join('');
            if (this.CODA_BAR_CHAR_SET[key] === undefined) return null;
            code.push(this.CODA_BAR_CHAR_SET[key]);
        }
        var strCode = code.join('');
        if (this.validCodaBarString(strCode)) return strCode;
        return null;
    };
    /**
     * check if the string is a CodaBar string
     * @param src string to determine
     */ CodaBarReader.prototype.validCodaBarString = function(src) {
        var reg = /^[A-D].{1,}[A-D]$/;
        return reg.test(src);
    };
    return CodaBarReader;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = CodaBarReader;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/AbstractRSSReader.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/detector/MathUtils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NotFoundException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/OneDReader.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
;
// import Integer from '../../util/Integer';
// import Float from '../../util/Float';
var AbstractRSSReader = function(_super) {
    __extends(AbstractRSSReader, _super);
    function AbstractRSSReader() {
        var _this = _super.call(this) || this;
        _this.decodeFinderCounters = new Int32Array(4);
        _this.dataCharacterCounters = new Int32Array(8);
        _this.oddRoundingErrors = new Array(4);
        _this.evenRoundingErrors = new Array(4);
        _this.oddCounts = new Array(_this.dataCharacterCounters.length / 2);
        _this.evenCounts = new Array(_this.dataCharacterCounters.length / 2);
        return _this;
    }
    AbstractRSSReader.prototype.getDecodeFinderCounters = function() {
        return this.decodeFinderCounters;
    };
    AbstractRSSReader.prototype.getDataCharacterCounters = function() {
        return this.dataCharacterCounters;
    };
    AbstractRSSReader.prototype.getOddRoundingErrors = function() {
        return this.oddRoundingErrors;
    };
    AbstractRSSReader.prototype.getEvenRoundingErrors = function() {
        return this.evenRoundingErrors;
    };
    AbstractRSSReader.prototype.getOddCounts = function() {
        return this.oddCounts;
    };
    AbstractRSSReader.prototype.getEvenCounts = function() {
        return this.evenCounts;
    };
    AbstractRSSReader.prototype.parseFinderValue = function(counters, finderPatterns) {
        for(var value = 0; value < finderPatterns.length; value++){
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].patternMatchVariance(counters, finderPatterns[value], AbstractRSSReader.MAX_INDIVIDUAL_VARIANCE) < AbstractRSSReader.MAX_AVG_VARIANCE) {
                return value;
            }
        }
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
    };
    /**
     * @param array values to sum
     * @return sum of values
     * @deprecated call {@link MathUtils#sum(int[])}
     */ AbstractRSSReader.count = function(array) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sum(new Int32Array(array));
    };
    AbstractRSSReader.increment = function(array, errors) {
        var index = 0;
        var biggestError = errors[0];
        for(var i = 1; i < array.length; i++){
            if (errors[i] > biggestError) {
                biggestError = errors[i];
                index = i;
            }
        }
        array[index]++;
    };
    AbstractRSSReader.decrement = function(array, errors) {
        var index = 0;
        var biggestError = errors[0];
        for(var i = 1; i < array.length; i++){
            if (errors[i] < biggestError) {
                biggestError = errors[i];
                index = i;
            }
        }
        array[index]--;
    };
    AbstractRSSReader.isFinderPattern = function(counters) {
        var e_1, _a;
        var firstTwoSum = counters[0] + counters[1];
        var sum = firstTwoSum + counters[2] + counters[3];
        var ratio = firstTwoSum / sum;
        if (ratio >= AbstractRSSReader.MIN_FINDER_PATTERN_RATIO && ratio <= AbstractRSSReader.MAX_FINDER_PATTERN_RATIO) {
            // passes ratio test in spec, but see if the counts are unreasonable
            var minCounter = Number.MAX_SAFE_INTEGER;
            var maxCounter = Number.MIN_SAFE_INTEGER;
            try {
                for(var counters_1 = __values(counters), counters_1_1 = counters_1.next(); !counters_1_1.done; counters_1_1 = counters_1.next()){
                    var counter = counters_1_1.value;
                    if (counter > maxCounter) {
                        maxCounter = counter;
                    }
                    if (counter < minCounter) {
                        minCounter = counter;
                    }
                }
            } catch (e_1_1) {
                e_1 = {
                    error: e_1_1
                };
            } finally{
                try {
                    if (counters_1_1 && !counters_1_1.done && (_a = counters_1.return)) _a.call(counters_1);
                } finally{
                    if (e_1) throw e_1.error;
                }
            }
            return maxCounter < 10 * minCounter;
        }
        return false;
    };
    AbstractRSSReader.MAX_AVG_VARIANCE = 0.2;
    AbstractRSSReader.MAX_INDIVIDUAL_VARIANCE = 0.45;
    AbstractRSSReader.MIN_FINDER_PATTERN_RATIO = 9.5 / 12.0;
    AbstractRSSReader.MAX_FINDER_PATTERN_RATIO = 12.5 / 14.0;
    return AbstractRSSReader;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = AbstractRSSReader;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/DataCharacter.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var DataCharacter = function() {
    function DataCharacter(value, checksumPortion) {
        this.value = value;
        this.checksumPortion = checksumPortion;
    }
    DataCharacter.prototype.getValue = function() {
        return this.value;
    };
    DataCharacter.prototype.getChecksumPortion = function() {
        return this.checksumPortion;
    };
    DataCharacter.prototype.toString = function() {
        return this.value + '(' + this.checksumPortion + ')';
    };
    DataCharacter.prototype.equals = function(o) {
        if (!(o instanceof DataCharacter)) {
            return false;
        }
        var that = o;
        return this.value === that.value && this.checksumPortion === that.checksumPortion;
    };
    DataCharacter.prototype.hashCode = function() {
        return this.value ^ this.checksumPortion;
    };
    return DataCharacter;
}();
const __TURBOPACK__default__export__ = DataCharacter;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/FinderPattern.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ResultPoint.js [app-ssr] (ecmascript)");
;
var FinderPattern = function() {
    function FinderPattern(value, startEnd, start, end, rowNumber) {
        this.value = value;
        this.startEnd = startEnd;
        this.value = value;
        this.startEnd = startEnd;
        this.resultPoints = new Array();
        this.resultPoints.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](start, rowNumber));
        this.resultPoints.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](end, rowNumber));
    }
    FinderPattern.prototype.getValue = function() {
        return this.value;
    };
    FinderPattern.prototype.getStartEnd = function() {
        return this.startEnd;
    };
    FinderPattern.prototype.getResultPoints = function() {
        return this.resultPoints;
    };
    FinderPattern.prototype.equals = function(o) {
        if (!(o instanceof FinderPattern)) {
            return false;
        }
        var that = o;
        return this.value === that.value;
    };
    FinderPattern.prototype.hashCode = function() {
        return this.value;
    };
    return FinderPattern;
}();
const __TURBOPACK__default__export__ = FinderPattern;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/RSSUtils.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
/**
 * RSS util functions.
 */ var RSSUtils = function() {
    function RSSUtils() {}
    RSSUtils.getRSSvalue = function(widths, maxWidth, noNarrow) {
        var e_1, _a;
        var n = 0;
        try {
            for(var widths_1 = __values(widths), widths_1_1 = widths_1.next(); !widths_1_1.done; widths_1_1 = widths_1.next()){
                var width = widths_1_1.value;
                n += width;
            }
        } catch (e_1_1) {
            e_1 = {
                error: e_1_1
            };
        } finally{
            try {
                if (widths_1_1 && !widths_1_1.done && (_a = widths_1.return)) _a.call(widths_1);
            } finally{
                if (e_1) throw e_1.error;
            }
        }
        var val = 0;
        var narrowMask = 0;
        var elements = widths.length;
        for(var bar = 0; bar < elements - 1; bar++){
            var elmWidth = void 0;
            for(elmWidth = 1, narrowMask |= 1 << bar; elmWidth < widths[bar]; elmWidth++, narrowMask &= ~(1 << bar)){
                var subVal = RSSUtils.combins(n - elmWidth - 1, elements - bar - 2);
                if (noNarrow && narrowMask === 0 && n - elmWidth - (elements - bar - 1) >= elements - bar - 1) {
                    subVal -= RSSUtils.combins(n - elmWidth - (elements - bar), elements - bar - 2);
                }
                if (elements - bar - 1 > 1) {
                    var lessVal = 0;
                    for(var mxwElement = n - elmWidth - (elements - bar - 2); mxwElement > maxWidth; mxwElement--){
                        lessVal += RSSUtils.combins(n - elmWidth - mxwElement - 1, elements - bar - 3);
                    }
                    subVal -= lessVal * (elements - 1 - bar);
                } else if (n - elmWidth > maxWidth) {
                    subVal--;
                }
                val += subVal;
            }
            n -= elmWidth;
        }
        return val;
    };
    RSSUtils.combins = function(n, r) {
        var maxDenom;
        var minDenom;
        if (n - r > r) {
            minDenom = r;
            maxDenom = n - r;
        } else {
            minDenom = n - r;
            maxDenom = r;
        }
        var val = 1;
        var j = 1;
        for(var i = n; i > maxDenom; i--){
            val *= i;
            if (j <= minDenom) {
                val /= j;
                j++;
            }
        }
        while(j <= minDenom){
            val /= j;
            j++;
        }
        return val;
    };
    return RSSUtils;
}();
const __TURBOPACK__default__export__ = RSSUtils;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/BitArrayBuilder.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/BitArray.js [app-ssr] (ecmascript)");
;
var BitArrayBuilder = function() {
    function BitArrayBuilder() {}
    BitArrayBuilder.buildBitArray = function(pairs) {
        var charNumber = pairs.length * 2 - 1;
        if (pairs[pairs.length - 1].getRightChar() == null) {
            charNumber -= 1;
        }
        var size = 12 * charNumber;
        var binary = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](size);
        var accPos = 0;
        var firstPair = pairs[0];
        var firstValue = firstPair.getRightChar().getValue();
        for(var i = 11; i >= 0; --i){
            if ((firstValue & 1 << i) !== 0) {
                binary.set(accPos);
            }
            accPos++;
        }
        for(var i = 1; i < pairs.length; ++i){
            var currentPair = pairs[i];
            var leftValue = currentPair.getLeftChar().getValue();
            for(var j = 11; j >= 0; --j){
                if ((leftValue & 1 << j) !== 0) {
                    binary.set(accPos);
                }
                accPos++;
            }
            if (currentPair.getRightChar() !== null) {
                var rightValue = currentPair.getRightChar().getValue();
                for(var j = 11; j >= 0; --j){
                    if ((rightValue & 1 << j) !== 0) {
                        binary.set(accPos);
                    }
                    accPos++;
                }
            }
        }
        return binary;
    };
    return BitArrayBuilder;
}();
const __TURBOPACK__default__export__ = BitArrayBuilder;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/BlockParsedResult.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var BlockParsedResult = function() {
    function BlockParsedResult(finished, decodedInformation) {
        if (decodedInformation) {
            this.decodedInformation = null;
        } else {
            this.finished = finished;
            this.decodedInformation = decodedInformation;
        }
    }
    BlockParsedResult.prototype.getDecodedInformation = function() {
        return this.decodedInformation;
    };
    BlockParsedResult.prototype.isFinished = function() {
        return this.finished;
    };
    return BlockParsedResult;
}();
const __TURBOPACK__default__export__ = BlockParsedResult;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/DecodedObject.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var DecodedObject = function() {
    function DecodedObject(newPosition) {
        this.newPosition = newPosition;
    }
    DecodedObject.prototype.getNewPosition = function() {
        return this.newPosition;
    };
    return DecodedObject;
}();
const __TURBOPACK__default__export__ = DecodedObject;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/DecodedChar.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedObject$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/DecodedObject.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
var DecodedChar = function(_super) {
    __extends(DecodedChar, _super);
    function DecodedChar(newPosition, value) {
        var _this = _super.call(this, newPosition) || this;
        _this.value = value;
        return _this;
    }
    DecodedChar.prototype.getValue = function() {
        return this.value;
    };
    DecodedChar.prototype.isFNC1 = function() {
        return this.value === DecodedChar.FNC1;
    };
    DecodedChar.FNC1 = '$';
    return DecodedChar;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedObject$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = DecodedChar;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/DecodedInformation.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedObject$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/DecodedObject.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
var DecodedInformation = function(_super) {
    __extends(DecodedInformation, _super);
    function DecodedInformation(newPosition, newString, remainingValue) {
        var _this = _super.call(this, newPosition) || this;
        if (remainingValue) {
            _this.remaining = true;
            _this.remainingValue = _this.remainingValue;
        } else {
            _this.remaining = false;
            _this.remainingValue = 0;
        }
        _this.newString = newString;
        return _this;
    }
    DecodedInformation.prototype.getNewString = function() {
        return this.newString;
    };
    DecodedInformation.prototype.isRemaining = function() {
        return this.remaining;
    };
    DecodedInformation.prototype.getRemainingValue = function() {
        return this.remainingValue;
    };
    return DecodedInformation;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedObject$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = DecodedInformation;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/DecodedNumeric.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/FormatException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedObject$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/DecodedObject.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
var DecodedNumeric = function(_super) {
    __extends(DecodedNumeric, _super);
    function DecodedNumeric(newPosition, firstDigit, secondDigit) {
        var _this = _super.call(this, newPosition) || this;
        if (firstDigit < 0 || firstDigit > 10 || secondDigit < 0 || secondDigit > 10) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        _this.firstDigit = firstDigit;
        _this.secondDigit = secondDigit;
        return _this;
    }
    DecodedNumeric.prototype.getFirstDigit = function() {
        return this.firstDigit;
    };
    DecodedNumeric.prototype.getSecondDigit = function() {
        return this.secondDigit;
    };
    DecodedNumeric.prototype.getValue = function() {
        return this.firstDigit * 10 + this.secondDigit;
    };
    DecodedNumeric.prototype.isFirstDigitFNC1 = function() {
        return this.firstDigit === DecodedNumeric.FNC1;
    };
    DecodedNumeric.prototype.isSecondDigitFNC1 = function() {
        return this.secondDigit === DecodedNumeric.FNC1;
    };
    DecodedNumeric.prototype.isAnyFNC1 = function() {
        return this.firstDigit === DecodedNumeric.FNC1 || this.secondDigit === DecodedNumeric.FNC1;
    };
    DecodedNumeric.FNC1 = 10;
    return DecodedNumeric;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedObject$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = DecodedNumeric;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/FieldParser.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NotFoundException.js [app-ssr] (ecmascript)");
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
var FieldParser = function() {
    function FieldParser() {}
    FieldParser.parseFieldsInGeneralPurpose = function(rawInformation) {
        var e_1, _a, e_2, _b, e_3, _c, e_4, _d;
        if (!rawInformation) {
            return null;
        }
        // Processing 2-digit AIs
        if (rawInformation.length < 2) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        var firstTwoDigits = rawInformation.substring(0, 2);
        try {
            for(var _e = __values(FieldParser.TWO_DIGIT_DATA_LENGTH), _f = _e.next(); !_f.done; _f = _e.next()){
                var dataLength = _f.value;
                if (dataLength[0] === firstTwoDigits) {
                    if (dataLength[1] === FieldParser.VARIABLE_LENGTH) {
                        return FieldParser.processVariableAI(2, dataLength[2], rawInformation);
                    }
                    return FieldParser.processFixedAI(2, dataLength[1], rawInformation);
                }
            }
        } catch (e_1_1) {
            e_1 = {
                error: e_1_1
            };
        } finally{
            try {
                if (_f && !_f.done && (_a = _e.return)) _a.call(_e);
            } finally{
                if (e_1) throw e_1.error;
            }
        }
        if (rawInformation.length < 3) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        var firstThreeDigits = rawInformation.substring(0, 3);
        try {
            for(var _g = __values(FieldParser.THREE_DIGIT_DATA_LENGTH), _h = _g.next(); !_h.done; _h = _g.next()){
                var dataLength = _h.value;
                if (dataLength[0] === firstThreeDigits) {
                    if (dataLength[1] === FieldParser.VARIABLE_LENGTH) {
                        return FieldParser.processVariableAI(3, dataLength[2], rawInformation);
                    }
                    return FieldParser.processFixedAI(3, dataLength[1], rawInformation);
                }
            }
        } catch (e_2_1) {
            e_2 = {
                error: e_2_1
            };
        } finally{
            try {
                if (_h && !_h.done && (_b = _g.return)) _b.call(_g);
            } finally{
                if (e_2) throw e_2.error;
            }
        }
        try {
            for(var _j = __values(FieldParser.THREE_DIGIT_PLUS_DIGIT_DATA_LENGTH), _k = _j.next(); !_k.done; _k = _j.next()){
                var dataLength = _k.value;
                if (dataLength[0] === firstThreeDigits) {
                    if (dataLength[1] === FieldParser.VARIABLE_LENGTH) {
                        return FieldParser.processVariableAI(4, dataLength[2], rawInformation);
                    }
                    return FieldParser.processFixedAI(4, dataLength[1], rawInformation);
                }
            }
        } catch (e_3_1) {
            e_3 = {
                error: e_3_1
            };
        } finally{
            try {
                if (_k && !_k.done && (_c = _j.return)) _c.call(_j);
            } finally{
                if (e_3) throw e_3.error;
            }
        }
        if (rawInformation.length < 4) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        var firstFourDigits = rawInformation.substring(0, 4);
        try {
            for(var _l = __values(FieldParser.FOUR_DIGIT_DATA_LENGTH), _m = _l.next(); !_m.done; _m = _l.next()){
                var dataLength = _m.value;
                if (dataLength[0] === firstFourDigits) {
                    if (dataLength[1] === FieldParser.VARIABLE_LENGTH) {
                        return FieldParser.processVariableAI(4, dataLength[2], rawInformation);
                    }
                    return FieldParser.processFixedAI(4, dataLength[1], rawInformation);
                }
            }
        } catch (e_4_1) {
            e_4 = {
                error: e_4_1
            };
        } finally{
            try {
                if (_m && !_m.done && (_d = _l.return)) _d.call(_l);
            } finally{
                if (e_4) throw e_4.error;
            }
        }
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
    };
    FieldParser.processFixedAI = function(aiSize, fieldSize, rawInformation) {
        if (rawInformation.length < aiSize) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        var ai = rawInformation.substring(0, aiSize);
        if (rawInformation.length < aiSize + fieldSize) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        var field = rawInformation.substring(aiSize, aiSize + fieldSize);
        var remaining = rawInformation.substring(aiSize + fieldSize);
        var result = '(' + ai + ')' + field;
        var parsedAI = FieldParser.parseFieldsInGeneralPurpose(remaining);
        return parsedAI == null ? result : result + parsedAI;
    };
    FieldParser.processVariableAI = function(aiSize, variableFieldSize, rawInformation) {
        var ai = rawInformation.substring(0, aiSize);
        var maxSize;
        if (rawInformation.length < aiSize + variableFieldSize) {
            maxSize = rawInformation.length;
        } else {
            maxSize = aiSize + variableFieldSize;
        }
        var field = rawInformation.substring(aiSize, maxSize);
        var remaining = rawInformation.substring(maxSize);
        var result = '(' + ai + ')' + field;
        var parsedAI = FieldParser.parseFieldsInGeneralPurpose(remaining);
        return parsedAI == null ? result : result + parsedAI;
    };
    FieldParser.VARIABLE_LENGTH = [];
    FieldParser.TWO_DIGIT_DATA_LENGTH = [
        [
            '00',
            18
        ],
        [
            '01',
            14
        ],
        [
            '02',
            14
        ],
        [
            '10',
            FieldParser.VARIABLE_LENGTH,
            20
        ],
        [
            '11',
            6
        ],
        [
            '12',
            6
        ],
        [
            '13',
            6
        ],
        [
            '15',
            6
        ],
        [
            '17',
            6
        ],
        [
            '20',
            2
        ],
        [
            '21',
            FieldParser.VARIABLE_LENGTH,
            20
        ],
        [
            '22',
            FieldParser.VARIABLE_LENGTH,
            29
        ],
        [
            '30',
            FieldParser.VARIABLE_LENGTH,
            8
        ],
        [
            '37',
            FieldParser.VARIABLE_LENGTH,
            8
        ],
        // internal company codes
        [
            '90',
            FieldParser.VARIABLE_LENGTH,
            30
        ],
        [
            '91',
            FieldParser.VARIABLE_LENGTH,
            30
        ],
        [
            '92',
            FieldParser.VARIABLE_LENGTH,
            30
        ],
        [
            '93',
            FieldParser.VARIABLE_LENGTH,
            30
        ],
        [
            '94',
            FieldParser.VARIABLE_LENGTH,
            30
        ],
        [
            '95',
            FieldParser.VARIABLE_LENGTH,
            30
        ],
        [
            '96',
            FieldParser.VARIABLE_LENGTH,
            30
        ],
        [
            '97',
            FieldParser.VARIABLE_LENGTH,
            3
        ],
        [
            '98',
            FieldParser.VARIABLE_LENGTH,
            30
        ],
        [
            '99',
            FieldParser.VARIABLE_LENGTH,
            30
        ]
    ];
    FieldParser.THREE_DIGIT_DATA_LENGTH = [
        // Same format as above
        [
            '240',
            FieldParser.VARIABLE_LENGTH,
            30
        ],
        [
            '241',
            FieldParser.VARIABLE_LENGTH,
            30
        ],
        [
            '242',
            FieldParser.VARIABLE_LENGTH,
            6
        ],
        [
            '250',
            FieldParser.VARIABLE_LENGTH,
            30
        ],
        [
            '251',
            FieldParser.VARIABLE_LENGTH,
            30
        ],
        [
            '253',
            FieldParser.VARIABLE_LENGTH,
            17
        ],
        [
            '254',
            FieldParser.VARIABLE_LENGTH,
            20
        ],
        [
            '400',
            FieldParser.VARIABLE_LENGTH,
            30
        ],
        [
            '401',
            FieldParser.VARIABLE_LENGTH,
            30
        ],
        [
            '402',
            17
        ],
        [
            '403',
            FieldParser.VARIABLE_LENGTH,
            30
        ],
        [
            '410',
            13
        ],
        [
            '411',
            13
        ],
        [
            '412',
            13
        ],
        [
            '413',
            13
        ],
        [
            '414',
            13
        ],
        [
            '420',
            FieldParser.VARIABLE_LENGTH,
            20
        ],
        [
            '421',
            FieldParser.VARIABLE_LENGTH,
            15
        ],
        [
            '422',
            3
        ],
        [
            '423',
            FieldParser.VARIABLE_LENGTH,
            15
        ],
        [
            '424',
            3
        ],
        [
            '425',
            3
        ],
        [
            '426',
            3
        ]
    ];
    FieldParser.THREE_DIGIT_PLUS_DIGIT_DATA_LENGTH = [
        // Same format as above
        [
            '310',
            6
        ],
        [
            '311',
            6
        ],
        [
            '312',
            6
        ],
        [
            '313',
            6
        ],
        [
            '314',
            6
        ],
        [
            '315',
            6
        ],
        [
            '316',
            6
        ],
        [
            '320',
            6
        ],
        [
            '321',
            6
        ],
        [
            '322',
            6
        ],
        [
            '323',
            6
        ],
        [
            '324',
            6
        ],
        [
            '325',
            6
        ],
        [
            '326',
            6
        ],
        [
            '327',
            6
        ],
        [
            '328',
            6
        ],
        [
            '329',
            6
        ],
        [
            '330',
            6
        ],
        [
            '331',
            6
        ],
        [
            '332',
            6
        ],
        [
            '333',
            6
        ],
        [
            '334',
            6
        ],
        [
            '335',
            6
        ],
        [
            '336',
            6
        ],
        [
            '340',
            6
        ],
        [
            '341',
            6
        ],
        [
            '342',
            6
        ],
        [
            '343',
            6
        ],
        [
            '344',
            6
        ],
        [
            '345',
            6
        ],
        [
            '346',
            6
        ],
        [
            '347',
            6
        ],
        [
            '348',
            6
        ],
        [
            '349',
            6
        ],
        [
            '350',
            6
        ],
        [
            '351',
            6
        ],
        [
            '352',
            6
        ],
        [
            '353',
            6
        ],
        [
            '354',
            6
        ],
        [
            '355',
            6
        ],
        [
            '356',
            6
        ],
        [
            '357',
            6
        ],
        [
            '360',
            6
        ],
        [
            '361',
            6
        ],
        [
            '362',
            6
        ],
        [
            '363',
            6
        ],
        [
            '364',
            6
        ],
        [
            '365',
            6
        ],
        [
            '366',
            6
        ],
        [
            '367',
            6
        ],
        [
            '368',
            6
        ],
        [
            '369',
            6
        ],
        [
            '390',
            FieldParser.VARIABLE_LENGTH,
            15
        ],
        [
            '391',
            FieldParser.VARIABLE_LENGTH,
            18
        ],
        [
            '392',
            FieldParser.VARIABLE_LENGTH,
            15
        ],
        [
            '393',
            FieldParser.VARIABLE_LENGTH,
            18
        ],
        [
            '703',
            FieldParser.VARIABLE_LENGTH,
            30
        ]
    ];
    FieldParser.FOUR_DIGIT_DATA_LENGTH = [
        // Same format as above
        [
            '7001',
            13
        ],
        [
            '7002',
            FieldParser.VARIABLE_LENGTH,
            30
        ],
        [
            '7003',
            10
        ],
        [
            '8001',
            14
        ],
        [
            '8002',
            FieldParser.VARIABLE_LENGTH,
            20
        ],
        [
            '8003',
            FieldParser.VARIABLE_LENGTH,
            30
        ],
        [
            '8004',
            FieldParser.VARIABLE_LENGTH,
            30
        ],
        [
            '8005',
            6
        ],
        [
            '8006',
            18
        ],
        [
            '8007',
            FieldParser.VARIABLE_LENGTH,
            30
        ],
        [
            '8008',
            FieldParser.VARIABLE_LENGTH,
            12
        ],
        [
            '8018',
            18
        ],
        [
            '8020',
            FieldParser.VARIABLE_LENGTH,
            25
        ],
        [
            '8100',
            6
        ],
        [
            '8101',
            10
        ],
        [
            '8102',
            2
        ],
        [
            '8110',
            FieldParser.VARIABLE_LENGTH,
            70
        ],
        [
            '8200',
            FieldParser.VARIABLE_LENGTH,
            70
        ]
    ];
    return FieldParser;
}();
const __TURBOPACK__default__export__ = FieldParser;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/GeneralAppIdDecoder.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/FormatException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalStateException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/IllegalStateException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/StringBuilder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$BlockParsedResult$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/BlockParsedResult.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedChar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/DecodedChar.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedInformation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/DecodedInformation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedNumeric$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/DecodedNumeric.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$FieldParser$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/FieldParser.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
var GeneralAppIdDecoder = function() {
    function GeneralAppIdDecoder(information) {
        this.buffer = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        this.information = information;
    }
    GeneralAppIdDecoder.prototype.decodeAllCodes = function(buff, initialPosition) {
        var currentPosition = initialPosition;
        var remaining = null;
        do {
            var info = this.decodeGeneralPurposeField(currentPosition, remaining);
            var parsedFields = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$FieldParser$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].parseFieldsInGeneralPurpose(info.getNewString());
            if (parsedFields != null) {
                buff.append(parsedFields);
            }
            if (info.isRemaining()) {
                remaining = '' + info.getRemainingValue();
            } else {
                remaining = null;
            }
            if (currentPosition === info.getNewPosition()) {
                break;
            }
            currentPosition = info.getNewPosition();
        }while (true)
        return buff.toString();
    };
    GeneralAppIdDecoder.prototype.isStillNumeric = function(pos) {
        // It's numeric if it still has 7 positions
        // and one of the first 4 bits is "1".
        if (pos + 7 > this.information.getSize()) {
            return pos + 4 <= this.information.getSize();
        }
        for(var i = pos; i < pos + 3; ++i){
            if (this.information.get(i)) {
                return true;
            }
        }
        return this.information.get(pos + 3);
    };
    GeneralAppIdDecoder.prototype.decodeNumeric = function(pos) {
        if (pos + 7 > this.information.getSize()) {
            var numeric_1 = this.extractNumericValueFromBitArray(pos, 4);
            if (numeric_1 === 0) {
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedNumeric$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](this.information.getSize(), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedNumeric$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].FNC1, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedNumeric$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].FNC1);
            }
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedNumeric$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](this.information.getSize(), numeric_1 - 1, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedNumeric$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].FNC1);
        }
        var numeric = this.extractNumericValueFromBitArray(pos, 7);
        var digit1 = (numeric - 8) / 11;
        var digit2 = (numeric - 8) % 11;
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedNumeric$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](pos + 7, digit1, digit2);
    };
    GeneralAppIdDecoder.prototype.extractNumericValueFromBitArray = function(pos, bits) {
        return GeneralAppIdDecoder.extractNumericValueFromBitArray(this.information, pos, bits);
    };
    GeneralAppIdDecoder.extractNumericValueFromBitArray = function(information, pos, bits) {
        var value = 0;
        for(var i = 0; i < bits; ++i){
            if (information.get(pos + i)) {
                value |= 1 << bits - i - 1;
            }
        }
        return value;
    };
    GeneralAppIdDecoder.prototype.decodeGeneralPurposeField = function(pos, remaining) {
        // this.buffer.setLength(0);
        this.buffer.setLengthToZero();
        if (remaining != null) {
            this.buffer.append(remaining);
        }
        this.current.setPosition(pos);
        var lastDecoded = this.parseBlocks();
        if (lastDecoded != null && lastDecoded.isRemaining()) {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedInformation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](this.current.getPosition(), this.buffer.toString(), lastDecoded.getRemainingValue());
        }
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedInformation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](this.current.getPosition(), this.buffer.toString());
    };
    GeneralAppIdDecoder.prototype.parseBlocks = function() {
        var isFinished;
        var result;
        do {
            var initialPosition = this.current.getPosition();
            if (this.current.isAlpha()) {
                result = this.parseAlphaBlock();
                isFinished = result.isFinished();
            } else if (this.current.isIsoIec646()) {
                result = this.parseIsoIec646Block();
                isFinished = result.isFinished();
            } else {
                result = this.parseNumericBlock();
                isFinished = result.isFinished();
            }
            var positionChanged = initialPosition !== this.current.getPosition();
            if (!positionChanged && !isFinished) {
                break;
            }
        }while (!isFinished)
        return result.getDecodedInformation();
    };
    GeneralAppIdDecoder.prototype.parseNumericBlock = function() {
        while(this.isStillNumeric(this.current.getPosition())){
            var numeric = this.decodeNumeric(this.current.getPosition());
            this.current.setPosition(numeric.getNewPosition());
            if (numeric.isFirstDigitFNC1()) {
                var information = void 0;
                if (numeric.isSecondDigitFNC1()) {
                    information = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedInformation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](this.current.getPosition(), this.buffer.toString());
                } else {
                    information = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedInformation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](this.current.getPosition(), this.buffer.toString(), numeric.getSecondDigit());
                }
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$BlockParsedResult$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](true, information);
            }
            this.buffer.append(numeric.getFirstDigit());
            if (numeric.isSecondDigitFNC1()) {
                var information = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedInformation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](this.current.getPosition(), this.buffer.toString());
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$BlockParsedResult$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](true, information);
            }
            this.buffer.append(numeric.getSecondDigit());
        }
        if (this.isNumericToAlphaNumericLatch(this.current.getPosition())) {
            this.current.setAlpha();
            this.current.incrementPosition(4);
        }
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$BlockParsedResult$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](false);
    };
    GeneralAppIdDecoder.prototype.parseIsoIec646Block = function() {
        while(this.isStillIsoIec646(this.current.getPosition())){
            var iso = this.decodeIsoIec646(this.current.getPosition());
            this.current.setPosition(iso.getNewPosition());
            if (iso.isFNC1()) {
                var information = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedInformation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](this.current.getPosition(), this.buffer.toString());
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$BlockParsedResult$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](true, information);
            }
            this.buffer.append(iso.getValue());
        }
        if (this.isAlphaOr646ToNumericLatch(this.current.getPosition())) {
            this.current.incrementPosition(3);
            this.current.setNumeric();
        } else if (this.isAlphaTo646ToAlphaLatch(this.current.getPosition())) {
            if (this.current.getPosition() + 5 < this.information.getSize()) {
                this.current.incrementPosition(5);
            } else {
                this.current.setPosition(this.information.getSize());
            }
            this.current.setAlpha();
        }
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$BlockParsedResult$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](false);
    };
    GeneralAppIdDecoder.prototype.parseAlphaBlock = function() {
        while(this.isStillAlpha(this.current.getPosition())){
            var alpha = this.decodeAlphanumeric(this.current.getPosition());
            this.current.setPosition(alpha.getNewPosition());
            if (alpha.isFNC1()) {
                var information = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedInformation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](this.current.getPosition(), this.buffer.toString());
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$BlockParsedResult$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](true, information); // end of the char block
            }
            this.buffer.append(alpha.getValue());
        }
        if (this.isAlphaOr646ToNumericLatch(this.current.getPosition())) {
            this.current.incrementPosition(3);
            this.current.setNumeric();
        } else if (this.isAlphaTo646ToAlphaLatch(this.current.getPosition())) {
            if (this.current.getPosition() + 5 < this.information.getSize()) {
                this.current.incrementPosition(5);
            } else {
                this.current.setPosition(this.information.getSize());
            }
            this.current.setIsoIec646();
        }
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$BlockParsedResult$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](false);
    };
    GeneralAppIdDecoder.prototype.isStillIsoIec646 = function(pos) {
        if (pos + 5 > this.information.getSize()) {
            return false;
        }
        var fiveBitValue = this.extractNumericValueFromBitArray(pos, 5);
        if (fiveBitValue >= 5 && fiveBitValue < 16) {
            return true;
        }
        if (pos + 7 > this.information.getSize()) {
            return false;
        }
        var sevenBitValue = this.extractNumericValueFromBitArray(pos, 7);
        if (sevenBitValue >= 64 && sevenBitValue < 116) {
            return true;
        }
        if (pos + 8 > this.information.getSize()) {
            return false;
        }
        var eightBitValue = this.extractNumericValueFromBitArray(pos, 8);
        return eightBitValue >= 232 && eightBitValue < 253;
    };
    GeneralAppIdDecoder.prototype.decodeIsoIec646 = function(pos) {
        var fiveBitValue = this.extractNumericValueFromBitArray(pos, 5);
        if (fiveBitValue === 15) {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedChar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](pos + 5, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedChar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].FNC1);
        }
        if (fiveBitValue >= 5 && fiveBitValue < 15) {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedChar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](pos + 5, '0' + (fiveBitValue - 5));
        }
        var sevenBitValue = this.extractNumericValueFromBitArray(pos, 7);
        if (sevenBitValue >= 64 && sevenBitValue < 90) {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedChar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](pos + 7, '' + (sevenBitValue + 1));
        }
        if (sevenBitValue >= 90 && sevenBitValue < 116) {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedChar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](pos + 7, '' + (sevenBitValue + 7));
        }
        var eightBitValue = this.extractNumericValueFromBitArray(pos, 8);
        var c;
        switch(eightBitValue){
            case 232:
                c = '!';
                break;
            case 233:
                c = '"';
                break;
            case 234:
                c = '%';
                break;
            case 235:
                c = '&';
                break;
            case 236:
                c = '\'';
                break;
            case 237:
                c = '(';
                break;
            case 238:
                c = ')';
                break;
            case 239:
                c = '*';
                break;
            case 240:
                c = '+';
                break;
            case 241:
                c = ',';
                break;
            case 242:
                c = '-';
                break;
            case 243:
                c = '.';
                break;
            case 244:
                c = '/';
                break;
            case 245:
                c = ':';
                break;
            case 246:
                c = ';';
                break;
            case 247:
                c = '<';
                break;
            case 248:
                c = '=';
                break;
            case 249:
                c = '>';
                break;
            case 250:
                c = '?';
                break;
            case 251:
                c = '_';
                break;
            case 252:
                c = ' ';
                break;
            default:
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedChar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](pos + 8, c);
    };
    GeneralAppIdDecoder.prototype.isStillAlpha = function(pos) {
        if (pos + 5 > this.information.getSize()) {
            return false;
        }
        // We now check if it's a valid 5-bit value (0..9 and FNC1)
        var fiveBitValue = this.extractNumericValueFromBitArray(pos, 5);
        if (fiveBitValue >= 5 && fiveBitValue < 16) {
            return true;
        }
        if (pos + 6 > this.information.getSize()) {
            return false;
        }
        var sixBitValue = this.extractNumericValueFromBitArray(pos, 6);
        return sixBitValue >= 16 && sixBitValue < 63; // 63 not included
    };
    GeneralAppIdDecoder.prototype.decodeAlphanumeric = function(pos) {
        var fiveBitValue = this.extractNumericValueFromBitArray(pos, 5);
        if (fiveBitValue === 15) {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedChar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](pos + 5, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedChar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].FNC1);
        }
        if (fiveBitValue >= 5 && fiveBitValue < 15) {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedChar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](pos + 5, '0' + (fiveBitValue - 5));
        }
        var sixBitValue = this.extractNumericValueFromBitArray(pos, 6);
        if (sixBitValue >= 32 && sixBitValue < 58) {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedChar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](pos + 6, '' + (sixBitValue + 33));
        }
        var c;
        switch(sixBitValue){
            case 58:
                c = '*';
                break;
            case 59:
                c = ',';
                break;
            case 60:
                c = '-';
                break;
            case 61:
                c = '.';
                break;
            case 62:
                c = '/';
                break;
            default:
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalStateException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('Decoding invalid alphanumeric value: ' + sixBitValue);
        }
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$DecodedChar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](pos + 6, c);
    };
    GeneralAppIdDecoder.prototype.isAlphaTo646ToAlphaLatch = function(pos) {
        if (pos + 1 > this.information.getSize()) {
            return false;
        }
        for(var i = 0; i < 5 && i + pos < this.information.getSize(); ++i){
            if (i === 2) {
                if (!this.information.get(pos + 2)) {
                    return false;
                }
            } else if (this.information.get(pos + i)) {
                return false;
            }
        }
        return true;
    };
    GeneralAppIdDecoder.prototype.isAlphaOr646ToNumericLatch = function(pos) {
        // Next is alphanumeric if there are 3 positions and they are all zeros
        if (pos + 3 > this.information.getSize()) {
            return false;
        }
        for(var i = pos; i < pos + 3; ++i){
            if (this.information.get(i)) {
                return false;
            }
        }
        return true;
    };
    GeneralAppIdDecoder.prototype.isNumericToAlphaNumericLatch = function(pos) {
        // Next is alphanumeric if there are 4 positions and they are all zeros, or
        // if there is a subset of this just before the end of the symbol
        if (pos + 1 > this.information.getSize()) {
            return false;
        }
        for(var i = 0; i < 4 && i + pos < this.information.getSize(); ++i){
            if (this.information.get(pos + i)) {
                return false;
            }
        }
        return true;
    };
    return GeneralAppIdDecoder;
}();
const __TURBOPACK__default__export__ = GeneralAppIdDecoder;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AbstractExpandedDecoder.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$GeneralAppIdDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/GeneralAppIdDecoder.js [app-ssr] (ecmascript)");
;
var AbstractExpandedDecoder = function() {
    function AbstractExpandedDecoder(information) {
        this.information = information;
        this.generalDecoder = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$GeneralAppIdDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](information);
    }
    AbstractExpandedDecoder.prototype.getInformation = function() {
        return this.information;
    };
    AbstractExpandedDecoder.prototype.getGeneralDecoder = function() {
        return this.generalDecoder;
    };
    return AbstractExpandedDecoder;
}();
const __TURBOPACK__default__export__ = AbstractExpandedDecoder;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AI01decoder.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AbstractExpandedDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AbstractExpandedDecoder.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
var AI01decoder = function(_super) {
    __extends(AI01decoder, _super);
    function AI01decoder(information) {
        return _super.call(this, information) || this;
    }
    AI01decoder.prototype.encodeCompressedGtin = function(buf, currentPos) {
        buf.append('(01)');
        var initialPosition = buf.length();
        buf.append('9');
        this.encodeCompressedGtinWithoutAI(buf, currentPos, initialPosition);
    };
    AI01decoder.prototype.encodeCompressedGtinWithoutAI = function(buf, currentPos, initialBufferPosition) {
        for(var i = 0; i < 4; ++i){
            var currentBlock = this.getGeneralDecoder().extractNumericValueFromBitArray(currentPos + 10 * i, 10);
            if (currentBlock / 100 === 0) {
                buf.append('0');
            }
            if (currentBlock / 10 === 0) {
                buf.append('0');
            }
            buf.append(currentBlock);
        }
        AI01decoder.appendCheckDigit(buf, initialBufferPosition);
    };
    AI01decoder.appendCheckDigit = function(buf, currentPos) {
        var checkDigit = 0;
        for(var i = 0; i < 13; i++){
            // let digit = buf.charAt(i + currentPos) - '0';
            // To be checked
            var digit = buf.charAt(i + currentPos).charCodeAt(0) - '0'.charCodeAt(0);
            checkDigit += (i & 0x01) === 0 ? 3 * digit : digit;
        }
        checkDigit = 10 - checkDigit % 10;
        if (checkDigit === 10) {
            checkDigit = 0;
        }
        buf.append(checkDigit);
    };
    AI01decoder.GTIN_SIZE = 40;
    return AI01decoder;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AbstractExpandedDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = AI01decoder;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AI01AndOtherAIs.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI01decoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AI01decoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/StringBuilder.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
var AI01AndOtherAIs = function(_super) {
    __extends(AI01AndOtherAIs, _super);
    // the second one is the encodation method, and the other two are for the variable length
    function AI01AndOtherAIs(information) {
        return _super.call(this, information) || this;
    }
    AI01AndOtherAIs.prototype.parseInformation = function() {
        var buff = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        buff.append('(01)');
        var initialGtinPosition = buff.length();
        var firstGtinDigit = this.getGeneralDecoder().extractNumericValueFromBitArray(AI01AndOtherAIs.HEADER_SIZE, 4);
        buff.append(firstGtinDigit);
        this.encodeCompressedGtinWithoutAI(buff, AI01AndOtherAIs.HEADER_SIZE + 4, initialGtinPosition);
        return this.getGeneralDecoder().decodeAllCodes(buff, AI01AndOtherAIs.HEADER_SIZE + 44);
    };
    AI01AndOtherAIs.HEADER_SIZE = 1 + 1 + 2; // first bit encodes the linkage flag,
    return AI01AndOtherAIs;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI01decoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = AI01AndOtherAIs;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AnyAIDecoder.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/StringBuilder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AbstractExpandedDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AbstractExpandedDecoder.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
var AnyAIDecoder = function(_super) {
    __extends(AnyAIDecoder, _super);
    function AnyAIDecoder(information) {
        return _super.call(this, information) || this;
    }
    AnyAIDecoder.prototype.parseInformation = function() {
        var buf = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        return this.getGeneralDecoder().decodeAllCodes(buf, AnyAIDecoder.HEADER_SIZE);
    };
    AnyAIDecoder.HEADER_SIZE = 2 + 1 + 2;
    return AnyAIDecoder;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AbstractExpandedDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = AnyAIDecoder;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AI01weightDecoder.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI01decoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AI01decoder.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
var AI01weightDecoder = function(_super) {
    __extends(AI01weightDecoder, _super);
    function AI01weightDecoder(information) {
        return _super.call(this, information) || this;
    }
    AI01weightDecoder.prototype.encodeCompressedWeight = function(buf, currentPos, weightSize) {
        var originalWeightNumeric = this.getGeneralDecoder().extractNumericValueFromBitArray(currentPos, weightSize);
        this.addWeightCode(buf, originalWeightNumeric);
        var weightNumeric = this.checkWeight(originalWeightNumeric);
        var currentDivisor = 100000;
        for(var i = 0; i < 5; ++i){
            if (weightNumeric / currentDivisor === 0) {
                buf.append('0');
            }
            currentDivisor /= 10;
        }
        buf.append(weightNumeric);
    };
    return AI01weightDecoder;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI01decoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = AI01weightDecoder;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AI013x0xDecoder.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI01weightDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AI01weightDecoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/StringBuilder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NotFoundException.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
;
var AI013x0xDecoder = function(_super) {
    __extends(AI013x0xDecoder, _super);
    function AI013x0xDecoder(information) {
        return _super.call(this, information) || this;
    }
    AI013x0xDecoder.prototype.parseInformation = function() {
        if (this.getInformation().getSize() !== AI013x0xDecoder.HEADER_SIZE + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI01weightDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].GTIN_SIZE + AI013x0xDecoder.WEIGHT_SIZE) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        var buf = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        this.encodeCompressedGtin(buf, AI013x0xDecoder.HEADER_SIZE);
        this.encodeCompressedWeight(buf, AI013x0xDecoder.HEADER_SIZE + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI01weightDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].GTIN_SIZE, AI013x0xDecoder.WEIGHT_SIZE);
        return buf.toString();
    };
    AI013x0xDecoder.HEADER_SIZE = 4 + 1;
    AI013x0xDecoder.WEIGHT_SIZE = 15;
    return AI013x0xDecoder;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI01weightDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = AI013x0xDecoder;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AI013103decoder.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI013x0xDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AI013x0xDecoder.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
var AI013103decoder = function(_super) {
    __extends(AI013103decoder, _super);
    function AI013103decoder(information) {
        return _super.call(this, information) || this;
    }
    AI013103decoder.prototype.addWeightCode = function(buf, weight) {
        buf.append('(3103)');
    };
    AI013103decoder.prototype.checkWeight = function(weight) {
        return weight;
    };
    return AI013103decoder;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI013x0xDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = AI013103decoder;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AI01320xDecoder.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI013x0xDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AI013x0xDecoder.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
var AI01320xDecoder = function(_super) {
    __extends(AI01320xDecoder, _super);
    function AI01320xDecoder(information) {
        return _super.call(this, information) || this;
    }
    AI01320xDecoder.prototype.addWeightCode = function(buf, weight) {
        if (weight < 10000) {
            buf.append('(3202)');
        } else {
            buf.append('(3203)');
        }
    };
    AI01320xDecoder.prototype.checkWeight = function(weight) {
        if (weight < 10000) {
            return weight;
        }
        return weight - 10000;
    };
    return AI01320xDecoder;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI013x0xDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = AI01320xDecoder;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AI01392xDecoder.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI01decoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AI01decoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NotFoundException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/StringBuilder.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
;
var AI01392xDecoder = function(_super) {
    __extends(AI01392xDecoder, _super);
    function AI01392xDecoder(information) {
        return _super.call(this, information) || this;
    }
    AI01392xDecoder.prototype.parseInformation = function() {
        if (this.getInformation().getSize() < AI01392xDecoder.HEADER_SIZE + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI01decoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].GTIN_SIZE) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        var buf = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        this.encodeCompressedGtin(buf, AI01392xDecoder.HEADER_SIZE);
        var lastAIdigit = this.getGeneralDecoder().extractNumericValueFromBitArray(AI01392xDecoder.HEADER_SIZE + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI01decoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].GTIN_SIZE, AI01392xDecoder.LAST_DIGIT_SIZE);
        buf.append('(392');
        buf.append(lastAIdigit);
        buf.append(')');
        var decodedInformation = this.getGeneralDecoder().decodeGeneralPurposeField(AI01392xDecoder.HEADER_SIZE + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI01decoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].GTIN_SIZE + AI01392xDecoder.LAST_DIGIT_SIZE, null);
        buf.append(decodedInformation.getNewString());
        return buf.toString();
    };
    AI01392xDecoder.HEADER_SIZE = 5 + 1 + 2;
    AI01392xDecoder.LAST_DIGIT_SIZE = 2;
    return AI01392xDecoder;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI01decoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = AI01392xDecoder;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AI01393xDecoder.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI01decoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AI01decoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NotFoundException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/StringBuilder.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
;
var AI01393xDecoder = function(_super) {
    __extends(AI01393xDecoder, _super);
    function AI01393xDecoder(information) {
        return _super.call(this, information) || this;
    }
    AI01393xDecoder.prototype.parseInformation = function() {
        if (this.getInformation().getSize() < AI01393xDecoder.HEADER_SIZE + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI01decoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].GTIN_SIZE) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        var buf = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        this.encodeCompressedGtin(buf, AI01393xDecoder.HEADER_SIZE);
        var lastAIdigit = this.getGeneralDecoder().extractNumericValueFromBitArray(AI01393xDecoder.HEADER_SIZE + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI01decoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].GTIN_SIZE, AI01393xDecoder.LAST_DIGIT_SIZE);
        buf.append('(393');
        buf.append(lastAIdigit);
        buf.append(')');
        var firstThreeDigits = this.getGeneralDecoder().extractNumericValueFromBitArray(AI01393xDecoder.HEADER_SIZE + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI01decoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].GTIN_SIZE + AI01393xDecoder.LAST_DIGIT_SIZE, AI01393xDecoder.FIRST_THREE_DIGITS_SIZE);
        if (firstThreeDigits / 100 === 0) {
            buf.append('0');
        }
        if (firstThreeDigits / 10 === 0) {
            buf.append('0');
        }
        buf.append(firstThreeDigits);
        var generalInformation = this.getGeneralDecoder().decodeGeneralPurposeField(AI01393xDecoder.HEADER_SIZE + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI01decoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].GTIN_SIZE + AI01393xDecoder.LAST_DIGIT_SIZE + AI01393xDecoder.FIRST_THREE_DIGITS_SIZE, null);
        buf.append(generalInformation.getNewString());
        return buf.toString();
    };
    AI01393xDecoder.HEADER_SIZE = 5 + 1 + 2;
    AI01393xDecoder.LAST_DIGIT_SIZE = 2;
    AI01393xDecoder.FIRST_THREE_DIGITS_SIZE = 10;
    return AI01393xDecoder;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI01decoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = AI01393xDecoder;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AI013x0x1xDecoder.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI01weightDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AI01weightDecoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NotFoundException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/StringBuilder.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
;
var AI013x0x1xDecoder = function(_super) {
    __extends(AI013x0x1xDecoder, _super);
    function AI013x0x1xDecoder(information, firstAIdigits, dateCode) {
        var _this = _super.call(this, information) || this;
        _this.dateCode = dateCode;
        _this.firstAIdigits = firstAIdigits;
        return _this;
    }
    AI013x0x1xDecoder.prototype.parseInformation = function() {
        if (this.getInformation().getSize() !== AI013x0x1xDecoder.HEADER_SIZE + AI013x0x1xDecoder.GTIN_SIZE + AI013x0x1xDecoder.WEIGHT_SIZE + AI013x0x1xDecoder.DATE_SIZE) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        var buf = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        this.encodeCompressedGtin(buf, AI013x0x1xDecoder.HEADER_SIZE);
        this.encodeCompressedWeight(buf, AI013x0x1xDecoder.HEADER_SIZE + AI013x0x1xDecoder.GTIN_SIZE, AI013x0x1xDecoder.WEIGHT_SIZE);
        this.encodeCompressedDate(buf, AI013x0x1xDecoder.HEADER_SIZE + AI013x0x1xDecoder.GTIN_SIZE + AI013x0x1xDecoder.WEIGHT_SIZE);
        return buf.toString();
    };
    AI013x0x1xDecoder.prototype.encodeCompressedDate = function(buf, currentPos) {
        var numericDate = this.getGeneralDecoder().extractNumericValueFromBitArray(currentPos, AI013x0x1xDecoder.DATE_SIZE);
        if (numericDate === 38400) {
            return;
        }
        buf.append('(');
        buf.append(this.dateCode);
        buf.append(')');
        var day = numericDate % 32;
        numericDate /= 32;
        var month = numericDate % 12 + 1;
        numericDate /= 12;
        var year = numericDate;
        if (year / 10 === 0) {
            buf.append('0');
        }
        buf.append(year);
        if (month / 10 === 0) {
            buf.append('0');
        }
        buf.append(month);
        if (day / 10 === 0) {
            buf.append('0');
        }
        buf.append(day);
    };
    AI013x0x1xDecoder.prototype.addWeightCode = function(buf, weight) {
        buf.append('(');
        buf.append(this.firstAIdigits);
        buf.append(weight / 100000);
        buf.append(')');
    };
    AI013x0x1xDecoder.prototype.checkWeight = function(weight) {
        return weight % 100000;
    };
    AI013x0x1xDecoder.HEADER_SIZE = 7 + 1;
    AI013x0x1xDecoder.WEIGHT_SIZE = 20;
    AI013x0x1xDecoder.DATE_SIZE = 16;
    return AI013x0x1xDecoder;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI01weightDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = AI013x0x1xDecoder;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AbstractExpandedDecoderComplement.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "createDecoder": ()=>createDecoder
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalStateException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/IllegalStateException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$GeneralAppIdDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/GeneralAppIdDecoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI01AndOtherAIs$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AI01AndOtherAIs.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AnyAIDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AnyAIDecoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI013103decoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AI013103decoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI01320xDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AI01320xDecoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI01392xDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AI01392xDecoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI01393xDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AI01393xDecoder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI013x0x1xDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AI013x0x1xDecoder.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
function createDecoder(information) {
    try {
        if (information.get(1)) {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI01AndOtherAIs$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](information);
        }
        if (!information.get(2)) {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AnyAIDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](information);
        }
        var fourBitEncodationMethod = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$GeneralAppIdDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].extractNumericValueFromBitArray(information, 1, 4);
        switch(fourBitEncodationMethod){
            case 4:
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI013103decoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](information);
            case 5:
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI01320xDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](information);
        }
        var fiveBitEncodationMethod = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$GeneralAppIdDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].extractNumericValueFromBitArray(information, 1, 5);
        switch(fiveBitEncodationMethod){
            case 12:
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI01392xDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](information);
            case 13:
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI01393xDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](information);
        }
        var sevenBitEncodationMethod = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$GeneralAppIdDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].extractNumericValueFromBitArray(information, 1, 7);
        switch(sevenBitEncodationMethod){
            case 56:
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI013x0x1xDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](information, '310', '11');
            case 57:
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI013x0x1xDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](information, '320', '11');
            case 58:
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI013x0x1xDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](information, '310', '13');
            case 59:
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI013x0x1xDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](information, '320', '13');
            case 60:
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI013x0x1xDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](information, '310', '15');
            case 61:
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI013x0x1xDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](information, '320', '15');
            case 62:
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI013x0x1xDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](information, '310', '17');
            case 63:
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AI013x0x1xDecoder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](information, '320', '17');
        }
    } catch (e) {
        console.log(e);
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalStateException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('unknown decoder: ' + information);
    }
}
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/ExpandedPair.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var ExpandedPair = function() {
    function ExpandedPair(leftChar, rightChar, finderPatter, mayBeLast) {
        this.leftchar = leftChar;
        this.rightchar = rightChar;
        this.finderpattern = finderPatter;
        this.maybeLast = mayBeLast;
    }
    ExpandedPair.prototype.mayBeLast = function() {
        return this.maybeLast;
    };
    ExpandedPair.prototype.getLeftChar = function() {
        return this.leftchar;
    };
    ExpandedPair.prototype.getRightChar = function() {
        return this.rightchar;
    };
    ExpandedPair.prototype.getFinderPattern = function() {
        return this.finderpattern;
    };
    ExpandedPair.prototype.mustBeLast = function() {
        return this.rightchar == null;
    };
    ExpandedPair.prototype.toString = function() {
        return '[ ' + this.leftchar + ', ' + this.rightchar + ' : ' + (this.finderpattern == null ? 'null' : this.finderpattern.getValue()) + ' ]';
    };
    ExpandedPair.equals = function(o1, o2) {
        if (!(o1 instanceof ExpandedPair)) {
            return false;
        }
        return ExpandedPair.equalsOrNull(o1.leftchar, o2.leftchar) && ExpandedPair.equalsOrNull(o1.rightchar, o2.rightchar) && ExpandedPair.equalsOrNull(o1.finderpattern, o2.finderpattern);
    };
    ExpandedPair.equalsOrNull = function(o1, o2) {
        return o1 === null ? o2 === null : ExpandedPair.equals(o1, o2);
    };
    ExpandedPair.prototype.hashCode = function() {
        // return ExpandedPair.hashNotNull(leftChar) ^ hashNotNull(rightChar) ^ hashNotNull(finderPattern);
        var value = this.leftchar.getValue() ^ this.rightchar.getValue() ^ this.finderpattern.getValue();
        return value;
    };
    return ExpandedPair;
}();
const __TURBOPACK__default__export__ = ExpandedPair;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/ExpandedRow.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var ExpandedRow = function() {
    function ExpandedRow(pairs, rowNumber, wasReversed) {
        this.pairs = pairs;
        this.rowNumber = rowNumber;
        this.wasReversed = wasReversed;
    }
    ExpandedRow.prototype.getPairs = function() {
        return this.pairs;
    };
    ExpandedRow.prototype.getRowNumber = function() {
        return this.rowNumber;
    };
    ExpandedRow.prototype.isReversed = function() {
        return this.wasReversed;
    };
    // check implementation
    ExpandedRow.prototype.isEquivalent = function(otherPairs) {
        return this.checkEqualitity(this, otherPairs);
    };
    // @Override
    ExpandedRow.prototype.toString = function() {
        return '{ ' + this.pairs + ' }';
    };
    /**
     * Two rows are equal if they contain the same pairs in the same order.
     */ // @Override
    // check implementation
    ExpandedRow.prototype.equals = function(o1, o2) {
        if (!(o1 instanceof ExpandedRow)) {
            return false;
        }
        return this.checkEqualitity(o1, o2) && o1.wasReversed === o2.wasReversed;
    };
    ExpandedRow.prototype.checkEqualitity = function(pair1, pair2) {
        if (!pair1 || !pair2) return;
        var result;
        pair1.forEach(function(e1, i) {
            pair2.forEach(function(e2) {
                if (e1.getLeftChar().getValue() === e2.getLeftChar().getValue() && e1.getRightChar().getValue() === e2.getRightChar().getValue() && e1.getFinderPatter().getValue() === e2.getFinderPatter().getValue()) {
                    result = true;
                }
            });
        });
        return result;
    };
    return ExpandedRow;
}();
const __TURBOPACK__default__export__ = ExpandedRow;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/RSSExpandedReader.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/BarcodeFormat.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/detector/MathUtils.js [app-ssr] (ecmascript)");
// import FormatException from '../../../FormatException';
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NotFoundException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Result.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/System.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$AbstractRSSReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/AbstractRSSReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$DataCharacter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/DataCharacter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$FinderPattern$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/FinderPattern.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$RSSUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/RSSUtils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$BitArrayBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/BitArrayBuilder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AbstractExpandedDecoderComplement$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AbstractExpandedDecoderComplement.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$ExpandedPair$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/ExpandedPair.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$ExpandedRow$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/ExpandedRow.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
;
;
;
;
;
;
;
;
;
;
;
// import java.util.ArrayList;
// import java.util.Iterator;
// import java.util.List;
// import java.util.Map;
// import java.util.Collections;
/** @experimental */ var RSSExpandedReader = function(_super) {
    __extends(RSSExpandedReader, _super);
    function RSSExpandedReader() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.pairs = new Array(RSSExpandedReader.MAX_PAIRS);
        _this.rows = new Array();
        _this.startEnd = [
            2
        ];
        return _this;
    }
    RSSExpandedReader.prototype.decodeRow = function(rowNumber, row, hints) {
        // Rows can start with even pattern in case in prev rows there where odd number of patters.
        // So lets try twice
        // this.pairs.clear();
        this.pairs.length = 0;
        this.startFromEven = false;
        try {
            return RSSExpandedReader.constructResult(this.decodeRow2pairs(rowNumber, row));
        } catch (e) {
        // OK
        // console.log(e);
        }
        this.pairs.length = 0;
        this.startFromEven = true;
        return RSSExpandedReader.constructResult(this.decodeRow2pairs(rowNumber, row));
    };
    RSSExpandedReader.prototype.reset = function() {
        this.pairs.length = 0;
        this.rows.length = 0;
    };
    // Not private for testing
    RSSExpandedReader.prototype.decodeRow2pairs = function(rowNumber, row) {
        var done = false;
        while(!done){
            try {
                this.pairs.push(this.retrieveNextPair(row, this.pairs, rowNumber));
            } catch (error) {
                if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]) {
                    if (!this.pairs.length) {
                        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                    }
                    // exit this loop when retrieveNextPair() fails and throws
                    done = true;
                }
            }
        }
        // TODO: verify sequence of finder patterns as in checkPairSequence()
        if (this.checkChecksum()) {
            return this.pairs;
        }
        var tryStackedDecode;
        if (this.rows.length) {
            tryStackedDecode = true;
        } else {
            tryStackedDecode = false;
        }
        // let tryStackedDecode = !this.rows.isEmpty();
        this.storeRow(rowNumber, false); // TODO: deal with reversed rows
        if (tryStackedDecode) {
            // When the image is 180-rotated, then rows are sorted in wrong direction.
            // Try twice with both the directions.
            var ps = this.checkRowsBoolean(false);
            if (ps != null) {
                return ps;
            }
            ps = this.checkRowsBoolean(true);
            if (ps != null) {
                return ps;
            }
        }
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
    };
    // Need to Verify
    RSSExpandedReader.prototype.checkRowsBoolean = function(reverse) {
        // Limit number of rows we are checking
        // We use recursive algorithm with pure complexity and don't want it to take forever
        // Stacked barcode can have up to 11 rows, so 25 seems reasonable enough
        if (this.rows.length > 25) {
            this.rows.length = 0; // We will never have a chance to get result, so clear it
            return null;
        }
        this.pairs.length = 0;
        if (reverse) {
            this.rows = this.rows.reverse();
        // Collections.reverse(this.rows);
        }
        var ps = null;
        try {
            ps = this.checkRows(new Array(), 0);
        } catch (e) {
            // OK
            console.log(e);
        }
        if (reverse) {
            this.rows = this.rows.reverse();
        // Collections.reverse(this.rows);
        }
        return ps;
    };
    // Try to construct a valid rows sequence
    // Recursion is used to implement backtracking
    RSSExpandedReader.prototype.checkRows = function(collectedRows, currentRow) {
        var e_1, _a;
        for(var i = currentRow; i < this.rows.length; i++){
            var row = this.rows[i];
            this.pairs.length = 0;
            try {
                for(var collectedRows_1 = (e_1 = void 0, __values(collectedRows)), collectedRows_1_1 = collectedRows_1.next(); !collectedRows_1_1.done; collectedRows_1_1 = collectedRows_1.next()){
                    var collectedRow = collectedRows_1_1.value;
                    this.pairs.push(collectedRow.getPairs());
                }
            } catch (e_1_1) {
                e_1 = {
                    error: e_1_1
                };
            } finally{
                try {
                    if (collectedRows_1_1 && !collectedRows_1_1.done && (_a = collectedRows_1.return)) _a.call(collectedRows_1);
                } finally{
                    if (e_1) throw e_1.error;
                }
            }
            this.pairs.push(row.getPairs());
            if (!RSSExpandedReader.isValidSequence(this.pairs)) {
                continue;
            }
            if (this.checkChecksum()) {
                return this.pairs;
            }
            var rs = new Array(collectedRows);
            rs.push(row);
            try {
                // Recursion: try to add more rows
                return this.checkRows(rs, i + 1);
            } catch (e) {
                // We failed, try the next candidate
                console.log(e);
            }
        }
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
    };
    // Whether the pairs form a valid find pattern sequence,
    // either complete or a prefix
    RSSExpandedReader.isValidSequence = function(pairs) {
        var e_2, _a;
        try {
            for(var _b = __values(RSSExpandedReader.FINDER_PATTERN_SEQUENCES), _c = _b.next(); !_c.done; _c = _b.next()){
                var sequence = _c.value;
                if (pairs.length > sequence.length) {
                    continue;
                }
                var stop_1 = true;
                for(var j = 0; j < pairs.length; j++){
                    if (pairs[j].getFinderPattern().getValue() !== sequence[j]) {
                        stop_1 = false;
                        break;
                    }
                }
                if (stop_1) {
                    return true;
                }
            }
        } catch (e_2_1) {
            e_2 = {
                error: e_2_1
            };
        } finally{
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            } finally{
                if (e_2) throw e_2.error;
            }
        }
        return false;
    };
    RSSExpandedReader.prototype.storeRow = function(rowNumber, wasReversed) {
        // Discard if duplicate above or below; otherwise insert in order by row number.
        var insertPos = 0;
        var prevIsSame = false;
        var nextIsSame = false;
        while(insertPos < this.rows.length){
            var erow = this.rows[insertPos];
            if (erow.getRowNumber() > rowNumber) {
                nextIsSame = erow.isEquivalent(this.pairs);
                break;
            }
            prevIsSame = erow.isEquivalent(this.pairs);
            insertPos++;
        }
        if (nextIsSame || prevIsSame) {
            return;
        }
        // When the row was partially decoded (e.g. 2 pairs found instead of 3),
        // it will prevent us from detecting the barcode.
        // Try to merge partial rows
        // Check whether the row is part of an allready detected row
        if (RSSExpandedReader.isPartialRow(this.pairs, this.rows)) {
            return;
        }
        this.rows.push(insertPos, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$ExpandedRow$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](this.pairs, rowNumber, wasReversed));
        this.removePartialRows(this.pairs, this.rows);
    };
    // Remove all the rows that contains only specified pairs
    RSSExpandedReader.prototype.removePartialRows = function(pairs, rows) {
        var e_3, _a, e_4, _b, e_5, _c;
        try {
            // for (Iterator<ExpandedRow> iterator = rows.iterator(); iterator.hasNext();) {
            //   ExpandedRow r = iterator.next();
            //   if (r.getPairs().size() == pairs.size()) {
            //     continue;
            //   }
            //   boolean allFound = true;
            //   for (ExpandedPair p : r.getPairs()) {
            //     boolean found = false;
            //     for (ExpandedPair pp : pairs) {
            //       if (p.equals(pp)) {
            //         found = true;
            //         break;
            //       }
            //     }
            //     if (!found) {
            //       allFound = false;
            //       break;
            //     }
            //   }
            //   if (allFound) {
            //     // 'pairs' contains all the pairs from the row 'r'
            //     iterator.remove();
            //   }
            // }
            for(var rows_1 = __values(rows), rows_1_1 = rows_1.next(); !rows_1_1.done; rows_1_1 = rows_1.next()){
                var row = rows_1_1.value;
                if (row.getPairs().length === pairs.length) {
                    continue;
                }
                var allFound = true;
                try {
                    for(var _d = (e_4 = void 0, __values(row.getPairs())), _e = _d.next(); !_e.done; _e = _d.next()){
                        var p = _e.value;
                        var found = false;
                        try {
                            for(var pairs_1 = (e_5 = void 0, __values(pairs)), pairs_1_1 = pairs_1.next(); !pairs_1_1.done; pairs_1_1 = pairs_1.next()){
                                var pp = pairs_1_1.value;
                                if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$ExpandedPair$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].equals(p, pp)) {
                                    found = true;
                                    break;
                                }
                            }
                        } catch (e_5_1) {
                            e_5 = {
                                error: e_5_1
                            };
                        } finally{
                            try {
                                if (pairs_1_1 && !pairs_1_1.done && (_c = pairs_1.return)) _c.call(pairs_1);
                            } finally{
                                if (e_5) throw e_5.error;
                            }
                        }
                        if (!found) {
                            allFound = false;
                        }
                    }
                } catch (e_4_1) {
                    e_4 = {
                        error: e_4_1
                    };
                } finally{
                    try {
                        if (_e && !_e.done && (_b = _d.return)) _b.call(_d);
                    } finally{
                        if (e_4) throw e_4.error;
                    }
                }
            }
        } catch (e_3_1) {
            e_3 = {
                error: e_3_1
            };
        } finally{
            try {
                if (rows_1_1 && !rows_1_1.done && (_a = rows_1.return)) _a.call(rows_1);
            } finally{
                if (e_3) throw e_3.error;
            }
        }
    };
    // Returns true when one of the rows already contains all the pairs
    RSSExpandedReader.isPartialRow = function(pairs, rows) {
        var e_6, _a, e_7, _b, e_8, _c;
        try {
            for(var rows_2 = __values(rows), rows_2_1 = rows_2.next(); !rows_2_1.done; rows_2_1 = rows_2.next()){
                var r = rows_2_1.value;
                var allFound = true;
                try {
                    for(var pairs_2 = (e_7 = void 0, __values(pairs)), pairs_2_1 = pairs_2.next(); !pairs_2_1.done; pairs_2_1 = pairs_2.next()){
                        var p = pairs_2_1.value;
                        var found = false;
                        try {
                            for(var _d = (e_8 = void 0, __values(r.getPairs())), _e = _d.next(); !_e.done; _e = _d.next()){
                                var pp = _e.value;
                                if (p.equals(pp)) {
                                    found = true;
                                    break;
                                }
                            }
                        } catch (e_8_1) {
                            e_8 = {
                                error: e_8_1
                            };
                        } finally{
                            try {
                                if (_e && !_e.done && (_c = _d.return)) _c.call(_d);
                            } finally{
                                if (e_8) throw e_8.error;
                            }
                        }
                        if (!found) {
                            allFound = false;
                            break;
                        }
                    }
                } catch (e_7_1) {
                    e_7 = {
                        error: e_7_1
                    };
                } finally{
                    try {
                        if (pairs_2_1 && !pairs_2_1.done && (_b = pairs_2.return)) _b.call(pairs_2);
                    } finally{
                        if (e_7) throw e_7.error;
                    }
                }
                if (allFound) {
                    // the row 'r' contain all the pairs from 'pairs'
                    return true;
                }
            }
        } catch (e_6_1) {
            e_6 = {
                error: e_6_1
            };
        } finally{
            try {
                if (rows_2_1 && !rows_2_1.done && (_a = rows_2.return)) _a.call(rows_2);
            } finally{
                if (e_6) throw e_6.error;
            }
        }
        return false;
    };
    // Only used for unit testing
    RSSExpandedReader.prototype.getRows = function() {
        return this.rows;
    };
    // Not private for unit testing
    RSSExpandedReader.constructResult = function(pairs) {
        var binary = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$BitArrayBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].buildBitArray(pairs);
        var decoder = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$decoders$2f$AbstractExpandedDecoderComplement$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createDecoder"])(binary);
        var resultingString = decoder.parseInformation();
        var firstPoints = pairs[0].getFinderPattern().getResultPoints();
        var lastPoints = pairs[pairs.length - 1].getFinderPattern().getResultPoints();
        var points = [
            firstPoints[0],
            firstPoints[1],
            lastPoints[0],
            lastPoints[1]
        ];
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](resultingString, null, null, points, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].RSS_EXPANDED, null);
    };
    RSSExpandedReader.prototype.checkChecksum = function() {
        var firstPair = this.pairs.get(0);
        var checkCharacter = firstPair.getLeftChar();
        var firstCharacter = firstPair.getRightChar();
        if (firstCharacter === null) {
            return false;
        }
        var checksum = firstCharacter.getChecksumPortion();
        var s = 2;
        for(var i = 1; i < this.pairs.size(); ++i){
            var currentPair = this.pairs.get(i);
            checksum += currentPair.getLeftChar().getChecksumPortion();
            s++;
            var currentRightChar = currentPair.getRightChar();
            if (currentRightChar != null) {
                checksum += currentRightChar.getChecksumPortion();
                s++;
            }
        }
        checksum %= 211;
        var checkCharacterValue = 211 * (s - 4) + checksum;
        return checkCharacterValue === checkCharacter.getValue();
    };
    RSSExpandedReader.getNextSecondBar = function(row, initialPos) {
        var currentPos;
        if (row.get(initialPos)) {
            currentPos = row.getNextUnset(initialPos);
            currentPos = row.getNextSet(currentPos);
        } else {
            currentPos = row.getNextSet(initialPos);
            currentPos = row.getNextUnset(currentPos);
        }
        return currentPos;
    };
    // not private for testing
    RSSExpandedReader.prototype.retrieveNextPair = function(row, previousPairs, rowNumber) {
        var isOddPattern = previousPairs.length % 2 === 0;
        if (this.startFromEven) {
            isOddPattern = !isOddPattern;
        }
        var pattern;
        var keepFinding = true;
        var forcedOffset = -1;
        do {
            this.findNextPair(row, previousPairs, forcedOffset);
            pattern = this.parseFoundFinderPattern(row, rowNumber, isOddPattern);
            if (pattern === null) {
                forcedOffset = RSSExpandedReader.getNextSecondBar(row, this.startEnd[0]);
            } else {
                keepFinding = false;
            }
        }while (keepFinding)
        // When stacked symbol is split over multiple rows, there's no way to guess if this pair can be last or not.
        // boolean mayBeLast = checkPairSequence(previousPairs, pattern);
        var leftChar = this.decodeDataCharacter(row, pattern, isOddPattern, true);
        if (!this.isEmptyPair(previousPairs) && previousPairs[previousPairs.length - 1].mustBeLast()) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        var rightChar;
        try {
            rightChar = this.decodeDataCharacter(row, pattern, isOddPattern, false);
        } catch (e) {
            rightChar = null;
            console.log(e);
        }
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$ExpandedPair$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](leftChar, rightChar, pattern, true);
    };
    RSSExpandedReader.prototype.isEmptyPair = function(pairs) {
        if (pairs.length === 0) {
            return true;
        }
        return false;
    };
    RSSExpandedReader.prototype.findNextPair = function(row, previousPairs, forcedOffset) {
        var counters = this.getDecodeFinderCounters();
        counters[0] = 0;
        counters[1] = 0;
        counters[2] = 0;
        counters[3] = 0;
        var width = row.getSize();
        var rowOffset;
        if (forcedOffset >= 0) {
            rowOffset = forcedOffset;
        } else if (this.isEmptyPair(previousPairs)) {
            rowOffset = 0;
        } else {
            var lastPair = previousPairs[previousPairs.length - 1];
            rowOffset = lastPair.getFinderPattern().getStartEnd()[1];
        }
        var searchingEvenPair = previousPairs.length % 2 !== 0;
        if (this.startFromEven) {
            searchingEvenPair = !searchingEvenPair;
        }
        var isWhite = false;
        while(rowOffset < width){
            isWhite = !row.get(rowOffset);
            if (!isWhite) {
                break;
            }
            rowOffset++;
        }
        var counterPosition = 0;
        var patternStart = rowOffset;
        for(var x = rowOffset; x < width; x++){
            if (row.get(x) !== isWhite) {
                counters[counterPosition]++;
            } else {
                if (counterPosition === 3) {
                    if (searchingEvenPair) {
                        RSSExpandedReader.reverseCounters(counters);
                    }
                    if (RSSExpandedReader.isFinderPattern(counters)) {
                        this.startEnd[0] = patternStart;
                        this.startEnd[1] = x;
                        return;
                    }
                    if (searchingEvenPair) {
                        RSSExpandedReader.reverseCounters(counters);
                    }
                    patternStart += counters[0] + counters[1];
                    counters[0] = counters[2];
                    counters[1] = counters[3];
                    counters[2] = 0;
                    counters[3] = 0;
                    counterPosition--;
                } else {
                    counterPosition++;
                }
                counters[counterPosition] = 1;
                isWhite = !isWhite;
            }
        }
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
    };
    RSSExpandedReader.reverseCounters = function(counters) {
        var length = counters.length;
        for(var i = 0; i < length / 2; ++i){
            var tmp = counters[i];
            counters[i] = counters[length - i - 1];
            counters[length - i - 1] = tmp;
        }
    };
    RSSExpandedReader.prototype.parseFoundFinderPattern = function(row, rowNumber, oddPattern) {
        // Actually we found elements 2-5.
        var firstCounter;
        var start;
        var end;
        if (oddPattern) {
            // If pattern number is odd, we need to locate element 1 *before* the current block.
            var firstElementStart = this.startEnd[0] - 1;
            // Locate element 1
            while(firstElementStart >= 0 && !row.get(firstElementStart)){
                firstElementStart--;
            }
            firstElementStart++;
            firstCounter = this.startEnd[0] - firstElementStart;
            start = firstElementStart;
            end = this.startEnd[1];
        } else {
            // If pattern number is even, the pattern is reversed, so we need to locate element 1 *after* the current block.
            start = this.startEnd[0];
            end = row.getNextUnset(this.startEnd[1] + 1);
            firstCounter = end - this.startEnd[1];
        }
        // Make 'counters' hold 1-4
        var counters = this.getDecodeFinderCounters();
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].arraycopy(counters, 0, counters, 1, counters.length - 1);
        counters[0] = firstCounter;
        var value;
        try {
            value = this.parseFinderValue(counters, RSSExpandedReader.FINDER_PATTERNS);
        } catch (e) {
            return null;
        }
        // return new FinderPattern(value, new int[] { start, end }, start, end, rowNumber});
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$FinderPattern$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](value, [
            start,
            end
        ], start, end, rowNumber);
    };
    RSSExpandedReader.prototype.decodeDataCharacter = function(row, pattern, isOddPattern, leftChar) {
        var counters = this.getDataCharacterCounters();
        for(var x = 0; x < counters.length; x++){
            counters[x] = 0;
        }
        if (leftChar) {
            RSSExpandedReader.recordPatternInReverse(row, pattern.getStartEnd()[0], counters);
        } else {
            RSSExpandedReader.recordPattern(row, pattern.getStartEnd()[1], counters);
            // reverse it
            for(var i = 0, j = counters.length - 1; i < j; i++, j--){
                var temp = counters[i];
                counters[i] = counters[j];
                counters[j] = temp;
            }
        } // counters[] has the pixels of the module
        var numModules = 17; // left and right data characters have all the same length
        var elementWidth = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sum(new Int32Array(counters)) / numModules;
        // Sanity check: element width for pattern and the character should match
        var expectedElementWidth = (pattern.getStartEnd()[1] - pattern.getStartEnd()[0]) / 15.0;
        if (Math.abs(elementWidth - expectedElementWidth) / expectedElementWidth > 0.3) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        var oddCounts = this.getOddCounts();
        var evenCounts = this.getEvenCounts();
        var oddRoundingErrors = this.getOddRoundingErrors();
        var evenRoundingErrors = this.getEvenRoundingErrors();
        for(var i = 0; i < counters.length; i++){
            var value_1 = 1.0 * counters[i] / elementWidth;
            var count = value_1 + 0.5; // Round
            if (count < 1) {
                if (value_1 < 0.3) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                }
                count = 1;
            } else if (count > 8) {
                if (value_1 > 8.7) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                }
                count = 8;
            }
            var offset = i / 2;
            if ((i & 0x01) === 0) {
                oddCounts[offset] = count;
                oddRoundingErrors[offset] = value_1 - count;
            } else {
                evenCounts[offset] = count;
                evenRoundingErrors[offset] = value_1 - count;
            }
        }
        this.adjustOddEvenCounts(numModules);
        var weightRowNumber = 4 * pattern.getValue() + (isOddPattern ? 0 : 2) + (leftChar ? 0 : 1) - 1;
        var oddSum = 0;
        var oddChecksumPortion = 0;
        for(var i = oddCounts.length - 1; i >= 0; i--){
            if (RSSExpandedReader.isNotA1left(pattern, isOddPattern, leftChar)) {
                var weight = RSSExpandedReader.WEIGHTS[weightRowNumber][2 * i];
                oddChecksumPortion += oddCounts[i] * weight;
            }
            oddSum += oddCounts[i];
        }
        var evenChecksumPortion = 0;
        // int evenSum = 0;
        for(var i = evenCounts.length - 1; i >= 0; i--){
            if (RSSExpandedReader.isNotA1left(pattern, isOddPattern, leftChar)) {
                var weight = RSSExpandedReader.WEIGHTS[weightRowNumber][2 * i + 1];
                evenChecksumPortion += evenCounts[i] * weight;
            }
        // evenSum += evenCounts[i];
        }
        var checksumPortion = oddChecksumPortion + evenChecksumPortion;
        if ((oddSum & 0x01) !== 0 || oddSum > 13 || oddSum < 4) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        var group = (13 - oddSum) / 2;
        var oddWidest = RSSExpandedReader.SYMBOL_WIDEST[group];
        var evenWidest = 9 - oddWidest;
        var vOdd = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$RSSUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getRSSvalue(oddCounts, oddWidest, true);
        var vEven = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$RSSUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getRSSvalue(evenCounts, evenWidest, false);
        var tEven = RSSExpandedReader.EVEN_TOTAL_SUBSET[group];
        var gSum = RSSExpandedReader.GSUM[group];
        var value = vOdd * tEven + vEven + gSum;
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$DataCharacter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](value, checksumPortion);
    };
    RSSExpandedReader.isNotA1left = function(pattern, isOddPattern, leftChar) {
        // A1: pattern.getValue is 0 (A), and it's an oddPattern, and it is a left char
        return !(pattern.getValue() === 0 && isOddPattern && leftChar);
    };
    RSSExpandedReader.prototype.adjustOddEvenCounts = function(numModules) {
        var oddSum = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sum(new Int32Array(this.getOddCounts()));
        var evenSum = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sum(new Int32Array(this.getEvenCounts()));
        var incrementOdd = false;
        var decrementOdd = false;
        if (oddSum > 13) {
            decrementOdd = true;
        } else if (oddSum < 4) {
            incrementOdd = true;
        }
        var incrementEven = false;
        var decrementEven = false;
        if (evenSum > 13) {
            decrementEven = true;
        } else if (evenSum < 4) {
            incrementEven = true;
        }
        var mismatch = oddSum + evenSum - numModules;
        var oddParityBad = (oddSum & 0x01) === 1;
        var evenParityBad = (evenSum & 0x01) === 0;
        if (mismatch === 1) {
            if (oddParityBad) {
                if (evenParityBad) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                }
                decrementOdd = true;
            } else {
                if (!evenParityBad) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                }
                decrementEven = true;
            }
        } else if (mismatch === -1) {
            if (oddParityBad) {
                if (evenParityBad) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                }
                incrementOdd = true;
            } else {
                if (!evenParityBad) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                }
                incrementEven = true;
            }
        } else if (mismatch === 0) {
            if (oddParityBad) {
                if (!evenParityBad) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                }
                // Both bad
                if (oddSum < evenSum) {
                    incrementOdd = true;
                    decrementEven = true;
                } else {
                    decrementOdd = true;
                    incrementEven = true;
                }
            } else {
                if (evenParityBad) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                }
            // Nothing to do!
            }
        } else {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        if (incrementOdd) {
            if (decrementOdd) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
            }
            RSSExpandedReader.increment(this.getOddCounts(), this.getOddRoundingErrors());
        }
        if (decrementOdd) {
            RSSExpandedReader.decrement(this.getOddCounts(), this.getOddRoundingErrors());
        }
        if (incrementEven) {
            if (decrementEven) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
            }
            RSSExpandedReader.increment(this.getEvenCounts(), this.getOddRoundingErrors());
        }
        if (decrementEven) {
            RSSExpandedReader.decrement(this.getEvenCounts(), this.getEvenRoundingErrors());
        }
    };
    RSSExpandedReader.SYMBOL_WIDEST = [
        7,
        5,
        4,
        3,
        1
    ];
    RSSExpandedReader.EVEN_TOTAL_SUBSET = [
        4,
        20,
        52,
        104,
        204
    ];
    RSSExpandedReader.GSUM = [
        0,
        348,
        1388,
        2948,
        3988
    ];
    RSSExpandedReader.FINDER_PATTERNS = [
        Int32Array.from([
            1,
            8,
            4,
            1
        ]),
        Int32Array.from([
            3,
            6,
            4,
            1
        ]),
        Int32Array.from([
            3,
            4,
            6,
            1
        ]),
        Int32Array.from([
            3,
            2,
            8,
            1
        ]),
        Int32Array.from([
            2,
            6,
            5,
            1
        ]),
        Int32Array.from([
            2,
            2,
            9,
            1
        ])
    ];
    RSSExpandedReader.WEIGHTS = [
        [
            1,
            3,
            9,
            27,
            81,
            32,
            96,
            77
        ],
        [
            20,
            60,
            180,
            118,
            143,
            7,
            21,
            63
        ],
        [
            189,
            145,
            13,
            39,
            117,
            140,
            209,
            205
        ],
        [
            193,
            157,
            49,
            147,
            19,
            57,
            171,
            91
        ],
        [
            62,
            186,
            136,
            197,
            169,
            85,
            44,
            132
        ],
        [
            185,
            133,
            188,
            142,
            4,
            12,
            36,
            108
        ],
        [
            113,
            128,
            173,
            97,
            80,
            29,
            87,
            50
        ],
        [
            150,
            28,
            84,
            41,
            123,
            158,
            52,
            156
        ],
        [
            46,
            138,
            203,
            187,
            139,
            206,
            196,
            166
        ],
        [
            76,
            17,
            51,
            153,
            37,
            111,
            122,
            155
        ],
        [
            43,
            129,
            176,
            106,
            107,
            110,
            119,
            146
        ],
        [
            16,
            48,
            144,
            10,
            30,
            90,
            59,
            177
        ],
        [
            109,
            116,
            137,
            200,
            178,
            112,
            125,
            164
        ],
        [
            70,
            210,
            208,
            202,
            184,
            130,
            179,
            115
        ],
        [
            134,
            191,
            151,
            31,
            93,
            68,
            204,
            190
        ],
        [
            148,
            22,
            66,
            198,
            172,
            94,
            71,
            2
        ],
        [
            6,
            18,
            54,
            162,
            64,
            192,
            154,
            40
        ],
        [
            120,
            149,
            25,
            75,
            14,
            42,
            126,
            167
        ],
        [
            79,
            26,
            78,
            23,
            69,
            207,
            199,
            175
        ],
        [
            103,
            98,
            83,
            38,
            114,
            131,
            182,
            124
        ],
        [
            161,
            61,
            183,
            127,
            170,
            88,
            53,
            159
        ],
        [
            55,
            165,
            73,
            8,
            24,
            72,
            5,
            15
        ],
        [
            45,
            135,
            194,
            160,
            58,
            174,
            100,
            89
        ]
    ];
    RSSExpandedReader.FINDER_PAT_A = 0;
    RSSExpandedReader.FINDER_PAT_B = 1;
    RSSExpandedReader.FINDER_PAT_C = 2;
    RSSExpandedReader.FINDER_PAT_D = 3;
    RSSExpandedReader.FINDER_PAT_E = 4;
    RSSExpandedReader.FINDER_PAT_F = 5;
    RSSExpandedReader.FINDER_PATTERN_SEQUENCES = [
        [
            RSSExpandedReader.FINDER_PAT_A,
            RSSExpandedReader.FINDER_PAT_A
        ],
        [
            RSSExpandedReader.FINDER_PAT_A,
            RSSExpandedReader.FINDER_PAT_B,
            RSSExpandedReader.FINDER_PAT_B
        ],
        [
            RSSExpandedReader.FINDER_PAT_A,
            RSSExpandedReader.FINDER_PAT_C,
            RSSExpandedReader.FINDER_PAT_B,
            RSSExpandedReader.FINDER_PAT_D
        ],
        [
            RSSExpandedReader.FINDER_PAT_A,
            RSSExpandedReader.FINDER_PAT_E,
            RSSExpandedReader.FINDER_PAT_B,
            RSSExpandedReader.FINDER_PAT_D,
            RSSExpandedReader.FINDER_PAT_C
        ],
        [
            RSSExpandedReader.FINDER_PAT_A,
            RSSExpandedReader.FINDER_PAT_E,
            RSSExpandedReader.FINDER_PAT_B,
            RSSExpandedReader.FINDER_PAT_D,
            RSSExpandedReader.FINDER_PAT_D,
            RSSExpandedReader.FINDER_PAT_F
        ],
        [
            RSSExpandedReader.FINDER_PAT_A,
            RSSExpandedReader.FINDER_PAT_E,
            RSSExpandedReader.FINDER_PAT_B,
            RSSExpandedReader.FINDER_PAT_D,
            RSSExpandedReader.FINDER_PAT_E,
            RSSExpandedReader.FINDER_PAT_F,
            RSSExpandedReader.FINDER_PAT_F
        ],
        [
            RSSExpandedReader.FINDER_PAT_A,
            RSSExpandedReader.FINDER_PAT_A,
            RSSExpandedReader.FINDER_PAT_B,
            RSSExpandedReader.FINDER_PAT_B,
            RSSExpandedReader.FINDER_PAT_C,
            RSSExpandedReader.FINDER_PAT_C,
            RSSExpandedReader.FINDER_PAT_D,
            RSSExpandedReader.FINDER_PAT_D
        ],
        [
            RSSExpandedReader.FINDER_PAT_A,
            RSSExpandedReader.FINDER_PAT_A,
            RSSExpandedReader.FINDER_PAT_B,
            RSSExpandedReader.FINDER_PAT_B,
            RSSExpandedReader.FINDER_PAT_C,
            RSSExpandedReader.FINDER_PAT_C,
            RSSExpandedReader.FINDER_PAT_D,
            RSSExpandedReader.FINDER_PAT_E,
            RSSExpandedReader.FINDER_PAT_E
        ],
        [
            RSSExpandedReader.FINDER_PAT_A,
            RSSExpandedReader.FINDER_PAT_A,
            RSSExpandedReader.FINDER_PAT_B,
            RSSExpandedReader.FINDER_PAT_B,
            RSSExpandedReader.FINDER_PAT_C,
            RSSExpandedReader.FINDER_PAT_C,
            RSSExpandedReader.FINDER_PAT_D,
            RSSExpandedReader.FINDER_PAT_E,
            RSSExpandedReader.FINDER_PAT_F,
            RSSExpandedReader.FINDER_PAT_F
        ],
        [
            RSSExpandedReader.FINDER_PAT_A,
            RSSExpandedReader.FINDER_PAT_A,
            RSSExpandedReader.FINDER_PAT_B,
            RSSExpandedReader.FINDER_PAT_B,
            RSSExpandedReader.FINDER_PAT_C,
            RSSExpandedReader.FINDER_PAT_D,
            RSSExpandedReader.FINDER_PAT_D,
            RSSExpandedReader.FINDER_PAT_E,
            RSSExpandedReader.FINDER_PAT_E,
            RSSExpandedReader.FINDER_PAT_F,
            RSSExpandedReader.FINDER_PAT_F
        ]
    ];
    RSSExpandedReader.MAX_PAIRS = 11;
    return RSSExpandedReader;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$AbstractRSSReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = RSSExpandedReader;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/Pair.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$DataCharacter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/DataCharacter.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
var Pair = function(_super) {
    __extends(Pair, _super);
    function Pair(value, checksumPortion, finderPattern) {
        var _this = _super.call(this, value, checksumPortion) || this;
        _this.count = 0;
        _this.finderPattern = finderPattern;
        return _this;
    }
    Pair.prototype.getFinderPattern = function() {
        return this.finderPattern;
    };
    Pair.prototype.getCount = function() {
        return this.count;
    };
    Pair.prototype.incrementCount = function() {
        this.count++;
    };
    return Pair;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$DataCharacter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = Pair;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/RSS14Reader.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$AbstractRSSReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/AbstractRSSReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$Pair$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/Pair.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Result.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/DecodeHintType.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NotFoundException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/StringBuilder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/BarcodeFormat.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ResultPoint.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$FinderPattern$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/FinderPattern.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$DataCharacter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/DataCharacter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/detector/MathUtils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$RSSUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/RSSUtils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/System.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/OneDReader.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
;
;
;
;
;
;
;
;
;
;
;
;
var RSS14Reader = function(_super) {
    __extends(RSS14Reader, _super);
    function RSS14Reader() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.possibleLeftPairs = [];
        _this.possibleRightPairs = [];
        return _this;
    }
    RSS14Reader.prototype.decodeRow = function(rowNumber, row, hints) {
        var e_1, _a, e_2, _b;
        var leftPair = this.decodePair(row, false, rowNumber, hints);
        RSS14Reader.addOrTally(this.possibleLeftPairs, leftPair);
        row.reverse();
        var rightPair = this.decodePair(row, true, rowNumber, hints);
        RSS14Reader.addOrTally(this.possibleRightPairs, rightPair);
        row.reverse();
        try {
            for(var _c = __values(this.possibleLeftPairs), _d = _c.next(); !_d.done; _d = _c.next()){
                var left = _d.value;
                if (left.getCount() > 1) {
                    try {
                        for(var _e = (e_2 = void 0, __values(this.possibleRightPairs)), _f = _e.next(); !_f.done; _f = _e.next()){
                            var right = _f.value;
                            if (right.getCount() > 1 && RSS14Reader.checkChecksum(left, right)) {
                                return RSS14Reader.constructResult(left, right);
                            }
                        }
                    } catch (e_2_1) {
                        e_2 = {
                            error: e_2_1
                        };
                    } finally{
                        try {
                            if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
                        } finally{
                            if (e_2) throw e_2.error;
                        }
                    }
                }
            }
        } catch (e_1_1) {
            e_1 = {
                error: e_1_1
            };
        } finally{
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            } finally{
                if (e_1) throw e_1.error;
            }
        }
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
    };
    RSS14Reader.addOrTally = function(possiblePairs, pair) {
        var e_3, _a;
        if (pair == null) {
            return;
        }
        var found = false;
        try {
            for(var possiblePairs_1 = __values(possiblePairs), possiblePairs_1_1 = possiblePairs_1.next(); !possiblePairs_1_1.done; possiblePairs_1_1 = possiblePairs_1.next()){
                var other = possiblePairs_1_1.value;
                if (other.getValue() === pair.getValue()) {
                    other.incrementCount();
                    found = true;
                    break;
                }
            }
        } catch (e_3_1) {
            e_3 = {
                error: e_3_1
            };
        } finally{
            try {
                if (possiblePairs_1_1 && !possiblePairs_1_1.done && (_a = possiblePairs_1.return)) _a.call(possiblePairs_1);
            } finally{
                if (e_3) throw e_3.error;
            }
        }
        if (!found) {
            possiblePairs.push(pair);
        }
    };
    RSS14Reader.prototype.reset = function() {
        this.possibleLeftPairs.length = 0;
        this.possibleRightPairs.length = 0;
    };
    RSS14Reader.constructResult = function(leftPair, rightPair) {
        var symbolValue = 4537077 * leftPair.getValue() + rightPair.getValue();
        var text = new String(symbolValue).toString();
        var buffer = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        for(var i = 13 - text.length; i > 0; i--){
            buffer.append('0');
        }
        buffer.append(text);
        var checkDigit = 0;
        for(var i = 0; i < 13; i++){
            var digit = buffer.charAt(i).charCodeAt(0) - '0'.charCodeAt(0);
            checkDigit += (i & 0x01) === 0 ? 3 * digit : digit;
        }
        checkDigit = 10 - checkDigit % 10;
        if (checkDigit === 10) {
            checkDigit = 0;
        }
        buffer.append(checkDigit.toString());
        var leftPoints = leftPair.getFinderPattern().getResultPoints();
        var rightPoints = rightPair.getFinderPattern().getResultPoints();
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](buffer.toString(), null, 0, [
            leftPoints[0],
            leftPoints[1],
            rightPoints[0],
            rightPoints[1]
        ], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].RSS_14, new Date().getTime());
    };
    RSS14Reader.checkChecksum = function(leftPair, rightPair) {
        var checkValue = (leftPair.getChecksumPortion() + 16 * rightPair.getChecksumPortion()) % 79;
        var targetCheckValue = 9 * leftPair.getFinderPattern().getValue() + rightPair.getFinderPattern().getValue();
        if (targetCheckValue > 72) {
            targetCheckValue--;
        }
        if (targetCheckValue > 8) {
            targetCheckValue--;
        }
        return checkValue === targetCheckValue;
    };
    RSS14Reader.prototype.decodePair = function(row, right, rowNumber, hints) {
        try {
            var startEnd = this.findFinderPattern(row, right);
            var pattern = this.parseFoundFinderPattern(row, rowNumber, right, startEnd);
            var resultPointCallback = hints == null ? null : hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].NEED_RESULT_POINT_CALLBACK);
            if (resultPointCallback != null) {
                var center = (startEnd[0] + startEnd[1]) / 2.0;
                if (right) {
                    // row is actually reversed
                    center = row.getSize() - 1 - center;
                }
                resultPointCallback.foundPossibleResultPoint(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](center, rowNumber));
            }
            var outside = this.decodeDataCharacter(row, pattern, true);
            var inside = this.decodeDataCharacter(row, pattern, false);
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$Pair$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](1597 * outside.getValue() + inside.getValue(), outside.getChecksumPortion() + 4 * inside.getChecksumPortion(), pattern);
        } catch (err) {
            return null;
        }
    };
    RSS14Reader.prototype.decodeDataCharacter = function(row, pattern, outsideChar) {
        var counters = this.getDataCharacterCounters();
        for(var x = 0; x < counters.length; x++){
            counters[x] = 0;
        }
        if (outsideChar) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].recordPatternInReverse(row, pattern.getStartEnd()[0], counters);
        } else {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].recordPattern(row, pattern.getStartEnd()[1] + 1, counters);
            // reverse it
            for(var i = 0, j = counters.length - 1; i < j; i++, j--){
                var temp = counters[i];
                counters[i] = counters[j];
                counters[j] = temp;
            }
        }
        var numModules = outsideChar ? 16 : 15;
        var elementWidth = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sum(new Int32Array(counters)) / numModules;
        var oddCounts = this.getOddCounts();
        var evenCounts = this.getEvenCounts();
        var oddRoundingErrors = this.getOddRoundingErrors();
        var evenRoundingErrors = this.getEvenRoundingErrors();
        for(var i = 0; i < counters.length; i++){
            var value = counters[i] / elementWidth;
            var count = Math.floor(value + 0.5);
            if (count < 1) {
                count = 1;
            } else if (count > 8) {
                count = 8;
            }
            var offset = Math.floor(i / 2);
            if ((i & 0x01) === 0) {
                oddCounts[offset] = count;
                oddRoundingErrors[offset] = value - count;
            } else {
                evenCounts[offset] = count;
                evenRoundingErrors[offset] = value - count;
            }
        }
        this.adjustOddEvenCounts(outsideChar, numModules);
        var oddSum = 0;
        var oddChecksumPortion = 0;
        for(var i = oddCounts.length - 1; i >= 0; i--){
            oddChecksumPortion *= 9;
            oddChecksumPortion += oddCounts[i];
            oddSum += oddCounts[i];
        }
        var evenChecksumPortion = 0;
        var evenSum = 0;
        for(var i = evenCounts.length - 1; i >= 0; i--){
            evenChecksumPortion *= 9;
            evenChecksumPortion += evenCounts[i];
            evenSum += evenCounts[i];
        }
        var checksumPortion = oddChecksumPortion + 3 * evenChecksumPortion;
        if (outsideChar) {
            if ((oddSum & 0x01) !== 0 || oddSum > 12 || oddSum < 4) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
            }
            var group = (12 - oddSum) / 2;
            var oddWidest = RSS14Reader.OUTSIDE_ODD_WIDEST[group];
            var evenWidest = 9 - oddWidest;
            var vOdd = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$RSSUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getRSSvalue(oddCounts, oddWidest, false);
            var vEven = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$RSSUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getRSSvalue(evenCounts, evenWidest, true);
            var tEven = RSS14Reader.OUTSIDE_EVEN_TOTAL_SUBSET[group];
            var gSum = RSS14Reader.OUTSIDE_GSUM[group];
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$DataCharacter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](vOdd * tEven + vEven + gSum, checksumPortion);
        } else {
            if ((evenSum & 0x01) !== 0 || evenSum > 10 || evenSum < 4) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
            }
            var group = (10 - evenSum) / 2;
            var oddWidest = RSS14Reader.INSIDE_ODD_WIDEST[group];
            var evenWidest = 9 - oddWidest;
            var vOdd = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$RSSUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getRSSvalue(oddCounts, oddWidest, true);
            var vEven = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$RSSUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getRSSvalue(evenCounts, evenWidest, false);
            var tOdd = RSS14Reader.INSIDE_ODD_TOTAL_SUBSET[group];
            var gSum = RSS14Reader.INSIDE_GSUM[group];
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$DataCharacter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](vEven * tOdd + vOdd + gSum, checksumPortion);
        }
    };
    RSS14Reader.prototype.findFinderPattern = function(row, rightFinderPattern) {
        var counters = this.getDecodeFinderCounters();
        counters[0] = 0;
        counters[1] = 0;
        counters[2] = 0;
        counters[3] = 0;
        var width = row.getSize();
        var isWhite = false;
        var rowOffset = 0;
        while(rowOffset < width){
            isWhite = !row.get(rowOffset);
            if (rightFinderPattern === isWhite) {
                break;
            }
            rowOffset++;
        }
        var counterPosition = 0;
        var patternStart = rowOffset;
        for(var x = rowOffset; x < width; x++){
            if (row.get(x) !== isWhite) {
                counters[counterPosition]++;
            } else {
                if (counterPosition === 3) {
                    if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$AbstractRSSReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isFinderPattern(counters)) {
                        return [
                            patternStart,
                            x
                        ];
                    }
                    patternStart += counters[0] + counters[1];
                    counters[0] = counters[2];
                    counters[1] = counters[3];
                    counters[2] = 0;
                    counters[3] = 0;
                    counterPosition--;
                } else {
                    counterPosition++;
                }
                counters[counterPosition] = 1;
                isWhite = !isWhite;
            }
        }
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
    };
    RSS14Reader.prototype.parseFoundFinderPattern = function(row, rowNumber, right, startEnd) {
        // Actually we found elements 2-5
        var firstIsBlack = row.get(startEnd[0]);
        var firstElementStart = startEnd[0] - 1;
        // Locate element 1
        while(firstElementStart >= 0 && firstIsBlack !== row.get(firstElementStart)){
            firstElementStart--;
        }
        firstElementStart++;
        var firstCounter = startEnd[0] - firstElementStart;
        // Make 'counters' hold 1-4
        var counters = this.getDecodeFinderCounters();
        var copy = new Int32Array(counters.length);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].arraycopy(counters, 0, copy, 1, counters.length - 1);
        copy[0] = firstCounter;
        var value = this.parseFinderValue(copy, RSS14Reader.FINDER_PATTERNS);
        var start = firstElementStart;
        var end = startEnd[1];
        if (right) {
            // row is actually reversed
            start = row.getSize() - 1 - start;
            end = row.getSize() - 1 - end;
        }
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$FinderPattern$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](value, [
            firstElementStart,
            startEnd[1]
        ], start, end, rowNumber);
    };
    RSS14Reader.prototype.adjustOddEvenCounts = function(outsideChar, numModules) {
        var oddSum = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sum(new Int32Array(this.getOddCounts()));
        var evenSum = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sum(new Int32Array(this.getEvenCounts()));
        var incrementOdd = false;
        var decrementOdd = false;
        var incrementEven = false;
        var decrementEven = false;
        if (outsideChar) {
            if (oddSum > 12) {
                decrementOdd = true;
            } else if (oddSum < 4) {
                incrementOdd = true;
            }
            if (evenSum > 12) {
                decrementEven = true;
            } else if (evenSum < 4) {
                incrementEven = true;
            }
        } else {
            if (oddSum > 11) {
                decrementOdd = true;
            } else if (oddSum < 5) {
                incrementOdd = true;
            }
            if (evenSum > 10) {
                decrementEven = true;
            } else if (evenSum < 4) {
                incrementEven = true;
            }
        }
        var mismatch = oddSum + evenSum - numModules;
        var oddParityBad = (oddSum & 0x01) === (outsideChar ? 1 : 0);
        var evenParityBad = (evenSum & 0x01) === 1;
        if (mismatch === 1) {
            if (oddParityBad) {
                if (evenParityBad) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                }
                decrementOdd = true;
            } else {
                if (!evenParityBad) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                }
                decrementEven = true;
            }
        } else if (mismatch === -1) {
            if (oddParityBad) {
                if (evenParityBad) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                }
                incrementOdd = true;
            } else {
                if (!evenParityBad) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                }
                incrementEven = true;
            }
        } else if (mismatch === 0) {
            if (oddParityBad) {
                if (!evenParityBad) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                }
                // Both bad
                if (oddSum < evenSum) {
                    incrementOdd = true;
                    decrementEven = true;
                } else {
                    decrementOdd = true;
                    incrementEven = true;
                }
            } else {
                if (evenParityBad) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                }
            // Nothing to do!
            }
        } else {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        if (incrementOdd) {
            if (decrementOdd) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
            }
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$AbstractRSSReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].increment(this.getOddCounts(), this.getOddRoundingErrors());
        }
        if (decrementOdd) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$AbstractRSSReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].decrement(this.getOddCounts(), this.getOddRoundingErrors());
        }
        if (incrementEven) {
            if (decrementEven) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
            }
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$AbstractRSSReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].increment(this.getEvenCounts(), this.getOddRoundingErrors());
        }
        if (decrementEven) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$AbstractRSSReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].decrement(this.getEvenCounts(), this.getEvenRoundingErrors());
        }
    };
    RSS14Reader.OUTSIDE_EVEN_TOTAL_SUBSET = [
        1,
        10,
        34,
        70,
        126
    ];
    RSS14Reader.INSIDE_ODD_TOTAL_SUBSET = [
        4,
        20,
        48,
        81
    ];
    RSS14Reader.OUTSIDE_GSUM = [
        0,
        161,
        961,
        2015,
        2715
    ];
    RSS14Reader.INSIDE_GSUM = [
        0,
        336,
        1036,
        1516
    ];
    RSS14Reader.OUTSIDE_ODD_WIDEST = [
        8,
        6,
        4,
        3,
        1
    ];
    RSS14Reader.INSIDE_ODD_WIDEST = [
        2,
        4,
        6,
        8
    ];
    RSS14Reader.FINDER_PATTERNS = [
        Int32Array.from([
            3,
            8,
            2,
            1
        ]),
        Int32Array.from([
            3,
            5,
            5,
            1
        ]),
        Int32Array.from([
            3,
            3,
            7,
            1
        ]),
        Int32Array.from([
            3,
            1,
            9,
            1
        ]),
        Int32Array.from([
            2,
            7,
            4,
            1
        ]),
        Int32Array.from([
            2,
            5,
            6,
            1
        ]),
        Int32Array.from([
            2,
            3,
            8,
            1
        ]),
        Int32Array.from([
            1,
            5,
            7,
            1
        ]),
        Int32Array.from([
            1,
            3,
            9,
            1
        ])
    ];
    return RSS14Reader;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$AbstractRSSReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = RSS14Reader;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/MultiFormatOneDReader.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2008 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
/*namespace com.google.zxing.oned {*/ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/BarcodeFormat.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/DecodeHintType.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NotFoundException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$Code128Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/Code128Reader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$Code39Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/Code39Reader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$Code93Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/Code93Reader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$ITFReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/ITFReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$MultiFormatUPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/MultiFormatUPCEANReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/OneDReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$CodaBarReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/CodaBarReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$RSSExpandedReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/expanded/RSSExpandedReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$RSS14Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/rss/RSS14Reader.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
;
;
;
;
;
;
;
;
;
;
/**
 * <AUTHOR> Switkin <<EMAIL>>
 * <AUTHOR> Owen
 */ var MultiFormatOneDReader = function(_super) {
    __extends(MultiFormatOneDReader, _super);
    function MultiFormatOneDReader(hints) {
        var _this = _super.call(this) || this;
        _this.readers = [];
        var possibleFormats = !hints ? null : hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].POSSIBLE_FORMATS);
        var useCode39CheckDigit = hints && hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].ASSUME_CODE_39_CHECK_DIGIT) !== undefined;
        var useCode39ExtendedMode = hints && hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].ENABLE_CODE_39_EXTENDED_MODE) !== undefined;
        if (possibleFormats) {
            if (possibleFormats.includes(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].EAN_13) || possibleFormats.includes(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].UPC_A) || possibleFormats.includes(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].EAN_8) || possibleFormats.includes(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].UPC_E)) {
                _this.readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$MultiFormatUPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](hints));
            }
            if (possibleFormats.includes(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].CODE_39)) {
                _this.readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$Code39Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](useCode39CheckDigit, useCode39ExtendedMode));
            }
            if (possibleFormats.includes(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].CODE_93)) {
                _this.readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$Code93Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
            }
            if (possibleFormats.includes(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].CODE_128)) {
                _this.readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$Code128Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
            }
            if (possibleFormats.includes(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].ITF)) {
                _this.readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$ITFReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
            }
            if (possibleFormats.includes(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].CODABAR)) {
                _this.readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$CodaBarReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
            }
            if (possibleFormats.includes(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].RSS_14)) {
                _this.readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$RSS14Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
            }
            if (possibleFormats.includes(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].RSS_EXPANDED)) {
                console.warn('RSS Expanded reader IS NOT ready for production yet! use at your own risk.');
                _this.readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$expanded$2f$RSSExpandedReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
            }
        }
        if (_this.readers.length === 0) {
            _this.readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$MultiFormatUPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](hints));
            _this.readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$Code39Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
            // this.readers.push(new CodaBarReader());
            _this.readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$Code93Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
            _this.readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$MultiFormatUPCEANReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](hints));
            _this.readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$Code128Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
            _this.readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$ITFReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
            _this.readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$rss$2f$RSS14Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
        // this.readers.push(new RSSExpandedReader());
        }
        return _this;
    }
    // @Override
    MultiFormatOneDReader.prototype.decodeRow = function(rowNumber, row, hints) {
        for(var i = 0; i < this.readers.length; i++){
            try {
                return this.readers[i].decodeRow(rowNumber, row, hints);
            } catch (re) {
            // continue
            }
        }
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
    };
    // @Override
    MultiFormatOneDReader.prototype.reset = function() {
        this.readers.forEach(function(reader) {
            return reader.reset();
        });
    };
    return MultiFormatOneDReader;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$OneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = MultiFormatOneDReader;
}),

};

//# sourceMappingURL=652c1_%40zxing_library_esm_core_oned_5c351d72._.js.map