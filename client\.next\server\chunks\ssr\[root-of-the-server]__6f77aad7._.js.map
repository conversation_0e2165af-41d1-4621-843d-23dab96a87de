{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/src/app/profiles/page.tsx"], "sourcesContent": ["import Link from 'next/link';\n\ninterface ProfileSummary {\n  id: number;\n  nama: string;\n  usia: number;\n  alamat: string;\n  riwayat_medis: string;\n  created_at: string;\n  total_checkups: number;\n  last_checkup: string | null;\n}\n\ninterface ProfilesData {\n  profiles: ProfileSummary[];\n  total: number;\n}\n\nasync function getProfilesData(): Promise<ProfilesData | null> {\n  try {\n    const response = await fetch('http://localhost:5000/api/profiles', {\n      cache: 'no-store'\n    });\n\n    if (!response.ok) {\n      return null;\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching profiles:', error);\n    return null;\n  }\n}\n\nfunction formatDate(dateString: string | null): string {\n  if (!dateString) return 'Belum ada';\n  \n  const date = new Date(dateString);\n  return date.toLocaleDateString('id-ID', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric'\n  });\n}\n\nfunction getAgeCategory(age: number): { category: string; color: string } {\n  if (age < 60) {\n    return { category: 'Pra-Lansia', color: 'text-blue-600 bg-blue-100' };\n  } else if (age < 70) {\n    return { category: 'Lansia Muda', color: 'text-green-600 bg-green-100' };\n  } else if (age < 80) {\n    return { category: 'Lansia Madya', color: 'text-yellow-600 bg-yellow-100' };\n  } else {\n    return { category: 'Lansia Tua', color: 'text-red-600 bg-red-100' };\n  }\n}\n\nexport default async function ProfilesPage() {\n  const data = await getProfilesData();\n\n  if (!data) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-green-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">Gagal Memuat Data</h1>\n          <p className=\"text-gray-600 mb-6\">Tidak dapat terhubung ke server. Pastikan server backend berjalan.</p>\n          <Link \n            href=\"/\" \n            className=\"bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            Kembali ke Beranda\n          </Link>\n        </div>\n      </div>\n    );\n  }\n\n  const { profiles, total } = data;\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-green-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-4\">\n            <div className=\"flex items-center space-x-3\">\n              <Link href=\"/\" className=\"flex items-center space-x-3\">\n                <div className=\"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-lg\">L</span>\n                </div>\n                <div>\n                  <h1 className=\"text-xl font-bold text-gray-900\">Kesehatan Lansia</h1>\n                  <p className=\"text-sm text-gray-500\">Daftar Semua Lansia</p>\n                </div>\n              </Link>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <Link \n                href=\"/form\" \n                className=\"bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors\"\n              >\n                + Tambah Lansia\n              </Link>\n              <Link \n                href=\"/\" \n                className=\"text-gray-600 hover:text-gray-900 transition-colors\"\n              >\n                ← Beranda\n              </Link>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        {/* Statistics */}\n        <div className=\"grid md:grid-cols-4 gap-6 mb-8\">\n          <div className=\"bg-white rounded-xl p-6 shadow-lg border border-gray-100 card-shadow\">\n            <div className=\"flex items-center\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n                <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">Total Lansia</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{total}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-xl p-6 shadow-lg border border-gray-100 card-shadow\">\n            <div className=\"flex items-center\">\n              <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n                <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">Aktif Bulan Ini</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {profiles.filter(p => p.last_checkup && new Date(p.last_checkup) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)).length}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-xl p-6 shadow-lg border border-gray-100 card-shadow\">\n            <div className=\"flex items-center\">\n              <div className=\"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center\">\n                <svg className=\"w-6 h-6 text-yellow-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">Rata-rata Usia</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {profiles.length > 0 ? Math.round(profiles.reduce((sum, p) => sum + p.usia, 0) / profiles.length) : 0}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-xl p-6 shadow-lg border border-gray-100 card-shadow\">\n            <div className=\"flex items-center\">\n              <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\">\n                <svg className=\"w-6 h-6 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-500\">Total Pemeriksaan</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {profiles.reduce((sum, p) => sum + p.total_checkups, 0)}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Profiles List */}\n        <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 card-shadow-lg\">\n          <div className=\"px-6 py-4 border-b border-gray-200\">\n            <h2 className=\"text-xl font-bold text-gray-900\">Daftar Lansia Terdaftar</h2>\n            <p className=\"text-sm text-gray-600\">Klik nama untuk melihat detail profil dan riwayat pemeriksaan</p>\n          </div>\n\n          {profiles.length === 0 ? (\n            <div className=\"px-6 py-12 text-center\">\n              <svg className=\"w-16 h-16 mx-auto text-gray-400 mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n              </svg>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Belum Ada Data Lansia</h3>\n              <p className=\"text-gray-600 mb-4\">Mulai dengan menambahkan data lansia pertama</p>\n              <Link \n                href=\"/form\" \n                className=\"bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors\"\n              >\n                Tambah Lansia Pertama\n              </Link>\n            </div>\n          ) : (\n            <div className=\"overflow-x-auto\">\n              <table className=\"w-full\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Nama & Usia\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Alamat\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Pemeriksaan\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Terakhir Diperiksa\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Aksi\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {profiles.map((profile) => (\n                    <tr key={profile.id} className=\"hover:bg-gray-50\">\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"flex items-center\">\n                          <div className=\"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\">\n                            <span className=\"text-blue-600 font-semibold text-sm\">\n                              {profile.nama.charAt(0).toUpperCase()}\n                            </span>\n                          </div>\n                          <div className=\"ml-4\">\n                            <div className=\"text-sm font-medium text-gray-900\">{profile.nama}</div>\n                            <div className=\"flex items-center space-x-2\">\n                              <span className=\"text-sm text-gray-500\">{profile.usia} tahun</span>\n                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getAgeCategory(profile.usia).color}`}>\n                                {getAgeCategory(profile.usia).category}\n                              </span>\n                            </div>\n                          </div>\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4\">\n                        <div className=\"text-sm text-gray-900 max-w-xs truncate\" title={profile.alamat}>\n                          {profile.alamat}\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"text-sm text-gray-900\">{profile.total_checkups} kali</div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"text-sm text-gray-900\">{formatDate(profile.last_checkup)}</div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                        <Link\n                          href={`/profile/${profile.id}`}\n                          className=\"text-blue-600 hover:text-blue-900 transition-colors\"\n                        >\n                          Lihat Detail\n                        </Link>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          )}\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAkBA,eAAe;IACb,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,sCAAsC;YACjE,OAAO;QACT;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO;QACT;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;IACT;AACF;AAEA,SAAS,WAAW,UAAyB;IAC3C,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEA,SAAS,eAAe,GAAW;IACjC,IAAI,MAAM,IAAI;QACZ,OAAO;YAAE,UAAU;YAAc,OAAO;QAA4B;IACtE,OAAO,IAAI,MAAM,IAAI;QACnB,OAAO;YAAE,UAAU;YAAe,OAAO;QAA8B;IACzE,OAAO,IAAI,MAAM,IAAI;QACnB,OAAO;YAAE,UAAU;YAAgB,OAAO;QAAgC;IAC5E,OAAO;QACL,OAAO;YAAE,UAAU;YAAc,OAAO;QAA0B;IACpE;AACF;AAEe,eAAe;IAC5B,MAAM,OAAO,MAAM;IAEnB,IAAI,CAAC,MAAM;QACT,qBACE,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6WAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,6WAAC,2RAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAO,WAAU;0BAChB,cAAA,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC,2RAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,6WAAC;4CAAI,WAAU;sDACb,cAAA,6WAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,6WAAC;;8DACC,6WAAC;oDAAG,WAAU;8DAAkC;;;;;;8DAChD,6WAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;0CAI3C,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,2RAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,6WAAC,2RAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,6WAAC;gBAAK,WAAU;;kCAEd,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;sDACb,cAAA,6WAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC/E,cAAA,6WAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6WAAC;oDAAE,WAAU;8DAAoC;;;;;;;;;;;;;;;;;;;;;;;0CAKvD,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;sDACb,cAAA,6WAAC;gDAAI,WAAU;gDAAyB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAChF,cAAA,6WAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6WAAC;oDAAE,WAAU;8DACV,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,IAAI,IAAI,KAAK,EAAE,YAAY,IAAI,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;;0CAMlI,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;sDACb,cAAA,6WAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjF,cAAA,6WAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6WAAC;oDAAE,WAAU;8DACV,SAAS,MAAM,GAAG,IAAI,KAAK,KAAK,CAAC,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,IAAI,EAAE,KAAK,SAAS,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;;;0CAM5G,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;sDACb,cAAA,6WAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjF,cAAA,6WAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6WAAC;oDAAE,WAAU;8DACV,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,cAAc,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ/D,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAG,WAAU;kDAAkC;;;;;;kDAChD,6WAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;4BAGtC,SAAS,MAAM,KAAK,kBACnB,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;wCAAuC,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC9F,cAAA,6WAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;kDAEvE,6WAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,6WAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAClC,6WAAC,2RAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;qDAKH,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAM,WAAU;;sDACf,6WAAC;4CAAM,WAAU;sDACf,cAAA,6WAAC;;kEACC,6WAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6WAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6WAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6WAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6WAAC;wDAAG,WAAU;kEAAiF;;;;;;;;;;;;;;;;;sDAKnG,6WAAC;4CAAM,WAAU;sDACd,SAAS,GAAG,CAAC,CAAC,wBACb,6WAAC;oDAAoB,WAAU;;sEAC7B,6WAAC;4DAAG,WAAU;sEACZ,cAAA,6WAAC;gEAAI,WAAU;;kFACb,6WAAC;wEAAI,WAAU;kFACb,cAAA,6WAAC;4EAAK,WAAU;sFACb,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;kFAGvC,6WAAC;wEAAI,WAAU;;0FACb,6WAAC;gFAAI,WAAU;0FAAqC,QAAQ,IAAI;;;;;;0FAChE,6WAAC;gFAAI,WAAU;;kGACb,6WAAC;wFAAK,WAAU;;4FAAyB,QAAQ,IAAI;4FAAC;;;;;;;kGACtD,6WAAC;wFAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,QAAQ,IAAI,EAAE,KAAK,EAAE;kGAChG,eAAe,QAAQ,IAAI,EAAE,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sEAMhD,6WAAC;4DAAG,WAAU;sEACZ,cAAA,6WAAC;gEAAI,WAAU;gEAA0C,OAAO,QAAQ,MAAM;0EAC3E,QAAQ,MAAM;;;;;;;;;;;sEAGnB,6WAAC;4DAAG,WAAU;sEACZ,cAAA,6WAAC;gEAAI,WAAU;;oEAAyB,QAAQ,cAAc;oEAAC;;;;;;;;;;;;sEAEjE,6WAAC;4DAAG,WAAU;sEACZ,cAAA,6WAAC;gEAAI,WAAU;0EAAyB,WAAW,QAAQ,YAAY;;;;;;;;;;;sEAEzE,6WAAC;4DAAG,WAAU;sEACZ,cAAA,6WAAC,2RAAA,CAAA,UAAI;gEACH,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE;gEAC9B,WAAU;0EACX;;;;;;;;;;;;mDAlCI,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDvC", "debugId": null}}]}