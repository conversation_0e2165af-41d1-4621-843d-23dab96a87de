(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/decoder/Decoder.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2010 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$DecoderResult$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/DecoderResult.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGF$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/reedsolomon/GenericGF.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$ReedSolomonDecoder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/reedsolomon/ReedSolomonDecoder.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalStateException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/IllegalStateException.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/FormatException.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/StringUtils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/Integer.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
// import java.util.Arrays;
var Table;
(function(Table) {
    Table[Table["UPPER"] = 0] = "UPPER";
    Table[Table["LOWER"] = 1] = "LOWER";
    Table[Table["MIXED"] = 2] = "MIXED";
    Table[Table["DIGIT"] = 3] = "DIGIT";
    Table[Table["PUNCT"] = 4] = "PUNCT";
    Table[Table["BINARY"] = 5] = "BINARY";
})(Table || (Table = {}));
/**
 * <p>The main class which implements Aztec Code decoding -- as opposed to locating and extracting
 * the Aztec Code from an image.</p>
 *
 * <AUTHOR> Olivier
 */ var Decoder = function() {
    function Decoder() {}
    Decoder.prototype.decode = function(detectorResult) {
        this.ddata = detectorResult;
        var matrix = detectorResult.getBits();
        var rawbits = this.extractBits(matrix);
        var correctedBits = this.correctBits(rawbits);
        var rawBytes = Decoder.convertBoolArrayToByteArray(correctedBits);
        var result = Decoder.getEncodedData(correctedBits);
        var decoderResult = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$DecoderResult$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](rawBytes, result, null, null);
        decoderResult.setNumBits(correctedBits.length);
        return decoderResult;
    };
    // This method is used for testing the high-level encoder
    Decoder.highLevelDecode = function(correctedBits) {
        return this.getEncodedData(correctedBits);
    };
    /**
     * Gets the string encoded in the aztec code bits
     *
     * @return the decoded string
     */ Decoder.getEncodedData = function(correctedBits) {
        var endIndex = correctedBits.length;
        var latchTable = Table.UPPER; // table most recently latched to
        var shiftTable = Table.UPPER; // table to use for the next read
        var result = '';
        var index = 0;
        while(index < endIndex){
            if (shiftTable === Table.BINARY) {
                if (endIndex - index < 5) {
                    break;
                }
                var length_1 = Decoder.readCode(correctedBits, index, 5);
                index += 5;
                if (length_1 === 0) {
                    if (endIndex - index < 11) {
                        break;
                    }
                    length_1 = Decoder.readCode(correctedBits, index, 11) + 31;
                    index += 11;
                }
                for(var charCount = 0; charCount < length_1; charCount++){
                    if (endIndex - index < 8) {
                        index = endIndex; // Force outer loop to exit
                        break;
                    }
                    var code = Decoder.readCode(correctedBits, index, 8);
                    result += /*(char)*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].castAsNonUtf8Char(code);
                    index += 8;
                }
                // Go back to whatever mode we had been in
                shiftTable = latchTable;
            } else {
                var size = shiftTable === Table.DIGIT ? 4 : 5;
                if (endIndex - index < size) {
                    break;
                }
                var code = Decoder.readCode(correctedBits, index, size);
                index += size;
                var str = Decoder.getCharacter(shiftTable, code);
                if (str.startsWith('CTRL_')) {
                    // Table changes
                    // ISO/IEC 24778:2008 prescribes ending a shift sequence in the mode from which it was invoked.
                    // That's including when that mode is a shift.
                    // Our test case dlusbs.png for issue #642 exercises that.
                    latchTable = shiftTable; // Latch the current mode, so as to return to Upper after U/S B/S
                    shiftTable = Decoder.getTable(str.charAt(5));
                    if (str.charAt(6) === 'L') {
                        latchTable = shiftTable;
                    }
                } else {
                    result += str;
                    // Go back to whatever mode we had been in
                    shiftTable = latchTable;
                }
            }
        }
        return result;
    };
    /**
     * gets the table corresponding to the char passed
     */ Decoder.getTable = function(t) {
        switch(t){
            case 'L':
                return Table.LOWER;
            case 'P':
                return Table.PUNCT;
            case 'M':
                return Table.MIXED;
            case 'D':
                return Table.DIGIT;
            case 'B':
                return Table.BINARY;
            case 'U':
            default:
                return Table.UPPER;
        }
    };
    /**
     * Gets the character (or string) corresponding to the passed code in the given table
     *
     * @param table the table used
     * @param code the code of the character
     */ Decoder.getCharacter = function(table, code) {
        switch(table){
            case Table.UPPER:
                return Decoder.UPPER_TABLE[code];
            case Table.LOWER:
                return Decoder.LOWER_TABLE[code];
            case Table.MIXED:
                return Decoder.MIXED_TABLE[code];
            case Table.PUNCT:
                return Decoder.PUNCT_TABLE[code];
            case Table.DIGIT:
                return Decoder.DIGIT_TABLE[code];
            default:
                // Should not reach here.
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalStateException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Bad table');
        }
    };
    /**
     * <p>Performs RS error correction on an array of bits.</p>
     *
     * @return the corrected array
     * @throws FormatException if the input contains too many errors
     */ Decoder.prototype.correctBits = function(rawbits) {
        var gf;
        var codewordSize;
        if (this.ddata.getNbLayers() <= 2) {
            codewordSize = 6;
            gf = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGF$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].AZTEC_DATA_6;
        } else if (this.ddata.getNbLayers() <= 8) {
            codewordSize = 8;
            gf = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGF$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].AZTEC_DATA_8;
        } else if (this.ddata.getNbLayers() <= 22) {
            codewordSize = 10;
            gf = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGF$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].AZTEC_DATA_10;
        } else {
            codewordSize = 12;
            gf = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGF$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].AZTEC_DATA_12;
        }
        var numDataCodewords = this.ddata.getNbDatablocks();
        var numCodewords = rawbits.length / codewordSize;
        if (numCodewords < numDataCodewords) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        var offset = rawbits.length % codewordSize;
        var dataWords = new Int32Array(numCodewords);
        for(var i = 0; i < numCodewords; i++, offset += codewordSize){
            dataWords[i] = Decoder.readCode(rawbits, offset, codewordSize);
        }
        try {
            var rsDecoder = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$ReedSolomonDecoder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](gf);
            rsDecoder.decode(dataWords, numCodewords - numDataCodewords);
        } catch (ex) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](ex);
        }
        // Now perform the unstuffing operation.
        // First, count how many bits are going to be thrown out as stuffing
        var mask = (1 << codewordSize) - 1;
        var stuffedBits = 0;
        for(var i = 0; i < numDataCodewords; i++){
            var dataWord = dataWords[i];
            if (dataWord === 0 || dataWord === mask) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
            } else if (dataWord === 1 || dataWord === mask - 1) {
                stuffedBits++;
            }
        }
        // Now, actually unpack the bits and remove the stuffing
        var correctedBits = new Array(numDataCodewords * codewordSize - stuffedBits);
        var index = 0;
        for(var i = 0; i < numDataCodewords; i++){
            var dataWord = dataWords[i];
            if (dataWord === 1 || dataWord === mask - 1) {
                // next codewordSize-1 bits are all zeros or all ones
                correctedBits.fill(dataWord > 1, index, index + codewordSize - 1);
                // Arrays.fill(correctedBits, index, index + codewordSize - 1, dataWord > 1);
                index += codewordSize - 1;
            } else {
                for(var bit = codewordSize - 1; bit >= 0; --bit){
                    correctedBits[index++] = (dataWord & 1 << bit) !== 0;
                }
            }
        }
        return correctedBits;
    };
    /**
     * Gets the array of bits from an Aztec Code matrix
     *
     * @return the array of bits
     */ Decoder.prototype.extractBits = function(matrix) {
        var compact = this.ddata.isCompact();
        var layers = this.ddata.getNbLayers();
        var baseMatrixSize = (compact ? 11 : 14) + layers * 4; // not including alignment lines
        var alignmentMap = new Int32Array(baseMatrixSize);
        var rawbits = new Array(this.totalBitsInLayer(layers, compact));
        if (compact) {
            for(var i = 0; i < alignmentMap.length; i++){
                alignmentMap[i] = i;
            }
        } else {
            var matrixSize = baseMatrixSize + 1 + 2 * __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].truncDivision(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].truncDivision(baseMatrixSize, 2) - 1, 15);
            var origCenter = baseMatrixSize / 2;
            var center = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].truncDivision(matrixSize, 2);
            for(var i = 0; i < origCenter; i++){
                var newOffset = i + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].truncDivision(i, 15);
                alignmentMap[origCenter - i - 1] = center - newOffset - 1;
                alignmentMap[origCenter + i] = center + newOffset + 1;
            }
        }
        for(var i = 0, rowOffset = 0; i < layers; i++){
            var rowSize = (layers - i) * 4 + (compact ? 9 : 12);
            // The top-left most point of this layer is <low, low> (not including alignment lines)
            var low = i * 2;
            // The bottom-right most point of this layer is <high, high> (not including alignment lines)
            var high = baseMatrixSize - 1 - low;
            // We pull bits from the two 2 x rowSize columns and two rowSize x 2 rows
            for(var j = 0; j < rowSize; j++){
                var columnOffset = j * 2;
                for(var k = 0; k < 2; k++){
                    // left column
                    rawbits[rowOffset + columnOffset + k] = matrix.get(alignmentMap[low + k], alignmentMap[low + j]);
                    // bottom row
                    rawbits[rowOffset + 2 * rowSize + columnOffset + k] = matrix.get(alignmentMap[low + j], alignmentMap[high - k]);
                    // right column
                    rawbits[rowOffset + 4 * rowSize + columnOffset + k] = matrix.get(alignmentMap[high - k], alignmentMap[high - j]);
                    // top row
                    rawbits[rowOffset + 6 * rowSize + columnOffset + k] = matrix.get(alignmentMap[high - j], alignmentMap[low + k]);
                }
            }
            rowOffset += rowSize * 8;
        }
        return rawbits;
    };
    /**
     * Reads a code of given length and at given index in an array of bits
     */ Decoder.readCode = function(rawbits, startIndex, length) {
        var res = 0;
        for(var i = startIndex; i < startIndex + length; i++){
            res <<= 1;
            if (rawbits[i]) {
                res |= 0x01;
            }
        }
        return res;
    };
    /**
     * Reads a code of length 8 in an array of bits, padding with zeros
     */ Decoder.readByte = function(rawbits, startIndex) {
        var n = rawbits.length - startIndex;
        if (n >= 8) {
            return Decoder.readCode(rawbits, startIndex, 8);
        }
        return Decoder.readCode(rawbits, startIndex, n) << 8 - n;
    };
    /**
     * Packs a bit array into bytes, most significant bit first
     */ Decoder.convertBoolArrayToByteArray = function(boolArr) {
        var byteArr = new Uint8Array((boolArr.length + 7) / 8);
        for(var i = 0; i < byteArr.length; i++){
            byteArr[i] = Decoder.readByte(boolArr, 8 * i);
        }
        return byteArr;
    };
    Decoder.prototype.totalBitsInLayer = function(layers, compact) {
        return ((compact ? 88 : 112) + 16 * layers) * layers;
    };
    Decoder.UPPER_TABLE = [
        'CTRL_PS',
        ' ',
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z',
        'CTRL_LL',
        'CTRL_ML',
        'CTRL_DL',
        'CTRL_BS'
    ];
    Decoder.LOWER_TABLE = [
        'CTRL_PS',
        ' ',
        'a',
        'b',
        'c',
        'd',
        'e',
        'f',
        'g',
        'h',
        'i',
        'j',
        'k',
        'l',
        'm',
        'n',
        'o',
        'p',
        'q',
        'r',
        's',
        't',
        'u',
        'v',
        'w',
        'x',
        'y',
        'z',
        'CTRL_US',
        'CTRL_ML',
        'CTRL_DL',
        'CTRL_BS'
    ];
    Decoder.MIXED_TABLE = [
        'CTRL_PS',
        ' ',
        '\x01',
        '\x02',
        '\x03',
        '\x04',
        '\x05',
        '\x06',
        '\x07',
        '\b',
        '\t',
        '\n',
        '\x0b',
        '\f',
        '\r',
        '\x1b',
        '\x1c',
        '\x1d',
        '\x1e',
        '\x1f',
        '@',
        '\\',
        '^',
        '_',
        '`',
        '|',
        '~',
        '\x7f',
        'CTRL_LL',
        'CTRL_UL',
        'CTRL_PL',
        'CTRL_BS'
    ];
    Decoder.PUNCT_TABLE = [
        '',
        '\r',
        '\r\n',
        '. ',
        ', ',
        ': ',
        '!',
        '"',
        '#',
        '$',
        '%',
        '&',
        '\'',
        '(',
        ')',
        '*',
        '+',
        ',',
        '-',
        '.',
        '/',
        ':',
        ';',
        '<',
        '=',
        '>',
        '?',
        '[',
        ']',
        '{',
        '}',
        'CTRL_UL'
    ];
    Decoder.DIGIT_TABLE = [
        'CTRL_PS',
        ' ',
        '0',
        '1',
        '2',
        '3',
        '4',
        '5',
        '6',
        '7',
        '8',
        '9',
        ',',
        '.',
        'CTRL_UL',
        'CTRL_US'
    ];
    return Decoder;
}();
const __TURBOPACK__default__export__ = Decoder;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/AztecDetectorResult.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2010 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$DetectorResult$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/DetectorResult.js [app-client] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
/**
 * <p>Extends {@link DetectorResult} with more information specific to the Aztec format,
 * like the number of layers and whether it's compact.</p>
 *
 * <AUTHOR> Owen
 */ var AztecDetectorResult = function(_super) {
    __extends(AztecDetectorResult, _super);
    function AztecDetectorResult(bits, points, compact, nbDatablocks, nbLayers) {
        var _this = _super.call(this, bits, points) || this;
        _this.compact = compact;
        _this.nbDatablocks = nbDatablocks;
        _this.nbLayers = nbLayers;
        return _this;
    }
    AztecDetectorResult.prototype.getNbLayers = function() {
        return this.nbLayers;
    };
    AztecDetectorResult.prototype.getNbDatablocks = function() {
        return this.nbDatablocks;
    };
    AztecDetectorResult.prototype.isCompact = function() {
        return this.compact;
    };
    return AztecDetectorResult;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$DetectorResult$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = AztecDetectorResult;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/detector/Detector.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2010 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "Point": ()=>Point,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ResultPoint.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$AztecDetectorResult$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/AztecDetectorResult.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/detector/MathUtils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$WhiteRectangleDetector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/detector/WhiteRectangleDetector.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGF$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/reedsolomon/GenericGF.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$ReedSolomonDecoder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/reedsolomon/ReedSolomonDecoder.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NotFoundException.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$GridSamplerInstance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/GridSamplerInstance.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/Integer.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
var Point = function() {
    function Point(x, y) {
        this.x = x;
        this.y = y;
    }
    Point.prototype.toResultPoint = function() {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](this.getX(), this.getY());
    };
    Point.prototype.getX = function() {
        return this.x;
    };
    Point.prototype.getY = function() {
        return this.y;
    };
    return Point;
}();
;
/**
 * Encapsulates logic that can detect an Aztec Code in an image, even if the Aztec Code
 * is rotated or skewed, or partially obscured.
 *
 * <AUTHOR> Olivier
 * <AUTHOR> Yellin
 */ var Detector = function() {
    function Detector(image) {
        this.EXPECTED_CORNER_BITS = new Int32Array([
            0xee0,
            0x1dc,
            0x83b,
            0x707
        ]);
        this.image = image;
    }
    Detector.prototype.detect = function() {
        return this.detectMirror(false);
    };
    /**
     * Detects an Aztec Code in an image.
     *
     * @param isMirror if true, image is a mirror-image of original
     * @return {@link AztecDetectorResult} encapsulating results of detecting an Aztec Code
     * @throws NotFoundException if no Aztec Code can be found
     */ Detector.prototype.detectMirror = function(isMirror) {
        // 1. Get the center of the aztec matrix
        var pCenter = this.getMatrixCenter();
        // 2. Get the center points of the four diagonal points just outside the bull's eye
        //  [topRight, bottomRight, bottomLeft, topLeft]
        var bullsEyeCorners = this.getBullsEyeCorners(pCenter);
        if (isMirror) {
            var temp = bullsEyeCorners[0];
            bullsEyeCorners[0] = bullsEyeCorners[2];
            bullsEyeCorners[2] = temp;
        }
        // 3. Get the size of the matrix and other parameters from the bull's eye
        this.extractParameters(bullsEyeCorners);
        // 4. Sample the grid
        var bits = this.sampleGrid(this.image, bullsEyeCorners[this.shift % 4], bullsEyeCorners[(this.shift + 1) % 4], bullsEyeCorners[(this.shift + 2) % 4], bullsEyeCorners[(this.shift + 3) % 4]);
        // 5. Get the corners of the matrix.
        var corners = this.getMatrixCornerPoints(bullsEyeCorners);
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$AztecDetectorResult$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](bits, corners, this.compact, this.nbDataBlocks, this.nbLayers);
    };
    /**
     * Extracts the number of data layers and data blocks from the layer around the bull's eye.
     *
     * @param bullsEyeCorners the array of bull's eye corners
     * @throws NotFoundException in case of too many errors or invalid parameters
     */ Detector.prototype.extractParameters = function(bullsEyeCorners) {
        if (!this.isValidPoint(bullsEyeCorners[0]) || !this.isValidPoint(bullsEyeCorners[1]) || !this.isValidPoint(bullsEyeCorners[2]) || !this.isValidPoint(bullsEyeCorners[3])) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        var length = 2 * this.nbCenterLayers;
        // Get the bits around the bull's eye
        var sides = new Int32Array([
            this.sampleLine(bullsEyeCorners[0], bullsEyeCorners[1], length),
            this.sampleLine(bullsEyeCorners[1], bullsEyeCorners[2], length),
            this.sampleLine(bullsEyeCorners[2], bullsEyeCorners[3], length),
            this.sampleLine(bullsEyeCorners[3], bullsEyeCorners[0], length) // Top
        ]);
        // bullsEyeCorners[shift] is the corner of the bulls'eye that has three
        // orientation marks.
        // sides[shift] is the row/column that goes from the corner with three
        // orientation marks to the corner with two.
        this.shift = this.getRotation(sides, length);
        // Flatten the parameter bits into a single 28- or 40-bit long
        var parameterData = 0;
        for(var i = 0; i < 4; i++){
            var side = sides[(this.shift + i) % 4];
            if (this.compact) {
                // Each side of the form ..XXXXXXX. where Xs are parameter data
                parameterData <<= 7;
                parameterData += side >> 1 & 0x7F;
            } else {
                // Each side of the form ..XXXXX.XXXXX. where Xs are parameter data
                parameterData <<= 10;
                parameterData += (side >> 2 & 0x1f << 5) + (side >> 1 & 0x1F);
            }
        }
        // Corrects parameter data using RS.  Returns just the data portion
        // without the error correction.
        var correctedData = this.getCorrectedParameterData(parameterData, this.compact);
        if (this.compact) {
            // 8 bits:  2 bits layers and 6 bits data blocks
            this.nbLayers = (correctedData >> 6) + 1;
            this.nbDataBlocks = (correctedData & 0x3F) + 1;
        } else {
            // 16 bits:  5 bits layers and 11 bits data blocks
            this.nbLayers = (correctedData >> 11) + 1;
            this.nbDataBlocks = (correctedData & 0x7FF) + 1;
        }
    };
    Detector.prototype.getRotation = function(sides, length) {
        // In a normal pattern, we expect to See
        //   **    .*             D       A
        //   *      *
        //
        //   .      *
        //   ..    ..             C       B
        //
        // Grab the 3 bits from each of the sides the form the locator pattern and concatenate
        // into a 12-bit integer.  Start with the bit at A
        var cornerBits = 0;
        sides.forEach(function(side, idx, arr) {
            // XX......X where X's are orientation marks
            var t = (side >> length - 2 << 1) + (side & 1);
            cornerBits = (cornerBits << 3) + t;
        });
        // for (var side in sides) {
        //     // XX......X where X's are orientation marks
        //     var t = ((side >> (length - 2)) << 1) + (side & 1);
        //     cornerBits = (cornerBits << 3) + t;
        // }
        // Mov the bottom bit to the top, so that the three bits of the locator pattern at A are
        // together.  cornerBits is now:
        //  3 orientation bits at A || 3 orientation bits at B || ... || 3 orientation bits at D
        cornerBits = ((cornerBits & 1) << 11) + (cornerBits >> 1);
        // The result shift indicates which element of BullsEyeCorners[] goes into the top-left
        // corner. Since the four rotation values have a Hamming distance of 8, we
        // can easily tolerate two errors.
        for(var shift = 0; shift < 4; shift++){
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].bitCount(cornerBits ^ this.EXPECTED_CORNER_BITS[shift]) <= 2) {
                return shift;
            }
        }
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
    };
    /**
     * Corrects the parameter bits using Reed-Solomon algorithm.
     *
     * @param parameterData parameter bits
     * @param compact true if this is a compact Aztec code
     * @throws NotFoundException if the array contains too many errors
     */ Detector.prototype.getCorrectedParameterData = function(parameterData, compact) {
        var numCodewords;
        var numDataCodewords;
        if (compact) {
            numCodewords = 7;
            numDataCodewords = 2;
        } else {
            numCodewords = 10;
            numDataCodewords = 4;
        }
        var numECCodewords = numCodewords - numDataCodewords;
        var parameterWords = new Int32Array(numCodewords);
        for(var i = numCodewords - 1; i >= 0; --i){
            parameterWords[i] = parameterData & 0xF;
            parameterData >>= 4;
        }
        try {
            var rsDecoder = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$ReedSolomonDecoder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGF$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].AZTEC_PARAM);
            rsDecoder.decode(parameterWords, numECCodewords);
        } catch (ignored) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        // Toss the error correction.  Just return the data as an integer
        var result = 0;
        for(var i = 0; i < numDataCodewords; i++){
            result = (result << 4) + parameterWords[i];
        }
        return result;
    };
    /**
     * Finds the corners of a bull-eye centered on the passed point.
     * This returns the centers of the diagonal points just outside the bull's eye
     * Returns [topRight, bottomRight, bottomLeft, topLeft]
     *
     * @param pCenter Center point
     * @return The corners of the bull-eye
     * @throws NotFoundException If no valid bull-eye can be found
     */ Detector.prototype.getBullsEyeCorners = function(pCenter) {
        var pina = pCenter;
        var pinb = pCenter;
        var pinc = pCenter;
        var pind = pCenter;
        var color = true;
        for(this.nbCenterLayers = 1; this.nbCenterLayers < 9; this.nbCenterLayers++){
            var pouta = this.getFirstDifferent(pina, color, 1, -1);
            var poutb = this.getFirstDifferent(pinb, color, 1, 1);
            var poutc = this.getFirstDifferent(pinc, color, -1, 1);
            var poutd = this.getFirstDifferent(pind, color, -1, -1);
            // d      a
            //
            // c      b
            if (this.nbCenterLayers > 2) {
                var q = this.distancePoint(poutd, pouta) * this.nbCenterLayers / (this.distancePoint(pind, pina) * (this.nbCenterLayers + 2));
                if (q < 0.75 || q > 1.25 || !this.isWhiteOrBlackRectangle(pouta, poutb, poutc, poutd)) {
                    break;
                }
            }
            pina = pouta;
            pinb = poutb;
            pinc = poutc;
            pind = poutd;
            color = !color;
        }
        if (this.nbCenterLayers !== 5 && this.nbCenterLayers !== 7) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        this.compact = this.nbCenterLayers === 5;
        // Expand the square by .5 pixel in each direction so that we're on the border
        // between the white square and the black square
        var pinax = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](pina.getX() + 0.5, pina.getY() - 0.5);
        var pinbx = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](pinb.getX() + 0.5, pinb.getY() + 0.5);
        var pincx = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](pinc.getX() - 0.5, pinc.getY() + 0.5);
        var pindx = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](pind.getX() - 0.5, pind.getY() - 0.5);
        // Expand the square so that its corners are the centers of the points
        // just outside the bull's eye.
        return this.expandSquare([
            pinax,
            pinbx,
            pincx,
            pindx
        ], 2 * this.nbCenterLayers - 3, 2 * this.nbCenterLayers);
    };
    /**
     * Finds a candidate center point of an Aztec code from an image
     *
     * @return the center point
     */ Detector.prototype.getMatrixCenter = function() {
        var pointA;
        var pointB;
        var pointC;
        var pointD;
        // Get a white rectangle that can be the border of the matrix in center bull's eye or
        try {
            var cornerPoints = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$WhiteRectangleDetector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](this.image).detect();
            pointA = cornerPoints[0];
            pointB = cornerPoints[1];
            pointC = cornerPoints[2];
            pointD = cornerPoints[3];
        } catch (e) {
            // This exception can be in case the initial rectangle is white
            // In that case, surely in the bull's eye, we try to expand the rectangle.
            var cx_1 = this.image.getWidth() / 2;
            var cy_1 = this.image.getHeight() / 2;
            pointA = this.getFirstDifferent(new Point(cx_1 + 7, cy_1 - 7), false, 1, -1).toResultPoint();
            pointB = this.getFirstDifferent(new Point(cx_1 + 7, cy_1 + 7), false, 1, 1).toResultPoint();
            pointC = this.getFirstDifferent(new Point(cx_1 - 7, cy_1 + 7), false, -1, 1).toResultPoint();
            pointD = this.getFirstDifferent(new Point(cx_1 - 7, cy_1 - 7), false, -1, -1).toResultPoint();
        }
        // Compute the center of the rectangle
        var cx = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].round((pointA.getX() + pointD.getX() + pointB.getX() + pointC.getX()) / 4.0);
        var cy = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].round((pointA.getY() + pointD.getY() + pointB.getY() + pointC.getY()) / 4.0);
        // Redetermine the white rectangle starting from previously computed center.
        // This will ensure that we end up with a white rectangle in center bull's eye
        // in order to compute a more accurate center.
        try {
            var cornerPoints = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$WhiteRectangleDetector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](this.image, 15, cx, cy).detect();
            pointA = cornerPoints[0];
            pointB = cornerPoints[1];
            pointC = cornerPoints[2];
            pointD = cornerPoints[3];
        } catch (e) {
            // This exception can be in case the initial rectangle is white
            // In that case we try to expand the rectangle.
            pointA = this.getFirstDifferent(new Point(cx + 7, cy - 7), false, 1, -1).toResultPoint();
            pointB = this.getFirstDifferent(new Point(cx + 7, cy + 7), false, 1, 1).toResultPoint();
            pointC = this.getFirstDifferent(new Point(cx - 7, cy + 7), false, -1, 1).toResultPoint();
            pointD = this.getFirstDifferent(new Point(cx - 7, cy - 7), false, -1, -1).toResultPoint();
        }
        // Recompute the center of the rectangle
        cx = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].round((pointA.getX() + pointD.getX() + pointB.getX() + pointC.getX()) / 4.0);
        cy = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].round((pointA.getY() + pointD.getY() + pointB.getY() + pointC.getY()) / 4.0);
        return new Point(cx, cy);
    };
    /**
     * Gets the Aztec code corners from the bull's eye corners and the parameters.
     *
     * @param bullsEyeCorners the array of bull's eye corners
     * @return the array of aztec code corners
     */ Detector.prototype.getMatrixCornerPoints = function(bullsEyeCorners) {
        return this.expandSquare(bullsEyeCorners, 2 * this.nbCenterLayers, this.getDimension());
    };
    /**
     * Creates a BitMatrix by sampling the provided image.
     * topLeft, topRight, bottomRight, and bottomLeft are the centers of the squares on the
     * diagonal just outside the bull's eye.
     */ Detector.prototype.sampleGrid = function(image, topLeft, topRight, bottomRight, bottomLeft) {
        var sampler = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$GridSamplerInstance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getInstance();
        var dimension = this.getDimension();
        var low = dimension / 2 - this.nbCenterLayers;
        var high = dimension / 2 + this.nbCenterLayers;
        return sampler.sampleGrid(image, dimension, dimension, low, low, high, low, high, high, low, high, topLeft.getX(), topLeft.getY(), topRight.getX(), topRight.getY(), bottomRight.getX(), bottomRight.getY(), bottomLeft.getX(), bottomLeft.getY());
    };
    /**
     * Samples a line.
     *
     * @param p1   start point (inclusive)
     * @param p2   end point (exclusive)
     * @param size number of bits
     * @return the array of bits as an int (first bit is high-order bit of result)
     */ Detector.prototype.sampleLine = function(p1, p2, size) {
        var result = 0;
        var d = this.distanceResultPoint(p1, p2);
        var moduleSize = d / size;
        var px = p1.getX();
        var py = p1.getY();
        var dx = moduleSize * (p2.getX() - p1.getX()) / d;
        var dy = moduleSize * (p2.getY() - p1.getY()) / d;
        for(var i = 0; i < size; i++){
            if (this.image.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].round(px + i * dx), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].round(py + i * dy))) {
                result |= 1 << size - i - 1;
            }
        }
        return result;
    };
    /**
     * @return true if the border of the rectangle passed in parameter is compound of white points only
     *         or black points only
     */ Detector.prototype.isWhiteOrBlackRectangle = function(p1, p2, p3, p4) {
        var corr = 3;
        p1 = new Point(p1.getX() - corr, p1.getY() + corr);
        p2 = new Point(p2.getX() - corr, p2.getY() - corr);
        p3 = new Point(p3.getX() + corr, p3.getY() - corr);
        p4 = new Point(p4.getX() + corr, p4.getY() + corr);
        var cInit = this.getColor(p4, p1);
        if (cInit === 0) {
            return false;
        }
        var c = this.getColor(p1, p2);
        if (c !== cInit) {
            return false;
        }
        c = this.getColor(p2, p3);
        if (c !== cInit) {
            return false;
        }
        c = this.getColor(p3, p4);
        return c === cInit;
    };
    /**
     * Gets the color of a segment
     *
     * @return 1 if segment more than 90% black, -1 if segment is more than 90% white, 0 else
     */ Detector.prototype.getColor = function(p1, p2) {
        var d = this.distancePoint(p1, p2);
        var dx = (p2.getX() - p1.getX()) / d;
        var dy = (p2.getY() - p1.getY()) / d;
        var error = 0;
        var px = p1.getX();
        var py = p1.getY();
        var colorModel = this.image.get(p1.getX(), p1.getY());
        var iMax = Math.ceil(d);
        for(var i = 0; i < iMax; i++){
            px += dx;
            py += dy;
            if (this.image.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].round(px), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].round(py)) !== colorModel) {
                error++;
            }
        }
        var errRatio = error / d;
        if (errRatio > 0.1 && errRatio < 0.9) {
            return 0;
        }
        return errRatio <= 0.1 === colorModel ? 1 : -1;
    };
    /**
     * Gets the coordinate of the first point with a different color in the given direction
     */ Detector.prototype.getFirstDifferent = function(init, color, dx, dy) {
        var x = init.getX() + dx;
        var y = init.getY() + dy;
        while(this.isValid(x, y) && this.image.get(x, y) === color){
            x += dx;
            y += dy;
        }
        x -= dx;
        y -= dy;
        while(this.isValid(x, y) && this.image.get(x, y) === color){
            x += dx;
        }
        x -= dx;
        while(this.isValid(x, y) && this.image.get(x, y) === color){
            y += dy;
        }
        y -= dy;
        return new Point(x, y);
    };
    /**
     * Expand the square represented by the corner points by pushing out equally in all directions
     *
     * @param cornerPoints the corners of the square, which has the bull's eye at its center
     * @param oldSide the original length of the side of the square in the target bit matrix
     * @param newSide the new length of the size of the square in the target bit matrix
     * @return the corners of the expanded square
     */ Detector.prototype.expandSquare = function(cornerPoints, oldSide, newSide) {
        var ratio = newSide / (2.0 * oldSide);
        var dx = cornerPoints[0].getX() - cornerPoints[2].getX();
        var dy = cornerPoints[0].getY() - cornerPoints[2].getY();
        var centerx = (cornerPoints[0].getX() + cornerPoints[2].getX()) / 2.0;
        var centery = (cornerPoints[0].getY() + cornerPoints[2].getY()) / 2.0;
        var result0 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](centerx + ratio * dx, centery + ratio * dy);
        var result2 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](centerx - ratio * dx, centery - ratio * dy);
        dx = cornerPoints[1].getX() - cornerPoints[3].getX();
        dy = cornerPoints[1].getY() - cornerPoints[3].getY();
        centerx = (cornerPoints[1].getX() + cornerPoints[3].getX()) / 2.0;
        centery = (cornerPoints[1].getY() + cornerPoints[3].getY()) / 2.0;
        var result1 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](centerx + ratio * dx, centery + ratio * dy);
        var result3 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](centerx - ratio * dx, centery - ratio * dy);
        var results = [
            result0,
            result1,
            result2,
            result3
        ];
        return results;
    };
    Detector.prototype.isValid = function(x, y) {
        return x >= 0 && x < this.image.getWidth() && y > 0 && y < this.image.getHeight();
    };
    Detector.prototype.isValidPoint = function(point) {
        var x = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].round(point.getX());
        var y = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].round(point.getY());
        return this.isValid(x, y);
    };
    Detector.prototype.distancePoint = function(a, b) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].distance(a.getX(), a.getY(), b.getX(), b.getY());
    };
    Detector.prototype.distanceResultPoint = function(a, b) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].distance(a.getX(), a.getY(), b.getX(), b.getY());
    };
    Detector.prototype.getDimension = function() {
        if (this.compact) {
            return 4 * this.nbLayers + 11;
        }
        if (this.nbLayers <= 4) {
            return 4 * this.nbLayers + 15;
        }
        return 4 * this.nbLayers + 2 * (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].truncDivision(this.nbLayers - 4, 8) + 1) + 15;
    };
    return Detector;
}();
const __TURBOPACK__default__export__ = Detector;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/AztecReader.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2010 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Result.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/BarcodeFormat.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/DecodeHintType.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultMetadataType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ResultMetadataType.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/System.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$decoder$2f$Decoder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/decoder/Decoder.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$detector$2f$Detector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/detector/Detector.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
// import java.util.List;
// import java.util.Map;
/**
 * This implementation can detect and decode Aztec codes in an image.
 *
 * <AUTHOR> Olivier
 */ var AztecReader = function() {
    function AztecReader() {}
    /**
     * Locates and decodes a Data Matrix code in an image.
     *
     * @return a String representing the content encoded by the Data Matrix code
     * @throws NotFoundException if a Data Matrix code cannot be found
     * @throws FormatException if a Data Matrix code cannot be decoded
     */ AztecReader.prototype.decode = function(image, hints) {
        if (hints === void 0) {
            hints = null;
        }
        var exception = null;
        var detector = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$detector$2f$Detector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](image.getBlackMatrix());
        var points = null;
        var decoderResult = null;
        try {
            var detectorResult = detector.detectMirror(false);
            points = detectorResult.getPoints();
            this.reportFoundResultPoints(hints, points);
            decoderResult = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$decoder$2f$Decoder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]().decode(detectorResult);
        } catch (e) {
            exception = e;
        }
        if (decoderResult == null) {
            try {
                var detectorResult = detector.detectMirror(true);
                points = detectorResult.getPoints();
                this.reportFoundResultPoints(hints, points);
                decoderResult = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$decoder$2f$Decoder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]().decode(detectorResult);
            } catch (e) {
                if (exception != null) {
                    throw exception;
                }
                throw e;
            }
        }
        var result = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Result$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](decoderResult.getText(), decoderResult.getRawBytes(), decoderResult.getNumBits(), points, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].AZTEC, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].currentTimeMillis());
        var byteSegments = decoderResult.getByteSegments();
        if (byteSegments != null) {
            result.putMetadata(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultMetadataType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].BYTE_SEGMENTS, byteSegments);
        }
        var ecLevel = decoderResult.getECLevel();
        if (ecLevel != null) {
            result.putMetadata(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultMetadataType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].ERROR_CORRECTION_LEVEL, ecLevel);
        }
        return result;
    };
    AztecReader.prototype.reportFoundResultPoints = function(hints, points) {
        if (hints != null) {
            var rpcb_1 = hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].NEED_RESULT_POINT_CALLBACK);
            if (rpcb_1 != null) {
                points.forEach(function(point, idx, arr) {
                    rpcb_1.foundPossibleResultPoint(point);
                });
            }
        }
    };
    // @Override
    AztecReader.prototype.reset = function() {
    // do nothing
    };
    return AztecReader;
}();
const __TURBOPACK__default__export__ = AztecReader;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/encoder/AztecCode.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
* Copyright 2013 ZXing authors
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*      http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/ /**
 * Aztec 2D code representation
 *
 * <AUTHOR> Abdullaev
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var AztecCode = function() {
    function AztecCode() {}
    /**
     * @return {@code true} if compact instead of full mode
     */ AztecCode.prototype.isCompact = function() {
        return this.compact;
    };
    AztecCode.prototype.setCompact = function(compact) {
        this.compact = compact;
    };
    /**
     * @return size in pixels (width and height)
     */ AztecCode.prototype.getSize = function() {
        return this.size;
    };
    AztecCode.prototype.setSize = function(size) {
        this.size = size;
    };
    /**
     * @return number of levels
     */ AztecCode.prototype.getLayers = function() {
        return this.layers;
    };
    AztecCode.prototype.setLayers = function(layers) {
        this.layers = layers;
    };
    /**
     * @return number of data codewords
     */ AztecCode.prototype.getCodeWords = function() {
        return this.codeWords;
    };
    AztecCode.prototype.setCodeWords = function(codeWords) {
        this.codeWords = codeWords;
    };
    /**
     * @return the symbol image
     */ AztecCode.prototype.getMatrix = function() {
        return this.matrix;
    };
    AztecCode.prototype.setMatrix = function(matrix) {
        this.matrix = matrix;
    };
    return AztecCode;
}();
const __TURBOPACK__default__export__ = AztecCode;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/encoder/Token.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
* Copyright 2013 ZXing authors
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*      http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var Token = function() {
    function Token(previous) {
        this.previous = previous;
    }
    Token.prototype.getPrevious = function() {
        return this.previous;
    };
    return Token;
}();
const __TURBOPACK__default__export__ = Token;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/encoder/SimpleToken.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
* Copyright 2013 ZXing authors
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*      http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$Token$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/encoder/Token.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/Integer.js [app-client] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
var SimpleToken = function(_super) {
    __extends(SimpleToken, _super);
    function SimpleToken(previous, value, bitCount) {
        var _this = _super.call(this, previous) || this;
        _this.value = value;
        _this.bitCount = bitCount;
        return _this;
    }
    /**
     * @Override
     */ SimpleToken.prototype.appendTo = function(bitArray, text) {
        bitArray.appendBits(this.value, this.bitCount);
    };
    SimpleToken.prototype.add = function(value, bitCount) {
        return new SimpleToken(this, value, bitCount);
    };
    SimpleToken.prototype.addBinaryShift = function(start, byteCount) {
        // no-op can't binary shift a simple token
        console.warn('addBinaryShift on SimpleToken, this simply returns a copy of this token');
        return new SimpleToken(this, start, byteCount);
    };
    /**
     * @Override
     */ SimpleToken.prototype.toString = function() {
        var value = this.value & (1 << this.bitCount) - 1;
        value |= 1 << this.bitCount;
        return '<' + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].toBinaryString(value | 1 << this.bitCount).substring(1) + '>';
    };
    return SimpleToken;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$Token$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = SimpleToken;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/encoder/BinaryShiftToken.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
* Copyright 2013 ZXing authors
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*      http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$SimpleToken$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/encoder/SimpleToken.js [app-client] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
var BinaryShiftToken = function(_super) {
    __extends(BinaryShiftToken, _super);
    function BinaryShiftToken(previous, binaryShiftStart, binaryShiftByteCount) {
        var _this = _super.call(this, previous, 0, 0) || this;
        _this.binaryShiftStart = binaryShiftStart;
        _this.binaryShiftByteCount = binaryShiftByteCount;
        return _this;
    }
    /**
     * @Override
     */ BinaryShiftToken.prototype.appendTo = function(bitArray, text) {
        for(var i = 0; i < this.binaryShiftByteCount; i++){
            if (i === 0 || i === 31 && this.binaryShiftByteCount <= 62) {
                // We need a header before the first character, and before
                // character 31 when the total byte code is <= 62
                bitArray.appendBits(31, 5); // BINARY_SHIFT
                if (this.binaryShiftByteCount > 62) {
                    bitArray.appendBits(this.binaryShiftByteCount - 31, 16);
                } else if (i === 0) {
                    // 1 <= binaryShiftByteCode <= 62
                    bitArray.appendBits(Math.min(this.binaryShiftByteCount, 31), 5);
                } else {
                    // 32 <= binaryShiftCount <= 62 and i == 31
                    bitArray.appendBits(this.binaryShiftByteCount - 31, 5);
                }
            }
            bitArray.appendBits(text[this.binaryShiftStart + i], 8);
        }
    };
    BinaryShiftToken.prototype.addBinaryShift = function(start, byteCount) {
        // int bitCount = (byteCount * 8) + (byteCount <= 31 ? 10 : byteCount <= 62 ? 20 : 21);
        return new BinaryShiftToken(this, start, byteCount);
    };
    /**
     * @Override
     */ BinaryShiftToken.prototype.toString = function() {
        return '<' + this.binaryShiftStart + '::' + (this.binaryShiftStart + this.binaryShiftByteCount - 1) + '>';
    };
    return BinaryShiftToken;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$SimpleToken$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = BinaryShiftToken;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/encoder/TokenHelpers.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "add": ()=>add,
    "addBinaryShift": ()=>addBinaryShift
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$SimpleToken$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/encoder/SimpleToken.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$BinaryShiftToken$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/encoder/BinaryShiftToken.js [app-client] (ecmascript)");
;
;
function addBinaryShift(token, start, byteCount) {
    // int bitCount = (byteCount * 8) + (byteCount <= 31 ? 10 : byteCount <= 62 ? 20 : 21);
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$BinaryShiftToken$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](token, start, byteCount);
}
function add(token, value, bitCount) {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$SimpleToken$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](token, value, bitCount);
}
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/encoder/EncoderConstants.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "EMPTY_TOKEN": ()=>EMPTY_TOKEN,
    "MODE_DIGIT": ()=>MODE_DIGIT,
    "MODE_LOWER": ()=>MODE_LOWER,
    "MODE_MIXED": ()=>MODE_MIXED,
    "MODE_NAMES": ()=>MODE_NAMES,
    "MODE_PUNCT": ()=>MODE_PUNCT,
    "MODE_UPPER": ()=>MODE_UPPER
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$SimpleToken$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/encoder/SimpleToken.js [app-client] (ecmascript)");
;
var /*final*/ MODE_NAMES = [
    'UPPER',
    'LOWER',
    'DIGIT',
    'MIXED',
    'PUNCT'
];
var /*final*/ MODE_UPPER = 0; // 5 bits
var /*final*/ MODE_LOWER = 1; // 5 bits
var /*final*/ MODE_DIGIT = 2; // 4 bits
var /*final*/ MODE_MIXED = 3; // 5 bits
var /*final*/ MODE_PUNCT = 4; // 5 bits
var EMPTY_TOKEN = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$SimpleToken$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](null, 0, 0);
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/encoder/LatchTable.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// The Latch Table shows, for each pair of Modes, the optimal method for
// getting from one mode to another.  In the worst possible case, this can
// be up to 14 bits.  In the best possible case, we are already there!
// The high half-word of each entry gives the number of bits.
// The low half-word of each entry are the actual bits necessary to change
__turbopack_context__.s({
    "LATCH_TABLE": ()=>LATCH_TABLE
});
var LATCH_TABLE = [
    Int32Array.from([
        0,
        (5 << 16) + 28,
        (5 << 16) + 30,
        (5 << 16) + 29,
        (10 << 16) + (29 << 5) + 30 // UPPER -> MIXED -> PUNCT
    ]),
    Int32Array.from([
        (9 << 16) + (30 << 4) + 14,
        0,
        (5 << 16) + 30,
        (5 << 16) + 29,
        (10 << 16) + (29 << 5) + 30 // LOWER -> MIXED -> PUNCT
    ]),
    Int32Array.from([
        (4 << 16) + 14,
        (9 << 16) + (14 << 5) + 28,
        0,
        (9 << 16) + (14 << 5) + 29,
        (14 << 16) + (14 << 10) + (29 << 5) + 30
    ]),
    Int32Array.from([
        (5 << 16) + 29,
        (5 << 16) + 28,
        (10 << 16) + (29 << 5) + 30,
        0,
        (5 << 16) + 30 // MIXED -> PUNCT
    ]),
    Int32Array.from([
        (5 << 16) + 31,
        (10 << 16) + (31 << 5) + 28,
        (10 << 16) + (31 << 5) + 30,
        (10 << 16) + (31 << 5) + 29,
        0
    ])
];
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/encoder/ShiftTable.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "SHIFT_TABLE": ()=>SHIFT_TABLE,
    "static_SHIFT_TABLE": ()=>static_SHIFT_TABLE
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Arrays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/Arrays.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/encoder/EncoderConstants.js [app-client] (ecmascript)");
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
function static_SHIFT_TABLE(SHIFT_TABLE) {
    var e_1, _a;
    try {
        for(var SHIFT_TABLE_1 = __values(SHIFT_TABLE), SHIFT_TABLE_1_1 = SHIFT_TABLE_1.next(); !SHIFT_TABLE_1_1.done; SHIFT_TABLE_1_1 = SHIFT_TABLE_1.next()){
            var table = SHIFT_TABLE_1_1.value /*Int32Array*/ ;
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Arrays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].fill(table, -1);
        }
    } catch (e_1_1) {
        e_1 = {
            error: e_1_1
        };
    } finally{
        try {
            if (SHIFT_TABLE_1_1 && !SHIFT_TABLE_1_1.done && (_a = SHIFT_TABLE_1.return)) _a.call(SHIFT_TABLE_1);
        } finally{
            if (e_1) throw e_1.error;
        }
    }
    SHIFT_TABLE[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_UPPER"]][__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_PUNCT"]] = 0;
    SHIFT_TABLE[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_LOWER"]][__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_PUNCT"]] = 0;
    SHIFT_TABLE[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_LOWER"]][__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_UPPER"]] = 28;
    SHIFT_TABLE[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_MIXED"]][__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_PUNCT"]] = 0;
    SHIFT_TABLE[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_DIGIT"]][__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_PUNCT"]] = 0;
    SHIFT_TABLE[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_DIGIT"]][__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_UPPER"]] = 15;
    return SHIFT_TABLE;
}
var /*final*/ SHIFT_TABLE = static_SHIFT_TABLE(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Arrays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createInt32Array(6, 6)); // mode shift codes, per table
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/encoder/State.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2013 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
// package com.google.zxing.aztec.encoder;
// import java.util.Deque;
// import java.util.LinkedList;
// import com.google.zxing.common.BitArray;
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/BitArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$TokenHelpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/encoder/TokenHelpers.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/encoder/EncoderConstants.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$LatchTable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/encoder/LatchTable.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$ShiftTable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/encoder/ShiftTable.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/StringUtils.js [app-client] (ecmascript)");
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
;
;
;
;
/**
 * State represents all information about a sequence necessary to generate the current output.
 * Note that a state is immutable.
 */ var State = function() {
    function State(token, mode, binaryBytes, bitCount) {
        this.token = token;
        this.mode = mode;
        this.binaryShiftByteCount = binaryBytes;
        this.bitCount = bitCount;
    // Make sure we match the token
    // int binaryShiftBitCount = (binaryShiftByteCount * 8) +
    //    (binaryShiftByteCount === 0 ? 0 :
    //     binaryShiftByteCount <= 31 ? 10 :
    //     binaryShiftByteCount <= 62 ? 20 : 21);
    // assert this.bitCount === token.getTotalBitCount() + binaryShiftBitCount;
    }
    State.prototype.getMode = function() {
        return this.mode;
    };
    State.prototype.getToken = function() {
        return this.token;
    };
    State.prototype.getBinaryShiftByteCount = function() {
        return this.binaryShiftByteCount;
    };
    State.prototype.getBitCount = function() {
        return this.bitCount;
    };
    // Create a new state representing this state with a latch to a (not
    // necessary different) mode, and then a code.
    State.prototype.latchAndAppend = function(mode, value) {
        // assert binaryShiftByteCount === 0;
        var bitCount = this.bitCount;
        var token = this.token;
        if (mode !== this.mode) {
            var latch = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$LatchTable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LATCH_TABLE"][this.mode][mode];
            token = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$TokenHelpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["add"](token, latch & 0xffff, latch >> 16);
            bitCount += latch >> 16;
        }
        var latchModeBitCount = mode === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_DIGIT"] ? 4 : 5;
        token = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$TokenHelpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["add"](token, value, latchModeBitCount);
        return new State(token, mode, 0, bitCount + latchModeBitCount);
    };
    // Create a new state representing this state, with a temporary shift
    // to a different mode to output a single value.
    State.prototype.shiftAndAppend = function(mode, value) {
        // assert binaryShiftByteCount === 0 && this.mode !== mode;
        var token = this.token;
        var thisModeBitCount = this.mode === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_DIGIT"] ? 4 : 5;
        // Shifts exist only to UPPER and PUNCT, both with tokens size 5.
        token = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$TokenHelpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["add"](token, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$ShiftTable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SHIFT_TABLE"][this.mode][mode], thisModeBitCount);
        token = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$TokenHelpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["add"](token, value, 5);
        return new State(token, this.mode, 0, this.bitCount + thisModeBitCount + 5);
    };
    // Create a new state representing this state, but an additional character
    // output in Binary Shift mode.
    State.prototype.addBinaryShiftChar = function(index) {
        var token = this.token;
        var mode = this.mode;
        var bitCount = this.bitCount;
        if (this.mode === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_PUNCT"] || this.mode === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_DIGIT"]) {
            // assert binaryShiftByteCount === 0;
            var latch = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$LatchTable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LATCH_TABLE"][mode][__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_UPPER"]];
            token = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$TokenHelpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["add"](token, latch & 0xffff, latch >> 16);
            bitCount += latch >> 16;
            mode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_UPPER"];
        }
        var deltaBitCount = this.binaryShiftByteCount === 0 || this.binaryShiftByteCount === 31 ? 18 : this.binaryShiftByteCount === 62 ? 9 : 8;
        var result = new State(token, mode, this.binaryShiftByteCount + 1, bitCount + deltaBitCount);
        if (result.binaryShiftByteCount === 2047 + 31) {
            // The string is as long as it's allowed to be.  We should end it.
            result = result.endBinaryShift(index + 1);
        }
        return result;
    };
    // Create the state identical to this one, but we are no longer in
    // Binary Shift mode.
    State.prototype.endBinaryShift = function(index) {
        if (this.binaryShiftByteCount === 0) {
            return this;
        }
        var token = this.token;
        token = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$TokenHelpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addBinaryShift"](token, index - this.binaryShiftByteCount, this.binaryShiftByteCount);
        // assert token.getTotalBitCount() === this.bitCount;
        return new State(token, this.mode, 0, this.bitCount);
    };
    // Returns true if "this" state is better (equal: or) to be in than "that"
    // state under all possible circumstances.
    State.prototype.isBetterThanOrEqualTo = function(other) {
        var newModeBitCount = this.bitCount + (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$LatchTable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LATCH_TABLE"][this.mode][other.mode] >> 16);
        if (this.binaryShiftByteCount < other.binaryShiftByteCount) {
            // add additional B/S encoding cost of other, if any
            newModeBitCount += State.calculateBinaryShiftCost(other) - State.calculateBinaryShiftCost(this);
        } else if (this.binaryShiftByteCount > other.binaryShiftByteCount && other.binaryShiftByteCount > 0) {
            // maximum possible additional cost (it: h)
            newModeBitCount += 10;
        }
        return newModeBitCount <= other.bitCount;
    };
    State.prototype.toBitArray = function(text) {
        var e_1, _a;
        // Reverse the tokens, so that they are in the order that they should
        // be output
        var symbols = [];
        for(var token = this.endBinaryShift(text.length).token; token !== null; token = token.getPrevious()){
            symbols.unshift(token);
        }
        var bitArray = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        try {
            // Add each token to the result.
            for(var symbols_1 = __values(symbols), symbols_1_1 = symbols_1.next(); !symbols_1_1.done; symbols_1_1 = symbols_1.next()){
                var symbol = symbols_1_1.value;
                symbol.appendTo(bitArray, text);
            }
        } catch (e_1_1) {
            e_1 = {
                error: e_1_1
            };
        } finally{
            try {
                if (symbols_1_1 && !symbols_1_1.done && (_a = symbols_1.return)) _a.call(symbols_1);
            } finally{
                if (e_1) throw e_1.error;
            }
        }
        // assert bitArray.getSize() === this.bitCount;
        return bitArray;
    };
    /**
     * @Override
     */ State.prototype.toString = function() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format('%s bits=%d bytes=%d', __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_NAMES"][this.mode], this.bitCount, this.binaryShiftByteCount);
    };
    State.calculateBinaryShiftCost = function(state) {
        if (state.binaryShiftByteCount > 62) {
            return 21; // B/S with extended length
        }
        if (state.binaryShiftByteCount > 31) {
            return 20; // two B/S
        }
        if (state.binaryShiftByteCount > 0) {
            return 10; // one B/S
        }
        return 0;
    };
    State.INITIAL_STATE = new State(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EMPTY_TOKEN"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_UPPER"], 0, 0);
    return State;
}();
const __TURBOPACK__default__export__ = State;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/encoder/CharMap.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "CHAR_MAP": ()=>CHAR_MAP,
    "static_CHAR_MAP": ()=>static_CHAR_MAP
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/encoder/EncoderConstants.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Arrays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/Arrays.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/StringUtils.js [app-client] (ecmascript)");
;
;
;
function static_CHAR_MAP(CHAR_MAP) {
    var spaceCharCode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getCharCode(' ');
    var pointCharCode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getCharCode('.');
    var commaCharCode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getCharCode(',');
    CHAR_MAP[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_UPPER"]][spaceCharCode] = 1;
    var zUpperCharCode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getCharCode('Z');
    var aUpperCharCode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getCharCode('A');
    for(var c = aUpperCharCode; c <= zUpperCharCode; c++){
        CHAR_MAP[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_UPPER"]][c] = c - aUpperCharCode + 2;
    }
    CHAR_MAP[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_LOWER"]][spaceCharCode] = 1;
    var zLowerCharCode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getCharCode('z');
    var aLowerCharCode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getCharCode('a');
    for(var c = aLowerCharCode; c <= zLowerCharCode; c++){
        CHAR_MAP[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_LOWER"]][c] = c - aLowerCharCode + 2;
    }
    CHAR_MAP[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_DIGIT"]][spaceCharCode] = 1;
    var nineCharCode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getCharCode('9');
    var zeroCharCode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getCharCode('0');
    for(var c = zeroCharCode; c <= nineCharCode; c++){
        CHAR_MAP[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_DIGIT"]][c] = c - zeroCharCode + 2;
    }
    CHAR_MAP[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_DIGIT"]][commaCharCode] = 12;
    CHAR_MAP[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_DIGIT"]][pointCharCode] = 13;
    var mixedTable = [
        '\x00',
        ' ',
        '\x01',
        '\x02',
        '\x03',
        '\x04',
        '\x05',
        '\x06',
        '\x07',
        '\b',
        '\t',
        '\n',
        '\x0b',
        '\f',
        '\r',
        '\x1b',
        '\x1c',
        '\x1d',
        '\x1e',
        '\x1f',
        '@',
        '\\',
        '^',
        '_',
        '`',
        '|',
        '~',
        '\x7f'
    ];
    for(var i = 0; i < mixedTable.length; i++){
        CHAR_MAP[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_MIXED"]][__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getCharCode(mixedTable[i])] = i;
    }
    var punctTable = [
        '\x00',
        '\r',
        '\x00',
        '\x00',
        '\x00',
        '\x00',
        '!',
        '\'',
        '#',
        '$',
        '%',
        '&',
        '\'',
        '(',
        ')',
        '*',
        '+',
        ',',
        '-',
        '.',
        '/',
        ':',
        ';',
        '<',
        '=',
        '>',
        '?',
        '[',
        ']',
        '{',
        '}'
    ];
    for(var i = 0; i < punctTable.length; i++){
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getCharCode(punctTable[i]) > 0) {
            CHAR_MAP[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_PUNCT"]][__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getCharCode(punctTable[i])] = i;
        }
    }
    return CHAR_MAP;
}
var CHAR_MAP = static_CHAR_MAP(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Arrays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createInt32Array(5, 256));
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/encoder/HighLevelEncoder.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2013 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
// import java.util.Collection;
// import java.util.Collections;
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Collections$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/Collections.js [app-client] (ecmascript)");
// import java.util.Comparator;
// import java.util.Iterator;
// import java.util.LinkedList;
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$State$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/encoder/State.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/encoder/EncoderConstants.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$CharMap$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/encoder/CharMap.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$ShiftTable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/encoder/ShiftTable.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/StringUtils.js [app-client] (ecmascript)");
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
;
;
;
;
/**
 * This produces nearly optimal encodings of text into the first-level of
 * encoding used by Aztec code.
 *
 * It uses a dynamic algorithm.  For each prefix of the string, it determines
 * a set of encodings that could lead to this prefix.  We repeatedly add a
 * character and generate a new set of optimal encodings until we have read
 * through the entire input.
 *
 * <AUTHOR> Yellin
 * <AUTHOR> Abdullaev
 */ var HighLevelEncoder = function() {
    function HighLevelEncoder(text) {
        this.text = text;
    }
    /**
     * @return text represented by this encoder encoded as a {@link BitArray}
     */ HighLevelEncoder.prototype.encode = function() {
        var spaceCharCode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getCharCode(' ');
        var lineBreakCharCode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getCharCode('\n');
        var states = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Collections$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].singletonList(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$State$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].INITIAL_STATE);
        for(var index = 0; index < this.text.length; index++){
            var pairCode = void 0;
            var nextChar = index + 1 < this.text.length ? this.text[index + 1] : 0;
            switch(this.text[index]){
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getCharCode('\r'):
                    pairCode = nextChar === lineBreakCharCode ? 2 : 0;
                    break;
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getCharCode('.'):
                    pairCode = nextChar === spaceCharCode ? 3 : 0;
                    break;
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getCharCode(','):
                    pairCode = nextChar === spaceCharCode ? 4 : 0;
                    break;
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getCharCode(':'):
                    pairCode = nextChar === spaceCharCode ? 5 : 0;
                    break;
                default:
                    pairCode = 0;
            }
            if (pairCode > 0) {
                // We have one of the four special PUNCT pairs.  Treat them specially.
                // Get a new set of states for the two new characters.
                states = HighLevelEncoder.updateStateListForPair(states, index, pairCode);
                index++;
            } else {
                // Get a new set of states for the new character.
                states = this.updateStateListForChar(states, index);
            }
        }
        // We are left with a set of states.  Find the shortest one.
        var minState = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Collections$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].min(states, function(a, b) {
            return a.getBitCount() - b.getBitCount();
        });
        // Convert it to a bit array, and return.
        return minState.toBitArray(this.text);
    };
    // We update a set of states for a new character by updating each state
    // for the new character, merging the results, and then removing the
    // non-optimal states.
    HighLevelEncoder.prototype.updateStateListForChar = function(states, index) {
        var e_1, _a;
        var result = [];
        try {
            for(var states_1 = __values(states), states_1_1 = states_1.next(); !states_1_1.done; states_1_1 = states_1.next()){
                var state = states_1_1.value /*State*/ ;
                this.updateStateForChar(state, index, result);
            }
        } catch (e_1_1) {
            e_1 = {
                error: e_1_1
            };
        } finally{
            try {
                if (states_1_1 && !states_1_1.done && (_a = states_1.return)) _a.call(states_1);
            } finally{
                if (e_1) throw e_1.error;
            }
        }
        return HighLevelEncoder.simplifyStates(result);
    };
    // Return a set of states that represent the possible ways of updating this
    // state for the next character.  The resulting set of states are added to
    // the "result" list.
    HighLevelEncoder.prototype.updateStateForChar = function(state, index, result) {
        var ch = this.text[index] & 0xff;
        var charInCurrentTable = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$CharMap$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CHAR_MAP"][state.getMode()][ch] > 0;
        var stateNoBinary = null;
        for(var mode /*int*/  = 0; mode <= __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_PUNCT"]; mode++){
            var charInMode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$CharMap$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CHAR_MAP"][mode][ch];
            if (charInMode > 0) {
                if (stateNoBinary == null) {
                    // Only create stateNoBinary the first time it's required.
                    stateNoBinary = state.endBinaryShift(index);
                }
                // Try generating the character by latching to its mode
                if (!charInCurrentTable || mode === state.getMode() || mode === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_DIGIT"]) {
                    // If the character is in the current table, we don't want to latch to
                    // any other mode except possibly digit (which uses only 4 bits).  Any
                    // other latch would be equally successful *after* this character, and
                    // so wouldn't save any bits.
                    var latchState = stateNoBinary.latchAndAppend(mode, charInMode);
                    result.push(latchState);
                }
                // Try generating the character by switching to its mode.
                if (!charInCurrentTable && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$ShiftTable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SHIFT_TABLE"][state.getMode()][mode] >= 0) {
                    // It never makes sense to temporarily shift to another mode if the
                    // character exists in the current mode.  That can never save bits.
                    var shiftState = stateNoBinary.shiftAndAppend(mode, charInMode);
                    result.push(shiftState);
                }
            }
        }
        if (state.getBinaryShiftByteCount() > 0 || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$CharMap$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CHAR_MAP"][state.getMode()][ch] === 0) {
            // It's never worthwhile to go into binary shift mode if you're not already
            // in binary shift mode, and the character exists in your current mode.
            // That can never save bits over just outputting the char in the current mode.
            var binaryState = state.addBinaryShiftChar(index);
            result.push(binaryState);
        }
    };
    HighLevelEncoder.updateStateListForPair = function(states, index, pairCode) {
        var e_2, _a;
        var result = [];
        try {
            for(var states_2 = __values(states), states_2_1 = states_2.next(); !states_2_1.done; states_2_1 = states_2.next()){
                var state = states_2_1.value /*State*/ ;
                this.updateStateForPair(state, index, pairCode, result);
            }
        } catch (e_2_1) {
            e_2 = {
                error: e_2_1
            };
        } finally{
            try {
                if (states_2_1 && !states_2_1.done && (_a = states_2.return)) _a.call(states_2);
            } finally{
                if (e_2) throw e_2.error;
            }
        }
        return this.simplifyStates(result);
    };
    HighLevelEncoder.updateStateForPair = function(state, index, pairCode, result) {
        var stateNoBinary = state.endBinaryShift(index);
        // Possibility 1.  Latch to C.MODE_PUNCT, and then append this code
        result.push(stateNoBinary.latchAndAppend(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_PUNCT"], pairCode));
        if (state.getMode() !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_PUNCT"]) {
            // Possibility 2.  Shift to C.MODE_PUNCT, and then append this code.
            // Every state except C.MODE_PUNCT (handled above) can shift
            result.push(stateNoBinary.shiftAndAppend(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_PUNCT"], pairCode));
        }
        if (pairCode === 3 || pairCode === 4) {
            // both characters are in DIGITS.  Sometimes better to just add two digits
            var digitState = stateNoBinary.latchAndAppend(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_DIGIT"], 16 - pairCode) // period or comma in DIGIT
            .latchAndAppend(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$EncoderConstants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODE_DIGIT"], 1); // space in DIGIT
            result.push(digitState);
        }
        if (state.getBinaryShiftByteCount() > 0) {
            // It only makes sense to do the characters as binary if we're already
            // in binary mode.
            var binaryState = state.addBinaryShiftChar(index).addBinaryShiftChar(index + 1);
            result.push(binaryState);
        }
    };
    HighLevelEncoder.simplifyStates = function(states) {
        var e_3, _a, e_4, _b;
        var result = [];
        try {
            for(var states_3 = __values(states), states_3_1 = states_3.next(); !states_3_1.done; states_3_1 = states_3.next()){
                var newState = states_3_1.value;
                var add = true;
                var _loop_1 = function(oldState) {
                    if (oldState.isBetterThanOrEqualTo(newState)) {
                        add = false;
                        return "break";
                    }
                    if (newState.isBetterThanOrEqualTo(oldState)) {
                        // iterator.remove();
                        result = result.filter(function(x) {
                            return x !== oldState;
                        }); // remove old state
                    }
                };
                try {
                    for(var result_1 = (e_4 = void 0, __values(result)), result_1_1 = result_1.next(); !result_1_1.done; result_1_1 = result_1.next()){
                        var oldState = result_1_1.value;
                        var state_1 = _loop_1(oldState);
                        if (state_1 === "break") break;
                    }
                } catch (e_4_1) {
                    e_4 = {
                        error: e_4_1
                    };
                } finally{
                    try {
                        if (result_1_1 && !result_1_1.done && (_b = result_1.return)) _b.call(result_1);
                    } finally{
                        if (e_4) throw e_4.error;
                    }
                }
                if (add) {
                    result.push(newState);
                }
            }
        } catch (e_3_1) {
            e_3 = {
                error: e_3_1
            };
        } finally{
            try {
                if (states_3_1 && !states_3_1.done && (_a = states_3.return)) _a.call(states_3);
            } finally{
                if (e_3) throw e_3.error;
            }
        }
        return result;
    };
    return HighLevelEncoder;
}();
const __TURBOPACK__default__export__ = HighLevelEncoder;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/encoder/Encoder.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/BitArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/IllegalArgumentException.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/StringUtils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitMatrix$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/BitMatrix.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$AztecCode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/encoder/AztecCode.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$ReedSolomonEncoder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/reedsolomon/ReedSolomonEncoder.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGF$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/reedsolomon/GenericGF.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/encoder/HighLevelEncoder.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/Integer.js [app-client] (ecmascript)");
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
;
;
;
;
;
;
;
/*
 * Copyright 2013 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ // package com.google.zxing.aztec.encoder;
// import com.google.zxing.common.BitArray;
// import com.google.zxing.common.BitMatrix;
// import com.google.zxing.common.reedsolomon.GenericGF;
// import com.google.zxing.common.reedsolomon.ReedSolomonEncoder;
/**
 * Generates Aztec 2D barcodes.
 *
 * <AUTHOR> Abdullaev
 */ var Encoder = function() {
    function Encoder() {}
    /**
     * Encodes the given binary content as an Aztec symbol
     *
     * @param data input data string
     * @return Aztec symbol matrix with metadata
     */ Encoder.encodeBytes = function(data) {
        return Encoder.encode(data, Encoder.DEFAULT_EC_PERCENT, Encoder.DEFAULT_AZTEC_LAYERS);
    };
    /**
     * Encodes the given binary content as an Aztec symbol
     *
     * @param data input data string
     * @param minECCPercent minimal percentage of error check words (According to ISO/IEC 24778:2008,
     *                      a minimum of 23% + 3 words is recommended)
     * @param userSpecifiedLayers if non-zero, a user-specified value for the number of layers
     * @return Aztec symbol matrix with metadata
     */ Encoder.encode = function(data, minECCPercent, userSpecifiedLayers) {
        // High-level encode
        var bits = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$HighLevelEncoder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](data).encode();
        // stuff bits and choose symbol size
        var eccBits = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].truncDivision(bits.getSize() * minECCPercent, 100) + 11;
        var totalSizeBits = bits.getSize() + eccBits;
        var compact;
        var layers;
        var totalBitsInLayer;
        var wordSize;
        var stuffedBits;
        if (userSpecifiedLayers !== Encoder.DEFAULT_AZTEC_LAYERS) {
            compact = userSpecifiedLayers < 0;
            layers = Math.abs(userSpecifiedLayers);
            if (layers > (compact ? Encoder.MAX_NB_BITS_COMPACT : Encoder.MAX_NB_BITS)) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format('Illegal value %s for layers', userSpecifiedLayers));
            }
            totalBitsInLayer = Encoder.totalBitsInLayer(layers, compact);
            wordSize = Encoder.WORD_SIZE[layers];
            var usableBitsInLayers = totalBitsInLayer - totalBitsInLayer % wordSize;
            stuffedBits = Encoder.stuffBits(bits, wordSize);
            if (stuffedBits.getSize() + eccBits > usableBitsInLayers) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Data to large for user specified layer');
            }
            if (compact && stuffedBits.getSize() > wordSize * 64) {
                // Compact format only allows 64 data words, though C4 can hold more words than that
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Data to large for user specified layer');
            }
        } else {
            wordSize = 0;
            stuffedBits = null;
            // We look at the possible table sizes in the order Compact1, Compact2, Compact3,
            // Compact4, Normal4,...  Normal(i) for i < 4 isn't typically used since Compact(i+1)
            // is the same size, but has more data.
            for(var i /*int*/  = 0;; i++){
                if (i > Encoder.MAX_NB_BITS) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Data too large for an Aztec code');
                }
                compact = i <= 3;
                layers = compact ? i + 1 : i;
                totalBitsInLayer = Encoder.totalBitsInLayer(layers, compact);
                if (totalSizeBits > totalBitsInLayer) {
                    continue;
                }
                // [Re]stuff the bits if this is the first opportunity, or if the
                // wordSize has changed
                if (stuffedBits == null || wordSize !== Encoder.WORD_SIZE[layers]) {
                    wordSize = Encoder.WORD_SIZE[layers];
                    stuffedBits = Encoder.stuffBits(bits, wordSize);
                }
                var usableBitsInLayers = totalBitsInLayer - totalBitsInLayer % wordSize;
                if (compact && stuffedBits.getSize() > wordSize * 64) {
                    continue;
                }
                if (stuffedBits.getSize() + eccBits <= usableBitsInLayers) {
                    break;
                }
            }
        }
        var messageBits = Encoder.generateCheckWords(stuffedBits, totalBitsInLayer, wordSize);
        // generate mode message
        var messageSizeInWords = stuffedBits.getSize() / wordSize;
        var modeMessage = Encoder.generateModeMessage(compact, layers, messageSizeInWords);
        // allocate symbol
        var baseMatrixSize = (compact ? 11 : 14) + layers * 4; // not including alignment lines
        var alignmentMap = new Int32Array(baseMatrixSize);
        var matrixSize;
        if (compact) {
            // no alignment marks in compact mode, alignmentMap is a no-op
            matrixSize = baseMatrixSize;
            for(var i /*int*/  = 0; i < alignmentMap.length; i++){
                alignmentMap[i] = i;
            }
        } else {
            matrixSize = baseMatrixSize + 1 + 2 * __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].truncDivision(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].truncDivision(baseMatrixSize, 2) - 1, 15);
            var origCenter = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].truncDivision(baseMatrixSize, 2);
            var center = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].truncDivision(matrixSize, 2);
            for(var i /*int*/  = 0; i < origCenter; i++){
                var newOffset = i + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].truncDivision(i, 15);
                alignmentMap[origCenter - i - 1] = center - newOffset - 1;
                alignmentMap[origCenter + i] = center + newOffset + 1;
            }
        }
        var matrix = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitMatrix$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](matrixSize);
        // draw data bits
        for(var i /*int*/  = 0, rowOffset = 0; i < layers; i++){
            var rowSize = (layers - i) * 4 + (compact ? 9 : 12);
            for(var j /*int*/  = 0; j < rowSize; j++){
                var columnOffset = j * 2;
                for(var k /*int*/  = 0; k < 2; k++){
                    if (messageBits.get(rowOffset + columnOffset + k)) {
                        matrix.set(alignmentMap[i * 2 + k], alignmentMap[i * 2 + j]);
                    }
                    if (messageBits.get(rowOffset + rowSize * 2 + columnOffset + k)) {
                        matrix.set(alignmentMap[i * 2 + j], alignmentMap[baseMatrixSize - 1 - i * 2 - k]);
                    }
                    if (messageBits.get(rowOffset + rowSize * 4 + columnOffset + k)) {
                        matrix.set(alignmentMap[baseMatrixSize - 1 - i * 2 - k], alignmentMap[baseMatrixSize - 1 - i * 2 - j]);
                    }
                    if (messageBits.get(rowOffset + rowSize * 6 + columnOffset + k)) {
                        matrix.set(alignmentMap[baseMatrixSize - 1 - i * 2 - j], alignmentMap[i * 2 + k]);
                    }
                }
            }
            rowOffset += rowSize * 8;
        }
        // draw mode message
        Encoder.drawModeMessage(matrix, compact, matrixSize, modeMessage);
        // draw alignment marks
        if (compact) {
            Encoder.drawBullsEye(matrix, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].truncDivision(matrixSize, 2), 5);
        } else {
            Encoder.drawBullsEye(matrix, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].truncDivision(matrixSize, 2), 7);
            for(var i /*int*/  = 0, j = 0; i < __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].truncDivision(baseMatrixSize, 2) - 1; i += 15, j += 16){
                for(var k /*int*/  = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].truncDivision(matrixSize, 2) & 1; k < matrixSize; k += 2){
                    matrix.set(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].truncDivision(matrixSize, 2) - j, k);
                    matrix.set(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].truncDivision(matrixSize, 2) + j, k);
                    matrix.set(k, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].truncDivision(matrixSize, 2) - j);
                    matrix.set(k, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].truncDivision(matrixSize, 2) + j);
                }
            }
        }
        var aztec = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$AztecCode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        aztec.setCompact(compact);
        aztec.setSize(matrixSize);
        aztec.setLayers(layers);
        aztec.setCodeWords(messageSizeInWords);
        aztec.setMatrix(matrix);
        return aztec;
    };
    Encoder.drawBullsEye = function(matrix, center, size) {
        for(var i /*int*/  = 0; i < size; i += 2){
            for(var j /*int*/  = center - i; j <= center + i; j++){
                matrix.set(j, center - i);
                matrix.set(j, center + i);
                matrix.set(center - i, j);
                matrix.set(center + i, j);
            }
        }
        matrix.set(center - size, center - size);
        matrix.set(center - size + 1, center - size);
        matrix.set(center - size, center - size + 1);
        matrix.set(center + size, center - size);
        matrix.set(center + size, center - size + 1);
        matrix.set(center + size, center + size - 1);
    };
    Encoder.generateModeMessage = function(compact, layers, messageSizeInWords) {
        var modeMessage = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        if (compact) {
            modeMessage.appendBits(layers - 1, 2);
            modeMessage.appendBits(messageSizeInWords - 1, 6);
            modeMessage = Encoder.generateCheckWords(modeMessage, 28, 4);
        } else {
            modeMessage.appendBits(layers - 1, 5);
            modeMessage.appendBits(messageSizeInWords - 1, 11);
            modeMessage = Encoder.generateCheckWords(modeMessage, 40, 4);
        }
        return modeMessage;
    };
    Encoder.drawModeMessage = function(matrix, compact, matrixSize, modeMessage) {
        var center = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].truncDivision(matrixSize, 2);
        if (compact) {
            for(var i /*int*/  = 0; i < 7; i++){
                var offset = center - 3 + i;
                if (modeMessage.get(i)) {
                    matrix.set(offset, center - 5);
                }
                if (modeMessage.get(i + 7)) {
                    matrix.set(center + 5, offset);
                }
                if (modeMessage.get(20 - i)) {
                    matrix.set(offset, center + 5);
                }
                if (modeMessage.get(27 - i)) {
                    matrix.set(center - 5, offset);
                }
            }
        } else {
            for(var i /*int*/  = 0; i < 10; i++){
                var offset = center - 5 + i + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].truncDivision(i, 5);
                if (modeMessage.get(i)) {
                    matrix.set(offset, center - 7);
                }
                if (modeMessage.get(i + 10)) {
                    matrix.set(center + 7, offset);
                }
                if (modeMessage.get(29 - i)) {
                    matrix.set(offset, center + 7);
                }
                if (modeMessage.get(39 - i)) {
                    matrix.set(center - 7, offset);
                }
            }
        }
    };
    Encoder.generateCheckWords = function(bitArray, totalBits, wordSize) {
        var e_1, _a;
        // bitArray is guaranteed to be a multiple of the wordSize, so no padding needed
        var messageSizeInWords = bitArray.getSize() / wordSize;
        var rs = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$ReedSolomonEncoder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](Encoder.getGF(wordSize));
        var totalWords = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].truncDivision(totalBits, wordSize);
        var messageWords = Encoder.bitsToWords(bitArray, wordSize, totalWords);
        rs.encode(messageWords, totalWords - messageSizeInWords);
        var startPad = totalBits % wordSize;
        var messageBits = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        messageBits.appendBits(0, startPad);
        try {
            for(var _b = __values(Array.from(messageWords)), _c = _b.next(); !_c.done; _c = _b.next()){
                var messageWord = _c.value /*: int*/ ;
                messageBits.appendBits(messageWord, wordSize);
            }
        } catch (e_1_1) {
            e_1 = {
                error: e_1_1
            };
        } finally{
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            } finally{
                if (e_1) throw e_1.error;
            }
        }
        return messageBits;
    };
    Encoder.bitsToWords = function(stuffedBits, wordSize, totalWords) {
        var message = new Int32Array(totalWords);
        var i;
        var n;
        for(i = 0, n = stuffedBits.getSize() / wordSize; i < n; i++){
            var value = 0;
            for(var j /*int*/  = 0; j < wordSize; j++){
                value |= stuffedBits.get(i * wordSize + j) ? 1 << wordSize - j - 1 : 0;
            }
            message[i] = value;
        }
        return message;
    };
    Encoder.getGF = function(wordSize) {
        switch(wordSize){
            case 4:
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGF$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].AZTEC_PARAM;
            case 6:
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGF$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].AZTEC_DATA_6;
            case 8:
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGF$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].AZTEC_DATA_8;
            case 10:
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGF$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].AZTEC_DATA_10;
            case 12:
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGF$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].AZTEC_DATA_12;
            default:
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Unsupported word size ' + wordSize);
        }
    };
    Encoder.stuffBits = function(bits, wordSize) {
        var out = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        var n = bits.getSize();
        var mask = (1 << wordSize) - 2;
        for(var i /*int*/  = 0; i < n; i += wordSize){
            var word = 0;
            for(var j /*int*/  = 0; j < wordSize; j++){
                if (i + j >= n || bits.get(i + j)) {
                    word |= 1 << wordSize - 1 - j;
                }
            }
            if ((word & mask) === mask) {
                out.appendBits(word & mask, wordSize);
                i--;
            } else if ((word & mask) === 0) {
                out.appendBits(word | 1, wordSize);
                i--;
            } else {
                out.appendBits(word, wordSize);
            }
        }
        return out;
    };
    Encoder.totalBitsInLayer = function(layers, compact) {
        return ((compact ? 88 : 112) + 16 * layers) * layers;
    };
    Encoder.DEFAULT_EC_PERCENT = 33; // default minimal percentage of error check words
    Encoder.DEFAULT_AZTEC_LAYERS = 0;
    Encoder.MAX_NB_BITS = 32;
    Encoder.MAX_NB_BITS_COMPACT = 4;
    Encoder.WORD_SIZE = Int32Array.from([
        4,
        6,
        6,
        8,
        8,
        8,
        8,
        8,
        8,
        10,
        10,
        10,
        10,
        10,
        10,
        10,
        10,
        10,
        10,
        10,
        10,
        10,
        10,
        12,
        12,
        12,
        12,
        12,
        12,
        12,
        12,
        12,
        12
    ]);
    return Encoder;
}();
const __TURBOPACK__default__export__ = Encoder;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/AztecWriter.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
* Copyright 2013 ZXing authors
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*      http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/ // package com.google.zxing.aztec;
// import com.google.zxing.BarcodeFormat;
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/BarcodeFormat.js [app-client] (ecmascript)");
// import com.google.zxing.EncodeHintType;
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/EncodeHintType.js [app-client] (ecmascript)");
// import com.google.zxing.aztec.encoder.Encoder;
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$Encoder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/encoder/Encoder.js [app-client] (ecmascript)");
// import com.google.zxing.common.BitMatrix;
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitMatrix$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/BitMatrix.js [app-client] (ecmascript)");
// import java.nio.charset.Charset;
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Charset$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/Charset.js [app-client] (ecmascript)");
// import java.nio.charset.StandardCharsets;
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StandardCharsets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/StandardCharsets.js [app-client] (ecmascript)");
// import java.util.Map;
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/Integer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalStateException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/IllegalStateException.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/IllegalArgumentException.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/StringUtils.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
/**
 * Renders an Aztec code as a {@link BitMatrix}.
 */ var AztecWriter = function() {
    function AztecWriter() {}
    // @Override
    AztecWriter.prototype.encode = function(contents, format, width, height) {
        return this.encodeWithHints(contents, format, width, height, null);
    };
    // @Override
    AztecWriter.prototype.encodeWithHints = function(contents, format, width, height, hints) {
        var charset = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StandardCharsets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].ISO_8859_1;
        var eccPercent = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$Encoder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].DEFAULT_EC_PERCENT;
        var layers = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$Encoder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].DEFAULT_AZTEC_LAYERS;
        if (hints != null) {
            if (hints.has(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].CHARACTER_SET)) {
                charset = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Charset$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].forName(hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].CHARACTER_SET).toString());
            }
            if (hints.has(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].ERROR_CORRECTION)) {
                eccPercent = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].parseInt(hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].ERROR_CORRECTION).toString());
            }
            if (hints.has(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].AZTEC_LAYERS)) {
                layers = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].parseInt(hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$EncodeHintType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].AZTEC_LAYERS).toString());
            }
        }
        return AztecWriter.encodeLayers(contents, format, width, height, charset, eccPercent, layers);
    };
    AztecWriter.encodeLayers = function(contents, format, width, height, charset, eccPercent, layers) {
        if (format !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].AZTEC) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Can only encode AZTEC, but got ' + format);
        }
        var aztec = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$encoder$2f$Encoder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].encode(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getBytes(contents, charset), eccPercent, layers);
        return AztecWriter.renderResult(aztec, width, height);
    };
    AztecWriter.renderResult = function(code, width, height) {
        var input = code.getMatrix();
        if (input == null) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalStateException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        var inputWidth = input.getWidth();
        var inputHeight = input.getHeight();
        var outputWidth = Math.max(width, inputWidth);
        var outputHeight = Math.max(height, inputHeight);
        var multiple = Math.min(outputWidth / inputWidth, outputHeight / inputHeight);
        var leftPadding = (outputWidth - inputWidth * multiple) / 2;
        var topPadding = (outputHeight - inputHeight * multiple) / 2;
        var output = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitMatrix$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](outputWidth, outputHeight);
        for(var inputY /*int*/  = 0, outputY = topPadding; inputY < inputHeight; inputY++, outputY += multiple){
            // Write the contents of this row of the barcode
            for(var inputX /*int*/  = 0, outputX = leftPadding; inputX < inputWidth; inputX++, outputX += multiple){
                if (input.get(inputX, inputY)) {
                    output.setRegion(outputX, outputY, multiple, multiple);
                }
            }
        }
        return output;
    };
    return AztecWriter;
}();
const __TURBOPACK__default__export__ = AztecWriter;
}),
}]);

//# sourceMappingURL=652c1_%40zxing_library_esm_core_aztec_ed244887._.js.map