{"name": "lansia-server", "version": "1.0.0", "description": "Backend API untuk aplikasi pencatatan kesehatan lansia", "main": "index.js", "scripts": {"start": "node index.js", "dev": "node index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"express": "^4.21.2", "mysql2": "^3.11.5", "cors": "^2.8.5"}, "keywords": ["lansia", "k<PERSON><PERSON>an", "posyandu", "qr-code"], "author": "Developer", "license": "MIT"}