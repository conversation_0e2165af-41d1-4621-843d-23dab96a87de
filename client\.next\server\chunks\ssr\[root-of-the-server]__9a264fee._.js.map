{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\n\ninterface HeaderProps {\n  title?: string;\n  subtitle?: string;\n  showBackButton?: boolean;\n  backUrl?: string;\n  backText?: string;\n  rightActions?: React.ReactNode;\n}\n\nexport default function Header({\n  title = \"Kesehatan Lansia\",\n  subtitle = \"Posyandu Digital Modern\",\n  showBackButton = false,\n  backUrl = \"/\",\n  backText = \"Beranda\",\n  rightActions\n}: HeaderProps) {\n  const pathname = usePathname();\n\n  // Determine page-specific content\n  const getPageContent = () => {\n    if (pathname === '/') {\n      return {\n        title: \"Kesehatan Lansia\",\n        subtitle: \"Posyandu Digital Modern\",\n        showStatus: true\n      };\n    } else if (pathname === '/form') {\n      return {\n        title: \"Kesehatan Lansia\",\n        subtitle: \"Form Pendaftaran\",\n        showStatus: false\n      };\n    } else if (pathname === '/scan') {\n      return {\n        title: \"Kesehat<PERSON>\",\n        subtitle: \"Scan QR Code\",\n        showStatus: false\n      };\n    } else if (pathname === '/profiles') {\n      return {\n        title: \"Kesehatan Lansia\",\n        subtitle: \"Daftar Semua Lansia\",\n        showStatus: false\n      };\n    } else if (pathname.startsWith('/profile/')) {\n      return {\n        title: \"Kesehatan Lansia\",\n        subtitle: subtitle || \"Profil Lansia\",\n        showStatus: false\n      };\n    }\n    return {\n      title,\n      subtitle,\n      showStatus: false\n    };\n  };\n\n  const pageContent = getPageContent();\n\n  return (\n    <header className=\"bg-white border-b border-gray-200 shadow-sm\">\n      <div className=\"container-responsive\">\n        <div className=\"flex justify-between items-center py-4 sm:py-6\">\n          {/* Left Section - Logo and Title */}\n          <div className=\"flex items-center space-x-2 sm:space-x-4 min-w-0 flex-1\">\n            <Link href=\"/\" className=\"flex items-center space-x-2 sm:space-x-3 min-w-0\">\n              <div className=\"w-10 h-10 sm:w-12 sm:h-12 bg-blue-600 rounded-2xl flex items-center justify-center shadow-sm flex-shrink-0\">\n                <svg className=\"w-5 h-5 sm:w-6 sm:h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n                </svg>\n              </div>\n              <div className=\"min-w-0\">\n                <h1 className=\"text-lg sm:text-2xl font-bold text-gray-900 truncate\">{pageContent.title}</h1>\n                <p className=\"text-xs sm:text-sm text-gray-600 truncate\">{pageContent.subtitle}</p>\n              </div>\n            </Link>\n          </div>\n\n          {/* Right Section - Actions and Navigation */}\n          <div className=\"flex items-center space-x-2 sm:space-x-4 flex-shrink-0\">\n            {/* Status Indicator (only on home page) */}\n            {pageContent.showStatus && (\n              <div className=\"hidden md:flex items-center space-x-4\">\n                <span className=\"badge-success\">\n                  <span className=\"w-2 h-2 bg-green-400 rounded-full mr-2\"></span>\n                  Online\n                </span>\n              </div>\n            )}\n\n            {/* Custom Right Actions */}\n            {rightActions}\n\n            {/* Back Button */}\n            {showBackButton && (\n              <Link\n                href={backUrl}\n                className=\"btn-secondary text-xs sm:text-sm flex-shrink-0\"\n              >\n                <span className=\"hidden sm:inline\">← {backText}</span>\n                <span className=\"sm:hidden\">←</span>\n              </Link>\n            )}\n\n            {/* Default Navigation for specific pages */}\n            {!showBackButton && pathname !== '/' && (\n              <Link\n                href=\"/\"\n                className=\"text-gray-600 hover:text-gray-900 transition-colors text-xs sm:text-sm\"\n              >\n                <span className=\"hidden sm:inline\">← Beranda</span>\n                <span className=\"sm:hidden\">←</span>\n              </Link>\n            )}\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAce,SAAS,OAAO,EAC7B,QAAQ,kBAAkB,EAC1B,WAAW,yBAAyB,EACpC,iBAAiB,KAAK,EACtB,UAAU,GAAG,EACb,WAAW,SAAS,EACpB,YAAY,EACA;IACZ,MAAM,WAAW,CAAA,GAAA,iQAAA,CAAA,cAAW,AAAD;IAE3B,kCAAkC;IAClC,MAAM,iBAAiB;QACrB,IAAI,aAAa,KAAK;YACpB,OAAO;gBACL,OAAO;gBACP,UAAU;gBACV,YAAY;YACd;QACF,OAAO,IAAI,aAAa,SAAS;YAC/B,OAAO;gBACL,OAAO;gBACP,UAAU;gBACV,YAAY;YACd;QACF,OAAO,IAAI,aAAa,SAAS;YAC/B,OAAO;gBACL,OAAO;gBACP,UAAU;gBACV,YAAY;YACd;QACF,OAAO,IAAI,aAAa,aAAa;YACnC,OAAO;gBACL,OAAO;gBACP,UAAU;gBACV,YAAY;YACd;QACF,OAAO,IAAI,SAAS,UAAU,CAAC,cAAc;YAC3C,OAAO;gBACL,OAAO;gBACP,UAAU,YAAY;gBACtB,YAAY;YACd;QACF;QACA,OAAO;YACL;YACA;YACA,YAAY;QACd;IACF;IAEA,MAAM,cAAc;IAEpB,qBACE,6WAAC;QAAO,WAAU;kBAChB,cAAA,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC;gBAAI,WAAU;;kCAEb,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC,2RAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6WAAC;oCAAI,WAAU;8CACb,cAAA,6WAAC;wCAAI,WAAU;wCAAmC,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC1F,cAAA,6WAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAG,WAAU;sDAAwD,YAAY,KAAK;;;;;;sDACvF,6WAAC;4CAAE,WAAU;sDAA6C,YAAY,QAAQ;;;;;;;;;;;;;;;;;;;;;;;kCAMpF,6WAAC;wBAAI,WAAU;;4BAEZ,YAAY,UAAU,kBACrB,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAK,WAAU;;sDACd,6WAAC;4CAAK,WAAU;;;;;;wCAAgD;;;;;;;;;;;;4BAOrE;4BAGA,gCACC,6WAAC,2RAAA,CAAA,UAAI;gCACH,MAAM;gCACN,WAAU;;kDAEV,6WAAC;wCAAK,WAAU;;4CAAmB;4CAAG;;;;;;;kDACtC,6WAAC;wCAAK,WAAU;kDAAY;;;;;;;;;;;;4BAK/B,CAAC,kBAAkB,aAAa,qBAC/B,6WAAC,2RAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6WAAC;wCAAK,WAAU;kDAAmB;;;;;;kDACnC,6WAAC;wCAAK,WAAU;kDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5C", "debugId": null}}]}