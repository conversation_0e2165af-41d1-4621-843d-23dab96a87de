{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/src/app/scan/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { BrowserMultiFormatReader } from '@zxing/library';\n\nexport default function ScanPage() {\n  const router = useRouter();\n  const videoRef = useRef<HTMLVideoElement>(null);\n  const [isScanning, setIsScanning] = useState(false);\n  const [error, setError] = useState<string>('');\n  const [codeReader, setCodeReader] = useState<BrowserMultiFormatReader | null>(null);\n  const [hasPermission, setHasPermission] = useState<boolean | null>(null);\n\n  useEffect(() => {\n    const reader = new BrowserMultiFormatReader();\n    setCodeReader(reader);\n\n    return () => {\n      reader.reset();\n    };\n  }, []);\n\n  const startScanning = async () => {\n    if (!codeReader || !videoRef.current) return;\n\n    try {\n      setError('');\n      setIsScanning(true);\n\n      // Request camera permission\n      const stream = await navigator.mediaDevices.getUserMedia({ \n        video: { \n          facingMode: 'environment' // Use back camera if available\n        } \n      });\n      \n      setHasPermission(true);\n      videoRef.current.srcObject = stream;\n\n      // Start decoding\n      codeReader.decodeFromVideoDevice(null, videoRef.current, (result, error) => {\n        if (result) {\n          const text = result.getText();\n          console.log('QR Code detected:', text);\n          \n          // Check if it's a valid profile URL\n          const profileMatch = text.match(/\\/profile\\/(\\d+)$/);\n          if (profileMatch) {\n            const profileId = profileMatch[1];\n            stopScanning();\n            router.push(`/profile/${profileId}`);\n          } else {\n            // If it's a full URL, try to extract the profile ID\n            const urlMatch = text.match(/profile\\/(\\d+)/);\n            if (urlMatch) {\n              const profileId = urlMatch[1];\n              stopScanning();\n              router.push(`/profile/${profileId}`);\n            } else {\n              setError('QR Code tidak valid. Pastikan ini adalah QR Code dari aplikasi kesehatan lansia.');\n            }\n          }\n        }\n        \n        if (error && !(error.name === 'NotFoundException')) {\n          console.error('Scan error:', error);\n        }\n      });\n\n    } catch (err) {\n      console.error('Camera error:', err);\n      setHasPermission(false);\n      setError('Gagal mengakses kamera. Pastikan Anda memberikan izin kamera dan menggunakan HTTPS.');\n      setIsScanning(false);\n    }\n  };\n\n  const stopScanning = () => {\n    if (codeReader) {\n      codeReader.reset();\n    }\n    \n    if (videoRef.current && videoRef.current.srcObject) {\n      const stream = videoRef.current.srcObject as MediaStream;\n      stream.getTracks().forEach(track => track.stop());\n      videoRef.current.srcObject = null;\n    }\n    \n    setIsScanning(false);\n  };\n\n  const handleManualInput = () => {\n    const profileId = prompt('Masukkan ID Profil Lansia:');\n    if (profileId && /^\\d+$/.test(profileId)) {\n      router.push(`/profile/${profileId}`);\n    } else if (profileId) {\n      alert('ID Profil harus berupa angka');\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-green-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-4\">\n            <div className=\"flex items-center space-x-3\">\n              <Link href=\"/\" className=\"flex items-center space-x-3\">\n                <div className=\"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-lg\">L</span>\n                </div>\n                <div>\n                  <h1 className=\"text-xl font-bold text-gray-900\">Kesehatan Lansia</h1>\n                  <p className=\"text-sm text-gray-500\">Scan QR Code</p>\n                </div>\n              </Link>\n            </div>\n            <Link \n              href=\"/\" \n              className=\"text-gray-600 hover:text-gray-900 transition-colors\"\n            >\n              ← Kembali ke Beranda\n            </Link>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 card-shadow-lg\">\n          <div className=\"px-8 py-6 border-b border-gray-200\">\n            <h2 className=\"text-2xl font-bold text-gray-900\">Scan QR Code Lansia</h2>\n            <p className=\"text-gray-600 mt-2\">\n              Pindai QR Code untuk melihat profil dan riwayat pemeriksaan kesehatan lansia\n            </p>\n          </div>\n\n          <div className=\"px-8 py-6\">\n            {/* Camera View */}\n            <div className=\"relative bg-gray-900 rounded-lg overflow-hidden mb-6\" style={{ aspectRatio: '4/3' }}>\n              <video\n                ref={videoRef}\n                className=\"w-full h-full object-cover\"\n                autoPlay\n                playsInline\n                muted\n              />\n              \n              {!isScanning && (\n                <div className=\"absolute inset-0 flex items-center justify-center bg-gray-800 bg-opacity-75\">\n                  <div className=\"text-center text-white\">\n                    <svg className=\"w-16 h-16 mx-auto mb-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z\" />\n                    </svg>\n                    <p className=\"text-lg font-medium\">Kamera Belum Aktif</p>\n                    <p className=\"text-sm text-gray-300 mt-1\">Klik tombol \"Mulai Scan\" untuk mengaktifkan kamera</p>\n                  </div>\n                </div>\n              )}\n\n              {isScanning && (\n                <div className=\"absolute inset-0 pointer-events-none\">\n                  {/* Scanning overlay */}\n                  <div className=\"absolute inset-4 border-2 border-green-400 rounded-lg\">\n                    <div className=\"absolute top-0 left-0 w-6 h-6 border-t-4 border-l-4 border-green-400\"></div>\n                    <div className=\"absolute top-0 right-0 w-6 h-6 border-t-4 border-r-4 border-green-400\"></div>\n                    <div className=\"absolute bottom-0 left-0 w-6 h-6 border-b-4 border-l-4 border-green-400\"></div>\n                    <div className=\"absolute bottom-0 right-0 w-6 h-6 border-b-4 border-r-4 border-green-400\"></div>\n                  </div>\n                  <div className=\"absolute bottom-4 left-4 right-4 text-center\">\n                    <p className=\"text-white text-sm bg-black bg-opacity-50 rounded px-3 py-1\">\n                      Arahkan kamera ke QR Code\n                    </p>\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Error Message */}\n            {error && (\n              <div className=\"mb-6 bg-red-50 border border-red-200 rounded-lg p-4\">\n                <div className=\"flex\">\n                  <div className=\"flex-shrink-0\">\n                    <svg className=\"h-5 w-5 text-red-400\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                      <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                    </svg>\n                  </div>\n                  <div className=\"ml-3\">\n                    <h3 className=\"text-sm font-medium text-red-800\">Error</h3>\n                    <div className=\"mt-2 text-sm text-red-700\">\n                      <p>{error}</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Control Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-4\">\n              {!isScanning ? (\n                <button\n                  onClick={startScanning}\n                  className=\"flex-1 bg-green-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all duration-200 btn-hover\"\n                >\n                  <span className=\"flex items-center justify-center\">\n                    <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z\" />\n                    </svg>\n                    Mulai Scan\n                  </span>\n                </button>\n              ) : (\n                <button\n                  onClick={stopScanning}\n                  className=\"flex-1 bg-red-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-all duration-200 btn-hover\"\n                >\n                  <span className=\"flex items-center justify-center\">\n                    <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z\" />\n                    </svg>\n                    Stop Scan\n                  </span>\n                </button>\n              )}\n\n              <button\n                onClick={handleManualInput}\n                className=\"flex-1 bg-gray-200 text-gray-800 py-3 px-6 rounded-lg font-medium hover:bg-gray-300 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-200 btn-hover\"\n              >\n                Input Manual\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Instructions */}\n        <div className=\"mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4\">\n          <div className=\"flex\">\n            <div className=\"flex-shrink-0\">\n              <svg className=\"h-5 w-5 text-blue-400\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\n              </svg>\n            </div>\n            <div className=\"ml-3\">\n              <h3 className=\"text-sm font-medium text-blue-800\">Cara Menggunakan</h3>\n              <div className=\"mt-2 text-sm text-blue-700\">\n                <ul className=\"list-disc list-inside space-y-1\">\n                  <li>Klik \"Mulai Scan\" untuk mengaktifkan kamera</li>\n                  <li>Arahkan kamera ke QR Code lansia</li>\n                  <li>Tunggu hingga QR Code terdeteksi secara otomatis</li>\n                  <li>Jika QR Code tidak terbaca, gunakan \"Input Manual\"</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,oUAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAmC;IAC9E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAkB;IAEnE,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,SAAS,IAAI,gPAAA,CAAA,2BAAwB;QAC3C,cAAc;QAEd,OAAO;YACL,OAAO,KAAK;QACd;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI,CAAC,cAAc,CAAC,SAAS,OAAO,EAAE;QAEtC,IAAI;YACF,SAAS;YACT,cAAc;YAEd,4BAA4B;YAC5B,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBACvD,OAAO;oBACL,YAAY,cAAc,+BAA+B;gBAC3D;YACF;YAEA,iBAAiB;YACjB,SAAS,OAAO,CAAC,SAAS,GAAG;YAE7B,iBAAiB;YACjB,WAAW,qBAAqB,CAAC,MAAM,SAAS,OAAO,EAAE,CAAC,QAAQ;gBAChE,IAAI,QAAQ;oBACV,MAAM,OAAO,OAAO,OAAO;oBAC3B,QAAQ,GAAG,CAAC,qBAAqB;oBAEjC,oCAAoC;oBACpC,MAAM,eAAe,KAAK,KAAK,CAAC;oBAChC,IAAI,cAAc;wBAChB,MAAM,YAAY,YAAY,CAAC,EAAE;wBACjC;wBACA,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,WAAW;oBACrC,OAAO;wBACL,oDAAoD;wBACpD,MAAM,WAAW,KAAK,KAAK,CAAC;wBAC5B,IAAI,UAAU;4BACZ,MAAM,YAAY,QAAQ,CAAC,EAAE;4BAC7B;4BACA,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,WAAW;wBACrC,OAAO;4BACL,SAAS;wBACX;oBACF;gBACF;gBAEA,IAAI,SAAS,CAAC,CAAC,MAAM,IAAI,KAAK,mBAAmB,GAAG;oBAClD,QAAQ,KAAK,CAAC,eAAe;gBAC/B;YACF;QAEF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,iBAAiB;YACjB,SAAS;YACT,cAAc;QAChB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,YAAY;YACd,WAAW,KAAK;QAClB;QAEA,IAAI,SAAS,OAAO,IAAI,SAAS,OAAO,CAAC,SAAS,EAAE;YAClD,MAAM,SAAS,SAAS,OAAO,CAAC,SAAS;YACzC,OAAO,SAAS,GAAG,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI;YAC9C,SAAS,OAAO,CAAC,SAAS,GAAG;QAC/B;QAEA,cAAc;IAChB;IAEA,MAAM,oBAAoB;QACxB,MAAM,YAAY,OAAO;QACzB,IAAI,aAAa,QAAQ,IAAI,CAAC,YAAY;YACxC,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,WAAW;QACrC,OAAO,IAAI,WAAW;YACpB,MAAM;QACR;IACF;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAO,WAAU;0BAChB,cAAA,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC,2RAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,6WAAC;4CAAI,WAAU;sDACb,cAAA,6WAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,6WAAC;;8DACC,6WAAC;oDAAG,WAAU;8DAAkC;;;;;;8DAChD,6WAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;0CAI3C,6WAAC,2RAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,6WAAC;gBAAK,WAAU;;kCACd,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6WAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;0CAKpC,6WAAC;gCAAI,WAAU;;kDAEb,6WAAC;wCAAI,WAAU;wCAAuD,OAAO;4CAAE,aAAa;wCAAM;;0DAChG,6WAAC;gDACC,KAAK;gDACL,WAAU;gDACV,QAAQ;gDACR,WAAW;gDACX,KAAK;;;;;;4CAGN,CAAC,4BACA,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;4DAAI,WAAU;4DAAuC,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC9F,cAAA,6WAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;sEAEvE,6WAAC;4DAAE,WAAU;sEAAsB;;;;;;sEACnC,6WAAC;4DAAE,WAAU;sEAA6B;;;;;;;;;;;;;;;;;4CAK/C,4BACC,6WAAC;gDAAI,WAAU;;kEAEb,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;gEAAI,WAAU;;;;;;0EACf,6WAAC;gEAAI,WAAU;;;;;;0EACf,6WAAC;gEAAI,WAAU;;;;;;0EACf,6WAAC;gEAAI,WAAU;;;;;;;;;;;;kEAEjB,6WAAC;wDAAI,WAAU;kEACb,cAAA,6WAAC;4DAAE,WAAU;sEAA8D;;;;;;;;;;;;;;;;;;;;;;;oCASlF,uBACC,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAI,WAAU;8DACb,cAAA,6WAAC;wDAAI,WAAU;wDAAuB,OAAM;wDAA6B,SAAQ;wDAAY,MAAK;kEAChG,cAAA,6WAAC;4DAAK,UAAS;4DAAU,GAAE;4DAA0N,UAAS;;;;;;;;;;;;;;;;8DAGlQ,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;4DAAG,WAAU;sEAAmC;;;;;;sEACjD,6WAAC;4DAAI,WAAU;sEACb,cAAA,6WAAC;0EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQd,6WAAC;wCAAI,WAAU;;4CACZ,CAAC,2BACA,6WAAC;gDACC,SAAS;gDACT,WAAU;0DAEV,cAAA,6WAAC;oDAAK,WAAU;;sEACd,6WAAC;4DAAI,WAAU;4DAAe,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACtE,cAAA,6WAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;wDACjE;;;;;;;;;;;qEAKV,6WAAC;gDACC,SAAS;gDACT,WAAU;0DAEV,cAAA,6WAAC;oDAAK,WAAU;;sEACd,6WAAC;4DAAI,WAAU;4DAAe,MAAK;4DAAO,QAAO;4DAAe,SAAQ;;8EACtE,6WAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;8EACrE,6WAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;wDACjE;;;;;;;;;;;;0DAMZ,6WAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;kCAQP,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAI,WAAU;8CACb,cAAA,6WAAC;wCAAI,WAAU;wCAAwB,OAAM;wCAA6B,SAAQ;wCAAY,MAAK;kDACjG,cAAA,6WAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAmI,UAAS;;;;;;;;;;;;;;;;8CAG3K,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,6WAAC;4CAAI,WAAU;sDACb,cAAA,6WAAC;gDAAG,WAAU;;kEACZ,6WAAC;kEAAG;;;;;;kEACJ,6WAAC;kEAAG;;;;;;kEACJ,6WAAC;kEAAG;;;;;;kEACJ,6WAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStB", "debugId": null}}]}