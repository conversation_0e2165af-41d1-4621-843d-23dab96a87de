module.exports = {

"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Exception.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ts$2d$custom$2d$error$40$3$2e$3$2e$1$2f$node_modules$2f$ts$2d$custom$2d$error$2f$dist$2f$custom$2d$error$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/ts-custom-error@3.3.1/node_modules/ts-custom-error/dist/custom-error.mjs [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
/**
 * Custom Error class of type Exception.
 */ var Exception = function(_super) {
    __extends(Exception, _super);
    /**
     * Allows Exception to be constructed directly
     * with some message and prototype definition.
     */ function Exception(message) {
        if (message === void 0) {
            message = undefined;
        }
        var _this = _super.call(this, message) || this;
        _this.message = message;
        return _this;
    }
    Exception.prototype.getKind = function() {
        var ex = this.constructor;
        return ex.kind;
    };
    /**
     * It's typed as string so it can be extended and overriden.
     */ Exception.kind = 'Exception';
    return Exception;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$ts$2d$custom$2d$error$40$3$2e$3$2e$1$2f$node_modules$2f$ts$2d$custom$2d$error$2f$dist$2f$custom$2d$error$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CustomError"]);
const __TURBOPACK__default__export__ = Exception;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ArgumentException.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Exception$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Exception.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
/**
 * Custom Error class of type Exception.
 */ var ArgumentException = function(_super) {
    __extends(ArgumentException, _super);
    function ArgumentException() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    ArgumentException.kind = 'ArgumentException';
    return ArgumentException;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Exception$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = ArgumentException;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/IllegalArgumentException.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Exception$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Exception.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
/**
 * Custom Error class of type Exception.
 */ var IllegalArgumentException = function(_super) {
    __extends(IllegalArgumentException, _super);
    function IllegalArgumentException() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    IllegalArgumentException.kind = 'IllegalArgumentException';
    return IllegalArgumentException;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Exception$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = IllegalArgumentException;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/BinaryBitmap.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2009 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/IllegalArgumentException.js [app-ssr] (ecmascript)");
;
var BinaryBitmap = function() {
    function BinaryBitmap(binarizer) {
        this.binarizer = binarizer;
        if (binarizer === null) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('Binarizer must be non-null.');
        }
    }
    /**
     * @return The width of the bitmap.
     */ BinaryBitmap.prototype.getWidth = function() {
        return this.binarizer.getWidth();
    };
    /**
     * @return The height of the bitmap.
     */ BinaryBitmap.prototype.getHeight = function() {
        return this.binarizer.getHeight();
    };
    /**
     * Converts one row of luminance data to 1 bit data. May actually do the conversion, or return
     * cached data. Callers should assume this method is expensive and call it as seldom as possible.
     * This method is intended for decoding 1D barcodes and may choose to apply sharpening.
     *
     * @param y The row to fetch, which must be in [0, bitmap height)
     * @param row An optional preallocated array. If null or too small, it will be ignored.
     *            If used, the Binarizer will call BitArray.clear(). Always use the returned object.
     * @return The array of bits for this row (true means black).
     * @throws NotFoundException if row can't be binarized
     */ BinaryBitmap.prototype.getBlackRow = function(y /*int*/ , row) {
        return this.binarizer.getBlackRow(y, row);
    };
    /**
     * Converts a 2D array of luminance data to 1 bit. As above, assume this method is expensive
     * and do not call it repeatedly. This method is intended for decoding 2D barcodes and may or
     * may not apply sharpening. Therefore, a row from this matrix may not be identical to one
     * fetched using getBlackRow(), so don't mix and match between them.
     *
     * @return The 2D array of bits for the image (true means black).
     * @throws NotFoundException if image can't be binarized to make a matrix
     */ BinaryBitmap.prototype.getBlackMatrix = function() {
        // The matrix is created on demand the first time it is requested, then cached. There are two
        // reasons for this:
        // 1. This work will never be done if the caller only installs 1D Reader objects, or if a
        //    1D Reader finds a barcode before the 2D Readers run.
        // 2. This work will only be done once even if the caller installs multiple 2D Readers.
        if (this.matrix === null || this.matrix === undefined) {
            this.matrix = this.binarizer.getBlackMatrix();
        }
        return this.matrix;
    };
    /**
     * @return Whether this bitmap can be cropped.
     */ BinaryBitmap.prototype.isCropSupported = function() {
        return this.binarizer.getLuminanceSource().isCropSupported();
    };
    /**
     * Returns a new object with cropped image data. Implementations may keep a reference to the
     * original data rather than a copy. Only callable if isCropSupported() is true.
     *
     * @param left The left coordinate, which must be in [0,getWidth())
     * @param top The top coordinate, which must be in [0,getHeight())
     * @param width The width of the rectangle to crop.
     * @param height The height of the rectangle to crop.
     * @return A cropped version of this object.
     */ BinaryBitmap.prototype.crop = function(left /*int*/ , top /*int*/ , width /*int*/ , height /*int*/ ) {
        var newSource = this.binarizer.getLuminanceSource().crop(left, top, width, height);
        return new BinaryBitmap(this.binarizer.createBinarizer(newSource));
    };
    /**
     * @return Whether this bitmap supports counter-clockwise rotation.
     */ BinaryBitmap.prototype.isRotateSupported = function() {
        return this.binarizer.getLuminanceSource().isRotateSupported();
    };
    /**
     * Returns a new object with rotated image data by 90 degrees counterclockwise.
     * Only callable if {@link #isRotateSupported()} is true.
     *
     * @return A rotated version of this object.
     */ BinaryBitmap.prototype.rotateCounterClockwise = function() {
        var newSource = this.binarizer.getLuminanceSource().rotateCounterClockwise();
        return new BinaryBitmap(this.binarizer.createBinarizer(newSource));
    };
    /**
     * Returns a new object with rotated image data by 45 degrees counterclockwise.
     * Only callable if {@link #isRotateSupported()} is true.
     *
     * @return A rotated version of this object.
     */ BinaryBitmap.prototype.rotateCounterClockwise45 = function() {
        var newSource = this.binarizer.getLuminanceSource().rotateCounterClockwise45();
        return new BinaryBitmap(this.binarizer.createBinarizer(newSource));
    };
    /*@Override*/ BinaryBitmap.prototype.toString = function() {
        try {
            return this.getBlackMatrix().toString();
        } catch (e /*: NotFoundException*/ ) {
            return '';
        }
    };
    return BinaryBitmap;
}();
const __TURBOPACK__default__export__ = BinaryBitmap;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ChecksumException.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Exception$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Exception.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
/**
 * Custom Error class of type Exception.
 */ var ChecksumException = function(_super) {
    __extends(ChecksumException, _super);
    function ChecksumException() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    ChecksumException.getChecksumInstance = function() {
        return new ChecksumException();
    };
    ChecksumException.kind = 'ChecksumException';
    return ChecksumException;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Exception$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = ChecksumException;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Binarizer.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2009 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * This class hierarchy provides a set of methods to convert luminance data to 1 bit data.
 * It allows the algorithm to vary polymorphically, for example allowing a very expensive
 * thresholding technique for servers and a fast one for mobile. It also permits the implementation
 * to vary, e.g. a JNI version for Android and a Java fallback version for other platforms.
 *
 * <AUTHOR> (Daniel Switkin)
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var Binarizer = function() {
    function Binarizer(source) {
        this.source = source;
    }
    Binarizer.prototype.getLuminanceSource = function() {
        return this.source;
    };
    Binarizer.prototype.getWidth = function() {
        return this.source.getWidth();
    };
    Binarizer.prototype.getHeight = function() {
        return this.source.getHeight();
    };
    return Binarizer;
}();
const __TURBOPACK__default__export__ = Binarizer;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/System.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var System = function() {
    function System() {}
    // public static void arraycopy(Object src, int srcPos, Object dest, int destPos, int length)
    /**
     * Makes a copy of a array.
     */ System.arraycopy = function(src, srcPos, dest, destPos, length) {
        // TODO: better use split or set?
        while(length--){
            dest[destPos++] = src[srcPos++];
        }
    };
    /**
     * Returns the current time in milliseconds.
     */ System.currentTimeMillis = function() {
        return Date.now();
    };
    return System;
}();
const __TURBOPACK__default__export__ = System;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/Arrays.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/System.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/IllegalArgumentException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ArrayIndexOutOfBoundsException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ArrayIndexOutOfBoundsException.js [app-ssr] (ecmascript)");
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
;
var Arrays = function() {
    function Arrays() {}
    /**
     * Assigns the specified int value to each element of the specified array
     * of ints.
     *
     * @param a the array to be filled
     * @param val the value to be stored in all elements of the array
     */ Arrays.fill = function(a, val) {
        for(var i = 0, len = a.length; i < len; i++)a[i] = val;
    };
    /**
     * Assigns the specified int value to each element of the specified
     * range of the specified array of ints.  The range to be filled
     * extends from index {@code fromIndex}, inclusive, to index
     * {@code toIndex}, exclusive.  (If {@code fromIndex==toIndex}, the
     * range to be filled is empty.)
     *
     * @param a the array to be filled
     * @param fromIndex the index of the first element (inclusive) to be
     *        filled with the specified value
     * @param toIndex the index of the last element (exclusive) to be
     *        filled with the specified value
     * @param val the value to be stored in all elements of the array
     * @throws IllegalArgumentException if {@code fromIndex > toIndex}
     * @throws ArrayIndexOutOfBoundsException if {@code fromIndex < 0} or
     *         {@code toIndex > a.length}
     */ Arrays.fillWithin = function(a, fromIndex, toIndex, val) {
        Arrays.rangeCheck(a.length, fromIndex, toIndex);
        for(var i = fromIndex; i < toIndex; i++)a[i] = val;
    };
    /**
     * Checks that {@code fromIndex} and {@code toIndex} are in
     * the range and throws an exception if they aren't.
     */ Arrays.rangeCheck = function(arrayLength, fromIndex, toIndex) {
        if (fromIndex > toIndex) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('fromIndex(' + fromIndex + ') > toIndex(' + toIndex + ')');
        }
        if (fromIndex < 0) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ArrayIndexOutOfBoundsException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](fromIndex);
        }
        if (toIndex > arrayLength) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ArrayIndexOutOfBoundsException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](toIndex);
        }
    };
    Arrays.asList = function() {
        var args = [];
        for(var _i = 0; _i < arguments.length; _i++){
            args[_i] = arguments[_i];
        }
        return args;
    };
    Arrays.create = function(rows, cols, value) {
        var arr = Array.from({
            length: rows
        });
        return arr.map(function(x) {
            return Array.from({
                length: cols
            }).fill(value);
        });
    };
    Arrays.createInt32Array = function(rows, cols, value) {
        var arr = Array.from({
            length: rows
        });
        return arr.map(function(x) {
            return Int32Array.from({
                length: cols
            }).fill(value);
        });
    };
    Arrays.equals = function(first, second) {
        if (!first) {
            return false;
        }
        if (!second) {
            return false;
        }
        if (!first.length) {
            return false;
        }
        if (!second.length) {
            return false;
        }
        if (first.length !== second.length) {
            return false;
        }
        for(var i = 0, length_1 = first.length; i < length_1; i++){
            if (first[i] !== second[i]) {
                return false;
            }
        }
        return true;
    };
    Arrays.hashCode = function(a) {
        var e_1, _a;
        if (a === null) {
            return 0;
        }
        var result = 1;
        try {
            for(var a_1 = __values(a), a_1_1 = a_1.next(); !a_1_1.done; a_1_1 = a_1.next()){
                var element = a_1_1.value;
                result = 31 * result + element;
            }
        } catch (e_1_1) {
            e_1 = {
                error: e_1_1
            };
        } finally{
            try {
                if (a_1_1 && !a_1_1.done && (_a = a_1.return)) _a.call(a_1);
            } finally{
                if (e_1) throw e_1.error;
            }
        }
        return result;
    };
    Arrays.fillUint8Array = function(a, value) {
        for(var i = 0; i !== a.length; i++){
            a[i] = value;
        }
    };
    Arrays.copyOf = function(original, newLength) {
        return original.slice(0, newLength);
    };
    Arrays.copyOfUint8Array = function(original, newLength) {
        if (original.length <= newLength) {
            var newArray = new Uint8Array(newLength);
            newArray.set(original);
            return newArray;
        }
        return original.slice(0, newLength);
    };
    Arrays.copyOfRange = function(original, from, to) {
        var newLength = to - from;
        var copy = new Int32Array(newLength);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].arraycopy(original, from, copy, 0, newLength);
        return copy;
    };
    /*
    * Returns the index of of the element in a sorted array or (-n-1) where n is the insertion point
    * for the new element.
    * Parameters:
    *     ar - A sorted array
    *     el - An element to search for
    *     comparator - A comparator function. The function takes two arguments: (a, b) and returns:
    *        a negative number  if a is less than b;
    *        0 if a is equal to b;
    *        a positive number of a is greater than b.
    * The array may contain duplicate elements. If there are more than one equal elements in the array,
    * the returned value can be the index of any one of the equal elements.
    *
    * http://jsfiddle.net/aryzhov/pkfst550/
    */ Arrays.binarySearch = function(ar, el, comparator) {
        if (undefined === comparator) {
            comparator = Arrays.numberComparator;
        }
        var m = 0;
        var n = ar.length - 1;
        while(m <= n){
            var k = n + m >> 1;
            var cmp = comparator(el, ar[k]);
            if (cmp > 0) {
                m = k + 1;
            } else if (cmp < 0) {
                n = k - 1;
            } else {
                return k;
            }
        }
        return -m - 1;
    };
    Arrays.numberComparator = function(a, b) {
        return a - b;
    };
    return Arrays;
}();
const __TURBOPACK__default__export__ = Arrays;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/Integer.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * Ponyfill for Java's Integer class.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var Integer = function() {
    function Integer() {}
    Integer.numberOfTrailingZeros = function(i) {
        var y;
        if (i === 0) return 32;
        var n = 31;
        y = i << 16;
        if (y !== 0) {
            n -= 16;
            i = y;
        }
        y = i << 8;
        if (y !== 0) {
            n -= 8;
            i = y;
        }
        y = i << 4;
        if (y !== 0) {
            n -= 4;
            i = y;
        }
        y = i << 2;
        if (y !== 0) {
            n -= 2;
            i = y;
        }
        return n - (i << 1 >>> 31);
    };
    Integer.numberOfLeadingZeros = function(i) {
        // HD, Figure 5-6
        if (i === 0) {
            return 32;
        }
        var n = 1;
        if (i >>> 16 === 0) {
            n += 16;
            i <<= 16;
        }
        if (i >>> 24 === 0) {
            n += 8;
            i <<= 8;
        }
        if (i >>> 28 === 0) {
            n += 4;
            i <<= 4;
        }
        if (i >>> 30 === 0) {
            n += 2;
            i <<= 2;
        }
        n -= i >>> 31;
        return n;
    };
    Integer.toHexString = function(i) {
        return i.toString(16);
    };
    Integer.toBinaryString = function(intNumber) {
        return String(parseInt(String(intNumber), 2));
    };
    // Returns the number of one-bits in the two's complement binary representation of the specified int value. This function is sometimes referred to as the population count.
    // Returns:
    // the number of one-bits in the two's complement binary representation of the specified int value.
    Integer.bitCount = function(i) {
        // HD, Figure 5-2
        i = i - (i >>> 1 & 0x55555555);
        i = (i & 0x33333333) + (i >>> 2 & 0x33333333);
        i = i + (i >>> 4) & 0x0f0f0f0f;
        i = i + (i >>> 8);
        i = i + (i >>> 16);
        return i & 0x3f;
    };
    Integer.truncDivision = function(dividend, divisor) {
        return Math.trunc(dividend / divisor);
    };
    /**
     * Converts A string to an integer.
     * @param s A string to convert into a number.
     * @param radix A value between 2 and 36 that specifies the base of the number in numString. If this argument is not supplied, strings with a prefix of '0x' are considered hexadecimal. All other strings are considered decimal.
     */ Integer.parseInt = function(num, radix) {
        if (radix === void 0) {
            radix = undefined;
        }
        return parseInt(num, radix);
    };
    Integer.MIN_VALUE_32_BITS = -2147483648;
    Integer.MAX_VALUE = Number.MAX_SAFE_INTEGER;
    return Integer;
}();
const __TURBOPACK__default__export__ = Integer;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/StringEncoding.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$UnsupportedOperationException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/UnsupportedOperationException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$CharacterSetECI$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/CharacterSetECI.js [app-ssr] (ecmascript)");
;
;
/**
 * Responsible for en/decoding strings.
 */ var StringEncoding = function() {
    function StringEncoding() {}
    /**
     * Decodes some Uint8Array to a string format.
     */ StringEncoding.decode = function(bytes, encoding) {
        var encodingName = this.encodingName(encoding);
        if (this.customDecoder) {
            return this.customDecoder(bytes, encodingName);
        }
        // Increases browser support.
        if (typeof TextDecoder === 'undefined' || this.shouldDecodeOnFallback(encodingName)) {
            return this.decodeFallback(bytes, encodingName);
        }
        return new TextDecoder(encodingName).decode(bytes);
    };
    /**
     * Checks if the decoding method should use the fallback for decoding
     * once Node TextDecoder doesn't support all encoding formats.
     *
     * @param encodingName
     */ StringEncoding.shouldDecodeOnFallback = function(encodingName) {
        return !StringEncoding.isBrowser() && encodingName === 'ISO-8859-1';
    };
    /**
     * Encodes some string into a Uint8Array.
     */ StringEncoding.encode = function(s, encoding) {
        var encodingName = this.encodingName(encoding);
        if (this.customEncoder) {
            return this.customEncoder(s, encodingName);
        }
        // Increases browser support.
        if (typeof TextEncoder === 'undefined') {
            return this.encodeFallback(s);
        }
        // TextEncoder only encodes to UTF8 by default as specified by encoding.spec.whatwg.org
        return new TextEncoder().encode(s);
    };
    StringEncoding.isBrowser = function() {
        return "undefined" !== 'undefined' && ({}).toString.call(window) === '[object Window]';
    };
    /**
     * Returns the string value from some encoding character set.
     */ StringEncoding.encodingName = function(encoding) {
        return typeof encoding === 'string' ? encoding : encoding.getName();
    };
    /**
     * Returns character set from some encoding character set.
     */ StringEncoding.encodingCharacterSet = function(encoding) {
        if (encoding instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$CharacterSetECI$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]) {
            return encoding;
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$CharacterSetECI$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getCharacterSetECIByName(encoding);
    };
    /**
     * Runs a fallback for the native decoding funcion.
     */ StringEncoding.decodeFallback = function(bytes, encoding) {
        var characterSet = this.encodingCharacterSet(encoding);
        if (StringEncoding.isDecodeFallbackSupported(characterSet)) {
            var s = '';
            for(var i = 0, length_1 = bytes.length; i < length_1; i++){
                var h = bytes[i].toString(16);
                if (h.length < 2) {
                    h = '0' + h;
                }
                s += '%' + h;
            }
            return decodeURIComponent(s);
        }
        if (characterSet.equals(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$CharacterSetECI$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].UnicodeBigUnmarked)) {
            return String.fromCharCode.apply(null, new Uint16Array(bytes.buffer));
        }
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$UnsupportedOperationException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]("Encoding " + this.encodingName(encoding) + " not supported by fallback.");
    };
    StringEncoding.isDecodeFallbackSupported = function(characterSet) {
        return characterSet.equals(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$CharacterSetECI$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].UTF8) || characterSet.equals(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$CharacterSetECI$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].ISO8859_1) || characterSet.equals(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$CharacterSetECI$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].ASCII);
    };
    /**
     * Runs a fallback for the native encoding funcion.
     *
     * @see https://stackoverflow.com/a/17192845/4367683
     */ StringEncoding.encodeFallback = function(s) {
        var encodedURIstring = btoa(unescape(encodeURIComponent(s)));
        var charList = encodedURIstring.split('');
        var uintArray = [];
        for(var i = 0; i < charList.length; i++){
            uintArray.push(charList[i].charCodeAt(0));
        }
        return new Uint8Array(uintArray);
    };
    return StringEncoding;
}();
const __TURBOPACK__default__export__ = StringEncoding;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/StringBuilder.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/StringUtils.js [app-ssr] (ecmascript)");
;
var StringBuilder = function() {
    function StringBuilder(value) {
        if (value === void 0) {
            value = '';
        }
        this.value = value;
    }
    StringBuilder.prototype.enableDecoding = function(encoding) {
        this.encoding = encoding;
        return this;
    };
    StringBuilder.prototype.append = function(s) {
        if (typeof s === 'string') {
            this.value += s.toString();
        } else if (this.encoding) {
            // use passed format (fromCharCode will return UTF8 encoding)
            this.value += __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].castAsNonUtf8Char(s, this.encoding);
        } else {
            // correctly converts from UTF-8, but not other encodings
            this.value += String.fromCharCode(s);
        }
        return this;
    };
    StringBuilder.prototype.appendChars = function(str, offset, len) {
        for(var i = offset; offset < offset + len; i++){
            this.append(str[i]);
        }
        return this;
    };
    StringBuilder.prototype.length = function() {
        return this.value.length;
    };
    StringBuilder.prototype.charAt = function(n) {
        return this.value.charAt(n);
    };
    StringBuilder.prototype.deleteCharAt = function(n) {
        this.value = this.value.substr(0, n) + this.value.substring(n + 1);
    };
    StringBuilder.prototype.setCharAt = function(n, c) {
        this.value = this.value.substr(0, n) + c + this.value.substr(n + 1);
    };
    StringBuilder.prototype.substring = function(start, end) {
        return this.value.substring(start, end);
    };
    /**
     * @note helper method for RSS Expanded
     */ StringBuilder.prototype.setLengthToZero = function() {
        this.value = '';
    };
    StringBuilder.prototype.toString = function() {
        return this.value;
    };
    StringBuilder.prototype.insert = function(n, c) {
        this.value = this.value.substring(0, n) + c + this.value.substring(n);
    };
    return StringBuilder;
}();
const __TURBOPACK__default__export__ = StringBuilder;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/Float.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * Ponyfill for Java's Float class.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var Float = function() {
    function Float() {}
    /**
     * SincTS has no difference between int and float, there's all numbers,
     * this is used only to polyfill Java code.
     */ Float.floatToIntBits = function(f) {
        return f;
    };
    /**
     * The float max value in JS is the number max value.
     */ Float.MAX_VALUE = Number.MAX_SAFE_INTEGER;
    return Float;
}();
const __TURBOPACK__default__export__ = Float;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/Formatter.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * Java Formatter class polyfill that works in the JS way.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var Formatter = function() {
    function Formatter() {
        this.buffer = '';
    }
    /**
     *
     * @see https://stackoverflow.com/a/13439711/4367683
     *
     * @param str
     * @param arr
     */ Formatter.form = function(str, arr) {
        var i = -1;
        function callback(exp, p0, p1, p2, p3, p4) {
            if (exp === '%%') return '%';
            if (arr[++i] === undefined) return undefined;
            exp = p2 ? parseInt(p2.substr(1)) : undefined;
            var base = p3 ? parseInt(p3.substr(1)) : undefined;
            var val;
            switch(p4){
                case 's':
                    val = arr[i];
                    break;
                case 'c':
                    val = arr[i][0];
                    break;
                case 'f':
                    val = parseFloat(arr[i]).toFixed(exp);
                    break;
                case 'p':
                    val = parseFloat(arr[i]).toPrecision(exp);
                    break;
                case 'e':
                    val = parseFloat(arr[i]).toExponential(exp);
                    break;
                case 'x':
                    val = parseInt(arr[i]).toString(base ? base : 16);
                    break;
                case 'd':
                    val = parseFloat(parseInt(arr[i], base ? base : 10).toPrecision(exp)).toFixed(0);
                    break;
            }
            val = typeof val === 'object' ? JSON.stringify(val) : (+val).toString(base);
            var size = parseInt(p1); /* padding size */ 
            var ch = p1 && p1[0] + '' === '0' ? '0' : ' '; /* isnull? */ 
            while(val.length < size)val = p0 !== undefined ? val + ch : ch + val; /* isminus? */ 
            return val;
        }
        var regex = /%(-)?(0?[0-9]+)?([.][0-9]+)?([#][0-9]+)?([scfpexd%])/g;
        return str.replace(regex, callback);
    };
    /**
     *
     * @param append The new string to append.
     * @param args Argumets values to be formated.
     */ Formatter.prototype.format = function(append) {
        var args = [];
        for(var _i = 1; _i < arguments.length; _i++){
            args[_i - 1] = arguments[_i];
        }
        this.buffer += Formatter.form(append, args);
    };
    /**
     * Returns the Formatter string value.
     */ Formatter.prototype.toString = function() {
        return this.buffer;
    };
    return Formatter;
}();
const __TURBOPACK__default__export__ = Formatter;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/Long.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * Ponyfill for Java's Long class.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var Long = function() {
    function Long() {}
    /**
     * Parses a string to a number, since JS has no really Int64.
     *
     * @param num Numeric string.
     * @param radix Destination radix.
     */ Long.parseLong = function(num, radix) {
        if (radix === void 0) {
            radix = undefined;
        }
        return parseInt(num, radix);
    };
    return Long;
}();
const __TURBOPACK__default__export__ = Long;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/OutputStream.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IndexOutOfBoundsException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/IndexOutOfBoundsException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NullPointerException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NullPointerException.js [app-ssr] (ecmascript)");
;
;
/*
 * Copyright (c) 1994, 2004, Oracle and/or its affiliates. All rights reserved.
 * DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS FILE HEADER.
 *
 * This code is free software; you can redistribute it and/or modify it
 * under the terms of the GNU General Public License version 2 only, as
 * published by the Free Software Foundation.  Oracle designates this
 * particular file as subject to the "Classpath" exception as provided
 * by Oracle in the LICENSE file that accompanied this code.
 *
 * This code is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
 * FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
 * version 2 for more details (a copy is included in the LICENSE file that
 * accompanied this code).
 *
 * You should have received a copy of the GNU General Public License version
 * 2 along with this work; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301 USA.
 *
 * Please contact Oracle, 500 Oracle Parkway, Redwood Shores, CA 94065 USA
 * or visit www.oracle.com if you need additional information or have any
 * questions.
 */ // package java.io;
/**
 * This abstract class is the superclass of all classes representing
 * an output stream of bytes. An output stream accepts output bytes
 * and sends them to some sink.
 * <p>
 * Applications that need to define a subclass of
 * <code>OutputStream</code> must always provide at least a method
 * that writes one byte of output.
 *
 * <AUTHOR> van Hoff
 * @see     java.io.BufferedOutputStream
 * @see     java.io.ByteArrayOutputStream
 * @see     java.io.DataOutputStream
 * @see     java.io.FilterOutputStream
 * @see     java.io.InputStream
 * @see     java.io.OutputStream#write(int)
 * @since   JDK1.0
 */ var OutputStream /*implements Closeable, Flushable*/  = function() {
    function OutputStream() {}
    /**
     * Writes <code>b.length</code> bytes from the specified byte array
     * to this output stream. The general contract for <code>write(b)</code>
     * is that it should have exactly the same effect as the call
     * <code>write(b, 0, b.length)</code>.
     *
     * @param      b   the data.
     * @exception  IOException  if an I/O error occurs.
     * @see        java.io.OutputStream#write(byte[], int, int)
     */ OutputStream.prototype.writeBytes = function(b) {
        this.writeBytesOffset(b, 0, b.length);
    };
    /**
     * Writes <code>len</code> bytes from the specified byte array
     * starting at offset <code>off</code> to this output stream.
     * The general contract for <code>write(b, off, len)</code> is that
     * some of the bytes in the array <code>b</code> are written to the
     * output stream in order; element <code>b[off]</code> is the first
     * byte written and <code>b[off+len-1]</code> is the last byte written
     * by this operation.
     * <p>
     * The <code>write</code> method of <code>OutputStream</code> calls
     * the write method of one argument on each of the bytes to be
     * written out. Subclasses are encouraged to override this method and
     * provide a more efficient implementation.
     * <p>
     * If <code>b</code> is <code>null</code>, a
     * <code>NullPointerException</code> is thrown.
     * <p>
     * If <code>off</code> is negative, or <code>len</code> is negative, or
     * <code>off+len</code> is greater than the length of the array
     * <code>b</code>, then an <tt>IndexOutOfBoundsException</tt> is thrown.
     *
     * @param      b     the data.
     * @param      off   the start offset in the data.
     * @param      len   the number of bytes to write.
     * @exception  IOException  if an I/O error occurs. In particular,
     *             an <code>IOException</code> is thrown if the output
     *             stream is closed.
     */ OutputStream.prototype.writeBytesOffset = function(b, off, len) {
        if (b == null) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NullPointerException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        } else if (off < 0 || off > b.length || len < 0 || off + len > b.length || off + len < 0) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IndexOutOfBoundsException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        } else if (len === 0) {
            return;
        }
        for(var i = 0; i < len; i++){
            this.write(b[off + i]);
        }
    };
    /**
     * Flushes this output stream and forces any buffered output bytes
     * to be written out. The general contract of <code>flush</code> is
     * that calling it is an indication that, if any bytes previously
     * written have been buffered by the implementation of the output
     * stream, such bytes should immediately be written to their
     * intended destination.
     * <p>
     * If the intended destination of this stream is an abstraction provided by
     * the underlying operating system, for example a file, then flushing the
     * stream guarantees only that bytes previously written to the stream are
     * passed to the operating system for writing; it does not guarantee that
     * they are actually written to a physical device such as a disk drive.
     * <p>
     * The <code>flush</code> method of <code>OutputStream</code> does nothing.
     *
     * @exception  IOException  if an I/O error occurs.
     */ OutputStream.prototype.flush = function() {};
    /**
     * Closes this output stream and releases any system resources
     * associated with this stream. The general contract of <code>close</code>
     * is that it closes the output stream. A closed stream cannot perform
     * output operations and cannot be reopened.
     * <p>
     * The <code>close</code> method of <code>OutputStream</code> does nothing.
     *
     * @exception  IOException  if an I/O error occurs.
     */ OutputStream.prototype.close = function() {};
    return OutputStream;
}();
const __TURBOPACK__default__export__ = OutputStream;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/ByteArrayOutputStream.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright (c) 1994, 2010, Oracle and/or its affiliates. All rights reserved.
 * DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS FILE HEADER.
 *
 * This code is free software; you can redistribute it and/or modify it
 * under the terms of the GNU General Public License version 2 only, as
 * published by the Free Software Foundation.  Oracle designates this
 * particular file as subject to the "Classpath" exception as provided
 * by Oracle in the LICENSE file that accompanied this code.
 *
 * This code is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
 * FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
 * version 2 for more details (a copy is included in the LICENSE file that
 * accompanied this code).
 *
 * You should have received a copy of the GNU General Public License version
 * 2 along with this work; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301 USA.
 *
 * Please contact Oracle, 500 Oracle Parkway, Redwood Shores, CA 94065 USA
 * or visit www.oracle.com if you need additional information or have any
 * questions.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
// package java.io;
// import java.util.Arrays;
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Arrays$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/Arrays.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$OutputStream$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/OutputStream.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/Integer.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/IllegalArgumentException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$OutOfMemoryError$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/OutOfMemoryError.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/System.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IndexOutOfBoundsException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/IndexOutOfBoundsException.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
;
;
;
;
;
/**
 * This class implements an output stream in which the data is
 * written into a byte array. The buffer automatically grows as data
 * is written to it.
 * The data can be retrieved using <code>toByteArray()</code> and
 * <code>toString()</code>.
 * <p>
 * Closing a <tt>ByteArrayOutputStream</tt> has no effect. The methods in
 * this class can be called after the stream has been closed without
 * generating an <tt>IOException</tt>.
 *
 * <AUTHOR> van Hoff
 * @since   JDK1.0
 */ var ByteArrayOutputStream = function(_super) {
    __extends(ByteArrayOutputStream, _super);
    /**
     * Creates a new byte array output stream. The buffer capacity is
     * initially 32 bytes, though its size increases if necessary.
     */ // public constructor() {
    //     this(32);
    // }
    /**
     * Creates a new byte array output stream, with a buffer capacity of
     * the specified size, in bytes.
     *
     * @param   size   the initial size.
     * @exception  IllegalArgumentException if size is negative.
     */ function ByteArrayOutputStream(size) {
        if (size === void 0) {
            size = 32;
        }
        var _this = _super.call(this) || this;
        /**
         * The number of valid bytes in the buffer.
         */ _this.count = 0;
        if (size < 0) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('Negative initial size: ' + size);
        }
        _this.buf = new Uint8Array(size);
        return _this;
    }
    /**
     * Increases the capacity if necessary to ensure that it can hold
     * at least the number of elements specified by the minimum
     * capacity argument.
     *
     * @param minCapacity the desired minimum capacity
     * @throws OutOfMemoryError if {@code minCapacity < 0}.  This is
     * interpreted as a request for the unsatisfiably large capacity
     * {@code (long) Integer.MAX_VALUE + (minCapacity - Integer.MAX_VALUE)}.
     */ ByteArrayOutputStream.prototype.ensureCapacity = function(minCapacity) {
        // overflow-conscious code
        if (minCapacity - this.buf.length > 0) this.grow(minCapacity);
    };
    /**
     * Increases the capacity to ensure that it can hold at least the
     * number of elements specified by the minimum capacity argument.
     *
     * @param minCapacity the desired minimum capacity
     */ ByteArrayOutputStream.prototype.grow = function(minCapacity) {
        // overflow-conscious code
        var oldCapacity = this.buf.length;
        var newCapacity = oldCapacity << 1;
        if (newCapacity - minCapacity < 0) newCapacity = minCapacity;
        if (newCapacity < 0) {
            if (minCapacity < 0) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$OutOfMemoryError$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
            newCapacity = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].MAX_VALUE;
        }
        this.buf = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Arrays$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].copyOfUint8Array(this.buf, newCapacity);
    };
    /**
     * Writes the specified byte to this byte array output stream.
     *
     * @param   b   the byte to be written.
     */ ByteArrayOutputStream.prototype.write = function(b) {
        this.ensureCapacity(this.count + 1);
        this.buf[this.count] = /*(byte)*/ b;
        this.count += 1;
    };
    /**
     * Writes <code>len</code> bytes from the specified byte array
     * starting at offset <code>off</code> to this byte array output stream.
     *
     * @param   b     the data.
     * @param   off   the start offset in the data.
     * @param   len   the number of bytes to write.
     */ ByteArrayOutputStream.prototype.writeBytesOffset = function(b, off, len) {
        if (off < 0 || off > b.length || len < 0 || off + len - b.length > 0) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IndexOutOfBoundsException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        }
        this.ensureCapacity(this.count + len);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].arraycopy(b, off, this.buf, this.count, len);
        this.count += len;
    };
    /**
     * Writes the complete contents of this byte array output stream to
     * the specified output stream argument, as if by calling the output
     * stream's write method using <code>out.write(buf, 0, count)</code>.
     *
     * @param      out   the output stream to which to write the data.
     * @exception  IOException  if an I/O error occurs.
     */ ByteArrayOutputStream.prototype.writeTo = function(out) {
        out.writeBytesOffset(this.buf, 0, this.count);
    };
    /**
     * Resets the <code>count</code> field of this byte array output
     * stream to zero, so that all currently accumulated output in the
     * output stream is discarded. The output stream can be used again,
     * reusing the already allocated buffer space.
     *
     * @see     java.io.ByteArrayInputStream#count
     */ ByteArrayOutputStream.prototype.reset = function() {
        this.count = 0;
    };
    /**
     * Creates a newly allocated byte array. Its size is the current
     * size of this output stream and the valid contents of the buffer
     * have been copied into it.
     *
     * @return  the current contents of this output stream, as a byte array.
     * @see     java.io.ByteArrayOutputStream#size()
     */ ByteArrayOutputStream.prototype.toByteArray = function() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Arrays$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].copyOfUint8Array(this.buf, this.count);
    };
    /**
     * Returns the current size of the buffer.
     *
     * @return  the value of the <code>count</code> field, which is the number
     *          of valid bytes in this output stream.
     * @see     java.io.ByteArrayOutputStream#count
     */ ByteArrayOutputStream.prototype.size = function() {
        return this.count;
    };
    ByteArrayOutputStream.prototype.toString = function(param) {
        if (!param) {
            return this.toString_void();
        }
        if (typeof param === 'string') {
            return this.toString_string(param);
        }
        return this.toString_number(param);
    };
    /**
     * Converts the buffer's contents into a string decoding bytes using the
     * platform's default character set. The length of the new <tt>String</tt>
     * is a function of the character set, and hence may not be equal to the
     * size of the buffer.
     *
     * <p> This method always replaces malformed-input and unmappable-character
     * sequences with the default replacement string for the platform's
     * default character set. The {@linkplain java.nio.charset.CharsetDecoder}
     * class should be used when more control over the decoding process is
     * required.
     *
     * @return String decoded from the buffer's contents.
     * @since  JDK1.1
     */ ByteArrayOutputStream.prototype.toString_void = function() {
        return new String(this.buf /*, 0, this.count*/ ).toString();
    };
    /**
     * Converts the buffer's contents into a string by decoding the bytes using
     * the specified {@link java.nio.charset.Charset charsetName}. The length of
     * the new <tt>String</tt> is a function of the charset, and hence may not be
     * equal to the length of the byte array.
     *
     * <p> This method always replaces malformed-input and unmappable-character
     * sequences with this charset's default replacement string. The {@link
     * java.nio.charset.CharsetDecoder} class should be used when more control
     * over the decoding process is required.
     *
     * @param  charsetName  the name of a supported
     *              {@linkplain java.nio.charset.Charset </code>charset<code>}
     * @return String decoded from the buffer's contents.
     * @exception  UnsupportedEncodingException
     *             If the named charset is not supported
     * @since   JDK1.1
     */ ByteArrayOutputStream.prototype.toString_string = function(charsetName) {
        return new String(this.buf /*, 0, this.count, charsetName*/ ).toString();
    };
    /**
     * Creates a newly allocated string. Its size is the current size of
     * the output stream and the valid contents of the buffer have been
     * copied into it. Each character <i>c</i> in the resulting string is
     * constructed from the corresponding element <i>b</i> in the byte
     * array such that:
     * <blockquote><pre>
     *     c == (char)(((hibyte &amp; 0xff) &lt;&lt; 8) | (b &amp; 0xff))
     * </pre></blockquote>
     *
     * @deprecated This method does not properly convert bytes into characters.
     * As of JDK&nbsp;1.1, the preferred way to do this is via the
     * <code>toString(String enc)</code> method, which takes an encoding-name
     * argument, or the <code>toString()</code> method, which uses the
     * platform's default character encoding.
     *
     * @param      hibyte    the high byte of each resulting Unicode character.
     * @return     the current contents of the output stream, as a string.
     * @see        java.io.ByteArrayOutputStream#size()
     * @see        java.io.ByteArrayOutputStream#toString(String)
     * @see        java.io.ByteArrayOutputStream#toString()
     */ // @Deprecated
    ByteArrayOutputStream.prototype.toString_number = function(hibyte) {
        return new String(this.buf /*, hibyte, 0, this.count*/ ).toString();
    };
    /**
     * Closing a <tt>ByteArrayOutputStream</tt> has no effect. The methods in
     * this class can be called after the stream has been closed without
     * generating an <tt>IOException</tt>.
     * <p>
     *
     * @throws IOException
     */ ByteArrayOutputStream.prototype.close = function() {};
    return ByteArrayOutputStream;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$OutputStream$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = ByteArrayOutputStream;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/Charset.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$CharacterSetECI$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/CharacterSetECI.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
/**
 * Just to make a shortcut between Java code and TS code.
 */ var Charset = function(_super) {
    __extends(Charset, _super);
    function Charset() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    Charset.forName = function(name) {
        return this.getCharacterSetECIByName(name);
    };
    return Charset;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$CharacterSetECI$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = Charset;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/StandardCharsets.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$CharacterSetECI$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/CharacterSetECI.js [app-ssr] (ecmascript)");
;
/**
 * Just to make a shortcut between Java code and TS code.
 */ var StandardCharsets = function() {
    function StandardCharsets() {}
    StandardCharsets.ISO_8859_1 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$CharacterSetECI$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].ISO8859_1;
    return StandardCharsets;
}();
const __TURBOPACK__default__export__ = StandardCharsets;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/Collections.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var Collections = function() {
    function Collections() {}
    /**
     * The singletonList(T) method is used to return an immutable list containing only the specified object.
     */ Collections.singletonList = function(item) {
        return [
            item
        ];
    };
    /**
     * The min(Collection<? extends T>, Comparator<? super T>) method is used to return the minimum element of the given collection, according to the order induced by the specified comparator.
     */ Collections.min = function(collection, comparator) {
        return collection.sort(comparator)[0];
    };
    return Collections;
}();
const __TURBOPACK__default__export__ = Collections;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/IndexOutOfBoundsException.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Exception$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Exception.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
/**
 * Custom Error class of type Exception.
 */ var IndexOutOfBoundsException = function(_super) {
    __extends(IndexOutOfBoundsException, _super);
    function IndexOutOfBoundsException() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    IndexOutOfBoundsException.kind = 'IndexOutOfBoundsException';
    return IndexOutOfBoundsException;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Exception$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = IndexOutOfBoundsException;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ArrayIndexOutOfBoundsException.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IndexOutOfBoundsException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/IndexOutOfBoundsException.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
/**
 * Custom Error class of type Exception.
 */ var ArrayIndexOutOfBoundsException = function(_super) {
    __extends(ArrayIndexOutOfBoundsException, _super);
    function ArrayIndexOutOfBoundsException(index, message) {
        if (index === void 0) {
            index = undefined;
        }
        if (message === void 0) {
            message = undefined;
        }
        var _this = _super.call(this, message) || this;
        _this.index = index;
        _this.message = message;
        return _this;
    }
    ArrayIndexOutOfBoundsException.kind = 'ArrayIndexOutOfBoundsException';
    return ArrayIndexOutOfBoundsException;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IndexOutOfBoundsException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = ArrayIndexOutOfBoundsException;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/DecodeHintType.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2009 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /*namespace com.google.zxing {*/ /**
 * Encapsulates a type of hint that a caller may pass to a barcode reader to help it
 * more quickly or accurately decode it. It is up to implementations to decide what,
 * if anything, to do with the information that is supplied.
 *
 * <AUTHOR> Owen
 * <AUTHOR> (Daniel Switkin)
 * @see Reader#decode(BinaryBitmap,java.util.Map)
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var DecodeHintType;
(function(DecodeHintType) {
    /**
     * Unspecified, application-specific hint. Maps to an unspecified {@link Object}.
     */ DecodeHintType[DecodeHintType["OTHER"] = 0] = "OTHER"; /*(Object.class)*/ 
    /**
     * Image is a pure monochrome image of a barcode. Doesn't matter what it maps to;
     * use {@link Boolean#TRUE}.
     */ DecodeHintType[DecodeHintType["PURE_BARCODE"] = 1] = "PURE_BARCODE"; /*(Void.class)*/ 
    /**
     * Image is known to be of one of a few possible formats.
     * Maps to a {@link List} of {@link BarcodeFormat}s.
     */ DecodeHintType[DecodeHintType["POSSIBLE_FORMATS"] = 2] = "POSSIBLE_FORMATS"; /*(List.class)*/ 
    /**
     * Spend more time to try to find a barcode; optimize for accuracy, not speed.
     * Doesn't matter what it maps to; use {@link Boolean#TRUE}.
     */ DecodeHintType[DecodeHintType["TRY_HARDER"] = 3] = "TRY_HARDER"; /*(Void.class)*/ 
    /**
     * Specifies what character encoding to use when decoding, where applicable (type String)
     */ DecodeHintType[DecodeHintType["CHARACTER_SET"] = 4] = "CHARACTER_SET"; /*(String.class)*/ 
    /**
     * Allowed lengths of encoded data -- reject anything else. Maps to an {@code Int32Array}.
     */ DecodeHintType[DecodeHintType["ALLOWED_LENGTHS"] = 5] = "ALLOWED_LENGTHS"; /*(Int32Array.class)*/ 
    /**
     * Assume Code 39 codes employ a check digit. Doesn't matter what it maps to;
     * use {@link Boolean#TRUE}.
     */ DecodeHintType[DecodeHintType["ASSUME_CODE_39_CHECK_DIGIT"] = 6] = "ASSUME_CODE_39_CHECK_DIGIT"; /*(Void.class)*/ 
    /**
     * Enable extended mode for Code 39 codes. Doesn't matter what it maps to;
     * use {@link Boolean#TRUE}.
     */ DecodeHintType[DecodeHintType["ENABLE_CODE_39_EXTENDED_MODE"] = 7] = "ENABLE_CODE_39_EXTENDED_MODE"; /*(Void.class)*/ 
    /**
     * Assume the barcode is being processed as a GS1 barcode, and modify behavior as needed.
     * For example this affects FNC1 handling for Code 128 (aka GS1-128). Doesn't matter what it maps to;
     * use {@link Boolean#TRUE}.
     */ DecodeHintType[DecodeHintType["ASSUME_GS1"] = 8] = "ASSUME_GS1"; /*(Void.class)*/ 
    /**
     * If true, return the start and end digits in a Codabar barcode instead of stripping them. They
     * are alpha, whereas the rest are numeric. By default, they are stripped, but this causes them
     * to not be. Doesn't matter what it maps to; use {@link Boolean#TRUE}.
     */ DecodeHintType[DecodeHintType["RETURN_CODABAR_START_END"] = 9] = "RETURN_CODABAR_START_END"; /*(Void.class)*/ 
    /**
     * The caller needs to be notified via callback when a possible {@link ResultPoint}
     * is found. Maps to a {@link ResultPointCallback}.
     */ DecodeHintType[DecodeHintType["NEED_RESULT_POINT_CALLBACK"] = 10] = "NEED_RESULT_POINT_CALLBACK"; /*(ResultPointCallback.class)*/ 
    /**
     * Allowed extension lengths for EAN or UPC barcodes. Other formats will ignore this.
     * Maps to an {@code Int32Array} of the allowed extension lengths, for example [2], [5], or [2, 5].
     * If it is optional to have an extension, do not set this hint. If this is set,
     * and a UPC or EAN barcode is found but an extension is not, then no result will be returned
     * at all.
     */ DecodeHintType[DecodeHintType["ALLOWED_EAN_EXTENSIONS"] = 11] = "ALLOWED_EAN_EXTENSIONS"; /*(Int32Array.class)*/ 
// End of enumeration values.
/**
     * Data type the hint is expecting.
     * Among the possible values the {@link Void} stands out as being used for
     * hints that do not expect a value to be supplied (flag hints). Such hints
     * will possibly have their value ignored, or replaced by a
     * {@link Boolean#TRUE}. Hint suppliers should probably use
     * {@link Boolean#TRUE} as directed by the actual hint documentation.
     */ // private valueType: Class<?>
// DecodeHintType(valueType: Class<?>) {
//   this.valueType = valueType
// }
// public getValueType(): Class<?> {
//   return valueType
// }
})(DecodeHintType || (DecodeHintType = {}));
const __TURBOPACK__default__export__ = DecodeHintType;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/FormatException.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Exception$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Exception.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
/**
 * Custom Error class of type Exception.
 */ var FormatException = function(_super) {
    __extends(FormatException, _super);
    function FormatException() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    FormatException.getFormatInstance = function() {
        return new FormatException();
    };
    FormatException.kind = 'FormatException';
    return FormatException;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Exception$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = FormatException;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/UnsupportedOperationException.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Exception$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Exception.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
/**
 * Custom Error class of type Exception.
 */ var UnsupportedOperationException = function(_super) {
    __extends(UnsupportedOperationException, _super);
    function UnsupportedOperationException() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    UnsupportedOperationException.kind = 'UnsupportedOperationException';
    return UnsupportedOperationException;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Exception$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = UnsupportedOperationException;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NotFoundException.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Exception$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Exception.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
/**
 * Custom Error class of type Exception.
 */ var NotFoundException = function(_super) {
    __extends(NotFoundException, _super);
    function NotFoundException() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    NotFoundException.getNotFoundInstance = function() {
        return new NotFoundException();
    };
    NotFoundException.kind = 'NotFoundException';
    return NotFoundException;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Exception$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = NotFoundException;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/LuminanceSource.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2009 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/StringBuilder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$UnsupportedOperationException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/UnsupportedOperationException.js [app-ssr] (ecmascript)");
;
;
/*namespace com.google.zxing {*/ /**
 * The purpose of this class hierarchy is to abstract different bitmap implementations across
 * platforms into a standard interface for requesting greyscale luminance values. The interface
 * only provides immutable methods; therefore crop and rotation create copies. This is to ensure
 * that one Reader does not modify the original luminance source and leave it in an unknown state
 * for other Readers in the chain.
 *
 * <AUTHOR> (Daniel Switkin)
 */ var LuminanceSource = function() {
    function LuminanceSource(width /*int*/ , height /*int*/ ) {
        this.width = width;
        this.height = height;
    }
    /**
     * @return The width of the bitmap.
     */ LuminanceSource.prototype.getWidth = function() {
        return this.width;
    };
    /**
     * @return The height of the bitmap.
     */ LuminanceSource.prototype.getHeight = function() {
        return this.height;
    };
    /**
     * @return Whether this subclass supports cropping.
     */ LuminanceSource.prototype.isCropSupported = function() {
        return false;
    };
    /**
     * Returns a new object with cropped image data. Implementations may keep a reference to the
     * original data rather than a copy. Only callable if isCropSupported() is true.
     *
     * @param left The left coordinate, which must be in [0,getWidth())
     * @param top The top coordinate, which must be in [0,getHeight())
     * @param width The width of the rectangle to crop.
     * @param height The height of the rectangle to crop.
     * @return A cropped version of this object.
     */ LuminanceSource.prototype.crop = function(left /*int*/ , top /*int*/ , width /*int*/ , height /*int*/ ) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$UnsupportedOperationException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('This luminance source does not support cropping.');
    };
    /**
     * @return Whether this subclass supports counter-clockwise rotation.
     */ LuminanceSource.prototype.isRotateSupported = function() {
        return false;
    };
    /**
     * Returns a new object with rotated image data by 90 degrees counterclockwise.
     * Only callable if {@link #isRotateSupported()} is true.
     *
     * @return A rotated version of this object.
     */ LuminanceSource.prototype.rotateCounterClockwise = function() {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$UnsupportedOperationException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('This luminance source does not support rotation by 90 degrees.');
    };
    /**
     * Returns a new object with rotated image data by 45 degrees counterclockwise.
     * Only callable if {@link #isRotateSupported()} is true.
     *
     * @return A rotated version of this object.
     */ LuminanceSource.prototype.rotateCounterClockwise45 = function() {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$UnsupportedOperationException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('This luminance source does not support rotation by 45 degrees.');
    };
    /*@Override*/ LuminanceSource.prototype.toString = function() {
        var row = new Uint8ClampedArray(this.width);
        var result = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        for(var y = 0; y < this.height; y++){
            var sourceRow = this.getRow(y, row);
            for(var x = 0; x < this.width; x++){
                var luminance = sourceRow[x] & 0xFF;
                var c = void 0;
                if (luminance < 0x40) {
                    c = '#';
                } else if (luminance < 0x80) {
                    c = '+';
                } else if (luminance < 0xC0) {
                    c = '.';
                } else {
                    c = ' ';
                }
                result.append(c);
            }
            result.append('\n');
        }
        return result.toString();
    };
    return LuminanceSource;
}();
const __TURBOPACK__default__export__ = LuminanceSource;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/InvertedLuminanceSource.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2009 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$LuminanceSource$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/LuminanceSource.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
/*namespace com.google.zxing {*/ /**
 * A wrapper implementation of {@link LuminanceSource} which inverts the luminances it returns -- black becomes
 * white and vice versa, and each value becomes (255-value).
 *
 * <AUTHOR> Owen
 */ var InvertedLuminanceSource = function(_super) {
    __extends(InvertedLuminanceSource, _super);
    function InvertedLuminanceSource(delegate) {
        var _this = _super.call(this, delegate.getWidth(), delegate.getHeight()) || this;
        _this.delegate = delegate;
        return _this;
    }
    /*@Override*/ InvertedLuminanceSource.prototype.getRow = function(y /*int*/ , row) {
        var sourceRow = this.delegate.getRow(y, row);
        var width = this.getWidth();
        for(var i = 0; i < width; i++){
            sourceRow[i] = 255 - (sourceRow[i] & 0xFF);
        }
        return sourceRow;
    };
    /*@Override*/ InvertedLuminanceSource.prototype.getMatrix = function() {
        var matrix = this.delegate.getMatrix();
        var length = this.getWidth() * this.getHeight();
        var invertedMatrix = new Uint8ClampedArray(length);
        for(var i = 0; i < length; i++){
            invertedMatrix[i] = 255 - (matrix[i] & 0xFF);
        }
        return invertedMatrix;
    };
    /*@Override*/ InvertedLuminanceSource.prototype.isCropSupported = function() {
        return this.delegate.isCropSupported();
    };
    /*@Override*/ InvertedLuminanceSource.prototype.crop = function(left /*int*/ , top /*int*/ , width /*int*/ , height /*int*/ ) {
        return new InvertedLuminanceSource(this.delegate.crop(left, top, width, height));
    };
    /*@Override*/ InvertedLuminanceSource.prototype.isRotateSupported = function() {
        return this.delegate.isRotateSupported();
    };
    /**
     * @return original delegate {@link LuminanceSource} since invert undoes itself
     */ /*@Override*/ InvertedLuminanceSource.prototype.invert = function() {
        return this.delegate;
    };
    /*@Override*/ InvertedLuminanceSource.prototype.rotateCounterClockwise = function() {
        return new InvertedLuminanceSource(this.delegate.rotateCounterClockwise());
    };
    /*@Override*/ InvertedLuminanceSource.prototype.rotateCounterClockwise45 = function() {
        return new InvertedLuminanceSource(this.delegate.rotateCounterClockwise45());
    };
    return InvertedLuminanceSource;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$LuminanceSource$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = InvertedLuminanceSource;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Result.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/System.js [app-ssr] (ecmascript)");
;
/**
 * <p>Encapsulates the result of decoding a barcode within an image.</p>
 *
 * <AUTHOR> Owen
 */ var Result = function() {
    // public constructor(private text: string,
    //               Uint8Array rawBytes,
    //               ResultPoconst resultPoints: Int32Array,
    //               BarcodeFormat format) {
    //   this(text, rawBytes, resultPoints, format, System.currentTimeMillis())
    // }
    // public constructor(text: string,
    //               Uint8Array rawBytes,
    //               ResultPoconst resultPoints: Int32Array,
    //               BarcodeFormat format,
    //               long timestamp) {
    //   this(text, rawBytes, rawBytes == null ? 0 : 8 * rawBytes.length,
    //        resultPoints, format, timestamp)
    // }
    function Result(text, rawBytes, numBits, resultPoints, format, timestamp) {
        if (numBits === void 0) {
            numBits = rawBytes == null ? 0 : 8 * rawBytes.length;
        }
        if (timestamp === void 0) {
            timestamp = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].currentTimeMillis();
        }
        this.text = text;
        this.rawBytes = rawBytes;
        this.numBits = numBits;
        this.resultPoints = resultPoints;
        this.format = format;
        this.timestamp = timestamp;
        this.text = text;
        this.rawBytes = rawBytes;
        if (undefined === numBits || null === numBits) {
            this.numBits = rawBytes === null || rawBytes === undefined ? 0 : 8 * rawBytes.length;
        } else {
            this.numBits = numBits;
        }
        this.resultPoints = resultPoints;
        this.format = format;
        this.resultMetadata = null;
        if (undefined === timestamp || null === timestamp) {
            this.timestamp = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].currentTimeMillis();
        } else {
            this.timestamp = timestamp;
        }
    }
    /**
     * @return raw text encoded by the barcode
     */ Result.prototype.getText = function() {
        return this.text;
    };
    /**
     * @return raw bytes encoded by the barcode, if applicable, otherwise {@code null}
     */ Result.prototype.getRawBytes = function() {
        return this.rawBytes;
    };
    /**
     * @return how many bits of {@link #getRawBytes()} are valid; typically 8 times its length
     * @since 3.3.0
     */ Result.prototype.getNumBits = function() {
        return this.numBits;
    };
    /**
     * @return points related to the barcode in the image. These are typically points
     *         identifying finder patterns or the corners of the barcode. The exact meaning is
     *         specific to the type of barcode that was decoded.
     */ Result.prototype.getResultPoints = function() {
        return this.resultPoints;
    };
    /**
     * @return {@link BarcodeFormat} representing the format of the barcode that was decoded
     */ Result.prototype.getBarcodeFormat = function() {
        return this.format;
    };
    /**
     * @return {@link Map} mapping {@link ResultMetadataType} keys to values. May be
     *   {@code null}. This contains optional metadata about what was detected about the barcode,
     *   like orientation.
     */ Result.prototype.getResultMetadata = function() {
        return this.resultMetadata;
    };
    Result.prototype.putMetadata = function(type, value) {
        if (this.resultMetadata === null) {
            this.resultMetadata = new Map();
        }
        this.resultMetadata.set(type, value);
    };
    Result.prototype.putAllMetadata = function(metadata) {
        if (metadata !== null) {
            if (this.resultMetadata === null) {
                this.resultMetadata = metadata;
            } else {
                this.resultMetadata = new Map(metadata);
            }
        }
    };
    Result.prototype.addResultPoints = function(newPoints) {
        var oldPoints = this.resultPoints;
        if (oldPoints === null) {
            this.resultPoints = newPoints;
        } else if (newPoints !== null && newPoints.length > 0) {
            var allPoints = new Array(oldPoints.length + newPoints.length);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].arraycopy(oldPoints, 0, allPoints, 0, oldPoints.length);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].arraycopy(newPoints, 0, allPoints, oldPoints.length, newPoints.length);
            this.resultPoints = allPoints;
        }
    };
    Result.prototype.getTimestamp = function() {
        return this.timestamp;
    };
    /*@Override*/ Result.prototype.toString = function() {
        return this.text;
    };
    return Result;
}();
const __TURBOPACK__default__export__ = Result;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/BarcodeFormat.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Direct port to TypeScript of ZXing by Adrian Toșcă
 */ /*
 * Copyright 2009 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /*namespace com.google.zxing {*/ /**
 * Enumerates barcode formats known to this package. Please keep alphabetized.
 *
 * <AUTHOR> Owen
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var BarcodeFormat;
(function(BarcodeFormat) {
    /** Aztec 2D barcode format. */ BarcodeFormat[BarcodeFormat["AZTEC"] = 0] = "AZTEC";
    /** CODABAR 1D format. */ BarcodeFormat[BarcodeFormat["CODABAR"] = 1] = "CODABAR";
    /** Code 39 1D format. */ BarcodeFormat[BarcodeFormat["CODE_39"] = 2] = "CODE_39";
    /** Code 93 1D format. */ BarcodeFormat[BarcodeFormat["CODE_93"] = 3] = "CODE_93";
    /** Code 128 1D format. */ BarcodeFormat[BarcodeFormat["CODE_128"] = 4] = "CODE_128";
    /** Data Matrix 2D barcode format. */ BarcodeFormat[BarcodeFormat["DATA_MATRIX"] = 5] = "DATA_MATRIX";
    /** EAN-8 1D format. */ BarcodeFormat[BarcodeFormat["EAN_8"] = 6] = "EAN_8";
    /** EAN-13 1D format. */ BarcodeFormat[BarcodeFormat["EAN_13"] = 7] = "EAN_13";
    /** ITF (Interleaved Two of Five) 1D format. */ BarcodeFormat[BarcodeFormat["ITF"] = 8] = "ITF";
    /** MaxiCode 2D barcode format. */ BarcodeFormat[BarcodeFormat["MAXICODE"] = 9] = "MAXICODE";
    /** PDF417 format. */ BarcodeFormat[BarcodeFormat["PDF_417"] = 10] = "PDF_417";
    /** QR Code 2D barcode format. */ BarcodeFormat[BarcodeFormat["QR_CODE"] = 11] = "QR_CODE";
    /** RSS 14 */ BarcodeFormat[BarcodeFormat["RSS_14"] = 12] = "RSS_14";
    /** RSS EXPANDED */ BarcodeFormat[BarcodeFormat["RSS_EXPANDED"] = 13] = "RSS_EXPANDED";
    /** UPC-A 1D format. */ BarcodeFormat[BarcodeFormat["UPC_A"] = 14] = "UPC_A";
    /** UPC-E 1D format. */ BarcodeFormat[BarcodeFormat["UPC_E"] = 15] = "UPC_E";
    /** UPC/EAN extension format. Not a stand-alone format. */ BarcodeFormat[BarcodeFormat["UPC_EAN_EXTENSION"] = 16] = "UPC_EAN_EXTENSION";
})(BarcodeFormat || (BarcodeFormat = {}));
const __TURBOPACK__default__export__ = BarcodeFormat;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ResultMetadataType.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2008 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /*namespace com.google.zxing {*/ /**
 * Represents some type of metadata about the result of the decoding that the decoder
 * wishes to communicate back to the caller.
 *
 * <AUTHOR> Owen
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var ResultMetadataType;
(function(ResultMetadataType) {
    /**
     * Unspecified, application-specific metadata. Maps to an unspecified {@link Object}.
     */ ResultMetadataType[ResultMetadataType["OTHER"] = 0] = "OTHER";
    /**
     * Denotes the likely approximate orientation of the barcode in the image. This value
     * is given as degrees rotated clockwise from the normal, upright orientation.
     * For example a 1D barcode which was found by reading top-to-bottom would be
     * said to have orientation "90". This key maps to an {@link Integer} whose
     * value is in the range [0,360).
     */ ResultMetadataType[ResultMetadataType["ORIENTATION"] = 1] = "ORIENTATION";
    /**
     * <p>2D barcode formats typically encode text, but allow for a sort of 'byte mode'
     * which is sometimes used to encode binary data. While {@link Result} makes available
     * the complete raw bytes in the barcode for these formats, it does not offer the bytes
     * from the byte segments alone.</p>
     *
     * <p>This maps to a {@link java.util.List} of byte arrays corresponding to the
     * raw bytes in the byte segments in the barcode, in order.</p>
     */ ResultMetadataType[ResultMetadataType["BYTE_SEGMENTS"] = 2] = "BYTE_SEGMENTS";
    /**
     * Error correction level used, if applicable. The value type depends on the
     * format, but is typically a String.
     */ ResultMetadataType[ResultMetadataType["ERROR_CORRECTION_LEVEL"] = 3] = "ERROR_CORRECTION_LEVEL";
    /**
     * For some periodicals, indicates the issue number as an {@link Integer}.
     */ ResultMetadataType[ResultMetadataType["ISSUE_NUMBER"] = 4] = "ISSUE_NUMBER";
    /**
     * For some products, indicates the suggested retail price in the barcode as a
     * formatted {@link String}.
     */ ResultMetadataType[ResultMetadataType["SUGGESTED_PRICE"] = 5] = "SUGGESTED_PRICE";
    /**
     * For some products, the possible country of manufacture as a {@link String} denoting the
     * ISO country code. Some map to multiple possible countries, like "US/CA".
     */ ResultMetadataType[ResultMetadataType["POSSIBLE_COUNTRY"] = 6] = "POSSIBLE_COUNTRY";
    /**
     * For some products, the extension text
     */ ResultMetadataType[ResultMetadataType["UPC_EAN_EXTENSION"] = 7] = "UPC_EAN_EXTENSION";
    /**
     * PDF417-specific metadata
     */ ResultMetadataType[ResultMetadataType["PDF417_EXTRA_METADATA"] = 8] = "PDF417_EXTRA_METADATA";
    /**
     * If the code format supports structured append and the current scanned code is part of one then the
     * sequence number is given with it.
     */ ResultMetadataType[ResultMetadataType["STRUCTURED_APPEND_SEQUENCE"] = 9] = "STRUCTURED_APPEND_SEQUENCE";
    /**
     * If the code format supports structured append and the current scanned code is part of one then the
     * parity is given with it.
     */ ResultMetadataType[ResultMetadataType["STRUCTURED_APPEND_PARITY"] = 10] = "STRUCTURED_APPEND_PARITY";
})(ResultMetadataType || (ResultMetadataType = {}));
const __TURBOPACK__default__export__ = ResultMetadataType;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ArithmeticException.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Exception$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Exception.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
/**
 * Custom Error class of type Exception.
 */ var ArithmeticException = function(_super) {
    __extends(ArithmeticException, _super);
    function ArithmeticException() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    ArithmeticException.kind = 'ArithmeticException';
    return ArithmeticException;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Exception$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = ArithmeticException;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ReedSolomonException.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Exception$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Exception.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
/**
 * Custom Error class of type Exception.
 */ var ReedSolomonException = function(_super) {
    __extends(ReedSolomonException, _super);
    function ReedSolomonException() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    ReedSolomonException.kind = 'ReedSolomonException';
    return ReedSolomonException;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Exception$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = ReedSolomonException;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/IllegalStateException.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Exception$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Exception.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
/**
 * Custom Error class of type Exception.
 */ var IllegalStateException = function(_super) {
    __extends(IllegalStateException, _super);
    function IllegalStateException() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    IllegalStateException.kind = 'IllegalStateException';
    return IllegalStateException;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Exception$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = IllegalStateException;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ResultPoint.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /*namespace com.google.zxing {*/ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/detector/MathUtils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Float$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/Float.js [app-ssr] (ecmascript)");
;
;
/**
 * <p>Encapsulates a point of interest in an image containing a barcode. Typically, this
 * would be the location of a finder pattern or the corner of the barcode, for example.</p>
 *
 * <AUTHOR> Owen
 */ var ResultPoint = function() {
    function ResultPoint(x, y) {
        this.x = x;
        this.y = y;
    }
    ResultPoint.prototype.getX = function() {
        return this.x;
    };
    ResultPoint.prototype.getY = function() {
        return this.y;
    };
    /*@Override*/ ResultPoint.prototype.equals = function(other) {
        if (other instanceof ResultPoint) {
            var otherPoint = other;
            return this.x === otherPoint.x && this.y === otherPoint.y;
        }
        return false;
    };
    /*@Override*/ ResultPoint.prototype.hashCode = function() {
        return 31 * __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Float$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].floatToIntBits(this.x) + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Float$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].floatToIntBits(this.y);
    };
    /*@Override*/ ResultPoint.prototype.toString = function() {
        return '(' + this.x + ',' + this.y + ')';
    };
    /**
     * Orders an array of three ResultPoints in an order [A,B,C] such that AB is less than AC
     * and BC is less than AC, and the angle between BC and BA is less than 180 degrees.
     *
     * @param patterns array of three {@code ResultPoint} to order
     */ ResultPoint.orderBestPatterns = function(patterns) {
        // Find distances between pattern centers
        var zeroOneDistance = this.distance(patterns[0], patterns[1]);
        var oneTwoDistance = this.distance(patterns[1], patterns[2]);
        var zeroTwoDistance = this.distance(patterns[0], patterns[2]);
        var pointA;
        var pointB;
        var pointC;
        // Assume one closest to other two is B; A and C will just be guesses at first
        if (oneTwoDistance >= zeroOneDistance && oneTwoDistance >= zeroTwoDistance) {
            pointB = patterns[0];
            pointA = patterns[1];
            pointC = patterns[2];
        } else if (zeroTwoDistance >= oneTwoDistance && zeroTwoDistance >= zeroOneDistance) {
            pointB = patterns[1];
            pointA = patterns[0];
            pointC = patterns[2];
        } else {
            pointB = patterns[2];
            pointA = patterns[0];
            pointC = patterns[1];
        }
        // Use cross product to figure out whether A and C are correct or flipped.
        // This asks whether BC x BA has a positive z component, which is the arrangement
        // we want for A, B, C. If it's negative, then we've got it flipped around and
        // should swap A and C.
        if (this.crossProductZ(pointA, pointB, pointC) < 0.0) {
            var temp = pointA;
            pointA = pointC;
            pointC = temp;
        }
        patterns[0] = pointA;
        patterns[1] = pointB;
        patterns[2] = pointC;
    };
    /**
     * @param pattern1 first pattern
     * @param pattern2 second pattern
     * @return distance between two points
     */ ResultPoint.distance = function(pattern1, pattern2) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].distance(pattern1.x, pattern1.y, pattern2.x, pattern2.y);
    };
    /**
     * Returns the z component of the cross product between vectors BC and BA.
     */ ResultPoint.crossProductZ = function(pointA, pointB, pointC) {
        var bX = pointB.x;
        var bY = pointB.y;
        return (pointC.x - bX) * (pointA.y - bY) - (pointC.y - bY) * (pointA.x - bX);
    };
    return ResultPoint;
}();
const __TURBOPACK__default__export__ = ResultPoint;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NullPointerException.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Exception$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Exception.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
/**
 * Custom Error class of type Exception.
 */ var NullPointerException = function(_super) {
    __extends(NullPointerException, _super);
    function NullPointerException() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    NullPointerException.kind = 'NullPointerException';
    return NullPointerException;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Exception$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = NullPointerException;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/OutOfMemoryError.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Exception$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Exception.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
/**
 * Custom Error class of type Exception.
 */ var OutOfMemoryError = function(_super) {
    __extends(OutOfMemoryError, _super);
    function OutOfMemoryError() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    return OutOfMemoryError;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Exception$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = OutOfMemoryError;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ReaderException.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Exception$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Exception.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
/**
 * Custom Error class of type Exception.
 */ var ReaderException = function(_super) {
    __extends(ReaderException, _super);
    function ReaderException() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    ReaderException.kind = 'ReaderException';
    return ReaderException;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Exception$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = ReaderException;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/MultiFormatReader.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2009 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/DecodeHintType.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/BarcodeFormat.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$QRCodeReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/qrcode/QRCodeReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$AztecReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/aztec/AztecReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$MultiFormatOneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/oned/MultiFormatOneDReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$DataMatrixReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/datamatrix/DataMatrixReader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NotFoundException.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$pdf417$2f$PDF417Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/pdf417/PDF417Reader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ReaderException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ReaderException.js [app-ssr] (ecmascript)");
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
;
;
;
;
;
;
;
/*namespace com.google.zxing {*/ /**
 * MultiFormatReader is a convenience class and the main entry point into the library for most uses.
 * By default it attempts to decode all barcode formats that the library supports. Optionally, you
 * can provide a hints object to request different behavior, for example only decoding QR codes.
 *
 * <AUTHOR> Owen
 * <AUTHOR> (Daniel Switkin)
 */ var MultiFormatReader = function() {
    function MultiFormatReader() {}
    /**
     * This version of decode honors the intent of Reader.decode(BinaryBitmap) in that it
     * passes null as a hint to the decoders. However, that makes it inefficient to call repeatedly.
     * Use setHints() followed by decodeWithState() for continuous scan applications.
     *
     * @param image The pixel data to decode
     * @return The contents of the image
     *
     * @throws NotFoundException Any errors which occurred
     */ /*@Override*/ // public decode(image: BinaryBitmap): Result {
    //   setHints(null)
    //   return decodeInternal(image)
    // }
    /**
     * Decode an image using the hints provided. Does not honor existing state.
     *
     * @param image The pixel data to decode
     * @param hints The hints to use, clearing the previous state.
     * @return The contents of the image
     *
     * @throws NotFoundException Any errors which occurred
     */ /*@Override*/ MultiFormatReader.prototype.decode = function(image, hints) {
        this.setHints(hints);
        return this.decodeInternal(image);
    };
    /**
     * Decode an image using the state set up by calling setHints() previously. Continuous scan
     * clients will get a <b>large</b> speed increase by using this instead of decode().
     *
     * @param image The pixel data to decode
     * @return The contents of the image
     *
     * @throws NotFoundException Any errors which occurred
     */ MultiFormatReader.prototype.decodeWithState = function(image) {
        // Make sure to set up the default state so we don't crash
        if (this.readers === null || this.readers === undefined) {
            this.setHints(null);
        }
        return this.decodeInternal(image);
    };
    /**
     * This method adds state to the MultiFormatReader. By setting the hints once, subsequent calls
     * to decodeWithState(image) can reuse the same set of readers without reallocating memory. This
     * is important for performance in continuous scan clients.
     *
     * @param hints The set of hints to use for subsequent calls to decode(image)
     */ MultiFormatReader.prototype.setHints = function(hints) {
        this.hints = hints;
        var tryHarder = hints !== null && hints !== undefined && undefined !== hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].TRY_HARDER);
        /*@SuppressWarnings("unchecked")*/ var formats = hints === null || hints === undefined ? null : hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].POSSIBLE_FORMATS);
        var readers = new Array();
        if (formats !== null && formats !== undefined) {
            var addOneDReader = formats.some(function(f) {
                return f === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].UPC_A || f === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].UPC_E || f === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].EAN_13 || f === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].EAN_8 || f === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].CODABAR || f === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].CODE_39 || f === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].CODE_93 || f === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].CODE_128 || f === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].ITF || f === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].RSS_14 || f === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].RSS_EXPANDED;
            });
            // Put 1D readers upfront in "normal" mode
            // TYPESCRIPTPORT: TODO: uncomment below as they are ported
            if (addOneDReader && !tryHarder) {
                readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$MultiFormatOneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](hints));
            }
            if (formats.includes(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].QR_CODE)) {
                readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$QRCodeReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
            }
            if (formats.includes(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].DATA_MATRIX)) {
                readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$DataMatrixReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
            }
            if (formats.includes(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].AZTEC)) {
                readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$AztecReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
            }
            if (formats.includes(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].PDF_417)) {
                readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$pdf417$2f$PDF417Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
            }
            // if (formats.includes(BarcodeFormat.MAXICODE)) {
            //    readers.push(new MaxiCodeReader())
            // }
            // At end in "try harder" mode
            if (addOneDReader && tryHarder) {
                readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$MultiFormatOneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](hints));
            }
        }
        if (readers.length === 0) {
            if (!tryHarder) {
                readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$MultiFormatOneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](hints));
            }
            readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$QRCodeReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
            readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$datamatrix$2f$DataMatrixReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
            readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$aztec$2f$AztecReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
            readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$pdf417$2f$PDF417Reader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
            // readers.push(new MaxiCodeReader())
            if (tryHarder) {
                readers.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$oned$2f$MultiFormatOneDReader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](hints));
            }
        }
        this.readers = readers; // .toArray(new Reader[readers.size()])
    };
    /*@Override*/ MultiFormatReader.prototype.reset = function() {
        var e_1, _a;
        if (this.readers !== null) {
            try {
                for(var _b = __values(this.readers), _c = _b.next(); !_c.done; _c = _b.next()){
                    var reader = _c.value;
                    reader.reset();
                }
            } catch (e_1_1) {
                e_1 = {
                    error: e_1_1
                };
            } finally{
                try {
                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
                } finally{
                    if (e_1) throw e_1.error;
                }
            }
        }
    };
    /**
     * @throws NotFoundException
     */ MultiFormatReader.prototype.decodeInternal = function(image) {
        var e_2, _a;
        if (this.readers === null) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ReaderException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('No readers where selected, nothing can be read.');
        }
        try {
            for(var _b = __values(this.readers), _c = _b.next(); !_c.done; _c = _b.next()){
                var reader = _c.value;
                // Trying to decode with ${reader} reader.
                try {
                    return reader.decode(image, this.hints);
                } catch (ex) {
                    if (ex instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ReaderException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]) {
                        continue;
                    }
                // Bad Exception.
                }
            }
        } catch (e_2_1) {
            e_2 = {
                error: e_2_1
            };
        } finally{
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            } finally{
                if (e_2) throw e_2.error;
            }
        }
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('No MultiFormat Readers were able to detect the code.');
    };
    return MultiFormatReader;
}();
const __TURBOPACK__default__export__ = MultiFormatReader;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/EncodeHintType.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2009 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /*namespace com.google.zxing {*/ /**
 * These are a set of hints that you may pass to Writers to specify their behavior.
 *
 * <AUTHOR> (Daniel Switkin)
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var EncodeHintType;
(function(EncodeHintType) {
    /**
     * Specifies what degree of error correction to use, for example in QR Codes.
     * Type depends on the encoder. For example for QR codes it's type
     * {@link com.google.zxing.qrcode.decoder.ErrorCorrectionLevel ErrorCorrectionLevel}.
     * For Aztec it is of type {@link Integer}, representing the minimal percentage of error correction words.
     * For PDF417 it is of type {@link Integer}, valid values being 0 to 8.
     * In all cases, it can also be a {@link String} representation of the desired value as well.
     * Note: an Aztec symbol should have a minimum of 25% EC words.
     */ EncodeHintType[EncodeHintType["ERROR_CORRECTION"] = 0] = "ERROR_CORRECTION";
    /**
     * Specifies what character encoding to use where applicable (type {@link String})
     */ EncodeHintType[EncodeHintType["CHARACTER_SET"] = 1] = "CHARACTER_SET";
    /**
     * Specifies the matrix shape for Data Matrix (type {@link com.google.zxing.datamatrix.encoder.SymbolShapeHint})
     */ EncodeHintType[EncodeHintType["DATA_MATRIX_SHAPE"] = 2] = "DATA_MATRIX_SHAPE";
    /**
     * Specifies whether to use compact mode for Data Matrix (type {@link Boolean}, or "true" or "false"
     * {@link String } value).
     * The compact encoding mode also supports the encoding of characters that are not in the ISO-8859-1
     * character set via ECIs.
     * Please note that in that case, the most compact character encoding is chosen for characters in
     * the input that are not in the ISO-8859-1 character set. Based on experience, some scanners do not
     * support encodings like cp-1256 (Arabic). In such cases the encoding can be forced to UTF-8 by
     * means of the {@link #CHARACTER_SET} encoding hint.
     * Compact encoding also provides GS1-FNC1 support when {@link #GS1_FORMAT} is selected. In this case
     * group-separator character (ASCII 29 decimal) can be used to encode the positions of FNC1 codewords
     * for the purpose of delimiting AIs.
     * This option and {@link #FORCE_C40} are mutually exclusive.
     */ EncodeHintType[EncodeHintType["DATA_MATRIX_COMPACT"] = 3] = "DATA_MATRIX_COMPACT";
    /**
     * Specifies a minimum barcode size (type {@link Dimension}). Only applicable to Data Matrix now.
     *
     * @deprecated use width/height params in
     * {@link com.google.zxing.datamatrix.DataMatrixWriter#encode(String, BarcodeFormat, int, int)}
     */ /*@Deprecated*/ EncodeHintType[EncodeHintType["MIN_SIZE"] = 4] = "MIN_SIZE";
    /**
     * Specifies a maximum barcode size (type {@link Dimension}). Only applicable to Data Matrix now.
     *
     * @deprecated without replacement
     */ /*@Deprecated*/ EncodeHintType[EncodeHintType["MAX_SIZE"] = 5] = "MAX_SIZE";
    /**
     * Specifies margin, in pixels, to use when generating the barcode. The meaning can vary
     * by format; for example it controls margin before and after the barcode horizontally for
     * most 1D formats. (Type {@link Integer}, or {@link String} representation of the integer value).
     */ EncodeHintType[EncodeHintType["MARGIN"] = 6] = "MARGIN";
    /**
     * Specifies whether to use compact mode for PDF417 (type {@link Boolean}, or "true" or "false"
     * {@link String} value).
     */ EncodeHintType[EncodeHintType["PDF417_COMPACT"] = 7] = "PDF417_COMPACT";
    /**
     * Specifies what compaction mode to use for PDF417 (type
     * {@link com.google.zxing.pdf417.encoder.Compaction Compaction} or {@link String} value of one of its
     * enum values).
     */ EncodeHintType[EncodeHintType["PDF417_COMPACTION"] = 8] = "PDF417_COMPACTION";
    /**
     * Specifies the minimum and maximum number of rows and columns for PDF417 (type
     * {@link com.google.zxing.pdf417.encoder.Dimensions Dimensions}).
     */ EncodeHintType[EncodeHintType["PDF417_DIMENSIONS"] = 9] = "PDF417_DIMENSIONS";
    /**
     * Specifies the required number of layers for an Aztec code.
     * A negative number (-1, -2, -3, -4) specifies a compact Aztec code.
     * 0 indicates to use the minimum number of layers (the default).
     * A positive number (1, 2, .. 32) specifies a normal (non-compact) Aztec code.
     * (Type {@link Integer}, or {@link String} representation of the integer value).
     */ EncodeHintType[EncodeHintType["AZTEC_LAYERS"] = 10] = "AZTEC_LAYERS";
    /**
     * Specifies the exact version of QR code to be encoded.
     * (Type {@link Integer}, or {@link String} representation of the integer value).
     */ EncodeHintType[EncodeHintType["QR_VERSION"] = 11] = "QR_VERSION";
    /**
     * Specifies whether the data should be encoded to the GS1 standard (type {@link Boolean}, or "true" or "false"
     * {@link String } value).
     */ EncodeHintType[EncodeHintType["GS1_FORMAT"] = 12] = "GS1_FORMAT";
    /**
     * Forces C40 encoding for data-matrix (type {@link Boolean}, or "true" or "false") {@link String } value). This
     * option and {@link #DATA_MATRIX_COMPACT} are mutually exclusive.
     */ EncodeHintType[EncodeHintType["FORCE_C40"] = 13] = "FORCE_C40";
})(EncodeHintType || (EncodeHintType = {}));
const __TURBOPACK__default__export__ = EncodeHintType;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/WriterException.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Exception$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Exception.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
/**
 * Custom Error class of type Exception.
 */ var WriterException = function(_super) {
    __extends(WriterException, _super);
    function WriterException() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    WriterException.kind = 'WriterException';
    return WriterException;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Exception$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = WriterException;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/MultiFormatWriter.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2008 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ // import DataMatrixWriter from './datamatrix/DataMatrixWriter'
// import CodaBarWriter from './oned/CodaBarWriter'
// import Code128Writer from './oned/Code128Writer'
// import Code39Writer from './oned/Code39Writer'
// import Code93Writer from './oned/Code93Writer'
// import EAN13Writer from './oned/EAN13Writer'
// import EAN8Writer from './oned/EAN8Writer'
// import ITFWriter from './oned/ITFWriter'
// import UPCAWriter from './oned/UPCAWriter'
// import UPCEWriter from './oned/UPCEWriter'
// import PDF417Writer from './pdf417/PDF417Writer'
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$QRCodeWriter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/qrcode/QRCodeWriter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/BarcodeFormat.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/IllegalArgumentException.js [app-ssr] (ecmascript)");
;
;
;
/*import java.util.Map;*/ /**
 * This is a factory class which finds the appropriate Writer subclass for the BarcodeFormat
 * requested and encodes the barcode with the supplied contents.
 *
 * <AUTHOR> (Daniel Switkin)
 */ var MultiFormatWriter = function() {
    function MultiFormatWriter() {}
    /*@Override*/ // public encode(contents: string,
    //                         format: BarcodeFormat,
    //                         width: number /*int*/,
    //                         height: number /*int*/): BitMatrix /*throws WriterException */ {
    //   return encode(contents, format, width, height, null)
    // }
    /*@Override*/ MultiFormatWriter.prototype.encode = function(contents, format, width /*int*/ , height /*int*/ , hints) {
        var writer;
        switch(format){
            // case BarcodeFormat.EAN_8:
            //   writer = new EAN8Writer()
            //   break
            // case BarcodeFormat.UPC_E:
            //   writer = new UPCEWriter()
            //   break
            // case BarcodeFormat.EAN_13:
            //   writer = new EAN13Writer()
            //   break
            // case BarcodeFormat.UPC_A:
            //   writer = new UPCAWriter()
            //   break
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$BarcodeFormat$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].QR_CODE:
                writer = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$qrcode$2f$QRCodeWriter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                break;
            // case BarcodeFormat.CODE_39:
            //   writer = new Code39Writer()
            //   break
            // case BarcodeFormat.CODE_93:
            //   writer = new Code93Writer()
            //   break
            // case BarcodeFormat.CODE_128:
            //   writer = new Code128Writer()
            //   break
            // case BarcodeFormat.ITF:
            //   writer = new ITFWriter()
            //   break
            // case BarcodeFormat.PDF_417:
            //   writer = new PDF417Writer()
            //   break
            // case BarcodeFormat.CODABAR:
            //   writer = new CodaBarWriter()
            //   break
            // case BarcodeFormat.DATA_MATRIX:
            //   writer = new DataMatrixWriter()
            //   break
            // case BarcodeFormat.AZTEC:
            //   writer = new AztecWriter()
            //   break
            default:
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('No encoder available for format ' + format);
        }
        return writer.encode(contents, format, width, height, hints);
    };
    return MultiFormatWriter;
}();
const __TURBOPACK__default__export__ = MultiFormatWriter;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/PlanarYUVLuminanceSource.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2009 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
/*namespace com.google.zxing {*/ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/System.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$LuminanceSource$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/LuminanceSource.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$InvertedLuminanceSource$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/InvertedLuminanceSource.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/IllegalArgumentException.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
;
;
/**
 * This object extends LuminanceSource around an array of YUV data returned from the camera driver,
 * with the option to crop to a rectangle within the full data. This can be used to exclude
 * superfluous pixels around the perimeter and speed up decoding.
 *
 * It works for any pixel format where the Y channel is planar and appears first, including
 * YCbCr_420_SP and YCbCr_422_SP.
 *
 * <AUTHOR> (Daniel Switkin)
 */ var PlanarYUVLuminanceSource = function(_super) {
    __extends(PlanarYUVLuminanceSource, _super);
    function PlanarYUVLuminanceSource(yuvData, dataWidth /*int*/ , dataHeight /*int*/ , left /*int*/ , top /*int*/ , width /*int*/ , height /*int*/ , reverseHorizontal) {
        var _this = _super.call(this, width, height) || this;
        _this.yuvData = yuvData;
        _this.dataWidth = dataWidth;
        _this.dataHeight = dataHeight;
        _this.left = left;
        _this.top = top;
        if (left + width > dataWidth || top + height > dataHeight) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('Crop rectangle does not fit within image data.');
        }
        if (reverseHorizontal) {
            _this.reverseHorizontal(width, height);
        }
        return _this;
    }
    /*@Override*/ PlanarYUVLuminanceSource.prototype.getRow = function(y /*int*/ , row) {
        if (y < 0 || y >= this.getHeight()) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('Requested row is outside the image: ' + y);
        }
        var width = this.getWidth();
        if (row === null || row === undefined || row.length < width) {
            row = new Uint8ClampedArray(width);
        }
        var offset = (y + this.top) * this.dataWidth + this.left;
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].arraycopy(this.yuvData, offset, row, 0, width);
        return row;
    };
    /*@Override*/ PlanarYUVLuminanceSource.prototype.getMatrix = function() {
        var width = this.getWidth();
        var height = this.getHeight();
        // If the caller asks for the entire underlying image, save the copy and give them the
        // original data. The docs specifically warn that result.length must be ignored.
        if (width === this.dataWidth && height === this.dataHeight) {
            return this.yuvData;
        }
        var area = width * height;
        var matrix = new Uint8ClampedArray(area);
        var inputOffset = this.top * this.dataWidth + this.left;
        // If the width matches the full width of the underlying data, perform a single copy.
        if (width === this.dataWidth) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].arraycopy(this.yuvData, inputOffset, matrix, 0, area);
            return matrix;
        }
        // Otherwise copy one cropped row at a time.
        for(var y = 0; y < height; y++){
            var outputOffset = y * width;
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].arraycopy(this.yuvData, inputOffset, matrix, outputOffset, width);
            inputOffset += this.dataWidth;
        }
        return matrix;
    };
    /*@Override*/ PlanarYUVLuminanceSource.prototype.isCropSupported = function() {
        return true;
    };
    /*@Override*/ PlanarYUVLuminanceSource.prototype.crop = function(left /*int*/ , top /*int*/ , width /*int*/ , height /*int*/ ) {
        return new PlanarYUVLuminanceSource(this.yuvData, this.dataWidth, this.dataHeight, this.left + left, this.top + top, width, height, false);
    };
    PlanarYUVLuminanceSource.prototype.renderThumbnail = function() {
        var width = this.getWidth() / PlanarYUVLuminanceSource.THUMBNAIL_SCALE_FACTOR;
        var height = this.getHeight() / PlanarYUVLuminanceSource.THUMBNAIL_SCALE_FACTOR;
        var pixels = new Int32Array(width * height);
        var yuv = this.yuvData;
        var inputOffset = this.top * this.dataWidth + this.left;
        for(var y = 0; y < height; y++){
            var outputOffset = y * width;
            for(var x = 0; x < width; x++){
                var grey = yuv[inputOffset + x * PlanarYUVLuminanceSource.THUMBNAIL_SCALE_FACTOR] & 0xff;
                pixels[outputOffset + x] = 0xFF000000 | grey * 0x00010101;
            }
            inputOffset += this.dataWidth * PlanarYUVLuminanceSource.THUMBNAIL_SCALE_FACTOR;
        }
        return pixels;
    };
    /**
     * @return width of image from {@link #renderThumbnail()}
     */ PlanarYUVLuminanceSource.prototype.getThumbnailWidth = function() {
        return this.getWidth() / PlanarYUVLuminanceSource.THUMBNAIL_SCALE_FACTOR;
    };
    /**
     * @return height of image from {@link #renderThumbnail()}
     */ PlanarYUVLuminanceSource.prototype.getThumbnailHeight = function() {
        return this.getHeight() / PlanarYUVLuminanceSource.THUMBNAIL_SCALE_FACTOR;
    };
    PlanarYUVLuminanceSource.prototype.reverseHorizontal = function(width /*int*/ , height /*int*/ ) {
        var yuvData = this.yuvData;
        for(var y = 0, rowStart = this.top * this.dataWidth + this.left; y < height; y++, rowStart += this.dataWidth){
            var middle = rowStart + width / 2;
            for(var x1 = rowStart, x2 = rowStart + width - 1; x1 < middle; x1++, x2--){
                var temp = yuvData[x1];
                yuvData[x1] = yuvData[x2];
                yuvData[x2] = temp;
            }
        }
    };
    PlanarYUVLuminanceSource.prototype.invert = function() {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$InvertedLuminanceSource$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](this);
    };
    PlanarYUVLuminanceSource.THUMBNAIL_SCALE_FACTOR = 2;
    return PlanarYUVLuminanceSource;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$LuminanceSource$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = PlanarYUVLuminanceSource;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/RGBLuminanceSource.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2009 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
/*namespace com.google.zxing {*/ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$InvertedLuminanceSource$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/InvertedLuminanceSource.js [app-ssr] (ecmascript)"); // required because of circular dependencies between LuminanceSource and InvertedLuminanceSource
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$LuminanceSource$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/LuminanceSource.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/System.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/IllegalArgumentException.js [app-ssr] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
;
;
;
/**
 * This class is used to help decode images from files which arrive as RGB data from
 * an ARGB pixel array. It does not support rotation.
 *
 * <AUTHOR> (Daniel Switkin)
 * <AUTHOR>
 */ var RGBLuminanceSource = function(_super) {
    __extends(RGBLuminanceSource, _super);
    function RGBLuminanceSource(luminances, width /*int*/ , height /*int*/ , dataWidth /*int*/ , dataHeight /*int*/ , left /*int*/ , top /*int*/ ) {
        var _this = _super.call(this, width, height) || this;
        _this.dataWidth = dataWidth;
        _this.dataHeight = dataHeight;
        _this.left = left;
        _this.top = top;
        if (luminances.BYTES_PER_ELEMENT === 4) {
            var size = width * height;
            var luminancesUint8Array = new Uint8ClampedArray(size);
            for(var offset = 0; offset < size; offset++){
                var pixel = luminances[offset];
                var r = pixel >> 16 & 0xff; // red
                var g2 = pixel >> 7 & 0x1fe; // 2 * green
                var b = pixel & 0xff; // blue
                // Calculate green-favouring average cheaply
                luminancesUint8Array[offset] = /*(byte) */ (r + g2 + b) / 4 & 0xFF;
            }
            _this.luminances = luminancesUint8Array;
        } else {
            _this.luminances = luminances;
        }
        if (undefined === dataWidth) {
            _this.dataWidth = width;
        }
        if (undefined === dataHeight) {
            _this.dataHeight = height;
        }
        if (undefined === left) {
            _this.left = 0;
        }
        if (undefined === top) {
            _this.top = 0;
        }
        if (_this.left + width > _this.dataWidth || _this.top + height > _this.dataHeight) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('Crop rectangle does not fit within image data.');
        }
        return _this;
    }
    /*@Override*/ RGBLuminanceSource.prototype.getRow = function(y /*int*/ , row) {
        if (y < 0 || y >= this.getHeight()) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]('Requested row is outside the image: ' + y);
        }
        var width = this.getWidth();
        if (row === null || row === undefined || row.length < width) {
            row = new Uint8ClampedArray(width);
        }
        var offset = (y + this.top) * this.dataWidth + this.left;
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].arraycopy(this.luminances, offset, row, 0, width);
        return row;
    };
    /*@Override*/ RGBLuminanceSource.prototype.getMatrix = function() {
        var width = this.getWidth();
        var height = this.getHeight();
        // If the caller asks for the entire underlying image, save the copy and give them the
        // original data. The docs specifically warn that result.length must be ignored.
        if (width === this.dataWidth && height === this.dataHeight) {
            return this.luminances;
        }
        var area = width * height;
        var matrix = new Uint8ClampedArray(area);
        var inputOffset = this.top * this.dataWidth + this.left;
        // If the width matches the full width of the underlying data, perform a single copy.
        if (width === this.dataWidth) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].arraycopy(this.luminances, inputOffset, matrix, 0, area);
            return matrix;
        }
        // Otherwise copy one cropped row at a time.
        for(var y = 0; y < height; y++){
            var outputOffset = y * width;
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].arraycopy(this.luminances, inputOffset, matrix, outputOffset, width);
            inputOffset += this.dataWidth;
        }
        return matrix;
    };
    /*@Override*/ RGBLuminanceSource.prototype.isCropSupported = function() {
        return true;
    };
    /*@Override*/ RGBLuminanceSource.prototype.crop = function(left /*int*/ , top /*int*/ , width /*int*/ , height /*int*/ ) {
        return new RGBLuminanceSource(this.luminances, width, height, this.dataWidth, this.dataHeight, this.left + left, this.top + top);
    };
    RGBLuminanceSource.prototype.invert = function() {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$InvertedLuminanceSource$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](this);
    };
    return RGBLuminanceSource;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$LuminanceSource$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = RGBLuminanceSource;
}),

};

//# sourceMappingURL=652c1_%40zxing_library_esm_core_0f7734b9._.js.map