const http = require('http');
const url = require('url');

const PORT = process.env.PORT || 5000;

// Mock data untuk testing
let profiles = [
    {
        id: 1,
        nama: '<PERSON><PERSON>',
        usia: 65,
        alamat: 'Jl. Merdeka No. 123, Jakarta',
        riwayat_medis: 'Hip<PERSON><PERSON>i, Diabetes',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
    },
    {
        id: 2,
        nama: '<PERSON>i <PERSON>',
        usia: 70,
        alamat: 'Jl. Sudirman No. 456, Bandung',
        riwayat_medis: 'Kolesterol tinggi',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
    }
];

let checkups = [
    {
        id: 1,
        profile_id: 1,
        tekanan_darah: '140/90',
        gula_darah: 180,
        tanggal: new Date().toISOString(),
        catatan: '<PERSON><PERSON><PERSON> darah tinggi, perlu kontrol rutin'
    },
    {
        id: 2,
        profile_id: 1,
        tekanan_darah: '130/85',
        gula_darah: 160,
        tanggal: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        catatan: 'Membaik setelah minum obat'
    },
    {
        id: 3,
        profile_id: 2,
        tekanan_darah: '120/80',
        gula_darah: 110,
        tanggal: new Date().toISOString(),
        catatan: 'Normal'
    }
];

let nextProfileId = 3;
let nextCheckupId = 4;

// Helper function untuk parsing JSON body
function parseBody(req) {
    return new Promise((resolve, reject) => {
        let body = '';
        req.on('data', chunk => {
            body += chunk.toString();
        });
        req.on('end', () => {
            try {
                resolve(body ? JSON.parse(body) : {});
            } catch (error) {
                reject(error);
            }
        });
    });
}

// Helper function untuk response
function sendResponse(res, statusCode, data) {
    res.writeHead(statusCode, {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization'
    });
    res.end(JSON.stringify(data));
}

// Server
const server = http.createServer(async (req, res) => {
    const parsedUrl = url.parse(req.url, true);
    const path = parsedUrl.pathname;
    const method = req.method;

    console.log(`${new Date().toISOString()} - ${method} ${path}`);

    // Handle CORS preflight
    if (method === 'OPTIONS') {
        res.writeHead(200, {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization'
        });
        res.end();
        return;
    }

    try {
        // Health check
        if (path === '/api/health' && method === 'GET') {
            sendResponse(res, 200, {
                status: 'OK',
                message: 'Server berjalan dengan baik',
                timestamp: new Date().toISOString()
            });
            return;
        }

        // Create profile
        if (path === '/api/profiles' && method === 'POST') {
            const body = await parseBody(req);
            const { nama, usia, alamat, riwayat_medis, tekanan_darah, gula_darah, catatan } = body;

            if (!nama || !usia || !alamat || !tekanan_darah || !gula_darah) {
                sendResponse(res, 400, {
                    error: 'Data wajib tidak lengkap',
                    required: ['nama', 'usia', 'alamat', 'tekanan_darah', 'gula_darah']
                });
                return;
            }

            const newProfile = {
                id: nextProfileId++,
                nama,
                usia: parseInt(usia),
                alamat,
                riwayat_medis: riwayat_medis || '',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };

            profiles.push(newProfile);

            const newCheckup = {
                id: nextCheckupId++,
                profile_id: newProfile.id,
                tekanan_darah,
                gula_darah: parseInt(gula_darah),
                tanggal: new Date().toISOString(),
                catatan: catatan || ''
            };

            checkups.push(newCheckup);

            sendResponse(res, 201, {
                id: newProfile.id,
                message: 'Profil dan pemeriksaan berhasil disimpan',
                data: {
                    profile_id: newProfile.id,
                    nama,
                    usia: parseInt(usia)
                }
            });
            return;
        }

        // Get profile by ID
        if (path.startsWith('/api/profiles/') && method === 'GET') {
            const profileId = parseInt(path.split('/')[3]);
            const profile = profiles.find(p => p.id === profileId);

            if (!profile) {
                sendResponse(res, 404, { error: 'Profil tidak ditemukan' });
                return;
            }

            const profileCheckups = checkups
                .filter(c => c.profile_id === profileId)
                .sort((a, b) => new Date(b.tanggal).getTime() - new Date(a.tanggal).getTime());

            sendResponse(res, 200, {
                profile,
                checkups: profileCheckups
            });
            return;
        }

        // Get all profiles
        if (path === '/api/profiles' && method === 'GET') {
            const profilesWithStats = profiles.map(profile => {
                const profileCheckups = checkups.filter(c => c.profile_id === profile.id);
                const lastCheckup = profileCheckups.length > 0
                    ? profileCheckups.sort((a, b) => new Date(b.tanggal).getTime() - new Date(a.tanggal).getTime())[0]
                    : null;

                return {
                    ...profile,
                    total_checkups: profileCheckups.length,
                    last_checkup: lastCheckup ? lastCheckup.tanggal : null
                };
            });

            sendResponse(res, 200, {
                profiles: profilesWithStats,
                total: profilesWithStats.length
            });
            return;
        }

        // Add checkup
        if (path === '/api/checkups' && method === 'POST') {
            const body = await parseBody(req);
            const { profile_id, tekanan_darah, gula_darah, catatan } = body;

            if (!profile_id || !tekanan_darah || !gula_darah) {
                sendResponse(res, 400, {
                    error: 'Data wajib tidak lengkap',
                    required: ['profile_id', 'tekanan_darah', 'gula_darah']
                });
                return;
            }

            const profile = profiles.find(p => p.id === parseInt(profile_id));
            if (!profile) {
                sendResponse(res, 404, { error: 'Profil tidak ditemukan' });
                return;
            }

            const newCheckup = {
                id: nextCheckupId++,
                profile_id: parseInt(profile_id),
                tekanan_darah,
                gula_darah: parseInt(gula_darah),
                tanggal: new Date().toISOString(),
                catatan: catatan || ''
            };

            checkups.push(newCheckup);

            sendResponse(res, 201, {
                id: newCheckup.id,
                message: 'Pemeriksaan berhasil ditambahkan',
                data: {
                    checkup_id: newCheckup.id,
                    profile_id: parseInt(profile_id),
                    tekanan_darah,
                    gula_darah: parseInt(gula_darah)
                }
            });
            return;
        }

        // 404 for other routes
        sendResponse(res, 404, {
            error: 'Endpoint tidak ditemukan',
            path: path
        });

    } catch (error) {
        console.error('Server error:', error);
        sendResponse(res, 500, {
            error: 'Terjadi kesalahan server',
            message: error.message
        });
    }
});

server.listen(PORT, () => {
    console.log(`🚀 Server berjalan di http://localhost:${PORT}`);
    console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
    console.log(`📋 API Documentation:`);
    console.log(`   POST /api/profiles - Buat profil baru`);
    console.log(`   GET  /api/profiles/:id - Ambil profil dan riwayat`);
    console.log(`   POST /api/checkups - Tambah pemeriksaan baru`);
    console.log(`   GET  /api/profiles - Ambil semua profil`);
    console.log(`\n✅ Server siap digunakan dengan mock data!`);
});
