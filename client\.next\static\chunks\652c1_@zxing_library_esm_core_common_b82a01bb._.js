(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/BitArray.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /*namespace com.google.zxing.common {*/ /*import java.util.Arrays;*/ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/IllegalArgumentException.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Arrays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/Arrays.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/Integer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/System.js [app-client] (ecmascript)");
;
;
;
;
/**
 * <p>A simple, fast array of bits, represented compactly by an array of ints internally.</p>
 *
 * <AUTHOR> Owen
 */ var BitArray /*implements Cloneable*/  = function() {
    // public constructor() {
    //   this.size = 0
    //   this.bits = new Int32Array(1)
    // }
    // public constructor(size?: number /*int*/) {
    //   if (undefined === size) {
    //     this.size = 0
    //   } else {
    //     this.size = size
    //   }
    //   this.bits = this.makeArray(size)
    // }
    // For testing only
    function BitArray(size /*int*/ , bits) {
        if (undefined === size) {
            this.size = 0;
            this.bits = new Int32Array(1);
        } else {
            this.size = size;
            if (undefined === bits || null === bits) {
                this.bits = BitArray.makeArray(size);
            } else {
                this.bits = bits;
            }
        }
    }
    BitArray.prototype.getSize = function() {
        return this.size;
    };
    BitArray.prototype.getSizeInBytes = function() {
        return Math.floor((this.size + 7) / 8);
    };
    BitArray.prototype.ensureCapacity = function(size /*int*/ ) {
        if (size > this.bits.length * 32) {
            var newBits = BitArray.makeArray(size);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].arraycopy(this.bits, 0, newBits, 0, this.bits.length);
            this.bits = newBits;
        }
    };
    /**
     * @param i bit to get
     * @return true iff bit i is set
     */ BitArray.prototype.get = function(i /*int*/ ) {
        return (this.bits[Math.floor(i / 32)] & 1 << (i & 0x1F)) !== 0;
    };
    /**
     * Sets bit i.
     *
     * @param i bit to set
     */ BitArray.prototype.set = function(i /*int*/ ) {
        this.bits[Math.floor(i / 32)] |= 1 << (i & 0x1F);
    };
    /**
     * Flips bit i.
     *
     * @param i bit to set
     */ BitArray.prototype.flip = function(i /*int*/ ) {
        this.bits[Math.floor(i / 32)] ^= 1 << (i & 0x1F);
    };
    /**
     * @param from first bit to check
     * @return index of first bit that is set, starting from the given index, or size if none are set
     *  at or beyond this given index
     * @see #getNextUnset(int)
     */ BitArray.prototype.getNextSet = function(from /*int*/ ) {
        var size = this.size;
        if (from >= size) {
            return size;
        }
        var bits = this.bits;
        var bitsOffset = Math.floor(from / 32);
        var currentBits = bits[bitsOffset];
        // mask off lesser bits first
        currentBits &= ~((1 << (from & 0x1F)) - 1);
        var length = bits.length;
        while(currentBits === 0){
            if (++bitsOffset === length) {
                return size;
            }
            currentBits = bits[bitsOffset];
        }
        var result = bitsOffset * 32 + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].numberOfTrailingZeros(currentBits);
        return result > size ? size : result;
    };
    /**
     * @param from index to start looking for unset bit
     * @return index of next unset bit, or {@code size} if none are unset until the end
     * @see #getNextSet(int)
     */ BitArray.prototype.getNextUnset = function(from /*int*/ ) {
        var size = this.size;
        if (from >= size) {
            return size;
        }
        var bits = this.bits;
        var bitsOffset = Math.floor(from / 32);
        var currentBits = ~bits[bitsOffset];
        // mask off lesser bits first
        currentBits &= ~((1 << (from & 0x1F)) - 1);
        var length = bits.length;
        while(currentBits === 0){
            if (++bitsOffset === length) {
                return size;
            }
            currentBits = ~bits[bitsOffset];
        }
        var result = bitsOffset * 32 + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].numberOfTrailingZeros(currentBits);
        return result > size ? size : result;
    };
    /**
     * Sets a block of 32 bits, starting at bit i.
     *
     * @param i first bit to set
     * @param newBits the new value of the next 32 bits. Note again that the least-significant bit
     * corresponds to bit i, the next-least-significant to i+1, and so on.
     */ BitArray.prototype.setBulk = function(i /*int*/ , newBits /*int*/ ) {
        this.bits[Math.floor(i / 32)] = newBits;
    };
    /**
     * Sets a range of bits.
     *
     * @param start start of range, inclusive.
     * @param end end of range, exclusive
     */ BitArray.prototype.setRange = function(start /*int*/ , end /*int*/ ) {
        if (end < start || start < 0 || end > this.size) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        if (end === start) {
            return;
        }
        end--; // will be easier to treat this as the last actually set bit -- inclusive
        var firstInt = Math.floor(start / 32);
        var lastInt = Math.floor(end / 32);
        var bits = this.bits;
        for(var i = firstInt; i <= lastInt; i++){
            var firstBit = i > firstInt ? 0 : start & 0x1F;
            var lastBit = i < lastInt ? 31 : end & 0x1F;
            // Ones from firstBit to lastBit, inclusive
            var mask = (2 << lastBit) - (1 << firstBit);
            bits[i] |= mask;
        }
    };
    /**
     * Clears all bits (sets to false).
     */ BitArray.prototype.clear = function() {
        var max = this.bits.length;
        var bits = this.bits;
        for(var i = 0; i < max; i++){
            bits[i] = 0;
        }
    };
    /**
     * Efficient method to check if a range of bits is set, or not set.
     *
     * @param start start of range, inclusive.
     * @param end end of range, exclusive
     * @param value if true, checks that bits in range are set, otherwise checks that they are not set
     * @return true iff all bits are set or not set in range, according to value argument
     * @throws IllegalArgumentException if end is less than start or the range is not contained in the array
     */ BitArray.prototype.isRange = function(start /*int*/ , end /*int*/ , value) {
        if (end < start || start < 0 || end > this.size) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        if (end === start) {
            return true; // empty range matches
        }
        end--; // will be easier to treat this as the last actually set bit -- inclusive
        var firstInt = Math.floor(start / 32);
        var lastInt = Math.floor(end / 32);
        var bits = this.bits;
        for(var i = firstInt; i <= lastInt; i++){
            var firstBit = i > firstInt ? 0 : start & 0x1F;
            var lastBit = i < lastInt ? 31 : end & 0x1F;
            // Ones from firstBit to lastBit, inclusive
            var mask = (2 << lastBit) - (1 << firstBit) & 0xFFFFFFFF;
            // TYPESCRIPTPORT: & 0xFFFFFFFF added to discard anything after 32 bits, as ES has 53 bits
            // Return false if we're looking for 1s and the masked bits[i] isn't all 1s (is: that,
            // equals the mask, or we're looking for 0s and the masked portion is not all 0s
            if ((bits[i] & mask) !== (value ? mask : 0)) {
                return false;
            }
        }
        return true;
    };
    BitArray.prototype.appendBit = function(bit) {
        this.ensureCapacity(this.size + 1);
        if (bit) {
            this.bits[Math.floor(this.size / 32)] |= 1 << (this.size & 0x1F);
        }
        this.size++;
    };
    /**
     * Appends the least-significant bits, from value, in order from most-significant to
     * least-significant. For example, appending 6 bits from 0x000001E will append the bits
     * 0, 1, 1, 1, 1, 0 in that order.
     *
     * @param value {@code int} containing bits to append
     * @param numBits bits from value to append
     */ BitArray.prototype.appendBits = function(value /*int*/ , numBits /*int*/ ) {
        if (numBits < 0 || numBits > 32) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Num bits must be between 0 and 32');
        }
        this.ensureCapacity(this.size + numBits);
        // const appendBit = this.appendBit;
        for(var numBitsLeft = numBits; numBitsLeft > 0; numBitsLeft--){
            this.appendBit((value >> numBitsLeft - 1 & 0x01) === 1);
        }
    };
    BitArray.prototype.appendBitArray = function(other) {
        var otherSize = other.size;
        this.ensureCapacity(this.size + otherSize);
        // const appendBit = this.appendBit;
        for(var i = 0; i < otherSize; i++){
            this.appendBit(other.get(i));
        }
    };
    BitArray.prototype.xor = function(other) {
        if (this.size !== other.size) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Sizes don\'t match');
        }
        var bits = this.bits;
        for(var i = 0, length_1 = bits.length; i < length_1; i++){
            // The last int could be incomplete (i.e. not have 32 bits in
            // it) but there is no problem since 0 XOR 0 == 0.
            bits[i] ^= other.bits[i];
        }
    };
    /**
     *
     * @param bitOffset first bit to start writing
     * @param array array to write into. Bytes are written most-significant byte first. This is the opposite
     *  of the internal representation, which is exposed by {@link #getBitArray()}
     * @param offset position in array to start writing
     * @param numBytes how many bytes to write
     */ BitArray.prototype.toBytes = function(bitOffset /*int*/ , array, offset /*int*/ , numBytes /*int*/ ) {
        for(var i = 0; i < numBytes; i++){
            var theByte = 0;
            for(var j = 0; j < 8; j++){
                if (this.get(bitOffset)) {
                    theByte |= 1 << 7 - j;
                }
                bitOffset++;
            }
            array[offset + i] = /*(byte)*/ theByte;
        }
    };
    /**
     * @return underlying array of ints. The first element holds the first 32 bits, and the least
     *         significant bit is bit 0.
     */ BitArray.prototype.getBitArray = function() {
        return this.bits;
    };
    /**
     * Reverses all bits in the array.
     */ BitArray.prototype.reverse = function() {
        var newBits = new Int32Array(this.bits.length);
        // reverse all int's first
        var len = Math.floor((this.size - 1) / 32);
        var oldBitsLen = len + 1;
        var bits = this.bits;
        for(var i = 0; i < oldBitsLen; i++){
            var x = bits[i];
            x = x >> 1 & 0x55555555 | (x & 0x55555555) << 1;
            x = x >> 2 & 0x33333333 | (x & 0x33333333) << 2;
            x = x >> 4 & 0x0f0f0f0f | (x & 0x0f0f0f0f) << 4;
            x = x >> 8 & 0x00ff00ff | (x & 0x00ff00ff) << 8;
            x = x >> 16 & 0x0000ffff | (x & 0x0000ffff) << 16;
            newBits[len - i] = /*(int)*/ x;
        }
        // now correct the int's if the bit size isn't a multiple of 32
        if (this.size !== oldBitsLen * 32) {
            var leftOffset = oldBitsLen * 32 - this.size;
            var currentInt = newBits[0] >>> leftOffset;
            for(var i = 1; i < oldBitsLen; i++){
                var nextInt = newBits[i];
                currentInt |= nextInt << 32 - leftOffset;
                newBits[i - 1] = currentInt;
                currentInt = nextInt >>> leftOffset;
            }
            newBits[oldBitsLen - 1] = currentInt;
        }
        this.bits = newBits;
    };
    BitArray.makeArray = function(size /*int*/ ) {
        return new Int32Array(Math.floor((size + 31) / 32));
    };
    /*@Override*/ BitArray.prototype.equals = function(o) {
        if (!(o instanceof BitArray)) {
            return false;
        }
        var other = o;
        return this.size === other.size && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Arrays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].equals(this.bits, other.bits);
    };
    /*@Override*/ BitArray.prototype.hashCode = function() {
        return 31 * this.size + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Arrays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].hashCode(this.bits);
    };
    /*@Override*/ BitArray.prototype.toString = function() {
        var result = '';
        for(var i = 0, size = this.size; i < size; i++){
            if ((i & 0x07) === 0) {
                result += ' ';
            }
            result += this.get(i) ? 'X' : '.';
        }
        return result;
    };
    /*@Override*/ BitArray.prototype.clone = function() {
        return new BitArray(this.size, this.bits.slice());
    };
    /**
     * converts to boolean array.
     */ BitArray.prototype.toArray = function() {
        var result = [];
        for(var i = 0, size = this.size; i < size; i++){
            result.push(this.get(i));
        }
        return result;
    };
    return BitArray;
}();
const __TURBOPACK__default__export__ = BitArray;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/CharacterSetECI.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2008 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "CharacterSetValueIdentifiers": ()=>CharacterSetValueIdentifiers,
    "default": ()=>__TURBOPACK__default__export__
});
/*namespace com.google.zxing.common {*/ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/FormatException.js [app-client] (ecmascript)");
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
var CharacterSetValueIdentifiers;
(function(CharacterSetValueIdentifiers) {
    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers["Cp437"] = 0] = "Cp437";
    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers["ISO8859_1"] = 1] = "ISO8859_1";
    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers["ISO8859_2"] = 2] = "ISO8859_2";
    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers["ISO8859_3"] = 3] = "ISO8859_3";
    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers["ISO8859_4"] = 4] = "ISO8859_4";
    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers["ISO8859_5"] = 5] = "ISO8859_5";
    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers["ISO8859_6"] = 6] = "ISO8859_6";
    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers["ISO8859_7"] = 7] = "ISO8859_7";
    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers["ISO8859_8"] = 8] = "ISO8859_8";
    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers["ISO8859_9"] = 9] = "ISO8859_9";
    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers["ISO8859_10"] = 10] = "ISO8859_10";
    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers["ISO8859_11"] = 11] = "ISO8859_11";
    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers["ISO8859_13"] = 12] = "ISO8859_13";
    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers["ISO8859_14"] = 13] = "ISO8859_14";
    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers["ISO8859_15"] = 14] = "ISO8859_15";
    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers["ISO8859_16"] = 15] = "ISO8859_16";
    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers["SJIS"] = 16] = "SJIS";
    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers["Cp1250"] = 17] = "Cp1250";
    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers["Cp1251"] = 18] = "Cp1251";
    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers["Cp1252"] = 19] = "Cp1252";
    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers["Cp1256"] = 20] = "Cp1256";
    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers["UnicodeBigUnmarked"] = 21] = "UnicodeBigUnmarked";
    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers["UTF8"] = 22] = "UTF8";
    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers["ASCII"] = 23] = "ASCII";
    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers["Big5"] = 24] = "Big5";
    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers["GB18030"] = 25] = "GB18030";
    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers["EUC_KR"] = 26] = "EUC_KR";
})(CharacterSetValueIdentifiers || (CharacterSetValueIdentifiers = {}));
/**
 * Encapsulates a Character Set ECI, according to "Extended Channel Interpretations" 5.3.1.1
 * of ISO 18004.
 *
 * <AUTHOR> Owen
 */ var CharacterSetECI = function() {
    function CharacterSetECI(valueIdentifier, valuesParam, name) {
        var e_1, _a;
        var otherEncodingNames = [];
        for(var _i = 3; _i < arguments.length; _i++){
            otherEncodingNames[_i - 3] = arguments[_i];
        }
        this.valueIdentifier = valueIdentifier;
        this.name = name;
        if (typeof valuesParam === 'number') {
            this.values = Int32Array.from([
                valuesParam
            ]);
        } else {
            this.values = valuesParam;
        }
        this.otherEncodingNames = otherEncodingNames;
        CharacterSetECI.VALUE_IDENTIFIER_TO_ECI.set(valueIdentifier, this);
        CharacterSetECI.NAME_TO_ECI.set(name, this);
        var values = this.values;
        for(var i = 0, length_1 = values.length; i !== length_1; i++){
            var v = values[i];
            CharacterSetECI.VALUES_TO_ECI.set(v, this);
        }
        try {
            for(var otherEncodingNames_1 = __values(otherEncodingNames), otherEncodingNames_1_1 = otherEncodingNames_1.next(); !otherEncodingNames_1_1.done; otherEncodingNames_1_1 = otherEncodingNames_1.next()){
                var otherName = otherEncodingNames_1_1.value;
                CharacterSetECI.NAME_TO_ECI.set(otherName, this);
            }
        } catch (e_1_1) {
            e_1 = {
                error: e_1_1
            };
        } finally{
            try {
                if (otherEncodingNames_1_1 && !otherEncodingNames_1_1.done && (_a = otherEncodingNames_1.return)) _a.call(otherEncodingNames_1);
            } finally{
                if (e_1) throw e_1.error;
            }
        }
    }
    // CharacterSetECI(value: number /*int*/) {
    //   this(new Int32Array {value})
    // }
    // CharacterSetECI(value: number /*int*/, String... otherEncodingNames) {
    //   this.values = new Int32Array {value}
    //   this.otherEncodingNames = otherEncodingNames
    // }
    // CharacterSetECI(values: Int32Array, String... otherEncodingNames) {
    //   this.values = values
    //   this.otherEncodingNames = otherEncodingNames
    // }
    CharacterSetECI.prototype.getValueIdentifier = function() {
        return this.valueIdentifier;
    };
    CharacterSetECI.prototype.getName = function() {
        return this.name;
    };
    CharacterSetECI.prototype.getValue = function() {
        return this.values[0];
    };
    /**
     * @param value character set ECI value
     * @return {@code CharacterSetECI} representing ECI of given value, or null if it is legal but
     *   unsupported
     * @throws FormatException if ECI value is invalid
     */ CharacterSetECI.getCharacterSetECIByValue = function(value /*int*/ ) {
        if (value < 0 || value >= 900) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('incorect value');
        }
        var characterSet = CharacterSetECI.VALUES_TO_ECI.get(value);
        if (undefined === characterSet) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('incorect value');
        }
        return characterSet;
    };
    /**
     * @param name character set ECI encoding name
     * @return CharacterSetECI representing ECI for character encoding, or null if it is legal
     *   but unsupported
     */ CharacterSetECI.getCharacterSetECIByName = function(name) {
        var characterSet = CharacterSetECI.NAME_TO_ECI.get(name);
        if (undefined === characterSet) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$FormatException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('incorect value');
        }
        return characterSet;
    };
    CharacterSetECI.prototype.equals = function(o) {
        if (!(o instanceof CharacterSetECI)) {
            return false;
        }
        var other = o;
        return this.getName() === other.getName();
    };
    CharacterSetECI.VALUE_IDENTIFIER_TO_ECI = new Map();
    CharacterSetECI.VALUES_TO_ECI = new Map();
    CharacterSetECI.NAME_TO_ECI = new Map();
    // Enum name is a Java encoding valid for java.lang and java.io
    // TYPESCRIPTPORT: changed the main label for ISO as the TextEncoder did not recognized them in the form from java
    // (eg ISO8859_1 must be ISO88591 or ISO8859-1 or ISO-8859-1)
    // later on: well, except 16 wich does not work with ISO885916 so used ISO-8859-1 form for default
    CharacterSetECI.Cp437 = new CharacterSetECI(CharacterSetValueIdentifiers.Cp437, Int32Array.from([
        0,
        2
    ]), 'Cp437');
    CharacterSetECI.ISO8859_1 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_1, Int32Array.from([
        1,
        3
    ]), 'ISO-8859-1', 'ISO88591', 'ISO8859_1');
    CharacterSetECI.ISO8859_2 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_2, 4, 'ISO-8859-2', 'ISO88592', 'ISO8859_2');
    CharacterSetECI.ISO8859_3 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_3, 5, 'ISO-8859-3', 'ISO88593', 'ISO8859_3');
    CharacterSetECI.ISO8859_4 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_4, 6, 'ISO-8859-4', 'ISO88594', 'ISO8859_4');
    CharacterSetECI.ISO8859_5 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_5, 7, 'ISO-8859-5', 'ISO88595', 'ISO8859_5');
    CharacterSetECI.ISO8859_6 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_6, 8, 'ISO-8859-6', 'ISO88596', 'ISO8859_6');
    CharacterSetECI.ISO8859_7 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_7, 9, 'ISO-8859-7', 'ISO88597', 'ISO8859_7');
    CharacterSetECI.ISO8859_8 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_8, 10, 'ISO-8859-8', 'ISO88598', 'ISO8859_8');
    CharacterSetECI.ISO8859_9 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_9, 11, 'ISO-8859-9', 'ISO88599', 'ISO8859_9');
    CharacterSetECI.ISO8859_10 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_10, 12, 'ISO-8859-10', 'ISO885910', 'ISO8859_10');
    CharacterSetECI.ISO8859_11 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_11, 13, 'ISO-8859-11', 'ISO885911', 'ISO8859_11');
    CharacterSetECI.ISO8859_13 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_13, 15, 'ISO-8859-13', 'ISO885913', 'ISO8859_13');
    CharacterSetECI.ISO8859_14 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_14, 16, 'ISO-8859-14', 'ISO885914', 'ISO8859_14');
    CharacterSetECI.ISO8859_15 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_15, 17, 'ISO-8859-15', 'ISO885915', 'ISO8859_15');
    CharacterSetECI.ISO8859_16 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_16, 18, 'ISO-8859-16', 'ISO885916', 'ISO8859_16');
    CharacterSetECI.SJIS = new CharacterSetECI(CharacterSetValueIdentifiers.SJIS, 20, 'SJIS', 'Shift_JIS');
    CharacterSetECI.Cp1250 = new CharacterSetECI(CharacterSetValueIdentifiers.Cp1250, 21, 'Cp1250', 'windows-1250');
    CharacterSetECI.Cp1251 = new CharacterSetECI(CharacterSetValueIdentifiers.Cp1251, 22, 'Cp1251', 'windows-1251');
    CharacterSetECI.Cp1252 = new CharacterSetECI(CharacterSetValueIdentifiers.Cp1252, 23, 'Cp1252', 'windows-1252');
    CharacterSetECI.Cp1256 = new CharacterSetECI(CharacterSetValueIdentifiers.Cp1256, 24, 'Cp1256', 'windows-1256');
    CharacterSetECI.UnicodeBigUnmarked = new CharacterSetECI(CharacterSetValueIdentifiers.UnicodeBigUnmarked, 25, 'UnicodeBigUnmarked', 'UTF-16BE', 'UnicodeBig');
    CharacterSetECI.UTF8 = new CharacterSetECI(CharacterSetValueIdentifiers.UTF8, 26, 'UTF8', 'UTF-8');
    CharacterSetECI.ASCII = new CharacterSetECI(CharacterSetValueIdentifiers.ASCII, Int32Array.from([
        27,
        170
    ]), 'ASCII', 'US-ASCII');
    CharacterSetECI.Big5 = new CharacterSetECI(CharacterSetValueIdentifiers.Big5, 28, 'Big5');
    CharacterSetECI.GB18030 = new CharacterSetECI(CharacterSetValueIdentifiers.GB18030, 29, 'GB18030', 'GB2312', 'EUC_CN', 'GBK');
    CharacterSetECI.EUC_KR = new CharacterSetECI(CharacterSetValueIdentifiers.EUC_KR, 30, 'EUC_KR', 'EUC-KR');
    return CharacterSetECI;
}();
const __TURBOPACK__default__export__ = CharacterSetECI;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/StringUtils.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright (C) 2010 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /*namespace com.google.zxing.common {*/ /*import java.nio.charset.Charset;*/ /*import java.util.Map;*/ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/DecodeHintType.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$CharacterSetECI$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/CharacterSetECI.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringEncoding$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/StringEncoding.js [app-client] (ecmascript)");
;
;
;
/**
 * Common string-related functions.
 *
 * <AUTHOR> Owen
 * <AUTHOR> Dupre
 */ var StringUtils = function() {
    function StringUtils() {}
    // SHIFT_JIS.equalsIgnoreCase(PLATFORM_DEFAULT_ENCODING) ||
    // EUC_JP.equalsIgnoreCase(PLATFORM_DEFAULT_ENCODING);
    StringUtils.castAsNonUtf8Char = function(code, encoding) {
        if (encoding === void 0) {
            encoding = null;
        }
        // ISO 8859-1 is the Java default as UTF-8 is JavaScripts
        // you can see this method as a Java version of String.fromCharCode
        var e = encoding ? encoding.getName() : this.ISO88591;
        // use passed format (fromCharCode will return UTF8 encoding)
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringEncoding$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].decode(new Uint8Array([
            code
        ]), e);
    };
    /**
     * @param bytes bytes encoding a string, whose encoding should be guessed
     * @param hints decode hints if applicable
     * @return name of guessed encoding; at the moment will only guess one of:
     *  {@link #SHIFT_JIS}, {@link #UTF8}, {@link #ISO88591}, or the platform
     *  default encoding if none of these can possibly be correct
     */ StringUtils.guessEncoding = function(bytes, hints) {
        if (hints !== null && hints !== undefined && undefined !== hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].CHARACTER_SET)) {
            return hints.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$DecodeHintType$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].CHARACTER_SET).toString();
        }
        // For now, merely tries to distinguish ISO-8859-1, UTF-8 and Shift_JIS,
        // which should be by far the most common encodings.
        var length = bytes.length;
        var canBeISO88591 = true;
        var canBeShiftJIS = true;
        var canBeUTF8 = true;
        var utf8BytesLeft = 0;
        // int utf8LowChars = 0
        var utf2BytesChars = 0;
        var utf3BytesChars = 0;
        var utf4BytesChars = 0;
        var sjisBytesLeft = 0;
        // int sjisLowChars = 0
        var sjisKatakanaChars = 0;
        // int sjisDoubleBytesChars = 0
        var sjisCurKatakanaWordLength = 0;
        var sjisCurDoubleBytesWordLength = 0;
        var sjisMaxKatakanaWordLength = 0;
        var sjisMaxDoubleBytesWordLength = 0;
        // int isoLowChars = 0
        // int isoHighChars = 0
        var isoHighOther = 0;
        var utf8bom = bytes.length > 3 && bytes[0] === /*(byte) */ 0xEF && bytes[1] === /*(byte) */ 0xBB && bytes[2] === /*(byte) */ 0xBF;
        for(var i = 0; i < length && (canBeISO88591 || canBeShiftJIS || canBeUTF8); i++){
            var value = bytes[i] & 0xFF;
            // UTF-8 stuff
            if (canBeUTF8) {
                if (utf8BytesLeft > 0) {
                    if ((value & 0x80) === 0) {
                        canBeUTF8 = false;
                    } else {
                        utf8BytesLeft--;
                    }
                } else if ((value & 0x80) !== 0) {
                    if ((value & 0x40) === 0) {
                        canBeUTF8 = false;
                    } else {
                        utf8BytesLeft++;
                        if ((value & 0x20) === 0) {
                            utf2BytesChars++;
                        } else {
                            utf8BytesLeft++;
                            if ((value & 0x10) === 0) {
                                utf3BytesChars++;
                            } else {
                                utf8BytesLeft++;
                                if ((value & 0x08) === 0) {
                                    utf4BytesChars++;
                                } else {
                                    canBeUTF8 = false;
                                }
                            }
                        }
                    }
                } // else {
            // utf8LowChars++
            // }
            }
            // ISO-8859-1 stuff
            if (canBeISO88591) {
                if (value > 0x7F && value < 0xA0) {
                    canBeISO88591 = false;
                } else if (value > 0x9F) {
                    if (value < 0xC0 || value === 0xD7 || value === 0xF7) {
                        isoHighOther++;
                    } // else {
                // isoHighChars++
                // }
                } // else {
            // isoLowChars++
            // }
            }
            // Shift_JIS stuff
            if (canBeShiftJIS) {
                if (sjisBytesLeft > 0) {
                    if (value < 0x40 || value === 0x7F || value > 0xFC) {
                        canBeShiftJIS = false;
                    } else {
                        sjisBytesLeft--;
                    }
                } else if (value === 0x80 || value === 0xA0 || value > 0xEF) {
                    canBeShiftJIS = false;
                } else if (value > 0xA0 && value < 0xE0) {
                    sjisKatakanaChars++;
                    sjisCurDoubleBytesWordLength = 0;
                    sjisCurKatakanaWordLength++;
                    if (sjisCurKatakanaWordLength > sjisMaxKatakanaWordLength) {
                        sjisMaxKatakanaWordLength = sjisCurKatakanaWordLength;
                    }
                } else if (value > 0x7F) {
                    sjisBytesLeft++;
                    // sjisDoubleBytesChars++
                    sjisCurKatakanaWordLength = 0;
                    sjisCurDoubleBytesWordLength++;
                    if (sjisCurDoubleBytesWordLength > sjisMaxDoubleBytesWordLength) {
                        sjisMaxDoubleBytesWordLength = sjisCurDoubleBytesWordLength;
                    }
                } else {
                    // sjisLowChars++
                    sjisCurKatakanaWordLength = 0;
                    sjisCurDoubleBytesWordLength = 0;
                }
            }
        }
        if (canBeUTF8 && utf8BytesLeft > 0) {
            canBeUTF8 = false;
        }
        if (canBeShiftJIS && sjisBytesLeft > 0) {
            canBeShiftJIS = false;
        }
        // Easy -- if there is BOM or at least 1 valid not-single byte character (and no evidence it can't be UTF-8), done
        if (canBeUTF8 && (utf8bom || utf2BytesChars + utf3BytesChars + utf4BytesChars > 0)) {
            return StringUtils.UTF8;
        }
        // Easy -- if assuming Shift_JIS or at least 3 valid consecutive not-ascii characters (and no evidence it can't be), done
        if (canBeShiftJIS && (StringUtils.ASSUME_SHIFT_JIS || sjisMaxKatakanaWordLength >= 3 || sjisMaxDoubleBytesWordLength >= 3)) {
            return StringUtils.SHIFT_JIS;
        }
        // Distinguishing Shift_JIS and ISO-8859-1 can be a little tough for short words. The crude heuristic is:
        // - If we saw
        //   - only two consecutive katakana chars in the whole text, or
        //   - at least 10% of bytes that could be "upper" not-alphanumeric Latin1,
        // - then we conclude Shift_JIS, else ISO-8859-1
        if (canBeISO88591 && canBeShiftJIS) {
            return sjisMaxKatakanaWordLength === 2 && sjisKatakanaChars === 2 || isoHighOther * 10 >= length ? StringUtils.SHIFT_JIS : StringUtils.ISO88591;
        }
        // Otherwise, try in order ISO-8859-1, Shift JIS, UTF-8 and fall back to default platform encoding
        if (canBeISO88591) {
            return StringUtils.ISO88591;
        }
        if (canBeShiftJIS) {
            return StringUtils.SHIFT_JIS;
        }
        if (canBeUTF8) {
            return StringUtils.UTF8;
        }
        // Otherwise, we take a wild guess with platform encoding
        return StringUtils.PLATFORM_DEFAULT_ENCODING;
    };
    /**
     *
     * @see https://stackoverflow.com/a/13439711/4367683
     *
     * @param append The new string to append.
     * @param args Argumets values to be formated.
     */ StringUtils.format = function(append) {
        var args = [];
        for(var _i = 1; _i < arguments.length; _i++){
            args[_i - 1] = arguments[_i];
        }
        var i = -1;
        function callback(exp, p0, p1, p2, p3, p4) {
            if (exp === '%%') return '%';
            if (args[++i] === undefined) return undefined;
            exp = p2 ? parseInt(p2.substr(1)) : undefined;
            var base = p3 ? parseInt(p3.substr(1)) : undefined;
            var val;
            switch(p4){
                case 's':
                    val = args[i];
                    break;
                case 'c':
                    val = args[i][0];
                    break;
                case 'f':
                    val = parseFloat(args[i]).toFixed(exp);
                    break;
                case 'p':
                    val = parseFloat(args[i]).toPrecision(exp);
                    break;
                case 'e':
                    val = parseFloat(args[i]).toExponential(exp);
                    break;
                case 'x':
                    val = parseInt(args[i]).toString(base ? base : 16);
                    break;
                case 'd':
                    val = parseFloat(parseInt(args[i], base ? base : 10).toPrecision(exp)).toFixed(0);
                    break;
            }
            val = typeof val === 'object' ? JSON.stringify(val) : (+val).toString(base);
            var size = parseInt(p1); /* padding size */ 
            var ch = p1 && p1[0] + '' === '0' ? '0' : ' '; /* isnull? */ 
            while(val.length < size)val = p0 !== undefined ? val + ch : ch + val; /* isminus? */ 
            return val;
        }
        var regex = /%(-)?(0?[0-9]+)?([.][0-9]+)?([#][0-9]+)?([scfpexd%])/g;
        return append.replace(regex, callback);
    };
    /**
     *
     */ StringUtils.getBytes = function(str, encoding) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringEncoding$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].encode(str, encoding);
    };
    /**
     * Returns the charcode at the specified index or at index zero.
     */ StringUtils.getCharCode = function(str, index) {
        if (index === void 0) {
            index = 0;
        }
        return str.charCodeAt(index);
    };
    /**
     * Returns char for given charcode
     */ StringUtils.getCharAt = function(charCode) {
        return String.fromCharCode(charCode);
    };
    StringUtils.SHIFT_JIS = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$CharacterSetECI$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].SJIS.getName(); // "SJIS"
    StringUtils.GB2312 = 'GB2312';
    StringUtils.ISO88591 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$CharacterSetECI$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].ISO8859_1.getName(); // "ISO8859_1"
    StringUtils.EUC_JP = 'EUC_JP';
    StringUtils.UTF8 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$CharacterSetECI$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].UTF8.getName(); // "UTF8"
    StringUtils.PLATFORM_DEFAULT_ENCODING = StringUtils.UTF8; // "UTF8"//Charset.defaultCharset().name()
    StringUtils.ASSUME_SHIFT_JIS = false;
    return StringUtils;
}();
const __TURBOPACK__default__export__ = StringUtils;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/BitMatrix.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /*namespace com.google.zxing.common {*/ /*import java.util.Arrays;*/ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/BitArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/System.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Arrays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/Arrays.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/StringBuilder.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/IllegalArgumentException.js [app-client] (ecmascript)");
;
;
;
;
;
/**
 * <p>Represents a 2D matrix of bits. In function arguments below, and throughout the common
 * module, x is the column position, and y is the row position. The ordering is always x, y.
 * The origin is at the top-left.</p>
 *
 * <p>Internally the bits are represented in a 1-D array of 32-bit ints. However, each row begins
 * with a new int. This is done intentionally so that we can copy out a row into a BitArray very
 * efficiently.</p>
 *
 * <p>The ordering of bits is row-major. Within each int, the least significant bits are used first,
 * meaning they represent lower x values. This is compatible with BitArray's implementation.</p>
 *
 * <AUTHOR> Owen
 * <AUTHOR> (Daniel Switkin)
 */ var BitMatrix /*implements Cloneable*/  = function() {
    /**
     * Creates an empty square {@link BitMatrix}.
     *
     * @param dimension height and width
     */ // public constructor(dimension: number /*int*/) {
    //   this(dimension, dimension)
    // }
    /**
     * Creates an empty {@link BitMatrix}.
     *
     * @param width bit matrix width
     * @param height bit matrix height
     */ // public constructor(width: number /*int*/, height: number /*int*/) {
    //   if (width < 1 || height < 1) {
    //     throw new IllegalArgumentException("Both dimensions must be greater than 0")
    //   }
    //   this.width = width
    //   this.height = height
    //   this.rowSize = (width + 31) / 32
    //   bits = new int[rowSize * height];
    // }
    function BitMatrix(width /*int*/ , height /*int*/ , rowSize /*int*/ , bits) {
        this.width = width;
        this.height = height;
        this.rowSize = rowSize;
        this.bits = bits;
        if (undefined === height || null === height) {
            height = width;
        }
        this.height = height;
        if (width < 1 || height < 1) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Both dimensions must be greater than 0');
        }
        if (undefined === rowSize || null === rowSize) {
            rowSize = Math.floor((width + 31) / 32);
        }
        this.rowSize = rowSize;
        if (undefined === bits || null === bits) {
            this.bits = new Int32Array(this.rowSize * this.height);
        }
    }
    /**
     * Interprets a 2D array of booleans as a {@link BitMatrix}, where "true" means an "on" bit.
     *
     * @function parse
     * @param image bits of the image, as a row-major 2D array. Elements are arrays representing rows
     * @return {@link BitMatrix} representation of image
     */ BitMatrix.parseFromBooleanArray = function(image) {
        var height = image.length;
        var width = image[0].length;
        var bits = new BitMatrix(width, height);
        for(var i = 0; i < height; i++){
            var imageI = image[i];
            for(var j = 0; j < width; j++){
                if (imageI[j]) {
                    bits.set(j, i);
                }
            }
        }
        return bits;
    };
    /**
     *
     * @function parse
     * @param stringRepresentation
     * @param setString
     * @param unsetString
     */ BitMatrix.parseFromString = function(stringRepresentation, setString, unsetString) {
        if (stringRepresentation === null) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('stringRepresentation cannot be null');
        }
        var bits = new Array(stringRepresentation.length);
        var bitsPos = 0;
        var rowStartPos = 0;
        var rowLength = -1;
        var nRows = 0;
        var pos = 0;
        while(pos < stringRepresentation.length){
            if (stringRepresentation.charAt(pos) === '\n' || stringRepresentation.charAt(pos) === '\r') {
                if (bitsPos > rowStartPos) {
                    if (rowLength === -1) {
                        rowLength = bitsPos - rowStartPos;
                    } else if (bitsPos - rowStartPos !== rowLength) {
                        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('row lengths do not match');
                    }
                    rowStartPos = bitsPos;
                    nRows++;
                }
                pos++;
            } else if (stringRepresentation.substring(pos, pos + setString.length) === setString) {
                pos += setString.length;
                bits[bitsPos] = true;
                bitsPos++;
            } else if (stringRepresentation.substring(pos, pos + unsetString.length) === unsetString) {
                pos += unsetString.length;
                bits[bitsPos] = false;
                bitsPos++;
            } else {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('illegal character encountered: ' + stringRepresentation.substring(pos));
            }
        }
        // no EOL at end?
        if (bitsPos > rowStartPos) {
            if (rowLength === -1) {
                rowLength = bitsPos - rowStartPos;
            } else if (bitsPos - rowStartPos !== rowLength) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('row lengths do not match');
            }
            nRows++;
        }
        var matrix = new BitMatrix(rowLength, nRows);
        for(var i = 0; i < bitsPos; i++){
            if (bits[i]) {
                matrix.set(Math.floor(i % rowLength), Math.floor(i / rowLength));
            }
        }
        return matrix;
    };
    /**
     * <p>Gets the requested bit, where true means black.</p>
     *
     * @param x The horizontal component (i.e. which column)
     * @param y The vertical component (i.e. which row)
     * @return value of given bit in matrix
     */ BitMatrix.prototype.get = function(x /*int*/ , y /*int*/ ) {
        var offset = y * this.rowSize + Math.floor(x / 32);
        return (this.bits[offset] >>> (x & 0x1f) & 1) !== 0;
    };
    /**
     * <p>Sets the given bit to true.</p>
     *
     * @param x The horizontal component (i.e. which column)
     * @param y The vertical component (i.e. which row)
     */ BitMatrix.prototype.set = function(x /*int*/ , y /*int*/ ) {
        var offset = y * this.rowSize + Math.floor(x / 32);
        this.bits[offset] |= 1 << (x & 0x1f) & 0xFFFFFFFF;
    };
    BitMatrix.prototype.unset = function(x /*int*/ , y /*int*/ ) {
        var offset = y * this.rowSize + Math.floor(x / 32);
        this.bits[offset] &= ~(1 << (x & 0x1f) & 0xFFFFFFFF);
    };
    /**
     * <p>Flips the given bit.</p>
     *
     * @param x The horizontal component (i.e. which column)
     * @param y The vertical component (i.e. which row)
     */ BitMatrix.prototype.flip = function(x /*int*/ , y /*int*/ ) {
        var offset = y * this.rowSize + Math.floor(x / 32);
        this.bits[offset] ^= 1 << (x & 0x1f) & 0xFFFFFFFF;
    };
    /**
     * Exclusive-or (XOR): Flip the bit in this {@code BitMatrix} if the corresponding
     * mask bit is set.
     *
     * @param mask XOR mask
     */ BitMatrix.prototype.xor = function(mask) {
        if (this.width !== mask.getWidth() || this.height !== mask.getHeight() || this.rowSize !== mask.getRowSize()) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('input matrix dimensions do not match');
        }
        var rowArray = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](Math.floor(this.width / 32) + 1);
        var rowSize = this.rowSize;
        var bits = this.bits;
        for(var y = 0, height = this.height; y < height; y++){
            var offset = y * rowSize;
            var row = mask.getRow(y, rowArray).getBitArray();
            for(var x = 0; x < rowSize; x++){
                bits[offset + x] ^= row[x];
            }
        }
    };
    /**
     * Clears all bits (sets to false).
     */ BitMatrix.prototype.clear = function() {
        var bits = this.bits;
        var max = bits.length;
        for(var i = 0; i < max; i++){
            bits[i] = 0;
        }
    };
    /**
     * <p>Sets a square region of the bit matrix to true.</p>
     *
     * @param left The horizontal position to begin at (inclusive)
     * @param top The vertical position to begin at (inclusive)
     * @param width The width of the region
     * @param height The height of the region
     */ BitMatrix.prototype.setRegion = function(left /*int*/ , top /*int*/ , width /*int*/ , height /*int*/ ) {
        if (top < 0 || left < 0) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Left and top must be nonnegative');
        }
        if (height < 1 || width < 1) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Height and width must be at least 1');
        }
        var right = left + width;
        var bottom = top + height;
        if (bottom > this.height || right > this.width) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('The region must fit inside the matrix');
        }
        var rowSize = this.rowSize;
        var bits = this.bits;
        for(var y = top; y < bottom; y++){
            var offset = y * rowSize;
            for(var x = left; x < right; x++){
                bits[offset + Math.floor(x / 32)] |= 1 << (x & 0x1f) & 0xFFFFFFFF;
            }
        }
    };
    /**
     * A fast method to retrieve one row of data from the matrix as a BitArray.
     *
     * @param y The row to retrieve
     * @param row An optional caller-allocated BitArray, will be allocated if null or too small
     * @return The resulting BitArray - this reference should always be used even when passing
     *         your own row
     */ BitMatrix.prototype.getRow = function(y /*int*/ , row) {
        if (row === null || row === undefined || row.getSize() < this.width) {
            row = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](this.width);
        } else {
            row.clear();
        }
        var rowSize = this.rowSize;
        var bits = this.bits;
        var offset = y * rowSize;
        for(var x = 0; x < rowSize; x++){
            row.setBulk(x * 32, bits[offset + x]);
        }
        return row;
    };
    /**
     * @param y row to set
     * @param row {@link BitArray} to copy from
     */ BitMatrix.prototype.setRow = function(y /*int*/ , row) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].arraycopy(row.getBitArray(), 0, this.bits, y * this.rowSize, this.rowSize);
    };
    /**
     * Modifies this {@code BitMatrix} to represent the same but rotated 180 degrees
     */ BitMatrix.prototype.rotate180 = function() {
        var width = this.getWidth();
        var height = this.getHeight();
        var topRow = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](width);
        var bottomRow = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](width);
        for(var i = 0, length_1 = Math.floor((height + 1) / 2); i < length_1; i++){
            topRow = this.getRow(i, topRow);
            bottomRow = this.getRow(height - 1 - i, bottomRow);
            topRow.reverse();
            bottomRow.reverse();
            this.setRow(i, bottomRow);
            this.setRow(height - 1 - i, topRow);
        }
    };
    /**
     * This is useful in detecting the enclosing rectangle of a 'pure' barcode.
     *
     * @return {@code left,top,width,height} enclosing rectangle of all 1 bits, or null if it is all white
     */ BitMatrix.prototype.getEnclosingRectangle = function() {
        var width = this.width;
        var height = this.height;
        var rowSize = this.rowSize;
        var bits = this.bits;
        var left = width;
        var top = height;
        var right = -1;
        var bottom = -1;
        for(var y = 0; y < height; y++){
            for(var x32 = 0; x32 < rowSize; x32++){
                var theBits = bits[y * rowSize + x32];
                if (theBits !== 0) {
                    if (y < top) {
                        top = y;
                    }
                    if (y > bottom) {
                        bottom = y;
                    }
                    if (x32 * 32 < left) {
                        var bit = 0;
                        while((theBits << 31 - bit & 0xFFFFFFFF) === 0){
                            bit++;
                        }
                        if (x32 * 32 + bit < left) {
                            left = x32 * 32 + bit;
                        }
                    }
                    if (x32 * 32 + 31 > right) {
                        var bit = 31;
                        while(theBits >>> bit === 0){
                            bit--;
                        }
                        if (x32 * 32 + bit > right) {
                            right = x32 * 32 + bit;
                        }
                    }
                }
            }
        }
        if (right < left || bottom < top) {
            return null;
        }
        return Int32Array.from([
            left,
            top,
            right - left + 1,
            bottom - top + 1
        ]);
    };
    /**
     * This is useful in detecting a corner of a 'pure' barcode.
     *
     * @return {@code x,y} coordinate of top-left-most 1 bit, or null if it is all white
     */ BitMatrix.prototype.getTopLeftOnBit = function() {
        var rowSize = this.rowSize;
        var bits = this.bits;
        var bitsOffset = 0;
        while(bitsOffset < bits.length && bits[bitsOffset] === 0){
            bitsOffset++;
        }
        if (bitsOffset === bits.length) {
            return null;
        }
        var y = bitsOffset / rowSize;
        var x = bitsOffset % rowSize * 32;
        var theBits = bits[bitsOffset];
        var bit = 0;
        while((theBits << 31 - bit & 0xFFFFFFFF) === 0){
            bit++;
        }
        x += bit;
        return Int32Array.from([
            x,
            y
        ]);
    };
    BitMatrix.prototype.getBottomRightOnBit = function() {
        var rowSize = this.rowSize;
        var bits = this.bits;
        var bitsOffset = bits.length - 1;
        while(bitsOffset >= 0 && bits[bitsOffset] === 0){
            bitsOffset--;
        }
        if (bitsOffset < 0) {
            return null;
        }
        var y = Math.floor(bitsOffset / rowSize);
        var x = Math.floor(bitsOffset % rowSize) * 32;
        var theBits = bits[bitsOffset];
        var bit = 31;
        while(theBits >>> bit === 0){
            bit--;
        }
        x += bit;
        return Int32Array.from([
            x,
            y
        ]);
    };
    /**
     * @return The width of the matrix
     */ BitMatrix.prototype.getWidth = function() {
        return this.width;
    };
    /**
     * @return The height of the matrix
     */ BitMatrix.prototype.getHeight = function() {
        return this.height;
    };
    /**
     * @return The row size of the matrix
     */ BitMatrix.prototype.getRowSize = function() {
        return this.rowSize;
    };
    /*@Override*/ BitMatrix.prototype.equals = function(o) {
        if (!(o instanceof BitMatrix)) {
            return false;
        }
        var other = o;
        return this.width === other.width && this.height === other.height && this.rowSize === other.rowSize && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Arrays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].equals(this.bits, other.bits);
    };
    /*@Override*/ BitMatrix.prototype.hashCode = function() {
        var hash = this.width;
        hash = 31 * hash + this.width;
        hash = 31 * hash + this.height;
        hash = 31 * hash + this.rowSize;
        hash = 31 * hash + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Arrays$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].hashCode(this.bits);
        return hash;
    };
    /**
     * @return string representation using "X" for set and " " for unset bits
     */ /*@Override*/ // public toString(): string {
    //   return toString(": "X, "  ")
    // }
    /**
     * @param setString representation of a set bit
     * @param unsetString representation of an unset bit
     * @return string representation of entire matrix utilizing given strings
     */ // public toString(setString: string = "X ", unsetString: string = "  "): string {
    //   return this.buildToString(setString, unsetString, "\n")
    // }
    /**
     * @param setString representation of a set bit
     * @param unsetString representation of an unset bit
     * @param lineSeparator newline character in string representation
     * @return string representation of entire matrix utilizing given strings and line separator
     * @deprecated call {@link #toString(String,String)} only, which uses \n line separator always
     */ // @Deprecated
    BitMatrix.prototype.toString = function(setString, unsetString, lineSeparator) {
        if (setString === void 0) {
            setString = 'X ';
        }
        if (unsetString === void 0) {
            unsetString = '  ';
        }
        if (lineSeparator === void 0) {
            lineSeparator = '\n';
        }
        return this.buildToString(setString, unsetString, lineSeparator);
    };
    BitMatrix.prototype.buildToString = function(setString, unsetString, lineSeparator) {
        var result = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        // result.append(lineSeparator);
        for(var y = 0, height = this.height; y < height; y++){
            for(var x = 0, width = this.width; x < width; x++){
                result.append(this.get(x, y) ? setString : unsetString);
            }
            result.append(lineSeparator);
        }
        return result.toString();
    };
    /*@Override*/ BitMatrix.prototype.clone = function() {
        return new BitMatrix(this.width, this.height, this.rowSize, this.bits.slice());
    };
    return BitMatrix;
}();
const __TURBOPACK__default__export__ = BitMatrix;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/GlobalHistogramBinarizer.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2009 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
/*namespace com.google.zxing.common {*/ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Binarizer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/Binarizer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/BitArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitMatrix$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/BitMatrix.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NotFoundException.js [app-client] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
;
;
/**
 * This Binarizer implementation uses the old ZXing global histogram approach. It is suitable
 * for low-end mobile devices which don't have enough CPU or memory to use a local thresholding
 * algorithm. However, because it picks a global black point, it cannot handle difficult shadows
 * and gradients.
 *
 * Faster mobile devices and all desktop applications should probably use HybridBinarizer instead.
 *
 * <AUTHOR> (Daniel Switkin)
 * <AUTHOR> Owen
 */ var GlobalHistogramBinarizer = function(_super) {
    __extends(GlobalHistogramBinarizer, _super);
    function GlobalHistogramBinarizer(source) {
        var _this = _super.call(this, source) || this;
        _this.luminances = GlobalHistogramBinarizer.EMPTY;
        _this.buckets = new Int32Array(GlobalHistogramBinarizer.LUMINANCE_BUCKETS);
        return _this;
    }
    // Applies simple sharpening to the row data to improve performance of the 1D Readers.
    /*@Override*/ GlobalHistogramBinarizer.prototype.getBlackRow = function(y /*int*/ , row) {
        var source = this.getLuminanceSource();
        var width = source.getWidth();
        if (row === undefined || row === null || row.getSize() < width) {
            row = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](width);
        } else {
            row.clear();
        }
        this.initArrays(width);
        var localLuminances = source.getRow(y, this.luminances);
        var localBuckets = this.buckets;
        for(var x = 0; x < width; x++){
            localBuckets[(localLuminances[x] & 0xff) >> GlobalHistogramBinarizer.LUMINANCE_SHIFT]++;
        }
        var blackPoint = GlobalHistogramBinarizer.estimateBlackPoint(localBuckets);
        if (width < 3) {
            // Special case for very small images
            for(var x = 0; x < width; x++){
                if ((localLuminances[x] & 0xff) < blackPoint) {
                    row.set(x);
                }
            }
        } else {
            var left = localLuminances[0] & 0xff;
            var center = localLuminances[1] & 0xff;
            for(var x = 1; x < width - 1; x++){
                var right = localLuminances[x + 1] & 0xff;
                // A simple -1 4 -1 box filter with a weight of 2.
                if ((center * 4 - left - right) / 2 < blackPoint) {
                    row.set(x);
                }
                left = center;
                center = right;
            }
        }
        return row;
    };
    // Does not sharpen the data, as this call is intended to only be used by 2D Readers.
    /*@Override*/ GlobalHistogramBinarizer.prototype.getBlackMatrix = function() {
        var source = this.getLuminanceSource();
        var width = source.getWidth();
        var height = source.getHeight();
        var matrix = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitMatrix$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](width, height);
        // Quickly calculates the histogram by sampling four rows from the image. This proved to be
        // more robust on the blackbox tests than sampling a diagonal as we used to do.
        this.initArrays(width);
        var localBuckets = this.buckets;
        for(var y = 1; y < 5; y++){
            var row = Math.floor(height * y / 5);
            var localLuminances_1 = source.getRow(row, this.luminances);
            var right = Math.floor(width * 4 / 5);
            for(var x = Math.floor(width / 5); x < right; x++){
                var pixel = localLuminances_1[x] & 0xff;
                localBuckets[pixel >> GlobalHistogramBinarizer.LUMINANCE_SHIFT]++;
            }
        }
        var blackPoint = GlobalHistogramBinarizer.estimateBlackPoint(localBuckets);
        // We delay reading the entire image luminance until the black point estimation succeeds.
        // Although we end up reading four rows twice, it is consistent with our motto of
        // "fail quickly" which is necessary for continuous scanning.
        var localLuminances = source.getMatrix();
        for(var y = 0; y < height; y++){
            var offset = y * width;
            for(var x = 0; x < width; x++){
                var pixel = localLuminances[offset + x] & 0xff;
                if (pixel < blackPoint) {
                    matrix.set(x, y);
                }
            }
        }
        return matrix;
    };
    /*@Override*/ GlobalHistogramBinarizer.prototype.createBinarizer = function(source) {
        return new GlobalHistogramBinarizer(source);
    };
    GlobalHistogramBinarizer.prototype.initArrays = function(luminanceSize /*int*/ ) {
        if (this.luminances.length < luminanceSize) {
            this.luminances = new Uint8ClampedArray(luminanceSize);
        }
        var buckets = this.buckets;
        for(var x = 0; x < GlobalHistogramBinarizer.LUMINANCE_BUCKETS; x++){
            buckets[x] = 0;
        }
    };
    GlobalHistogramBinarizer.estimateBlackPoint = function(buckets) {
        // Find the tallest peak in the histogram.
        var numBuckets = buckets.length;
        var maxBucketCount = 0;
        var firstPeak = 0;
        var firstPeakSize = 0;
        for(var x = 0; x < numBuckets; x++){
            if (buckets[x] > firstPeakSize) {
                firstPeak = x;
                firstPeakSize = buckets[x];
            }
            if (buckets[x] > maxBucketCount) {
                maxBucketCount = buckets[x];
            }
        }
        // Find the second-tallest peak which is somewhat far from the tallest peak.
        var secondPeak = 0;
        var secondPeakScore = 0;
        for(var x = 0; x < numBuckets; x++){
            var distanceToBiggest = x - firstPeak;
            // Encourage more distant second peaks by multiplying by square of distance.
            var score = buckets[x] * distanceToBiggest * distanceToBiggest;
            if (score > secondPeakScore) {
                secondPeak = x;
                secondPeakScore = score;
            }
        }
        // Make sure firstPeak corresponds to the black peak.
        if (firstPeak > secondPeak) {
            var temp = firstPeak;
            firstPeak = secondPeak;
            secondPeak = temp;
        }
        // If there is too little contrast in the image to pick a meaningful black point, throw rather
        // than waste time trying to decode the image, and risk false positives.
        if (secondPeak - firstPeak <= numBuckets / 16) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        // Find a valley between them that is low and closer to the white peak.
        var bestValley = secondPeak - 1;
        var bestValleyScore = -1;
        for(var x = secondPeak - 1; x > firstPeak; x--){
            var fromFirst = x - firstPeak;
            var score = fromFirst * fromFirst * (secondPeak - x) * (maxBucketCount - buckets[x]);
            if (score > bestValleyScore) {
                bestValley = x;
                bestValleyScore = score;
            }
        }
        return bestValley << GlobalHistogramBinarizer.LUMINANCE_SHIFT;
    };
    GlobalHistogramBinarizer.LUMINANCE_BITS = 5;
    GlobalHistogramBinarizer.LUMINANCE_SHIFT = 8 - GlobalHistogramBinarizer.LUMINANCE_BITS;
    GlobalHistogramBinarizer.LUMINANCE_BUCKETS = 1 << GlobalHistogramBinarizer.LUMINANCE_BITS;
    GlobalHistogramBinarizer.EMPTY = Uint8ClampedArray.from([
        0
    ]);
    return GlobalHistogramBinarizer;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$Binarizer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = GlobalHistogramBinarizer;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/HybridBinarizer.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2009 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$GlobalHistogramBinarizer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/GlobalHistogramBinarizer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitMatrix$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/BitMatrix.js [app-client] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
/**
 * This class implements a local thresholding algorithm, which while slower than the
 * GlobalHistogramBinarizer, is fairly efficient for what it does. It is designed for
 * high frequency images of barcodes with black data on white backgrounds. For this application,
 * it does a much better job than a global blackpoint with severe shadows and gradients.
 * However it tends to produce artifacts on lower frequency images and is therefore not
 * a good general purpose binarizer for uses outside ZXing.
 *
 * This class extends GlobalHistogramBinarizer, using the older histogram approach for 1D readers,
 * and the newer local approach for 2D readers. 1D decoding using a per-row histogram is already
 * inherently local, and only fails for horizontal gradients. We can revisit that problem later,
 * but for now it was not a win to use local blocks for 1D.
 *
 * This Binarizer is the default for the unit tests and the recommended class for library users.
 *
 * <AUTHOR> (Daniel Switkin)
 */ var HybridBinarizer = function(_super) {
    __extends(HybridBinarizer, _super);
    function HybridBinarizer(source) {
        var _this = _super.call(this, source) || this;
        _this.matrix = null;
        return _this;
    }
    /**
     * Calculates the final BitMatrix once for all requests. This could be called once from the
     * constructor instead, but there are some advantages to doing it lazily, such as making
     * profiling easier, and not doing heavy lifting when callers don't expect it.
     */ /*@Override*/ HybridBinarizer.prototype.getBlackMatrix = function() {
        if (this.matrix !== null) {
            return this.matrix;
        }
        var source = this.getLuminanceSource();
        var width = source.getWidth();
        var height = source.getHeight();
        if (width >= HybridBinarizer.MINIMUM_DIMENSION && height >= HybridBinarizer.MINIMUM_DIMENSION) {
            var luminances = source.getMatrix();
            var subWidth = width >> HybridBinarizer.BLOCK_SIZE_POWER;
            if ((width & HybridBinarizer.BLOCK_SIZE_MASK) !== 0) {
                subWidth++;
            }
            var subHeight = height >> HybridBinarizer.BLOCK_SIZE_POWER;
            if ((height & HybridBinarizer.BLOCK_SIZE_MASK) !== 0) {
                subHeight++;
            }
            var blackPoints = HybridBinarizer.calculateBlackPoints(luminances, subWidth, subHeight, width, height);
            var newMatrix = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitMatrix$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](width, height);
            HybridBinarizer.calculateThresholdForBlock(luminances, subWidth, subHeight, width, height, blackPoints, newMatrix);
            this.matrix = newMatrix;
        } else {
            // If the image is too small, fall back to the global histogram approach.
            this.matrix = _super.prototype.getBlackMatrix.call(this);
        }
        return this.matrix;
    };
    /*@Override*/ HybridBinarizer.prototype.createBinarizer = function(source) {
        return new HybridBinarizer(source);
    };
    /**
     * For each block in the image, calculate the average black point using a 5x5 grid
     * of the blocks around it. Also handles the corner cases (fractional blocks are computed based
     * on the last pixels in the row/column which are also used in the previous block).
     */ HybridBinarizer.calculateThresholdForBlock = function(luminances, subWidth /*int*/ , subHeight /*int*/ , width /*int*/ , height /*int*/ , blackPoints, matrix) {
        var maxYOffset = height - HybridBinarizer.BLOCK_SIZE;
        var maxXOffset = width - HybridBinarizer.BLOCK_SIZE;
        for(var y = 0; y < subHeight; y++){
            var yoffset = y << HybridBinarizer.BLOCK_SIZE_POWER;
            if (yoffset > maxYOffset) {
                yoffset = maxYOffset;
            }
            var top_1 = HybridBinarizer.cap(y, 2, subHeight - 3);
            for(var x = 0; x < subWidth; x++){
                var xoffset = x << HybridBinarizer.BLOCK_SIZE_POWER;
                if (xoffset > maxXOffset) {
                    xoffset = maxXOffset;
                }
                var left = HybridBinarizer.cap(x, 2, subWidth - 3);
                var sum = 0;
                for(var z = -2; z <= 2; z++){
                    var blackRow = blackPoints[top_1 + z];
                    sum += blackRow[left - 2] + blackRow[left - 1] + blackRow[left] + blackRow[left + 1] + blackRow[left + 2];
                }
                var average = sum / 25;
                HybridBinarizer.thresholdBlock(luminances, xoffset, yoffset, average, width, matrix);
            }
        }
    };
    HybridBinarizer.cap = function(value /*int*/ , min /*int*/ , max /*int*/ ) {
        return value < min ? min : value > max ? max : value;
    };
    /**
     * Applies a single threshold to a block of pixels.
     */ HybridBinarizer.thresholdBlock = function(luminances, xoffset /*int*/ , yoffset /*int*/ , threshold /*int*/ , stride /*int*/ , matrix) {
        for(var y = 0, offset = yoffset * stride + xoffset; y < HybridBinarizer.BLOCK_SIZE; y++, offset += stride){
            for(var x = 0; x < HybridBinarizer.BLOCK_SIZE; x++){
                // Comparison needs to be <= so that black == 0 pixels are black even if the threshold is 0.
                if ((luminances[offset + x] & 0xFF) <= threshold) {
                    matrix.set(xoffset + x, yoffset + y);
                }
            }
        }
    };
    /**
     * Calculates a single black point for each block of pixels and saves it away.
     * See the following thread for a discussion of this algorithm:
     *  http://groups.google.com/group/zxing/browse_thread/thread/d06efa2c35a7ddc0
     */ HybridBinarizer.calculateBlackPoints = function(luminances, subWidth /*int*/ , subHeight /*int*/ , width /*int*/ , height /*int*/ ) {
        var maxYOffset = height - HybridBinarizer.BLOCK_SIZE;
        var maxXOffset = width - HybridBinarizer.BLOCK_SIZE;
        // tslint:disable-next-line:whitespace
        var blackPoints = new Array(subHeight); // subWidth
        for(var y = 0; y < subHeight; y++){
            blackPoints[y] = new Int32Array(subWidth);
            var yoffset = y << HybridBinarizer.BLOCK_SIZE_POWER;
            if (yoffset > maxYOffset) {
                yoffset = maxYOffset;
            }
            for(var x = 0; x < subWidth; x++){
                var xoffset = x << HybridBinarizer.BLOCK_SIZE_POWER;
                if (xoffset > maxXOffset) {
                    xoffset = maxXOffset;
                }
                var sum = 0;
                var min = 0xFF;
                var max = 0;
                for(var yy = 0, offset = yoffset * width + xoffset; yy < HybridBinarizer.BLOCK_SIZE; yy++, offset += width){
                    for(var xx = 0; xx < HybridBinarizer.BLOCK_SIZE; xx++){
                        var pixel = luminances[offset + xx] & 0xFF;
                        sum += pixel;
                        // still looking for good contrast
                        if (pixel < min) {
                            min = pixel;
                        }
                        if (pixel > max) {
                            max = pixel;
                        }
                    }
                    // short-circuit min/max tests once dynamic range is met
                    if (max - min > HybridBinarizer.MIN_DYNAMIC_RANGE) {
                        // finish the rest of the rows quickly
                        for(yy++, offset += width; yy < HybridBinarizer.BLOCK_SIZE; yy++, offset += width){
                            for(var xx = 0; xx < HybridBinarizer.BLOCK_SIZE; xx++){
                                sum += luminances[offset + xx] & 0xFF;
                            }
                        }
                    }
                }
                // The default estimate is the average of the values in the block.
                var average = sum >> HybridBinarizer.BLOCK_SIZE_POWER * 2;
                if (max - min <= HybridBinarizer.MIN_DYNAMIC_RANGE) {
                    // If variation within the block is low, assume this is a block with only light or only
                    // dark pixels. In that case we do not want to use the average, as it would divide this
                    // low contrast area into black and white pixels, essentially creating data out of noise.
                    //
                    // The default assumption is that the block is light/background. Since no estimate for
                    // the level of dark pixels exists locally, use half the min for the block.
                    average = min / 2;
                    if (y > 0 && x > 0) {
                        // Correct the "white background" assumption for blocks that have neighbors by comparing
                        // the pixels in this block to the previously calculated black points. This is based on
                        // the fact that dark barcode symbology is always surrounded by some amount of light
                        // background for which reasonable black point estimates were made. The bp estimated at
                        // the boundaries is used for the interior.
                        // The (min < bp) is arbitrary but works better than other heuristics that were tried.
                        var averageNeighborBlackPoint = (blackPoints[y - 1][x] + 2 * blackPoints[y][x - 1] + blackPoints[y - 1][x - 1]) / 4;
                        if (min < averageNeighborBlackPoint) {
                            average = averageNeighborBlackPoint;
                        }
                    }
                }
                blackPoints[y][x] = average;
            }
        }
        return blackPoints;
    };
    // This class uses 5x5 blocks to compute local luminance, where each block is 8x8 pixels.
    // So this is the smallest dimension in each axis we can accept.
    HybridBinarizer.BLOCK_SIZE_POWER = 3;
    HybridBinarizer.BLOCK_SIZE = 1 << HybridBinarizer.BLOCK_SIZE_POWER; // ...0100...00
    HybridBinarizer.BLOCK_SIZE_MASK = HybridBinarizer.BLOCK_SIZE - 1; // ...0011...11
    HybridBinarizer.MINIMUM_DIMENSION = HybridBinarizer.BLOCK_SIZE * 5;
    HybridBinarizer.MIN_DYNAMIC_RANGE = 24;
    return HybridBinarizer;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$GlobalHistogramBinarizer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = HybridBinarizer;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/DecoderResult.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /*namespace com.google.zxing.common {*/ /*import java.util.List;*/ /**
 * <p>Encapsulates the result of decoding a matrix of bits. This typically
 * applies to 2D barcode formats. For now it contains the raw bytes obtained,
 * as well as a String interpretation of those bytes, if applicable.</p>
 *
 * <AUTHOR> Owen
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var DecoderResult = function() {
    // public constructor(rawBytes: Uint8Array,
    //                      text: string,
    //                      List<Uint8Array> byteSegments,
    //                      String ecLevel) {
    //   this(rawBytes, text, byteSegments, ecLevel, -1, -1)
    // }
    function DecoderResult(rawBytes, text, byteSegments, ecLevel, structuredAppendSequenceNumber, structuredAppendParity) {
        if (structuredAppendSequenceNumber === void 0) {
            structuredAppendSequenceNumber = -1;
        }
        if (structuredAppendParity === void 0) {
            structuredAppendParity = -1;
        }
        this.rawBytes = rawBytes;
        this.text = text;
        this.byteSegments = byteSegments;
        this.ecLevel = ecLevel;
        this.structuredAppendSequenceNumber = structuredAppendSequenceNumber;
        this.structuredAppendParity = structuredAppendParity;
        this.numBits = rawBytes === undefined || rawBytes === null ? 0 : 8 * rawBytes.length;
    }
    /**
     * @return raw bytes representing the result, or {@code null} if not applicable
     */ DecoderResult.prototype.getRawBytes = function() {
        return this.rawBytes;
    };
    /**
     * @return how many bits of {@link #getRawBytes()} are valid; typically 8 times its length
     * @since 3.3.0
     */ DecoderResult.prototype.getNumBits = function() {
        return this.numBits;
    };
    /**
     * @param numBits overrides the number of bits that are valid in {@link #getRawBytes()}
     * @since 3.3.0
     */ DecoderResult.prototype.setNumBits = function(numBits /*int*/ ) {
        this.numBits = numBits;
    };
    /**
     * @return text representation of the result
     */ DecoderResult.prototype.getText = function() {
        return this.text;
    };
    /**
     * @return list of byte segments in the result, or {@code null} if not applicable
     */ DecoderResult.prototype.getByteSegments = function() {
        return this.byteSegments;
    };
    /**
     * @return name of error correction level used, or {@code null} if not applicable
     */ DecoderResult.prototype.getECLevel = function() {
        return this.ecLevel;
    };
    /**
     * @return number of errors corrected, or {@code null} if not applicable
     */ DecoderResult.prototype.getErrorsCorrected = function() {
        return this.errorsCorrected;
    };
    DecoderResult.prototype.setErrorsCorrected = function(errorsCorrected /*Integer*/ ) {
        this.errorsCorrected = errorsCorrected;
    };
    /**
     * @return number of erasures corrected, or {@code null} if not applicable
     */ DecoderResult.prototype.getErasures = function() {
        return this.erasures;
    };
    DecoderResult.prototype.setErasures = function(erasures /*Integer*/ ) {
        this.erasures = erasures;
    };
    /**
     * @return arbitrary additional metadata
     */ DecoderResult.prototype.getOther = function() {
        return this.other;
    };
    DecoderResult.prototype.setOther = function(other) {
        this.other = other;
    };
    DecoderResult.prototype.hasStructuredAppend = function() {
        return this.structuredAppendParity >= 0 && this.structuredAppendSequenceNumber >= 0;
    };
    DecoderResult.prototype.getStructuredAppendParity = function() {
        return this.structuredAppendParity;
    };
    DecoderResult.prototype.getStructuredAppendSequenceNumber = function() {
        return this.structuredAppendSequenceNumber;
    };
    return DecoderResult;
}();
const __TURBOPACK__default__export__ = DecoderResult;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/reedsolomon/AbstractGenericGF.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/IllegalArgumentException.js [app-client] (ecmascript)");
;
/**
 * <p>This class contains utility methods for performing mathematical operations over
 * the Galois Fields. Operations use a given primitive polynomial in calculations.</p>
 *
 * <p>Throughout this package, elements of the GF are represented as an {@code int}
 * for convenience and speed (but at the cost of memory).
 * </p>
 *
 * <AUTHOR> Owen
 * <AUTHOR> Olivier
 */ var AbstractGenericGF = function() {
    function AbstractGenericGF() {}
    /**
     * @return 2 to the power of a in GF(size)
     */ AbstractGenericGF.prototype.exp = function(a) {
        return this.expTable[a];
    };
    /**
     * @return base 2 log of a in GF(size)
     */ AbstractGenericGF.prototype.log = function(a /*int*/ ) {
        if (a === 0) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        return this.logTable[a];
    };
    /**
     * Implements both addition and subtraction -- they are the same in GF(size).
     *
     * @return sum/difference of a and b
     */ AbstractGenericGF.addOrSubtract = function(a /*int*/ , b /*int*/ ) {
        return a ^ b;
    };
    return AbstractGenericGF;
}();
const __TURBOPACK__default__export__ = AbstractGenericGF;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/reedsolomon/GenericGFPoly.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /*namespace com.google.zxing.common.reedsolomon {*/ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$AbstractGenericGF$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/reedsolomon/AbstractGenericGF.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/System.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/IllegalArgumentException.js [app-client] (ecmascript)");
;
;
;
/**
 * <p>Represents a polynomial whose coefficients are elements of a GF.
 * Instances of this class are immutable.</p>
 *
 * <p>Much credit is due to William Rucklidge since portions of this code are an indirect
 * port of his C++ Reed-Solomon implementation.</p>
 *
 * <AUTHOR> Owen
 */ var GenericGFPoly = function() {
    /**
     * @param field the {@link GenericGF} instance representing the field to use
     * to perform computations
     * @param coefficients coefficients as ints representing elements of GF(size), arranged
     * from most significant (highest-power term) coefficient to least significant
     * @throws IllegalArgumentException if argument is null or empty,
     * or if leading coefficient is 0 and this is not a
     * constant polynomial (that is, it is not the monomial "0")
     */ function GenericGFPoly(field, coefficients) {
        if (coefficients.length === 0) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        this.field = field;
        var coefficientsLength = coefficients.length;
        if (coefficientsLength > 1 && coefficients[0] === 0) {
            // Leading term must be non-zero for anything except the constant polynomial "0"
            var firstNonZero = 1;
            while(firstNonZero < coefficientsLength && coefficients[firstNonZero] === 0){
                firstNonZero++;
            }
            if (firstNonZero === coefficientsLength) {
                this.coefficients = Int32Array.from([
                    0
                ]);
            } else {
                this.coefficients = new Int32Array(coefficientsLength - firstNonZero);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].arraycopy(coefficients, firstNonZero, this.coefficients, 0, this.coefficients.length);
            }
        } else {
            this.coefficients = coefficients;
        }
    }
    GenericGFPoly.prototype.getCoefficients = function() {
        return this.coefficients;
    };
    /**
     * @return degree of this polynomial
     */ GenericGFPoly.prototype.getDegree = function() {
        return this.coefficients.length - 1;
    };
    /**
     * @return true iff this polynomial is the monomial "0"
     */ GenericGFPoly.prototype.isZero = function() {
        return this.coefficients[0] === 0;
    };
    /**
     * @return coefficient of x^degree term in this polynomial
     */ GenericGFPoly.prototype.getCoefficient = function(degree /*int*/ ) {
        return this.coefficients[this.coefficients.length - 1 - degree];
    };
    /**
     * @return evaluation of this polynomial at a given point
     */ GenericGFPoly.prototype.evaluateAt = function(a /*int*/ ) {
        if (a === 0) {
            // Just return the x^0 coefficient
            return this.getCoefficient(0);
        }
        var coefficients = this.coefficients;
        var result;
        if (a === 1) {
            // Just the sum of the coefficients
            result = 0;
            for(var i = 0, length_1 = coefficients.length; i !== length_1; i++){
                var coefficient = coefficients[i];
                result = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$AbstractGenericGF$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].addOrSubtract(result, coefficient);
            }
            return result;
        }
        result = coefficients[0];
        var size = coefficients.length;
        var field = this.field;
        for(var i = 1; i < size; i++){
            result = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$AbstractGenericGF$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].addOrSubtract(field.multiply(a, result), coefficients[i]);
        }
        return result;
    };
    GenericGFPoly.prototype.addOrSubtract = function(other) {
        if (!this.field.equals(other.field)) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('GenericGFPolys do not have same GenericGF field');
        }
        if (this.isZero()) {
            return other;
        }
        if (other.isZero()) {
            return this;
        }
        var smallerCoefficients = this.coefficients;
        var largerCoefficients = other.coefficients;
        if (smallerCoefficients.length > largerCoefficients.length) {
            var temp = smallerCoefficients;
            smallerCoefficients = largerCoefficients;
            largerCoefficients = temp;
        }
        var sumDiff = new Int32Array(largerCoefficients.length);
        var lengthDiff = largerCoefficients.length - smallerCoefficients.length;
        // Copy high-order terms only found in higher-degree polynomial's coefficients
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].arraycopy(largerCoefficients, 0, sumDiff, 0, lengthDiff);
        for(var i = lengthDiff; i < largerCoefficients.length; i++){
            sumDiff[i] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$AbstractGenericGF$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].addOrSubtract(smallerCoefficients[i - lengthDiff], largerCoefficients[i]);
        }
        return new GenericGFPoly(this.field, sumDiff);
    };
    GenericGFPoly.prototype.multiply = function(other) {
        if (!this.field.equals(other.field)) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('GenericGFPolys do not have same GenericGF field');
        }
        if (this.isZero() || other.isZero()) {
            return this.field.getZero();
        }
        var aCoefficients = this.coefficients;
        var aLength = aCoefficients.length;
        var bCoefficients = other.coefficients;
        var bLength = bCoefficients.length;
        var product = new Int32Array(aLength + bLength - 1);
        var field = this.field;
        for(var i = 0; i < aLength; i++){
            var aCoeff = aCoefficients[i];
            for(var j = 0; j < bLength; j++){
                product[i + j] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$AbstractGenericGF$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].addOrSubtract(product[i + j], field.multiply(aCoeff, bCoefficients[j]));
            }
        }
        return new GenericGFPoly(field, product);
    };
    GenericGFPoly.prototype.multiplyScalar = function(scalar /*int*/ ) {
        if (scalar === 0) {
            return this.field.getZero();
        }
        if (scalar === 1) {
            return this;
        }
        var size = this.coefficients.length;
        var field = this.field;
        var product = new Int32Array(size);
        var coefficients = this.coefficients;
        for(var i = 0; i < size; i++){
            product[i] = field.multiply(coefficients[i], scalar);
        }
        return new GenericGFPoly(field, product);
    };
    GenericGFPoly.prototype.multiplyByMonomial = function(degree /*int*/ , coefficient /*int*/ ) {
        if (degree < 0) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        if (coefficient === 0) {
            return this.field.getZero();
        }
        var coefficients = this.coefficients;
        var size = coefficients.length;
        var product = new Int32Array(size + degree);
        var field = this.field;
        for(var i = 0; i < size; i++){
            product[i] = field.multiply(coefficients[i], coefficient);
        }
        return new GenericGFPoly(field, product);
    };
    GenericGFPoly.prototype.divide = function(other) {
        if (!this.field.equals(other.field)) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('GenericGFPolys do not have same GenericGF field');
        }
        if (other.isZero()) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Divide by 0');
        }
        var field = this.field;
        var quotient = field.getZero();
        var remainder = this;
        var denominatorLeadingTerm = other.getCoefficient(other.getDegree());
        var inverseDenominatorLeadingTerm = field.inverse(denominatorLeadingTerm);
        while(remainder.getDegree() >= other.getDegree() && !remainder.isZero()){
            var degreeDifference = remainder.getDegree() - other.getDegree();
            var scale = field.multiply(remainder.getCoefficient(remainder.getDegree()), inverseDenominatorLeadingTerm);
            var term = other.multiplyByMonomial(degreeDifference, scale);
            var iterationQuotient = field.buildMonomial(degreeDifference, scale);
            quotient = quotient.addOrSubtract(iterationQuotient);
            remainder = remainder.addOrSubtract(term);
        }
        return [
            quotient,
            remainder
        ];
    };
    /*@Override*/ GenericGFPoly.prototype.toString = function() {
        var result = '';
        for(var degree = this.getDegree(); degree >= 0; degree--){
            var coefficient = this.getCoefficient(degree);
            if (coefficient !== 0) {
                if (coefficient < 0) {
                    result += ' - ';
                    coefficient = -coefficient;
                } else {
                    if (result.length > 0) {
                        result += ' + ';
                    }
                }
                if (degree === 0 || coefficient !== 1) {
                    var alphaPower = this.field.log(coefficient);
                    if (alphaPower === 0) {
                        result += '1';
                    } else if (alphaPower === 1) {
                        result += 'a';
                    } else {
                        result += 'a^';
                        result += alphaPower;
                    }
                }
                if (degree !== 0) {
                    if (degree === 1) {
                        result += 'x';
                    } else {
                        result += 'x^';
                        result += degree;
                    }
                }
            }
        }
        return result;
    };
    return GenericGFPoly;
}();
const __TURBOPACK__default__export__ = GenericGFPoly;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/reedsolomon/GenericGF.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
/*namespace com.google.zxing.common.reedsolomon {*/ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGFPoly$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/reedsolomon/GenericGFPoly.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$AbstractGenericGF$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/reedsolomon/AbstractGenericGF.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/Integer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/IllegalArgumentException.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ArithmeticException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ArithmeticException.js [app-client] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
;
;
;
/**
 * <p>This class contains utility methods for performing mathematical operations over
 * the Galois Fields. Operations use a given primitive polynomial in calculations.</p>
 *
 * <p>Throughout this package, elements of the GF are represented as an {@code int}
 * for convenience and speed (but at the cost of memory).
 * </p>
 *
 * <AUTHOR> Owen
 * <AUTHOR> Olivier
 */ var GenericGF = function(_super) {
    __extends(GenericGF, _super);
    /**
     * Create a representation of GF(size) using the given primitive polynomial.
     *
     * @param primitive irreducible polynomial whose coefficients are represented by
     *  the bits of an int, where the least-significant bit represents the constant
     *  coefficient
     * @param size the size of the field
     * @param b the factor b in the generator polynomial can be 0- or 1-based
     *  (g(x) = (x+a^b)(x+a^(b+1))...(x+a^(b+2t-1))).
     *  In most cases it should be 1, but for QR code it is 0.
     */ function GenericGF(primitive /*int*/ , size /*int*/ , generatorBase /*int*/ ) {
        var _this = _super.call(this) || this;
        _this.primitive = primitive;
        _this.size = size;
        _this.generatorBase = generatorBase;
        var expTable = new Int32Array(size);
        var x = 1;
        for(var i = 0; i < size; i++){
            expTable[i] = x;
            x *= 2; // we're assuming the generator alpha is 2
            if (x >= size) {
                x ^= primitive;
                x &= size - 1;
            }
        }
        _this.expTable = expTable;
        var logTable = new Int32Array(size);
        for(var i = 0; i < size - 1; i++){
            logTable[expTable[i]] = i;
        }
        _this.logTable = logTable;
        // logTable[0] == 0 but this should never be used
        _this.zero = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGFPoly$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](_this, Int32Array.from([
            0
        ]));
        _this.one = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGFPoly$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](_this, Int32Array.from([
            1
        ]));
        return _this;
    }
    GenericGF.prototype.getZero = function() {
        return this.zero;
    };
    GenericGF.prototype.getOne = function() {
        return this.one;
    };
    /**
     * @return the monomial representing coefficient * x^degree
     */ GenericGF.prototype.buildMonomial = function(degree /*int*/ , coefficient /*int*/ ) {
        if (degree < 0) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        if (coefficient === 0) {
            return this.zero;
        }
        var coefficients = new Int32Array(degree + 1);
        coefficients[0] = coefficient;
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGFPoly$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](this, coefficients);
    };
    /**
     * @return multiplicative inverse of a
     */ GenericGF.prototype.inverse = function(a /*int*/ ) {
        if (a === 0) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ArithmeticException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        return this.expTable[this.size - this.logTable[a] - 1];
    };
    /**
     * @return product of a and b in GF(size)
     */ GenericGF.prototype.multiply = function(a /*int*/ , b /*int*/ ) {
        if (a === 0 || b === 0) {
            return 0;
        }
        return this.expTable[(this.logTable[a] + this.logTable[b]) % (this.size - 1)];
    };
    GenericGF.prototype.getSize = function() {
        return this.size;
    };
    GenericGF.prototype.getGeneratorBase = function() {
        return this.generatorBase;
    };
    /*@Override*/ GenericGF.prototype.toString = function() {
        return 'GF(0x' + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].toHexString(this.primitive) + ',' + this.size + ')';
    };
    GenericGF.prototype.equals = function(o) {
        return o === this;
    };
    GenericGF.AZTEC_DATA_12 = new GenericGF(0x1069, 4096, 1); // x^12 + x^6 + x^5 + x^3 + 1
    GenericGF.AZTEC_DATA_10 = new GenericGF(0x409, 1024, 1); // x^10 + x^3 + 1
    GenericGF.AZTEC_DATA_6 = new GenericGF(0x43, 64, 1); // x^6 + x + 1
    GenericGF.AZTEC_PARAM = new GenericGF(0x13, 16, 1); // x^4 + x + 1
    GenericGF.QR_CODE_FIELD_256 = new GenericGF(0x011d, 256, 0); // x^8 + x^4 + x^3 + x^2 + 1
    GenericGF.DATA_MATRIX_FIELD_256 = new GenericGF(0x012d, 256, 1); // x^8 + x^5 + x^3 + x^2 + 1
    GenericGF.AZTEC_DATA_8 = GenericGF.DATA_MATRIX_FIELD_256;
    GenericGF.MAXICODE_FIELD_64 = GenericGF.AZTEC_DATA_6;
    return GenericGF;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$AbstractGenericGF$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = GenericGF;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/reedsolomon/ReedSolomonDecoder.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /*namespace com.google.zxing.common.reedsolomon {*/ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGF$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/reedsolomon/GenericGF.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGFPoly$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/reedsolomon/GenericGFPoly.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ReedSolomonException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ReedSolomonException.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalStateException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/IllegalStateException.js [app-client] (ecmascript)");
;
;
;
;
/**
 * <p>Implements Reed-Solomon decoding, as the name implies.</p>
 *
 * <p>The algorithm will not be explained here, but the following references were helpful
 * in creating this implementation:</p>
 *
 * <ul>
 * <li>Bruce Maggs.
 * <a href="http://www.cs.cmu.edu/afs/cs.cmu.edu/project/pscico-guyb/realworld/www/rs_decode.ps">
 * "Decoding Reed-Solomon Codes"</a> (see discussion of Forney's Formula)</li>
 * <li>J.I. Hall. <a href="www.mth.msu.edu/~jhall/classes/codenotes/GRS.pdf">
 * "Chapter 5. Generalized Reed-Solomon Codes"</a>
 * (see discussion of Euclidean algorithm)</li>
 * </ul>
 *
 * <p>Much credit is due to William Rucklidge since portions of this code are an indirect
 * port of his C++ Reed-Solomon implementation.</p>
 *
 * <AUTHOR> Owen
 * <AUTHOR> Rucklidge
 * <AUTHOR>
 */ var ReedSolomonDecoder = function() {
    function ReedSolomonDecoder(field) {
        this.field = field;
    }
    /**
     * <p>Decodes given set of received codewords, which include both data and error-correction
     * codewords. Really, this means it uses Reed-Solomon to detect and correct errors, in-place,
     * in the input.</p>
     *
     * @param received data and error-correction codewords
     * @param twoS number of error-correction codewords available
     * @throws ReedSolomonException if decoding fails for any reason
     */ ReedSolomonDecoder.prototype.decode = function(received, twoS /*int*/ ) {
        var field = this.field;
        var poly = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGFPoly$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](field, received);
        var syndromeCoefficients = new Int32Array(twoS);
        var noError = true;
        for(var i = 0; i < twoS; i++){
            var evalResult = poly.evaluateAt(field.exp(i + field.getGeneratorBase()));
            syndromeCoefficients[syndromeCoefficients.length - 1 - i] = evalResult;
            if (evalResult !== 0) {
                noError = false;
            }
        }
        if (noError) {
            return;
        }
        var syndrome = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGFPoly$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](field, syndromeCoefficients);
        var sigmaOmega = this.runEuclideanAlgorithm(field.buildMonomial(twoS, 1), syndrome, twoS);
        var sigma = sigmaOmega[0];
        var omega = sigmaOmega[1];
        var errorLocations = this.findErrorLocations(sigma);
        var errorMagnitudes = this.findErrorMagnitudes(omega, errorLocations);
        for(var i = 0; i < errorLocations.length; i++){
            var position = received.length - 1 - field.log(errorLocations[i]);
            if (position < 0) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ReedSolomonException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Bad error location');
            }
            received[position] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGF$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].addOrSubtract(received[position], errorMagnitudes[i]);
        }
    };
    ReedSolomonDecoder.prototype.runEuclideanAlgorithm = function(a, b, R /*int*/ ) {
        // Assume a's degree is >= b's
        if (a.getDegree() < b.getDegree()) {
            var temp = a;
            a = b;
            b = temp;
        }
        var field = this.field;
        var rLast = a;
        var r = b;
        var tLast = field.getZero();
        var t = field.getOne();
        // Run Euclidean algorithm until r's degree is less than R/2
        while(r.getDegree() >= (R / 2 | 0)){
            var rLastLast = rLast;
            var tLastLast = tLast;
            rLast = r;
            tLast = t;
            // Divide rLastLast by rLast, with quotient in q and remainder in r
            if (rLast.isZero()) {
                // Oops, Euclidean algorithm already terminated?
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ReedSolomonException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('r_{i-1} was zero');
            }
            r = rLastLast;
            var q = field.getZero();
            var denominatorLeadingTerm = rLast.getCoefficient(rLast.getDegree());
            var dltInverse = field.inverse(denominatorLeadingTerm);
            while(r.getDegree() >= rLast.getDegree() && !r.isZero()){
                var degreeDiff = r.getDegree() - rLast.getDegree();
                var scale = field.multiply(r.getCoefficient(r.getDegree()), dltInverse);
                q = q.addOrSubtract(field.buildMonomial(degreeDiff, scale));
                r = r.addOrSubtract(rLast.multiplyByMonomial(degreeDiff, scale));
            }
            t = q.multiply(tLast).addOrSubtract(tLastLast);
            if (r.getDegree() >= rLast.getDegree()) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalStateException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Division algorithm failed to reduce polynomial?');
            }
        }
        var sigmaTildeAtZero = t.getCoefficient(0);
        if (sigmaTildeAtZero === 0) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ReedSolomonException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('sigmaTilde(0) was zero');
        }
        var inverse = field.inverse(sigmaTildeAtZero);
        var sigma = t.multiplyScalar(inverse);
        var omega = r.multiplyScalar(inverse);
        return [
            sigma,
            omega
        ];
    };
    ReedSolomonDecoder.prototype.findErrorLocations = function(errorLocator) {
        // This is a direct application of Chien's search
        var numErrors = errorLocator.getDegree();
        if (numErrors === 1) {
            return Int32Array.from([
                errorLocator.getCoefficient(1)
            ]);
        }
        var result = new Int32Array(numErrors);
        var e = 0;
        var field = this.field;
        for(var i = 1; i < field.getSize() && e < numErrors; i++){
            if (errorLocator.evaluateAt(i) === 0) {
                result[e] = field.inverse(i);
                e++;
            }
        }
        if (e !== numErrors) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ReedSolomonException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('Error locator degree does not match number of roots');
        }
        return result;
    };
    ReedSolomonDecoder.prototype.findErrorMagnitudes = function(errorEvaluator, errorLocations) {
        // This is directly applying Forney's Formula
        var s = errorLocations.length;
        var result = new Int32Array(s);
        var field = this.field;
        for(var i = 0; i < s; i++){
            var xiInverse = field.inverse(errorLocations[i]);
            var denominator = 1;
            for(var j = 0; j < s; j++){
                if (i !== j) {
                    // denominator = field.multiply(denominator,
                    //    GenericGF.addOrSubtract(1, field.multiply(errorLocations[j], xiInverse)))
                    // Above should work but fails on some Apple and Linux JDKs due to a Hotspot bug.
                    // Below is a funny-looking workaround from Steven Parkes
                    var term = field.multiply(errorLocations[j], xiInverse);
                    var termPlus1 = (term & 0x1) === 0 ? term | 1 : term & ~1;
                    denominator = field.multiply(denominator, termPlus1);
                }
            }
            result[i] = field.multiply(errorEvaluator.evaluateAt(xiInverse), field.inverse(denominator));
            if (field.getGeneratorBase() !== 0) {
                result[i] = field.multiply(result[i], xiInverse);
            }
        }
        return result;
    };
    return ReedSolomonDecoder;
}();
const __TURBOPACK__default__export__ = ReedSolomonDecoder;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/detector/MathUtils.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2012 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /*namespace com.google.zxing.common.detector {*/ /**
 * General math-related and numeric utility functions.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var MathUtils = function() {
    function MathUtils() {}
    /**
     * Ends up being a bit faster than {@link Math#round(float)}. This merely rounds its
     * argument to the nearest int, where x.5 rounds up to x+1. Semantics of this shortcut
     * differ slightly from {@link Math#round(float)} in that half rounds down for negative
     * values. -2.5 rounds to -3, not -2. For purposes here it makes no difference.
     *
     * @param d real value to round
     * @return nearest {@code int}
     */ MathUtils.round = function(d /*float*/ ) {
        if (isNaN(d)) return 0;
        if (d <= Number.MIN_SAFE_INTEGER) return Number.MIN_SAFE_INTEGER;
        if (d >= Number.MAX_SAFE_INTEGER) return Number.MAX_SAFE_INTEGER;
        return /*(int) */ d + (d < 0.0 ? -0.5 : 0.5) | 0;
    };
    // TYPESCRIPTPORT: maybe remove round method and call directly Math.round, it looks like it doesn't make sense for js
    /**
     * @param aX point A x coordinate
     * @param aY point A y coordinate
     * @param bX point B x coordinate
     * @param bY point B y coordinate
     * @return Euclidean distance between points A and B
     */ MathUtils.distance = function(aX /*float|int*/ , aY /*float|int*/ , bX /*float|int*/ , bY /*float|int*/ ) {
        var xDiff = aX - bX;
        var yDiff = aY - bY;
        return /*(float) */ Math.sqrt(xDiff * xDiff + yDiff * yDiff);
    };
    /**
     * @param aX point A x coordinate
     * @param aY point A y coordinate
     * @param bX point B x coordinate
     * @param bY point B y coordinate
     * @return Euclidean distance between points A and B
     */ // public static distance(aX: number /*int*/, aY: number /*int*/, bX: number /*int*/, bY: number /*int*/): float {
    //   const xDiff = aX - bX
    //   const yDiff = aY - bY
    //   return (float) Math.sqrt(xDiff * xDiff + yDiff * yDiff);
    // }
    /**
     * @param array values to sum
     * @return sum of values in array
     */ MathUtils.sum = function(array) {
        var count = 0;
        for(var i = 0, length_1 = array.length; i !== length_1; i++){
            var a = array[i];
            count += a;
        }
        return count;
    };
    return MathUtils;
}();
const __TURBOPACK__default__export__ = MathUtils;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/DetectorResult.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * <p>Encapsulates the result of detecting a barcode in an image. This includes the raw
 * matrix of black/white pixels corresponding to the barcode, and possibly points of interest
 * in the image, like the location of finder patterns or corners of the barcode in the image.</p>
 *
 * <AUTHOR> Owen
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var DetectorResult = function() {
    function DetectorResult(bits, points) {
        this.bits = bits;
        this.points = points;
    }
    DetectorResult.prototype.getBits = function() {
        return this.bits;
    };
    DetectorResult.prototype.getPoints = function() {
        return this.points;
    };
    return DetectorResult;
}();
const __TURBOPACK__default__export__ = DetectorResult;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/detector/WhiteRectangleDetector.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2010 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /*namespace com.google.zxing.common.detector {*/ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/ResultPoint.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/detector/MathUtils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NotFoundException.js [app-client] (ecmascript)");
;
;
;
/**
 * <p>
 * Detects a candidate barcode-like rectangular region within an image. It
 * starts around the center of the image, increases the size of the candidate
 * region until it finds a white rectangular region. By keeping track of the
 * last black points it encountered, it determines the corners of the barcode.
 * </p>
 *
 * <AUTHOR> Olivier
 */ var WhiteRectangleDetector = function() {
    // public constructor(private image: BitMatrix) /*throws NotFoundException*/ {
    //   this(image, INIT_SIZE, image.getWidth() / 2, image.getHeight() / 2)
    // }
    /**
     * @param image barcode image to find a rectangle in
     * @param initSize initial size of search area around center
     * @param x x position of search center
     * @param y y position of search center
     * @throws NotFoundException if image is too small to accommodate {@code initSize}
     */ function WhiteRectangleDetector(image, initSize /*int*/ , x /*int*/ , y /*int*/ ) {
        this.image = image;
        this.height = image.getHeight();
        this.width = image.getWidth();
        if (undefined === initSize || null === initSize) {
            initSize = WhiteRectangleDetector.INIT_SIZE;
        }
        if (undefined === x || null === x) {
            x = image.getWidth() / 2 | 0;
        }
        if (undefined === y || null === y) {
            y = image.getHeight() / 2 | 0;
        }
        var halfsize = initSize / 2 | 0;
        this.leftInit = x - halfsize;
        this.rightInit = x + halfsize;
        this.upInit = y - halfsize;
        this.downInit = y + halfsize;
        if (this.upInit < 0 || this.leftInit < 0 || this.downInit >= this.height || this.rightInit >= this.width) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
    }
    /**
     * <p>
     * Detects a candidate barcode-like rectangular region within an image. It
     * starts around the center of the image, increases the size of the candidate
     * region until it finds a white rectangular region.
     * </p>
     *
     * @return {@link ResultPoint}[] describing the corners of the rectangular
     *         region. The first and last points are opposed on the diagonal, as
     *         are the second and third. The first point will be the topmost
     *         point and the last, the bottommost. The second point will be
     *         leftmost and the third, the rightmost
     * @throws NotFoundException if no Data Matrix Code can be found
     */ WhiteRectangleDetector.prototype.detect = function() {
        var left = this.leftInit;
        var right = this.rightInit;
        var up = this.upInit;
        var down = this.downInit;
        var sizeExceeded = false;
        var aBlackPointFoundOnBorder = true;
        var atLeastOneBlackPointFoundOnBorder = false;
        var atLeastOneBlackPointFoundOnRight = false;
        var atLeastOneBlackPointFoundOnBottom = false;
        var atLeastOneBlackPointFoundOnLeft = false;
        var atLeastOneBlackPointFoundOnTop = false;
        var width = this.width;
        var height = this.height;
        while(aBlackPointFoundOnBorder){
            aBlackPointFoundOnBorder = false;
            // .....
            // .   |
            // .....
            var rightBorderNotWhite = true;
            while((rightBorderNotWhite || !atLeastOneBlackPointFoundOnRight) && right < width){
                rightBorderNotWhite = this.containsBlackPoint(up, down, right, false);
                if (rightBorderNotWhite) {
                    right++;
                    aBlackPointFoundOnBorder = true;
                    atLeastOneBlackPointFoundOnRight = true;
                } else if (!atLeastOneBlackPointFoundOnRight) {
                    right++;
                }
            }
            if (right >= width) {
                sizeExceeded = true;
                break;
            }
            // .....
            // .   .
            // .___.
            var bottomBorderNotWhite = true;
            while((bottomBorderNotWhite || !atLeastOneBlackPointFoundOnBottom) && down < height){
                bottomBorderNotWhite = this.containsBlackPoint(left, right, down, true);
                if (bottomBorderNotWhite) {
                    down++;
                    aBlackPointFoundOnBorder = true;
                    atLeastOneBlackPointFoundOnBottom = true;
                } else if (!atLeastOneBlackPointFoundOnBottom) {
                    down++;
                }
            }
            if (down >= height) {
                sizeExceeded = true;
                break;
            }
            // .....
            // |   .
            // .....
            var leftBorderNotWhite = true;
            while((leftBorderNotWhite || !atLeastOneBlackPointFoundOnLeft) && left >= 0){
                leftBorderNotWhite = this.containsBlackPoint(up, down, left, false);
                if (leftBorderNotWhite) {
                    left--;
                    aBlackPointFoundOnBorder = true;
                    atLeastOneBlackPointFoundOnLeft = true;
                } else if (!atLeastOneBlackPointFoundOnLeft) {
                    left--;
                }
            }
            if (left < 0) {
                sizeExceeded = true;
                break;
            }
            // .___.
            // .   .
            // .....
            var topBorderNotWhite = true;
            while((topBorderNotWhite || !atLeastOneBlackPointFoundOnTop) && up >= 0){
                topBorderNotWhite = this.containsBlackPoint(left, right, up, true);
                if (topBorderNotWhite) {
                    up--;
                    aBlackPointFoundOnBorder = true;
                    atLeastOneBlackPointFoundOnTop = true;
                } else if (!atLeastOneBlackPointFoundOnTop) {
                    up--;
                }
            }
            if (up < 0) {
                sizeExceeded = true;
                break;
            }
            if (aBlackPointFoundOnBorder) {
                atLeastOneBlackPointFoundOnBorder = true;
            }
        }
        if (!sizeExceeded && atLeastOneBlackPointFoundOnBorder) {
            var maxSize = right - left;
            var z = null;
            for(var i = 1; z === null && i < maxSize; i++){
                z = this.getBlackPointOnSegment(left, down - i, left + i, down);
            }
            if (z == null) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
            }
            var t = null;
            // go down right
            for(var i = 1; t === null && i < maxSize; i++){
                t = this.getBlackPointOnSegment(left, up + i, left + i, up);
            }
            if (t == null) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
            }
            var x = null;
            // go down left
            for(var i = 1; x === null && i < maxSize; i++){
                x = this.getBlackPointOnSegment(right, up + i, right - i, up);
            }
            if (x == null) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
            }
            var y = null;
            // go up left
            for(var i = 1; y === null && i < maxSize; i++){
                y = this.getBlackPointOnSegment(right, down - i, right - i, down);
            }
            if (y == null) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
            }
            return this.centerEdges(y, z, x, t);
        } else {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
    };
    WhiteRectangleDetector.prototype.getBlackPointOnSegment = function(aX /*float*/ , aY /*float*/ , bX /*float*/ , bY /*float*/ ) {
        var dist = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].round(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].distance(aX, aY, bX, bY));
        var xStep = (bX - aX) / dist;
        var yStep = (bY - aY) / dist;
        var image = this.image;
        for(var i = 0; i < dist; i++){
            var x = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].round(aX + i * xStep);
            var y = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$detector$2f$MathUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].round(aY + i * yStep);
            if (image.get(x, y)) {
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](x, y);
            }
        }
        return null;
    };
    /**
     * recenters the points of a constant distance towards the center
     *
     * @param y bottom most point
     * @param z left most point
     * @param x right most point
     * @param t top most point
     * @return {@link ResultPoint}[] describing the corners of the rectangular
     *         region. The first and last points are opposed on the diagonal, as
     *         are the second and third. The first point will be the topmost
     *         point and the last, the bottommost. The second point will be
     *         leftmost and the third, the rightmost
     */ WhiteRectangleDetector.prototype.centerEdges = function(y, z, x, t) {
        //
        //       t            t
        //  z                      x
        //        x    OR    z
        //   y                    y
        //
        var yi = y.getX();
        var yj = y.getY();
        var zi = z.getX();
        var zj = z.getY();
        var xi = x.getX();
        var xj = x.getY();
        var ti = t.getX();
        var tj = t.getY();
        var CORR = WhiteRectangleDetector.CORR;
        if (yi < this.width / 2.0) {
            return [
                new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](ti - CORR, tj + CORR),
                new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](zi + CORR, zj + CORR),
                new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](xi - CORR, xj - CORR),
                new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](yi + CORR, yj - CORR)
            ];
        } else {
            return [
                new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](ti + CORR, tj + CORR),
                new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](zi + CORR, zj - CORR),
                new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](xi - CORR, xj + CORR),
                new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$ResultPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](yi - CORR, yj - CORR)
            ];
        }
    };
    /**
     * Determines whether a segment contains a black point
     *
     * @param a          min value of the scanned coordinate
     * @param b          max value of the scanned coordinate
     * @param fixed      value of fixed coordinate
     * @param horizontal set to true if scan must be horizontal, false if vertical
     * @return true if a black point has been found, else false.
     */ WhiteRectangleDetector.prototype.containsBlackPoint = function(a /*int*/ , b /*int*/ , fixed /*int*/ , horizontal) {
        var image = this.image;
        if (horizontal) {
            for(var x = a; x <= b; x++){
                if (image.get(x, fixed)) {
                    return true;
                }
            }
        } else {
            for(var y = a; y <= b; y++){
                if (image.get(fixed, y)) {
                    return true;
                }
            }
        }
        return false;
    };
    WhiteRectangleDetector.INIT_SIZE = 10;
    WhiteRectangleDetector.CORR = 1;
    return WhiteRectangleDetector;
}();
const __TURBOPACK__default__export__ = WhiteRectangleDetector;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/GridSampler.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NotFoundException.js [app-client] (ecmascript)");
;
/**
 * Implementations of this class can, given locations of finder patterns for a QR code in an
 * image, sample the right points in the image to reconstruct the QR code, accounting for
 * perspective distortion. It is abstracted since it is relatively expensive and should be allowed
 * to take advantage of platform-specific optimized implementations, like Sun's Java Advanced
 * Imaging library, but which may not be available in other environments such as J2ME, and vice
 * versa.
 *
 * The implementation used can be controlled by calling {@link #setGridSampler(GridSampler)}
 * with an instance of a class which implements this interface.
 *
 * <AUTHOR> Owen
 */ var GridSampler = function() {
    function GridSampler() {}
    /**
     * <p>Checks a set of points that have been transformed to sample points on an image against
     * the image's dimensions to see if the point are even within the image.</p>
     *
     * <p>This method will actually "nudge" the endpoints back onto the image if they are found to be
     * barely (less than 1 pixel) off the image. This accounts for imperfect detection of finder
     * patterns in an image where the QR Code runs all the way to the image border.</p>
     *
     * <p>For efficiency, the method will check points from either end of the line until one is found
     * to be within the image. Because the set of points are assumed to be linear, this is valid.</p>
     *
     * @param image image into which the points should map
     * @param points actual points in x1,y1,...,xn,yn form
     * @throws NotFoundException if an endpoint is lies outside the image boundaries
     */ GridSampler.checkAndNudgePoints = function(image, points) {
        var width = image.getWidth();
        var height = image.getHeight();
        // Check and nudge points from start until we see some that are OK:
        var nudged = true;
        for(var offset = 0; offset < points.length && nudged; offset += 2){
            var x = Math.floor(points[offset]);
            var y = Math.floor(points[offset + 1]);
            if (x < -1 || x > width || y < -1 || y > height) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
            }
            nudged = false;
            if (x === -1) {
                points[offset] = 0.0;
                nudged = true;
            } else if (x === width) {
                points[offset] = width - 1;
                nudged = true;
            }
            if (y === -1) {
                points[offset + 1] = 0.0;
                nudged = true;
            } else if (y === height) {
                points[offset + 1] = height - 1;
                nudged = true;
            }
        }
        // Check and nudge points from end:
        nudged = true;
        for(var offset = points.length - 2; offset >= 0 && nudged; offset -= 2){
            var x = Math.floor(points[offset]);
            var y = Math.floor(points[offset + 1]);
            if (x < -1 || x > width || y < -1 || y > height) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
            }
            nudged = false;
            if (x === -1) {
                points[offset] = 0.0;
                nudged = true;
            } else if (x === width) {
                points[offset] = width - 1;
                nudged = true;
            }
            if (y === -1) {
                points[offset + 1] = 0.0;
                nudged = true;
            } else if (y === height) {
                points[offset + 1] = height - 1;
                nudged = true;
            }
        }
    };
    return GridSampler;
}();
const __TURBOPACK__default__export__ = GridSampler;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/PerspectiveTransform.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /*namespace com.google.zxing.common {*/ /**
 * <p>This class implements a perspective transform in two dimensions. Given four source and four
 * destination points, it will compute the transformation implied between them. The code is based
 * directly upon section 3.4.2 of George Wolberg's "Digital Image Warping"; see pages 54-56.</p>
 *
 * <AUTHOR> Owen
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var PerspectiveTransform = function() {
    function PerspectiveTransform(a11 /*float*/ , a21 /*float*/ , a31 /*float*/ , a12 /*float*/ , a22 /*float*/ , a32 /*float*/ , a13 /*float*/ , a23 /*float*/ , a33 /*float*/ ) {
        this.a11 = a11;
        this.a21 = a21;
        this.a31 = a31;
        this.a12 = a12;
        this.a22 = a22;
        this.a32 = a32;
        this.a13 = a13;
        this.a23 = a23;
        this.a33 = a33;
    }
    PerspectiveTransform.quadrilateralToQuadrilateral = function(x0 /*float*/ , y0 /*float*/ , x1 /*float*/ , y1 /*float*/ , x2 /*float*/ , y2 /*float*/ , x3 /*float*/ , y3 /*float*/ , x0p /*float*/ , y0p /*float*/ , x1p /*float*/ , y1p /*float*/ , x2p /*float*/ , y2p /*float*/ , x3p /*float*/ , y3p /*float*/ ) {
        var qToS = PerspectiveTransform.quadrilateralToSquare(x0, y0, x1, y1, x2, y2, x3, y3);
        var sToQ = PerspectiveTransform.squareToQuadrilateral(x0p, y0p, x1p, y1p, x2p, y2p, x3p, y3p);
        return sToQ.times(qToS);
    };
    PerspectiveTransform.prototype.transformPoints = function(points) {
        var max = points.length;
        var a11 = this.a11;
        var a12 = this.a12;
        var a13 = this.a13;
        var a21 = this.a21;
        var a22 = this.a22;
        var a23 = this.a23;
        var a31 = this.a31;
        var a32 = this.a32;
        var a33 = this.a33;
        for(var i = 0; i < max; i += 2){
            var x = points[i];
            var y = points[i + 1];
            var denominator = a13 * x + a23 * y + a33;
            points[i] = (a11 * x + a21 * y + a31) / denominator;
            points[i + 1] = (a12 * x + a22 * y + a32) / denominator;
        }
    };
    PerspectiveTransform.prototype.transformPointsWithValues = function(xValues, yValues) {
        var a11 = this.a11;
        var a12 = this.a12;
        var a13 = this.a13;
        var a21 = this.a21;
        var a22 = this.a22;
        var a23 = this.a23;
        var a31 = this.a31;
        var a32 = this.a32;
        var a33 = this.a33;
        var n = xValues.length;
        for(var i = 0; i < n; i++){
            var x = xValues[i];
            var y = yValues[i];
            var denominator = a13 * x + a23 * y + a33;
            xValues[i] = (a11 * x + a21 * y + a31) / denominator;
            yValues[i] = (a12 * x + a22 * y + a32) / denominator;
        }
    };
    PerspectiveTransform.squareToQuadrilateral = function(x0 /*float*/ , y0 /*float*/ , x1 /*float*/ , y1 /*float*/ , x2 /*float*/ , y2 /*float*/ , x3 /*float*/ , y3 /*float*/ ) {
        var dx3 = x0 - x1 + x2 - x3;
        var dy3 = y0 - y1 + y2 - y3;
        if (dx3 === 0.0 && dy3 === 0.0) {
            // Affine
            return new PerspectiveTransform(x1 - x0, x2 - x1, x0, y1 - y0, y2 - y1, y0, 0.0, 0.0, 1.0);
        } else {
            var dx1 = x1 - x2;
            var dx2 = x3 - x2;
            var dy1 = y1 - y2;
            var dy2 = y3 - y2;
            var denominator = dx1 * dy2 - dx2 * dy1;
            var a13 = (dx3 * dy2 - dx2 * dy3) / denominator;
            var a23 = (dx1 * dy3 - dx3 * dy1) / denominator;
            return new PerspectiveTransform(x1 - x0 + a13 * x1, x3 - x0 + a23 * x3, x0, y1 - y0 + a13 * y1, y3 - y0 + a23 * y3, y0, a13, a23, 1.0);
        }
    };
    PerspectiveTransform.quadrilateralToSquare = function(x0 /*float*/ , y0 /*float*/ , x1 /*float*/ , y1 /*float*/ , x2 /*float*/ , y2 /*float*/ , x3 /*float*/ , y3 /*float*/ ) {
        // Here, the adjoint serves as the inverse:
        return PerspectiveTransform.squareToQuadrilateral(x0, y0, x1, y1, x2, y2, x3, y3).buildAdjoint();
    };
    PerspectiveTransform.prototype.buildAdjoint = function() {
        // Adjoint is the transpose of the cofactor matrix:
        return new PerspectiveTransform(this.a22 * this.a33 - this.a23 * this.a32, this.a23 * this.a31 - this.a21 * this.a33, this.a21 * this.a32 - this.a22 * this.a31, this.a13 * this.a32 - this.a12 * this.a33, this.a11 * this.a33 - this.a13 * this.a31, this.a12 * this.a31 - this.a11 * this.a32, this.a12 * this.a23 - this.a13 * this.a22, this.a13 * this.a21 - this.a11 * this.a23, this.a11 * this.a22 - this.a12 * this.a21);
    };
    PerspectiveTransform.prototype.times = function(other) {
        return new PerspectiveTransform(this.a11 * other.a11 + this.a21 * other.a12 + this.a31 * other.a13, this.a11 * other.a21 + this.a21 * other.a22 + this.a31 * other.a23, this.a11 * other.a31 + this.a21 * other.a32 + this.a31 * other.a33, this.a12 * other.a11 + this.a22 * other.a12 + this.a32 * other.a13, this.a12 * other.a21 + this.a22 * other.a22 + this.a32 * other.a23, this.a12 * other.a31 + this.a22 * other.a32 + this.a32 * other.a33, this.a13 * other.a11 + this.a23 * other.a12 + this.a33 * other.a13, this.a13 * other.a21 + this.a23 * other.a22 + this.a33 * other.a23, this.a13 * other.a31 + this.a23 * other.a32 + this.a33 * other.a33);
    };
    return PerspectiveTransform;
}();
const __TURBOPACK__default__export__ = PerspectiveTransform;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/DefaultGridSampler.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
/*namespace com.google.zxing.common {*/ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$GridSampler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/GridSampler.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitMatrix$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/BitMatrix.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$PerspectiveTransform$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/PerspectiveTransform.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/NotFoundException.js [app-client] (ecmascript)");
var __extends = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (b.hasOwnProperty(p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
;
;
/**
 * <AUTHOR> Owen
 */ var DefaultGridSampler = function(_super) {
    __extends(DefaultGridSampler, _super);
    function DefaultGridSampler() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /*@Override*/ DefaultGridSampler.prototype.sampleGrid = function(image, dimensionX /*int*/ , dimensionY /*int*/ , p1ToX /*float*/ , p1ToY /*float*/ , p2ToX /*float*/ , p2ToY /*float*/ , p3ToX /*float*/ , p3ToY /*float*/ , p4ToX /*float*/ , p4ToY /*float*/ , p1FromX /*float*/ , p1FromY /*float*/ , p2FromX /*float*/ , p2FromY /*float*/ , p3FromX /*float*/ , p3FromY /*float*/ , p4FromX /*float*/ , p4FromY /*float*/ ) {
        var transform = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$PerspectiveTransform$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].quadrilateralToQuadrilateral(p1ToX, p1ToY, p2ToX, p2ToY, p3ToX, p3ToY, p4ToX, p4ToY, p1FromX, p1FromY, p2FromX, p2FromY, p3FromX, p3FromY, p4FromX, p4FromY);
        return this.sampleGridWithTransform(image, dimensionX, dimensionY, transform);
    };
    /*@Override*/ DefaultGridSampler.prototype.sampleGridWithTransform = function(image, dimensionX /*int*/ , dimensionY /*int*/ , transform) {
        if (dimensionX <= 0 || dimensionY <= 0) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        }
        var bits = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$BitMatrix$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](dimensionX, dimensionY);
        var points = new Float32Array(2 * dimensionX);
        for(var y = 0; y < dimensionY; y++){
            var max = points.length;
            var iValue = y + 0.5;
            for(var x = 0; x < max; x += 2){
                points[x] = x / 2 + 0.5;
                points[x + 1] = iValue;
            }
            transform.transformPoints(points);
            // Quick check to see if points transformed to something inside the image
            // sufficient to check the endpoints
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$GridSampler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].checkAndNudgePoints(image, points);
            try {
                for(var x = 0; x < max; x += 2){
                    if (image.get(Math.floor(points[x]), Math.floor(points[x + 1]))) {
                        // Black(-ish) pixel
                        bits.set(x / 2, y);
                    }
                }
            } catch (aioobe /*: ArrayIndexOutOfBoundsException*/ ) {
                // This feels wrong, but, sometimes if the finder patterns are misidentified, the resulting
                // transform gets "twisted" such that it maps a straight line of points to a set of points
                // whose endpoints are in bounds, but others are not. There is probably some mathematical
                // way to detect this about the transformation that I don't know yet.
                // This results in an ugly runtime exception despite our clever checks above -- can't have
                // that. We could check each point's coordinates but that feels duplicative. We settle for
                // catching and wrapping ArrayIndexOutOfBoundsException.
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$NotFoundException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
            }
        }
        return bits;
    };
    return DefaultGridSampler;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$GridSampler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = DefaultGridSampler;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/GridSamplerInstance.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$DefaultGridSampler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/DefaultGridSampler.js [app-client] (ecmascript)");
;
var GridSamplerInstance = function() {
    function GridSamplerInstance() {}
    /**
     * Sets the implementation of GridSampler used by the library. One global
     * instance is stored, which may sound problematic. But, the implementation provided
     * ought to be appropriate for the entire platform, and all uses of this library
     * in the whole lifetime of the JVM. For instance, an Android activity can swap in
     * an implementation that takes advantage of native platform libraries.
     *
     * @param newGridSampler The platform-specific object to install.
     */ GridSamplerInstance.setGridSampler = function(newGridSampler) {
        GridSamplerInstance.gridSampler = newGridSampler;
    };
    /**
     * @return the current implementation of GridSampler
     */ GridSamplerInstance.getInstance = function() {
        return GridSamplerInstance.gridSampler;
    };
    GridSamplerInstance.gridSampler = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$DefaultGridSampler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
    return GridSamplerInstance;
}();
const __TURBOPACK__default__export__ = GridSamplerInstance;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/BitSource.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2007 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /*namespace com.google.zxing.common {*/ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/IllegalArgumentException.js [app-client] (ecmascript)");
;
/**
 * <p>This provides an easy abstraction to read bits at a time from a sequence of bytes, where the
 * number of bits read is not often a multiple of 8.</p>
 *
 * <p>This class is thread-safe but not reentrant -- unless the caller modifies the bytes array
 * it passed in, in which case all bets are off.</p>
 *
 * <AUTHOR> Owen
 */ var BitSource = function() {
    /**
     * @param bytes bytes from which this will read bits. Bits will be read from the first byte first.
     * Bits are read within a byte from most-significant to least-significant bit.
     */ function BitSource(bytes) {
        this.bytes = bytes;
        this.byteOffset = 0;
        this.bitOffset = 0;
    }
    /**
     * @return index of next bit in current byte which would be read by the next call to {@link #readBits(int)}.
     */ BitSource.prototype.getBitOffset = function() {
        return this.bitOffset;
    };
    /**
     * @return index of next byte in input byte array which would be read by the next call to {@link #readBits(int)}.
     */ BitSource.prototype.getByteOffset = function() {
        return this.byteOffset;
    };
    /**
     * @param numBits number of bits to read
     * @return int representing the bits read. The bits will appear as the least-significant
     *         bits of the int
     * @throws IllegalArgumentException if numBits isn't in [1,32] or more than is available
     */ BitSource.prototype.readBits = function(numBits /*int*/ ) {
        if (numBits < 1 || numBits > 32 || numBits > this.available()) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('' + numBits);
        }
        var result = 0;
        var bitOffset = this.bitOffset;
        var byteOffset = this.byteOffset;
        var bytes = this.bytes;
        // First, read remainder from current byte
        if (bitOffset > 0) {
            var bitsLeft = 8 - bitOffset;
            var toRead = numBits < bitsLeft ? numBits : bitsLeft;
            var bitsToNotRead = bitsLeft - toRead;
            var mask = 0xFF >> 8 - toRead << bitsToNotRead;
            result = (bytes[byteOffset] & mask) >> bitsToNotRead;
            numBits -= toRead;
            bitOffset += toRead;
            if (bitOffset === 8) {
                bitOffset = 0;
                byteOffset++;
            }
        }
        // Next read whole bytes
        if (numBits > 0) {
            while(numBits >= 8){
                result = result << 8 | bytes[byteOffset] & 0xFF;
                byteOffset++;
                numBits -= 8;
            }
            // Finally read a partial byte
            if (numBits > 0) {
                var bitsToNotRead = 8 - numBits;
                var mask = 0xFF >> bitsToNotRead << bitsToNotRead;
                result = result << numBits | (bytes[byteOffset] & mask) >> bitsToNotRead;
                bitOffset += numBits;
            }
        }
        this.bitOffset = bitOffset;
        this.byteOffset = byteOffset;
        return result;
    };
    /**
     * @return number of bits that can be read successfully
     */ BitSource.prototype.available = function() {
        return 8 * (this.bytes.length - this.byteOffset) - this.bitOffset;
    };
    return BitSource;
}();
const __TURBOPACK__default__export__ = BitSource;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/reedsolomon/ReedSolomonEncoder.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright 2008 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGFPoly$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/reedsolomon/GenericGFPoly.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/System.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/IllegalArgumentException.js [app-client] (ecmascript)");
;
;
;
/**
 * <p>Implements Reed-Solomon encoding, as the name implies.</p>
 *
 * <AUTHOR> Owen
 * <AUTHOR> Rucklidge
 */ var ReedSolomonEncoder = function() {
    /**
     * A reed solomon error-correcting encoding constructor is created by
     * passing as Galois Field with of size equal to the number of code
     * words (symbols) in the alphabet (the number of values in each
     * element of arrays that are encoded/decoded).
     * @param field A galois field with a number of elements equal to the size
     * of the alphabet of symbols to encode.
     */ function ReedSolomonEncoder(field) {
        this.field = field;
        this.cachedGenerators = [];
        this.cachedGenerators.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGFPoly$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](field, Int32Array.from([
            1
        ])));
    }
    ReedSolomonEncoder.prototype.buildGenerator = function(degree /*int*/ ) {
        var cachedGenerators = this.cachedGenerators;
        if (degree >= cachedGenerators.length) {
            var lastGenerator = cachedGenerators[cachedGenerators.length - 1];
            var field = this.field;
            for(var d = cachedGenerators.length; d <= degree; d++){
                var nextGenerator = lastGenerator.multiply(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGFPoly$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](field, Int32Array.from([
                    1,
                    field.exp(d - 1 + field.getGeneratorBase())
                ])));
                cachedGenerators.push(nextGenerator);
                lastGenerator = nextGenerator;
            }
        }
        return cachedGenerators[degree];
    };
    /**
     * <p>Encode a sequence of code words (symbols) using Reed-Solomon to allow decoders
     * to detect and correct errors that may have been introduced when the resulting
     * data is stored or transmitted.</p>
     *
     * @param toEncode array used for both and output. Caller initializes the array with
     * the code words (symbols) to be encoded followed by empty elements allocated to make
     * space for error-correction code words in the encoded output. The array contains
     * the encdoded output when encode returns. Code words are encoded as numbers from
     * 0 to n-1, where n is the number of possible code words (symbols), as determined
     * by the size of the Galois Field passed in the constructor of this object.
     * @param ecBytes the number of elements reserved in the array (first parameter)
     * to store error-correction code words. Thus, the number of code words (symbols)
     * to encode in the first parameter is thus toEncode.length - ecBytes.
     * Note, the use of "bytes" in the name of this parameter is misleading, as there may
     * be more or fewer than 256 symbols being encoded, as determined by the number of
     * elements in the Galois Field passed as a constructor to this object.
     * @throws IllegalArgumentException thrown in response to validation errros.
     */ ReedSolomonEncoder.prototype.encode = function(toEncode, ecBytes /*int*/ ) {
        if (ecBytes === 0) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('No error correction bytes');
        }
        var dataBytes = toEncode.length - ecBytes;
        if (dataBytes <= 0) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$IllegalArgumentException$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]('No data bytes provided');
        }
        var generator = this.buildGenerator(ecBytes);
        var infoCoefficients = new Int32Array(dataBytes);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].arraycopy(toEncode, 0, infoCoefficients, 0, dataBytes);
        var info = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$reedsolomon$2f$GenericGFPoly$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](this.field, infoCoefficients);
        info = info.multiplyByMonomial(ecBytes, 1);
        var remainder = info.divide(generator)[1];
        var coefficients = remainder.getCoefficients();
        var numZeroCoefficients = ecBytes - coefficients.length;
        for(var i = 0; i < numZeroCoefficients; i++){
            toEncode[dataBytes + i] = 0;
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$System$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].arraycopy(coefficients, 0, toEncode, dataBytes + numZeroCoefficients, coefficients.length);
    };
    return ReedSolomonEncoder;
}();
const __TURBOPACK__default__export__ = ReedSolomonEncoder;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/ECIEncoderSet.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * Set of CharsetEncoders for a given input string
 *
 * Invariants:
 * - The list contains only encoders from CharacterSetECI (list is shorter then the list of encoders available on
 *   the platform for which ECI values are defined).
 * - The list contains encoders at least one encoder for every character in the input.
 * - The first encoder in the list is always the ISO-8859-1 encoder even of no character in the input can be encoded
 *       by it.
 * - If the input contains a character that is not in ISO-8859-1 then the last two entries in the list will be the
 *   UTF-8 encoder and the UTF-16BE encoder.
 *
 * <AUTHOR> Geller
 */ __turbopack_context__.s({
    "ECIEncoderSet": ()=>ECIEncoderSet
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Charset$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/Charset.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StandardCharsets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/StandardCharsets.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringEncoding$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/StringEncoding.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/StringUtils.js [app-client] (ecmascript)");
var __values = ("TURBOPACK compile-time value", void 0) && ("TURBOPACK compile-time value", void 0).__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
;
;
var CharsetEncoder = function() {
    function CharsetEncoder(charset) {
        this.charset = charset;
        this.name = charset.name;
    }
    CharsetEncoder.prototype.canEncode = function(c) {
        try {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringEncoding$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].encode(c, this.charset) != null;
        } catch (ex) {
            return false;
        }
    };
    return CharsetEncoder;
}();
var ECIEncoderSet = function() {
    /**
     * Constructs an encoder set
     *
     * @param stringToEncode the string that needs to be encoded
     * @param priorityCharset The preferred {@link Charset} or null.
     * @param fnc1 fnc1 denotes the character in the input that represents the FNC1 character or -1 for a non-GS1 bar
     * code. When specified, it is considered an error to pass it as argument to the methods canEncode() or encode().
     */ function ECIEncoderSet(stringToEncode, priorityCharset, fnc1) {
        var e_1, _a, e_2, _b, e_3, _c;
        this.ENCODERS = [
            'IBM437',
            'ISO-8859-2',
            'ISO-8859-3',
            'ISO-8859-4',
            'ISO-8859-5',
            'ISO-8859-6',
            'ISO-8859-7',
            'ISO-8859-8',
            'ISO-8859-9',
            'ISO-8859-10',
            'ISO-8859-11',
            'ISO-8859-13',
            'ISO-8859-14',
            'ISO-8859-15',
            'ISO-8859-16',
            'windows-1250',
            'windows-1251',
            'windows-1252',
            'windows-1256',
            'Shift_JIS'
        ].map(function(name) {
            return new CharsetEncoder(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Charset$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].forName(name));
        });
        this.encoders = [];
        var neededEncoders = [];
        // we always need the ISO-8859-1 encoder. It is the default encoding
        neededEncoders.push(new CharsetEncoder(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StandardCharsets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].ISO_8859_1));
        var needUnicodeEncoder = priorityCharset != null && priorityCharset.name.startsWith('UTF');
        // Walk over the input string and see if all characters can be encoded with the list of encoders
        for(var i = 0; i < stringToEncode.length; i++){
            var canEncode = false;
            try {
                for(var neededEncoders_1 = (e_1 = void 0, __values(neededEncoders)), neededEncoders_1_1 = neededEncoders_1.next(); !neededEncoders_1_1.done; neededEncoders_1_1 = neededEncoders_1.next()){
                    var encoder = neededEncoders_1_1.value;
                    var singleCharacter = stringToEncode.charAt(i);
                    var c = singleCharacter.charCodeAt(0);
                    if (c === fnc1 || encoder.canEncode(singleCharacter)) {
                        canEncode = true;
                        break;
                    }
                }
            } catch (e_1_1) {
                e_1 = {
                    error: e_1_1
                };
            } finally{
                try {
                    if (neededEncoders_1_1 && !neededEncoders_1_1.done && (_a = neededEncoders_1.return)) _a.call(neededEncoders_1);
                } finally{
                    if (e_1) throw e_1.error;
                }
            }
            if (!canEncode) {
                try {
                    // for the character at position i we don't yet have an encoder in the list
                    for(var _d = (e_2 = void 0, __values(this.ENCODERS)), _e = _d.next(); !_e.done; _e = _d.next()){
                        var encoder = _e.value;
                        if (encoder.canEncode(stringToEncode.charAt(i))) {
                            // Good, we found an encoder that can encode the character. We add him to the list and continue scanning
                            // the input
                            neededEncoders.push(encoder);
                            canEncode = true;
                            break;
                        }
                    }
                } catch (e_2_1) {
                    e_2 = {
                        error: e_2_1
                    };
                } finally{
                    try {
                        if (_e && !_e.done && (_b = _d.return)) _b.call(_d);
                    } finally{
                        if (e_2) throw e_2.error;
                    }
                }
            }
            if (!canEncode) {
                // The character is not encodeable by any of the single byte encoders so we remember that we will need a
                // Unicode encoder.
                needUnicodeEncoder = true;
            }
        }
        if (neededEncoders.length === 1 && !needUnicodeEncoder) {
            // the entire input can be encoded by the ISO-8859-1 encoder
            this.encoders = [
                neededEncoders[0]
            ];
        } else {
            // we need more than one single byte encoder or we need a Unicode encoder.
            // In this case we append a UTF-8 and UTF-16 encoder to the list
            this.encoders = [];
            var index = 0;
            try {
                for(var neededEncoders_2 = __values(neededEncoders), neededEncoders_2_1 = neededEncoders_2.next(); !neededEncoders_2_1.done; neededEncoders_2_1 = neededEncoders_2.next()){
                    var encoder = neededEncoders_2_1.value;
                    this.encoders[index++] = encoder;
                }
            } catch (e_3_1) {
                e_3 = {
                    error: e_3_1
                };
            } finally{
                try {
                    if (neededEncoders_2_1 && !neededEncoders_2_1.done && (_c = neededEncoders_2.return)) _c.call(neededEncoders_2);
                } finally{
                    if (e_3) throw e_3.error;
                }
            }
        // this.encoders[index] = new CharsetEncoder(StandardCharsets.UTF_8);
        // this.encoders[index + 1] = new CharsetEncoder(StandardCharsets.UTF_16BE);
        }
        // Compute priorityEncoderIndex by looking up priorityCharset in encoders
        var priorityEncoderIndexValue = -1;
        if (priorityCharset != null) {
            for(var i = 0; i < this.encoders.length; i++){
                if (this.encoders[i] != null && priorityCharset.name === this.encoders[i].name) {
                    priorityEncoderIndexValue = i;
                    break;
                }
            }
        }
        this.priorityEncoderIndex = priorityEncoderIndexValue;
    // invariants
    // if(this?.encoders?.[0].name !== StandardCharsets.ISO_8859_1)){
    // throw new Error("ISO-8859-1 must be the first encoder");
    // }
    }
    ECIEncoderSet.prototype.length = function() {
        return this.encoders.length;
    };
    ECIEncoderSet.prototype.getCharsetName = function(index) {
        if (!(index < this.length())) {
            throw new Error('index must be less than length');
        }
        return this.encoders[index].name;
    };
    ECIEncoderSet.prototype.getCharset = function(index) {
        if (!(index < this.length())) {
            throw new Error('index must be less than length');
        }
        return this.encoders[index].charset;
    };
    ECIEncoderSet.prototype.getECIValue = function(encoderIndex) {
        return this.encoders[encoderIndex].charset.getValueIdentifier();
    };
    /*
     *  returns -1 if no priority charset was defined
     */ ECIEncoderSet.prototype.getPriorityEncoderIndex = function() {
        return this.priorityEncoderIndex;
    };
    ECIEncoderSet.prototype.canEncode = function(c, encoderIndex) {
        if (!(encoderIndex < this.length())) {
            throw new Error('index must be less than length');
        }
        return true;
    };
    ECIEncoderSet.prototype.encode = function(c, encoderIndex) {
        if (!(encoderIndex < this.length())) {
            throw new Error('index must be less than length');
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringEncoding$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].encode(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$StringUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getCharAt(c), this.encoders[encoderIndex].name);
    };
    return ECIEncoderSet;
}();
;
}),
"[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/MinimalECIInput.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "MinimalECIInput": ()=>MinimalECIInput
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$ECIEncoderSet$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/common/ECIEncoderSet.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/Integer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@zxing+library@0.21.3/node_modules/@zxing/library/esm/core/util/StringBuilder.js [app-client] (ecmascript)");
;
;
;
var COST_PER_ECI = 3; // approximated (latch + 2 codewords)
var MinimalECIInput = function() {
    /**
     * Constructs a minimal input
     *
     * @param stringToEncode the character string to encode
     * @param priorityCharset The preferred {@link Charset}. When the value of the argument is null, the algorithm
     *   chooses charsets that leads to a minimal representation. Otherwise the algorithm will use the priority
     *   charset to encode any character in the input that can be encoded by it if the charset is among the
     *   supported charsets.
     * @param fnc1 denotes the character in the input that represents the FNC1 character or -1 if this is not GS1
     *   input.
     */ function MinimalECIInput(stringToEncode, priorityCharset, fnc1) {
        this.fnc1 = fnc1;
        var encoderSet = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$common$2f$ECIEncoderSet$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ECIEncoderSet"](stringToEncode, priorityCharset, fnc1);
        if (encoderSet.length() === 1) {
            // optimization for the case when all can be encoded without ECI in ISO-8859-1
            for(var i = 0; i < this.bytes.length; i++){
                var c = stringToEncode.charAt(i).charCodeAt(0);
                this.bytes[i] = c === fnc1 ? 1000 : c;
            }
        } else {
            this.bytes = this.encodeMinimally(stringToEncode, encoderSet, fnc1);
        }
    }
    MinimalECIInput.prototype.getFNC1Character = function() {
        return this.fnc1;
    };
    /**
     * Returns the length of this input.  The length is the number
     * of {@code byte}s, FNC1 characters or ECIs in the sequence.
     *
     * @return  the number of {@code char}s in this sequence
     */ MinimalECIInput.prototype.length = function() {
        return this.bytes.length;
    };
    MinimalECIInput.prototype.haveNCharacters = function(index, n) {
        if (index + n - 1 >= this.bytes.length) {
            return false;
        }
        for(var i = 0; i < n; i++){
            if (this.isECI(index + i)) {
                return false;
            }
        }
        return true;
    };
    /**
     * Returns the {@code byte} value at the specified index.  An index ranges from zero
     * to {@code length() - 1}.  The first {@code byte} value of the sequence is at
     * index zero, the next at index one, and so on, as for array
     * indexing.
     *
     * @param   index the index of the {@code byte} value to be returned
     *
     * @return  the specified {@code byte} value as character or the FNC1 character
     *
     * @throws  IndexOutOfBoundsException
     *          if the {@code index} argument is negative or not less than
     *          {@code length()}
     * @throws  IllegalArgumentException
     *          if the value at the {@code index} argument is an ECI (@see #isECI)
     */ MinimalECIInput.prototype.charAt = function(index) {
        if (index < 0 || index >= this.length()) {
            throw new Error('' + index);
        }
        if (this.isECI(index)) {
            throw new Error('value at ' + index + ' is not a character but an ECI');
        }
        return this.isFNC1(index) ? this.fnc1 : this.bytes[index];
    };
    /**
     * Returns a {@code CharSequence} that is a subsequence of this sequence.
     * The subsequence starts with the {@code char} value at the specified index and
     * ends with the {@code char} value at index {@code end - 1}.  The length
     * (in {@code char}s) of the
     * returned sequence is {@code end - start}, so if {@code start == end}
     * then an empty sequence is returned.
     *
     * @param   start   the start index, inclusive
     * @param   end     the end index, exclusive
     *
     * @return  the specified subsequence
     *
     * @throws  IndexOutOfBoundsException
     *          if {@code start} or {@code end} are negative,
     *          if {@code end} is greater than {@code length()},
     *          or if {@code start} is greater than {@code end}
     * @throws  IllegalArgumentException
     *          if a value in the range {@code start}-{@code end} is an ECI (@see #isECI)
     */ MinimalECIInput.prototype.subSequence = function(start, end) {
        if (start < 0 || start > end || end > this.length()) {
            throw new Error('' + start);
        }
        var result = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$StringBuilder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
        for(var i = start; i < end; i++){
            if (this.isECI(i)) {
                throw new Error('value at ' + i + ' is not a character but an ECI');
            }
            result.append(this.charAt(i));
        }
        return result.toString();
    };
    /**
     * Determines if a value is an ECI
     *
     * @param   index the index of the value
     *
     * @return  true if the value at position {@code index} is an ECI
     *
     * @throws  IndexOutOfBoundsException
     *          if the {@code index} argument is negative or not less than
     *          {@code length()}
     */ MinimalECIInput.prototype.isECI = function(index) {
        if (index < 0 || index >= this.length()) {
            throw new Error('' + index);
        }
        return this.bytes[index] > 255 && this.bytes[index] <= 999;
    };
    /**
     * Determines if a value is the FNC1 character
     *
     * @param   index the index of the value
     *
     * @return  true if the value at position {@code index} is the FNC1 character
     *
     * @throws  IndexOutOfBoundsException
     *          if the {@code index} argument is negative or not less than
     *          {@code length()}
     */ MinimalECIInput.prototype.isFNC1 = function(index) {
        if (index < 0 || index >= this.length()) {
            throw new Error('' + index);
        }
        return this.bytes[index] === 1000;
    };
    /**
     * Returns the {@code int} ECI value at the specified index.  An index ranges from zero
     * to {@code length() - 1}.  The first {@code byte} value of the sequence is at
     * index zero, the next at index one, and so on, as for array
     * indexing.
     *
     * @param   index the index of the {@code int} value to be returned
     *
     * @return  the specified {@code int} ECI value.
     *          The ECI specified the encoding of all bytes with a higher index until the
     *          next ECI or until the end of the input if no other ECI follows.
     *
     * @throws  IndexOutOfBoundsException
     *          if the {@code index} argument is negative or not less than
     *          {@code length()}
     * @throws  IllegalArgumentException
     *          if the value at the {@code index} argument is not an ECI (@see #isECI)
     */ MinimalECIInput.prototype.getECIValue = function(index) {
        if (index < 0 || index >= this.length()) {
            throw new Error('' + index);
        }
        if (!this.isECI(index)) {
            throw new Error('value at ' + index + ' is not an ECI but a character');
        }
        return this.bytes[index] - 256;
    };
    MinimalECIInput.prototype.addEdge = function(edges, to, edge) {
        if (edges[to][edge.encoderIndex] == null || edges[to][edge.encoderIndex].cachedTotalSize > edge.cachedTotalSize) {
            edges[to][edge.encoderIndex] = edge;
        }
    };
    MinimalECIInput.prototype.addEdges = function(stringToEncode, encoderSet, edges, from, previous, fnc1) {
        var ch = stringToEncode.charAt(from).charCodeAt(0);
        var start = 0;
        var end = encoderSet.length();
        if (encoderSet.getPriorityEncoderIndex() >= 0 && (ch === fnc1 || encoderSet.canEncode(ch, encoderSet.getPriorityEncoderIndex()))) {
            start = encoderSet.getPriorityEncoderIndex();
            end = start + 1;
        }
        for(var i = start; i < end; i++){
            if (ch === fnc1 || encoderSet.canEncode(ch, i)) {
                this.addEdge(edges, from + 1, new InputEdge(ch, encoderSet, i, previous, fnc1));
            }
        }
    };
    MinimalECIInput.prototype.encodeMinimally = function(stringToEncode, encoderSet, fnc1) {
        var inputLength = stringToEncode.length;
        // Array that represents vertices. There is a vertex for every character and encoding.
        var edges = new InputEdge[inputLength + 1][encoderSet.length()]();
        this.addEdges(stringToEncode, encoderSet, edges, 0, null, fnc1);
        for(var i = 1; i <= inputLength; i++){
            for(var j = 0; j < encoderSet.length(); j++){
                if (edges[i][j] != null && i < inputLength) {
                    this.addEdges(stringToEncode, encoderSet, edges, i, edges[i][j], fnc1);
                }
            }
            // optimize memory by removing edges that have been passed.
            for(var j = 0; j < encoderSet.length(); j++){
                edges[i - 1][j] = null;
            }
        }
        var minimalJ = -1;
        var minimalSize = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$zxing$2b$library$40$0$2e$21$2e$3$2f$node_modules$2f40$zxing$2f$library$2f$esm$2f$core$2f$util$2f$Integer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].MAX_VALUE;
        for(var j = 0; j < encoderSet.length(); j++){
            if (edges[inputLength][j] != null) {
                var edge = edges[inputLength][j];
                if (edge.cachedTotalSize < minimalSize) {
                    minimalSize = edge.cachedTotalSize;
                    minimalJ = j;
                }
            }
        }
        if (minimalJ < 0) {
            throw new Error('Failed to encode "' + stringToEncode + '"');
        }
        var intsAL = [];
        var current = edges[inputLength][minimalJ];
        while(current != null){
            if (current.isFNC1()) {
                intsAL.unshift(1000);
            } else {
                var bytes = encoderSet.encode(current.c, current.encoderIndex);
                for(var i = bytes.length - 1; i >= 0; i--){
                    intsAL.unshift(bytes[i] & 0xff);
                }
            }
            var previousEncoderIndex = current.previous === null ? 0 : current.previous.encoderIndex;
            if (previousEncoderIndex !== current.encoderIndex) {
                intsAL.unshift(256 + encoderSet.getECIValue(current.encoderIndex));
            }
            current = current.previous;
        }
        var ints = [];
        for(var i = 0; i < ints.length; i++){
            ints[i] = intsAL[i];
        }
        return ints;
    };
    return MinimalECIInput;
}();
;
var InputEdge = function() {
    function InputEdge(c, encoderSet, encoderIndex, previous, fnc1) {
        this.c = c;
        this.encoderSet = encoderSet;
        this.encoderIndex = encoderIndex;
        this.previous = previous;
        this.fnc1 = fnc1;
        this.c = c === fnc1 ? 1000 : c;
        var size = this.isFNC1() ? 1 : encoderSet.encode(c, encoderIndex).length;
        var previousEncoderIndex = previous === null ? 0 : previous.encoderIndex;
        if (previousEncoderIndex !== encoderIndex) {
            size += COST_PER_ECI;
        }
        if (previous != null) {
            size += previous.cachedTotalSize;
        }
        this.cachedTotalSize = size;
    }
    InputEdge.prototype.isFNC1 = function() {
        return this.c === 1000;
    };
    return InputEdge;
}();
}),
}]);

//# sourceMappingURL=652c1_%40zxing_library_esm_core_common_b82a01bb._.js.map