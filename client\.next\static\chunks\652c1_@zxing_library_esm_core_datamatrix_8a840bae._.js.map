{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/core/datamatrix/decoder/Version.js"], "sourcesContent": ["var __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport FormatException from '../../FormatException';\n/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * <p>Encapsulates a set of error-correction blocks in one symbol version. Most versions will\n * use blocks of differing sizes within one version, so, this encapsulates the parameters for\n * each set of blocks. It also holds the number of error-correction codewords per block since it\n * will be the same across all blocks within one version.</p>\n */\nvar ECBlocks = /** @class */ (function () {\n    function ECBlocks(ecCodewords, ecBlocks1, ecBlocks2) {\n        this.ecCodewords = ecCodewords;\n        this.ecBlocks = [ecBlocks1];\n        ecBlocks2 && this.ecBlocks.push(ecBlocks2);\n    }\n    ECBlocks.prototype.getECCodewords = function () {\n        return this.ecCodewords;\n    };\n    ECBlocks.prototype.getECBlocks = function () {\n        return this.ecBlocks;\n    };\n    return ECBlocks;\n}());\nexport { ECBlocks };\n/**\n * <p>Encapsulates the parameters for one error-correction block in one symbol version.\n * This includes the number of data codewords, and the number of times a block with these\n * parameters is used consecutively in the Data Matrix code version's format.</p>\n */\nvar ECB = /** @class */ (function () {\n    function ECB(count, dataCodewords) {\n        this.count = count;\n        this.dataCodewords = dataCodewords;\n    }\n    ECB.prototype.getCount = function () {\n        return this.count;\n    };\n    ECB.prototype.getDataCodewords = function () {\n        return this.dataCodewords;\n    };\n    return ECB;\n}());\nexport { ECB };\n/**\n * The Version object encapsulates attributes about a particular\n * size Data Matrix Code.\n *\n * <AUTHOR> (Brian Brown)\n */\nvar Version = /** @class */ (function () {\n    function Version(versionNumber, symbolSizeRows, symbolSizeColumns, dataRegionSizeRows, dataRegionSizeColumns, ecBlocks) {\n        var e_1, _a;\n        this.versionNumber = versionNumber;\n        this.symbolSizeRows = symbolSizeRows;\n        this.symbolSizeColumns = symbolSizeColumns;\n        this.dataRegionSizeRows = dataRegionSizeRows;\n        this.dataRegionSizeColumns = dataRegionSizeColumns;\n        this.ecBlocks = ecBlocks;\n        // Calculate the total number of codewords\n        var total = 0;\n        var ecCodewords = ecBlocks.getECCodewords();\n        var ecbArray = ecBlocks.getECBlocks();\n        try {\n            for (var ecbArray_1 = __values(ecbArray), ecbArray_1_1 = ecbArray_1.next(); !ecbArray_1_1.done; ecbArray_1_1 = ecbArray_1.next()) {\n                var ecBlock = ecbArray_1_1.value;\n                total += ecBlock.getCount() * (ecBlock.getDataCodewords() + ecCodewords);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (ecbArray_1_1 && !ecbArray_1_1.done && (_a = ecbArray_1.return)) _a.call(ecbArray_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        this.totalCodewords = total;\n    }\n    Version.prototype.getVersionNumber = function () {\n        return this.versionNumber;\n    };\n    Version.prototype.getSymbolSizeRows = function () {\n        return this.symbolSizeRows;\n    };\n    Version.prototype.getSymbolSizeColumns = function () {\n        return this.symbolSizeColumns;\n    };\n    Version.prototype.getDataRegionSizeRows = function () {\n        return this.dataRegionSizeRows;\n    };\n    Version.prototype.getDataRegionSizeColumns = function () {\n        return this.dataRegionSizeColumns;\n    };\n    Version.prototype.getTotalCodewords = function () {\n        return this.totalCodewords;\n    };\n    Version.prototype.getECBlocks = function () {\n        return this.ecBlocks;\n    };\n    /**\n     * <p>Deduces version information from Data Matrix dimensions.</p>\n     *\n     * @param numRows Number of rows in modules\n     * @param numColumns Number of columns in modules\n     * @return Version for a Data Matrix Code of those dimensions\n     * @throws FormatException if dimensions do correspond to a valid Data Matrix size\n     */\n    Version.getVersionForDimensions = function (numRows, numColumns) {\n        var e_2, _a;\n        if ((numRows & 0x01) !== 0 || (numColumns & 0x01) !== 0) {\n            throw new FormatException();\n        }\n        try {\n            for (var _b = __values(Version.VERSIONS), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var version = _c.value;\n                if (version.symbolSizeRows === numRows && version.symbolSizeColumns === numColumns) {\n                    return version;\n                }\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        throw new FormatException();\n    };\n    //  @Override\n    Version.prototype.toString = function () {\n        return '' + this.versionNumber;\n    };\n    /**\n     * See ISO 16022:2006 5.5.1 Table 7\n     */\n    Version.buildVersions = function () {\n        return [\n            new Version(1, 10, 10, 8, 8, new ECBlocks(5, new ECB(1, 3))),\n            new Version(2, 12, 12, 10, 10, new ECBlocks(7, new ECB(1, 5))),\n            new Version(3, 14, 14, 12, 12, new ECBlocks(10, new ECB(1, 8))),\n            new Version(4, 16, 16, 14, 14, new ECBlocks(12, new ECB(1, 12))),\n            new Version(5, 18, 18, 16, 16, new ECBlocks(14, new ECB(1, 18))),\n            new Version(6, 20, 20, 18, 18, new ECBlocks(18, new ECB(1, 22))),\n            new Version(7, 22, 22, 20, 20, new ECBlocks(20, new ECB(1, 30))),\n            new Version(8, 24, 24, 22, 22, new ECBlocks(24, new ECB(1, 36))),\n            new Version(9, 26, 26, 24, 24, new ECBlocks(28, new ECB(1, 44))),\n            new Version(10, 32, 32, 14, 14, new ECBlocks(36, new ECB(1, 62))),\n            new Version(11, 36, 36, 16, 16, new ECBlocks(42, new ECB(1, 86))),\n            new Version(12, 40, 40, 18, 18, new ECBlocks(48, new ECB(1, 114))),\n            new Version(13, 44, 44, 20, 20, new ECBlocks(56, new ECB(1, 144))),\n            new Version(14, 48, 48, 22, 22, new ECBlocks(68, new ECB(1, 174))),\n            new Version(15, 52, 52, 24, 24, new ECBlocks(42, new ECB(2, 102))),\n            new Version(16, 64, 64, 14, 14, new ECBlocks(56, new ECB(2, 140))),\n            new Version(17, 72, 72, 16, 16, new ECBlocks(36, new ECB(4, 92))),\n            new Version(18, 80, 80, 18, 18, new ECBlocks(48, new ECB(4, 114))),\n            new Version(19, 88, 88, 20, 20, new ECBlocks(56, new ECB(4, 144))),\n            new Version(20, 96, 96, 22, 22, new ECBlocks(68, new ECB(4, 174))),\n            new Version(21, 104, 104, 24, 24, new ECBlocks(56, new ECB(6, 136))),\n            new Version(22, 120, 120, 18, 18, new ECBlocks(68, new ECB(6, 175))),\n            new Version(23, 132, 132, 20, 20, new ECBlocks(62, new ECB(8, 163))),\n            new Version(24, 144, 144, 22, 22, new ECBlocks(62, new ECB(8, 156), new ECB(2, 155))),\n            new Version(25, 8, 18, 6, 16, new ECBlocks(7, new ECB(1, 5))),\n            new Version(26, 8, 32, 6, 14, new ECBlocks(11, new ECB(1, 10))),\n            new Version(27, 12, 26, 10, 24, new ECBlocks(14, new ECB(1, 16))),\n            new Version(28, 12, 36, 10, 16, new ECBlocks(18, new ECB(1, 22))),\n            new Version(29, 16, 36, 14, 16, new ECBlocks(24, new ECB(1, 32))),\n            new Version(30, 16, 48, 14, 22, new ECBlocks(28, new ECB(1, 49)))\n        ];\n    };\n    Version.VERSIONS = Version.buildVersions();\n    return Version;\n}());\nexport default Version;\n"], "names": [], "mappings": ";;;;;AAWA;AAXA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;AAEA;;;;;;;;;;;;;;CAcC,GACD;;;;;CAKC,GACD,IAAI,WAA0B;IAC1B,SAAS,SAAS,WAAW,EAAE,SAAS,EAAE,SAAS;QAC/C,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,QAAQ,GAAG;YAAC;SAAU;QAC3B,aAAa,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;IACpC;IACA,SAAS,SAAS,CAAC,cAAc,GAAG;QAChC,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA,SAAS,SAAS,CAAC,WAAW,GAAG;QAC7B,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,OAAO;AACX;;AAEA;;;;CAIC,GACD,IAAI,MAAqB;IACrB,SAAS,IAAI,KAAK,EAAE,aAAa;QAC7B,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,aAAa,GAAG;IACzB;IACA,IAAI,SAAS,CAAC,QAAQ,GAAG;QACrB,OAAO,IAAI,CAAC,KAAK;IACrB;IACA,IAAI,SAAS,CAAC,gBAAgB,GAAG;QAC7B,OAAO,IAAI,CAAC,aAAa;IAC7B;IACA,OAAO;AACX;;AAEA;;;;;CAKC,GACD,IAAI,UAAyB;IACzB,SAAS,QAAQ,aAAa,EAAE,cAAc,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,qBAAqB,EAAE,QAAQ;QAClH,IAAI,KAAK;QACT,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,kBAAkB,GAAG;QAC1B,IAAI,CAAC,qBAAqB,GAAG;QAC7B,IAAI,CAAC,QAAQ,GAAG;QAChB,0CAA0C;QAC1C,IAAI,QAAQ;QACZ,IAAI,cAAc,SAAS,cAAc;QACzC,IAAI,WAAW,SAAS,WAAW;QACnC,IAAI;YACA,IAAK,IAAI,aAAa,SAAS,WAAW,eAAe,WAAW,IAAI,IAAI,CAAC,aAAa,IAAI,EAAE,eAAe,WAAW,IAAI,GAAI;gBAC9H,IAAI,UAAU,aAAa,KAAK;gBAChC,SAAS,QAAQ,QAAQ,KAAK,CAAC,QAAQ,gBAAgB,KAAK,WAAW;YAC3E;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,gBAAgB,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,WAAW,MAAM,GAAG,GAAG,IAAI,CAAC;YAChF,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,IAAI,CAAC,cAAc,GAAG;IAC1B;IACA,QAAQ,SAAS,CAAC,gBAAgB,GAAG;QACjC,OAAO,IAAI,CAAC,aAAa;IAC7B;IACA,QAAQ,SAAS,CAAC,iBAAiB,GAAG;QAClC,OAAO,IAAI,CAAC,cAAc;IAC9B;IACA,QAAQ,SAAS,CAAC,oBAAoB,GAAG;QACrC,OAAO,IAAI,CAAC,iBAAiB;IACjC;IACA,QAAQ,SAAS,CAAC,qBAAqB,GAAG;QACtC,OAAO,IAAI,CAAC,kBAAkB;IAClC;IACA,QAAQ,SAAS,CAAC,wBAAwB,GAAG;QACzC,OAAO,IAAI,CAAC,qBAAqB;IACrC;IACA,QAAQ,SAAS,CAAC,iBAAiB,GAAG;QAClC,OAAO,IAAI,CAAC,cAAc;IAC9B;IACA,QAAQ,SAAS,CAAC,WAAW,GAAG;QAC5B,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA;;;;;;;KAOC,GACD,QAAQ,uBAAuB,GAAG,SAAU,OAAO,EAAE,UAAU;QAC3D,IAAI,KAAK;QACT,IAAI,CAAC,UAAU,IAAI,MAAM,KAAK,CAAC,aAAa,IAAI,MAAM,GAAG;YACrD,MAAM,IAAI,uOAAA,CAAA,UAAe;QAC7B;QACA,IAAI;YACA,IAAK,IAAI,KAAK,SAAS,QAAQ,QAAQ,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;gBAChF,IAAI,UAAU,GAAG,KAAK;gBACtB,IAAI,QAAQ,cAAc,KAAK,WAAW,QAAQ,iBAAiB,KAAK,YAAY;oBAChF,OAAO;gBACX;YACJ;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;YACpD,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,MAAM,IAAI,uOAAA,CAAA,UAAe;IAC7B;IACA,aAAa;IACb,QAAQ,SAAS,CAAC,QAAQ,GAAG;QACzB,OAAO,KAAK,IAAI,CAAC,aAAa;IAClC;IACA;;KAEC,GACD,QAAQ,aAAa,GAAG;QACpB,OAAO;YACH,IAAI,QAAQ,GAAG,IAAI,IAAI,GAAG,GAAG,IAAI,SAAS,GAAG,IAAI,IAAI,GAAG;YACxD,IAAI,QAAQ,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,SAAS,GAAG,IAAI,IAAI,GAAG;YAC1D,IAAI,QAAQ,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,GAAG;YAC3D,IAAI,QAAQ,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,GAAG;YAC3D,IAAI,QAAQ,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,GAAG;YAC3D,IAAI,QAAQ,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,GAAG;YAC3D,IAAI,QAAQ,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,GAAG;YAC3D,IAAI,QAAQ,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,GAAG;YAC3D,IAAI,QAAQ,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,GAAG;YAC3D,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,GAAG;YAC5D,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,GAAG;YAC5D,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,GAAG;YAC5D,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,GAAG;YAC5D,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,GAAG;YAC5D,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,GAAG;YAC5D,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,GAAG;YAC5D,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,GAAG;YAC5D,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,GAAG;YAC5D,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,GAAG;YAC5D,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,GAAG;YAC5D,IAAI,QAAQ,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,GAAG;YAC9D,IAAI,QAAQ,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,GAAG;YAC9D,IAAI,QAAQ,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,GAAG;YAC9D,IAAI,QAAQ,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,GAAG,MAAM,IAAI,IAAI,GAAG;YAC/E,IAAI,QAAQ,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,SAAS,GAAG,IAAI,IAAI,GAAG;YACzD,IAAI,QAAQ,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,GAAG;YAC1D,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,GAAG;YAC5D,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,GAAG;YAC5D,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,GAAG;YAC5D,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,GAAG;SAC/D;IACL;IACA,QAAQ,QAAQ,GAAG,QAAQ,aAAa;IACxC,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/core/datamatrix/decoder/BitMatrixParser.js"], "sourcesContent": ["import BitMatrix from '../../common/BitMatrix';\nimport Version from './Version';\nimport FormatException from '../../FormatException';\nimport IllegalArgumentException from '../../IllegalArgumentException';\n/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * <AUTHOR> (<PERSON>)\n */\nvar BitMatrixParser = /** @class */ (function () {\n    /**\n     * @param bitMatrix {@link BitMatrix} to parse\n     * @throws FormatException if dimension is < 8 or > 144 or not 0 mod 2\n     */\n    function BitMatrixParser(bitMatrix) {\n        var dimension = bitMatrix.getHeight();\n        if (dimension < 8 || dimension > 144 || (dimension & 0x01) !== 0) {\n            throw new FormatException();\n        }\n        this.version = BitMatrixParser.readVersion(bitMatrix);\n        this.mappingBitMatrix = this.extractDataRegion(bitMatrix);\n        this.readMappingMatrix = new BitMatrix(this.mappingBitMatrix.getWidth(), this.mappingBitMatrix.getHeight());\n    }\n    BitMatrixParser.prototype.getVersion = function () {\n        return this.version;\n    };\n    /**\n     * <p>Creates the version object based on the dimension of the original bit matrix from\n     * the datamatrix code.</p>\n     *\n     * <p>See ISO 16022:2006 Table 7 - ECC 200 symbol attributes</p>\n     *\n     * @param bitMatrix Original {@link BitMatrix} including alignment patterns\n     * @return {@link Version} encapsulating the Data Matrix Code's \"version\"\n     * @throws FormatException if the dimensions of the mapping matrix are not valid\n     * Data Matrix dimensions.\n     */\n    BitMatrixParser.readVersion = function (bitMatrix) {\n        var numRows = bitMatrix.getHeight();\n        var numColumns = bitMatrix.getWidth();\n        return Version.getVersionForDimensions(numRows, numColumns);\n    };\n    /**\n     * <p>Reads the bits in the {@link BitMatrix} representing the mapping matrix (No alignment patterns)\n     * in the correct order in order to reconstitute the codewords bytes contained within the\n     * Data Matrix Code.</p>\n     *\n     * @return bytes encoded within the Data Matrix Code\n     * @throws FormatException if the exact number of bytes expected is not read\n     */\n    BitMatrixParser.prototype.readCodewords = function () {\n        var result = new Int8Array(this.version.getTotalCodewords());\n        var resultOffset = 0;\n        var row = 4;\n        var column = 0;\n        var numRows = this.mappingBitMatrix.getHeight();\n        var numColumns = this.mappingBitMatrix.getWidth();\n        var corner1Read = false;\n        var corner2Read = false;\n        var corner3Read = false;\n        var corner4Read = false;\n        // Read all of the codewords\n        do {\n            // Check the four corner cases\n            if ((row === numRows) && (column === 0) && !corner1Read) {\n                result[resultOffset++] = this.readCorner1(numRows, numColumns) & 0xff;\n                row -= 2;\n                column += 2;\n                corner1Read = true;\n            }\n            else if ((row === numRows - 2) && (column === 0) && ((numColumns & 0x03) !== 0) && !corner2Read) {\n                result[resultOffset++] = this.readCorner2(numRows, numColumns) & 0xff;\n                row -= 2;\n                column += 2;\n                corner2Read = true;\n            }\n            else if ((row === numRows + 4) && (column === 2) && ((numColumns & 0x07) === 0) && !corner3Read) {\n                result[resultOffset++] = this.readCorner3(numRows, numColumns) & 0xff;\n                row -= 2;\n                column += 2;\n                corner3Read = true;\n            }\n            else if ((row === numRows - 2) && (column === 0) && ((numColumns & 0x07) === 4) && !corner4Read) {\n                result[resultOffset++] = this.readCorner4(numRows, numColumns) & 0xff;\n                row -= 2;\n                column += 2;\n                corner4Read = true;\n            }\n            else {\n                // Sweep upward diagonally to the right\n                do {\n                    if ((row < numRows) && (column >= 0) && !this.readMappingMatrix.get(column, row)) {\n                        result[resultOffset++] = this.readUtah(row, column, numRows, numColumns) & 0xff;\n                    }\n                    row -= 2;\n                    column += 2;\n                } while ((row >= 0) && (column < numColumns));\n                row += 1;\n                column += 3;\n                // Sweep downward diagonally to the left\n                do {\n                    if ((row >= 0) && (column < numColumns) && !this.readMappingMatrix.get(column, row)) {\n                        result[resultOffset++] = this.readUtah(row, column, numRows, numColumns) & 0xff;\n                    }\n                    row += 2;\n                    column -= 2;\n                } while ((row < numRows) && (column >= 0));\n                row += 3;\n                column += 1;\n            }\n        } while ((row < numRows) || (column < numColumns));\n        if (resultOffset !== this.version.getTotalCodewords()) {\n            throw new FormatException();\n        }\n        return result;\n    };\n    /**\n     * <p>Reads a bit of the mapping matrix accounting for boundary wrapping.</p>\n     *\n     * @param row Row to read in the mapping matrix\n     * @param column Column to read in the mapping matrix\n     * @param numRows Number of rows in the mapping matrix\n     * @param numColumns Number of columns in the mapping matrix\n     * @return value of the given bit in the mapping matrix\n     */\n    BitMatrixParser.prototype.readModule = function (row, column, numRows, numColumns) {\n        // Adjust the row and column indices based on boundary wrapping\n        if (row < 0) {\n            row += numRows;\n            column += 4 - ((numRows + 4) & 0x07);\n        }\n        if (column < 0) {\n            column += numColumns;\n            row += 4 - ((numColumns + 4) & 0x07);\n        }\n        this.readMappingMatrix.set(column, row);\n        return this.mappingBitMatrix.get(column, row);\n    };\n    /**\n     * <p>Reads the 8 bits of the standard Utah-shaped pattern.</p>\n     *\n     * <p>See ISO 16022:2006, 5.8.1 Figure 6</p>\n     *\n     * @param row Current row in the mapping matrix, anchored at the 8th bit (LSB) of the pattern\n     * @param column Current column in the mapping matrix, anchored at the 8th bit (LSB) of the pattern\n     * @param numRows Number of rows in the mapping matrix\n     * @param numColumns Number of columns in the mapping matrix\n     * @return byte from the utah shape\n     */\n    BitMatrixParser.prototype.readUtah = function (row, column, numRows, numColumns) {\n        var currentByte = 0;\n        if (this.readModule(row - 2, column - 2, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(row - 2, column - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(row - 1, column - 2, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(row - 1, column - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(row - 1, column, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(row, column - 2, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(row, column - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(row, column, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        return currentByte;\n    };\n    /**\n     * <p>Reads the 8 bits of the special corner condition 1.</p>\n     *\n     * <p>See ISO 16022:2006, Figure F.3</p>\n     *\n     * @param numRows Number of rows in the mapping matrix\n     * @param numColumns Number of columns in the mapping matrix\n     * @return byte from the Corner condition 1\n     */\n    BitMatrixParser.prototype.readCorner1 = function (numRows, numColumns) {\n        var currentByte = 0;\n        if (this.readModule(numRows - 1, 0, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(numRows - 1, 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(numRows - 1, 2, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(0, numColumns - 2, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(0, numColumns - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(1, numColumns - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(2, numColumns - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(3, numColumns - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        return currentByte;\n    };\n    /**\n     * <p>Reads the 8 bits of the special corner condition 2.</p>\n     *\n     * <p>See ISO 16022:2006, Figure F.4</p>\n     *\n     * @param numRows Number of rows in the mapping matrix\n     * @param numColumns Number of columns in the mapping matrix\n     * @return byte from the Corner condition 2\n     */\n    BitMatrixParser.prototype.readCorner2 = function (numRows, numColumns) {\n        var currentByte = 0;\n        if (this.readModule(numRows - 3, 0, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(numRows - 2, 0, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(numRows - 1, 0, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(0, numColumns - 4, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(0, numColumns - 3, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(0, numColumns - 2, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(0, numColumns - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(1, numColumns - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        return currentByte;\n    };\n    /**\n     * <p>Reads the 8 bits of the special corner condition 3.</p>\n     *\n     * <p>See ISO 16022:2006, Figure F.5</p>\n     *\n     * @param numRows Number of rows in the mapping matrix\n     * @param numColumns Number of columns in the mapping matrix\n     * @return byte from the Corner condition 3\n     */\n    BitMatrixParser.prototype.readCorner3 = function (numRows, numColumns) {\n        var currentByte = 0;\n        if (this.readModule(numRows - 1, 0, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(numRows - 1, numColumns - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(0, numColumns - 3, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(0, numColumns - 2, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(0, numColumns - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(1, numColumns - 3, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(1, numColumns - 2, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(1, numColumns - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        return currentByte;\n    };\n    /**\n     * <p>Reads the 8 bits of the special corner condition 4.</p>\n     *\n     * <p>See ISO 16022:2006, Figure F.6</p>\n     *\n     * @param numRows Number of rows in the mapping matrix\n     * @param numColumns Number of columns in the mapping matrix\n     * @return byte from the Corner condition 4\n     */\n    BitMatrixParser.prototype.readCorner4 = function (numRows, numColumns) {\n        var currentByte = 0;\n        if (this.readModule(numRows - 3, 0, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(numRows - 2, 0, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(numRows - 1, 0, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(0, numColumns - 2, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(0, numColumns - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(1, numColumns - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(2, numColumns - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(3, numColumns - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        return currentByte;\n    };\n    /**\n     * <p>Extracts the data region from a {@link BitMatrix} that contains\n     * alignment patterns.</p>\n     *\n     * @param bitMatrix Original {@link BitMatrix} with alignment patterns\n     * @return BitMatrix that has the alignment patterns removed\n     */\n    BitMatrixParser.prototype.extractDataRegion = function (bitMatrix) {\n        var symbolSizeRows = this.version.getSymbolSizeRows();\n        var symbolSizeColumns = this.version.getSymbolSizeColumns();\n        if (bitMatrix.getHeight() !== symbolSizeRows) {\n            throw new IllegalArgumentException('Dimension of bitMatrix must match the version size');\n        }\n        var dataRegionSizeRows = this.version.getDataRegionSizeRows();\n        var dataRegionSizeColumns = this.version.getDataRegionSizeColumns();\n        var numDataRegionsRow = symbolSizeRows / dataRegionSizeRows | 0;\n        var numDataRegionsColumn = symbolSizeColumns / dataRegionSizeColumns | 0;\n        var sizeDataRegionRow = numDataRegionsRow * dataRegionSizeRows;\n        var sizeDataRegionColumn = numDataRegionsColumn * dataRegionSizeColumns;\n        var bitMatrixWithoutAlignment = new BitMatrix(sizeDataRegionColumn, sizeDataRegionRow);\n        for (var dataRegionRow = 0; dataRegionRow < numDataRegionsRow; ++dataRegionRow) {\n            var dataRegionRowOffset = dataRegionRow * dataRegionSizeRows;\n            for (var dataRegionColumn = 0; dataRegionColumn < numDataRegionsColumn; ++dataRegionColumn) {\n                var dataRegionColumnOffset = dataRegionColumn * dataRegionSizeColumns;\n                for (var i = 0; i < dataRegionSizeRows; ++i) {\n                    var readRowOffset = dataRegionRow * (dataRegionSizeRows + 2) + 1 + i;\n                    var writeRowOffset = dataRegionRowOffset + i;\n                    for (var j = 0; j < dataRegionSizeColumns; ++j) {\n                        var readColumnOffset = dataRegionColumn * (dataRegionSizeColumns + 2) + 1 + j;\n                        if (bitMatrix.get(readColumnOffset, readRowOffset)) {\n                            var writeColumnOffset = dataRegionColumnOffset + j;\n                            bitMatrixWithoutAlignment.set(writeColumnOffset, writeRowOffset);\n                        }\n                    }\n                }\n            }\n        }\n        return bitMatrixWithoutAlignment;\n    };\n    return BitMatrixParser;\n}());\nexport default BitMatrixParser;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACA;;;;;;;;;;;;;;CAcC,GACD;;CAEC,GACD,IAAI,kBAAiC;IACjC;;;KAGC,GACD,SAAS,gBAAgB,SAAS;QAC9B,IAAI,YAAY,UAAU,SAAS;QACnC,IAAI,YAAY,KAAK,YAAY,OAAO,CAAC,YAAY,IAAI,MAAM,GAAG;YAC9D,MAAM,IAAI,uOAAA,CAAA,UAAe;QAC7B;QACA,IAAI,CAAC,OAAO,GAAG,gBAAgB,WAAW,CAAC;QAC3C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAC/C,IAAI,CAAC,iBAAiB,GAAG,IAAI,2OAAA,CAAA,UAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,IAAI,IAAI,CAAC,gBAAgB,CAAC,SAAS;IAC5G;IACA,gBAAgB,SAAS,CAAC,UAAU,GAAG;QACnC,OAAO,IAAI,CAAC,OAAO;IACvB;IACA;;;;;;;;;;KAUC,GACD,gBAAgB,WAAW,GAAG,SAAU,SAAS;QAC7C,IAAI,UAAU,UAAU,SAAS;QACjC,IAAI,aAAa,UAAU,QAAQ;QACnC,OAAO,wPAAA,CAAA,UAAO,CAAC,uBAAuB,CAAC,SAAS;IACpD;IACA;;;;;;;KAOC,GACD,gBAAgB,SAAS,CAAC,aAAa,GAAG;QACtC,IAAI,SAAS,IAAI,UAAU,IAAI,CAAC,OAAO,CAAC,iBAAiB;QACzD,IAAI,eAAe;QACnB,IAAI,MAAM;QACV,IAAI,SAAS;QACb,IAAI,UAAU,IAAI,CAAC,gBAAgB,CAAC,SAAS;QAC7C,IAAI,aAAa,IAAI,CAAC,gBAAgB,CAAC,QAAQ;QAC/C,IAAI,cAAc;QAClB,IAAI,cAAc;QAClB,IAAI,cAAc;QAClB,IAAI,cAAc;QAClB,4BAA4B;QAC5B,GAAG;YACC,8BAA8B;YAC9B,IAAI,AAAC,QAAQ,WAAa,WAAW,KAAM,CAAC,aAAa;gBACrD,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,cAAc;gBACjE,OAAO;gBACP,UAAU;gBACV,cAAc;YAClB,OACK,IAAI,AAAC,QAAQ,UAAU,KAAO,WAAW,KAAO,CAAC,aAAa,IAAI,MAAM,KAAM,CAAC,aAAa;gBAC7F,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,cAAc;gBACjE,OAAO;gBACP,UAAU;gBACV,cAAc;YAClB,OACK,IAAI,AAAC,QAAQ,UAAU,KAAO,WAAW,KAAO,CAAC,aAAa,IAAI,MAAM,KAAM,CAAC,aAAa;gBAC7F,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,cAAc;gBACjE,OAAO;gBACP,UAAU;gBACV,cAAc;YAClB,OACK,IAAI,AAAC,QAAQ,UAAU,KAAO,WAAW,KAAO,CAAC,aAAa,IAAI,MAAM,KAAM,CAAC,aAAa;gBAC7F,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,cAAc;gBACjE,OAAO;gBACP,UAAU;gBACV,cAAc;YAClB,OACK;gBACD,uCAAuC;gBACvC,GAAG;oBACC,IAAI,AAAC,MAAM,WAAa,UAAU,KAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,MAAM;wBAC9E,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,SAAS,cAAc;oBAC/E;oBACA,OAAO;oBACP,UAAU;gBACd,QAAS,AAAC,OAAO,KAAO,SAAS,WAAa;gBAC9C,OAAO;gBACP,UAAU;gBACV,wCAAwC;gBACxC,GAAG;oBACC,IAAI,AAAC,OAAO,KAAO,SAAS,cAAe,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,MAAM;wBACjF,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,SAAS,cAAc;oBAC/E;oBACA,OAAO;oBACP,UAAU;gBACd,QAAS,AAAC,MAAM,WAAa,UAAU,EAAI;gBAC3C,OAAO;gBACP,UAAU;YACd;QACJ,QAAS,AAAC,MAAM,WAAa,SAAS,WAAa;QACnD,IAAI,iBAAiB,IAAI,CAAC,OAAO,CAAC,iBAAiB,IAAI;YACnD,MAAM,IAAI,uOAAA,CAAA,UAAe;QAC7B;QACA,OAAO;IACX;IACA;;;;;;;;KAQC,GACD,gBAAgB,SAAS,CAAC,UAAU,GAAG,SAAU,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU;QAC7E,+DAA+D;QAC/D,IAAI,MAAM,GAAG;YACT,OAAO;YACP,UAAU,IAAI,CAAC,AAAC,UAAU,IAAK,IAAI;QACvC;QACA,IAAI,SAAS,GAAG;YACZ,UAAU;YACV,OAAO,IAAI,CAAC,AAAC,aAAa,IAAK,IAAI;QACvC;QACA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ;QACnC,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ;IAC7C;IACA;;;;;;;;;;KAUC,GACD,gBAAgB,SAAS,CAAC,QAAQ,GAAG,SAAU,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU;QAC3E,IAAI,cAAc;QAClB,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,SAAS,GAAG,SAAS,aAAa;YAC3D,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,SAAS,GAAG,SAAS,aAAa;YAC3D,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,SAAS,GAAG,SAAS,aAAa;YAC3D,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,SAAS,GAAG,SAAS,aAAa;YAC3D,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,QAAQ,SAAS,aAAa;YACvD,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,SAAS,GAAG,SAAS,aAAa;YACvD,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,SAAS,GAAG,SAAS,aAAa;YACvD,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,QAAQ,SAAS,aAAa;YACnD,eAAe;QACnB;QACA,OAAO;IACX;IACA;;;;;;;;KAQC,GACD,gBAAgB,SAAS,CAAC,WAAW,GAAG,SAAU,OAAO,EAAE,UAAU;QACjE,IAAI,cAAc;QAClB,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,GAAG,SAAS,aAAa;YACtD,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,GAAG,SAAS,aAAa;YACtD,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,GAAG,SAAS,aAAa;YACtD,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,aAAa,GAAG,SAAS,aAAa;YACzD,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,aAAa,GAAG,SAAS,aAAa;YACzD,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,aAAa,GAAG,SAAS,aAAa;YACzD,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,aAAa,GAAG,SAAS,aAAa;YACzD,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,aAAa,GAAG,SAAS,aAAa;YACzD,eAAe;QACnB;QACA,OAAO;IACX;IACA;;;;;;;;KAQC,GACD,gBAAgB,SAAS,CAAC,WAAW,GAAG,SAAU,OAAO,EAAE,UAAU;QACjE,IAAI,cAAc;QAClB,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,GAAG,SAAS,aAAa;YACtD,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,GAAG,SAAS,aAAa;YACtD,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,GAAG,SAAS,aAAa;YACtD,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,aAAa,GAAG,SAAS,aAAa;YACzD,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,aAAa,GAAG,SAAS,aAAa;YACzD,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,aAAa,GAAG,SAAS,aAAa;YACzD,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,aAAa,GAAG,SAAS,aAAa;YACzD,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,aAAa,GAAG,SAAS,aAAa;YACzD,eAAe;QACnB;QACA,OAAO;IACX;IACA;;;;;;;;KAQC,GACD,gBAAgB,SAAS,CAAC,WAAW,GAAG,SAAU,OAAO,EAAE,UAAU;QACjE,IAAI,cAAc;QAClB,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,GAAG,SAAS,aAAa;YACtD,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,aAAa,GAAG,SAAS,aAAa;YACnE,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,aAAa,GAAG,SAAS,aAAa;YACzD,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,aAAa,GAAG,SAAS,aAAa;YACzD,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,aAAa,GAAG,SAAS,aAAa;YACzD,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,aAAa,GAAG,SAAS,aAAa;YACzD,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,aAAa,GAAG,SAAS,aAAa;YACzD,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,aAAa,GAAG,SAAS,aAAa;YACzD,eAAe;QACnB;QACA,OAAO;IACX;IACA;;;;;;;;KAQC,GACD,gBAAgB,SAAS,CAAC,WAAW,GAAG,SAAU,OAAO,EAAE,UAAU;QACjE,IAAI,cAAc;QAClB,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,GAAG,SAAS,aAAa;YACtD,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,GAAG,SAAS,aAAa;YACtD,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,GAAG,SAAS,aAAa;YACtD,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,aAAa,GAAG,SAAS,aAAa;YACzD,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,aAAa,GAAG,SAAS,aAAa;YACzD,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,aAAa,GAAG,SAAS,aAAa;YACzD,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,aAAa,GAAG,SAAS,aAAa;YACzD,eAAe;QACnB;QACA,gBAAgB;QAChB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,aAAa,GAAG,SAAS,aAAa;YACzD,eAAe;QACnB;QACA,OAAO;IACX;IACA;;;;;;KAMC,GACD,gBAAgB,SAAS,CAAC,iBAAiB,GAAG,SAAU,SAAS;QAC7D,IAAI,iBAAiB,IAAI,CAAC,OAAO,CAAC,iBAAiB;QACnD,IAAI,oBAAoB,IAAI,CAAC,OAAO,CAAC,oBAAoB;QACzD,IAAI,UAAU,SAAS,OAAO,gBAAgB;YAC1C,MAAM,IAAI,gPAAA,CAAA,UAAwB,CAAC;QACvC;QACA,IAAI,qBAAqB,IAAI,CAAC,OAAO,CAAC,qBAAqB;QAC3D,IAAI,wBAAwB,IAAI,CAAC,OAAO,CAAC,wBAAwB;QACjE,IAAI,oBAAoB,iBAAiB,qBAAqB;QAC9D,IAAI,uBAAuB,oBAAoB,wBAAwB;QACvE,IAAI,oBAAoB,oBAAoB;QAC5C,IAAI,uBAAuB,uBAAuB;QAClD,IAAI,4BAA4B,IAAI,2OAAA,CAAA,UAAS,CAAC,sBAAsB;QACpE,IAAK,IAAI,gBAAgB,GAAG,gBAAgB,mBAAmB,EAAE,cAAe;YAC5E,IAAI,sBAAsB,gBAAgB;YAC1C,IAAK,IAAI,mBAAmB,GAAG,mBAAmB,sBAAsB,EAAE,iBAAkB;gBACxF,IAAI,yBAAyB,mBAAmB;gBAChD,IAAK,IAAI,IAAI,GAAG,IAAI,oBAAoB,EAAE,EAAG;oBACzC,IAAI,gBAAgB,gBAAgB,CAAC,qBAAqB,CAAC,IAAI,IAAI;oBACnE,IAAI,iBAAiB,sBAAsB;oBAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,uBAAuB,EAAE,EAAG;wBAC5C,IAAI,mBAAmB,mBAAmB,CAAC,wBAAwB,CAAC,IAAI,IAAI;wBAC5E,IAAI,UAAU,GAAG,CAAC,kBAAkB,gBAAgB;4BAChD,IAAI,oBAAoB,yBAAyB;4BACjD,0BAA0B,GAAG,CAAC,mBAAmB;wBACrD;oBACJ;gBACJ;YACJ;QACJ;QACA,OAAO;IACX;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 627, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/core/datamatrix/decoder/DataBlock.js"], "sourcesContent": ["var __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport IllegalArgumentException from '../../IllegalArgumentException';\n/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * <p>Encapsulates a block of data within a Data Matrix Code. Data Matrix Codes may split their data into\n * multiple blocks, each of which is a unit of data and error-correction codewords. Each\n * is represented by an instance of this class.</p>\n *\n * <AUTHOR> (Brian Brown)\n */\nvar DataBlock = /** @class */ (function () {\n    function DataBlock(numDataCodewords, codewords) {\n        this.numDataCodewords = numDataCodewords;\n        this.codewords = codewords;\n    }\n    /**\n     * <p>When Data Matrix Codes use multiple data blocks, they actually interleave the bytes of each of them.\n     * That is, the first byte of data block 1 to n is written, then the second bytes, and so on. This\n     * method will separate the data into original blocks.</p>\n     *\n     * @param rawCodewords bytes as read directly from the Data Matrix Code\n     * @param version version of the Data Matrix Code\n     * @return DataBlocks containing original bytes, \"de-interleaved\" from representation in the\n     *         Data Matrix Code\n     */\n    DataBlock.getDataBlocks = function (rawCodewords, version) {\n        var e_1, _a, e_2, _b;\n        // Figure out the number and size of data blocks used by this version\n        var ecBlocks = version.getECBlocks();\n        // First count the total number of data blocks\n        var totalBlocks = 0;\n        var ecBlockArray = ecBlocks.getECBlocks();\n        try {\n            for (var ecBlockArray_1 = __values(ecBlockArray), ecBlockArray_1_1 = ecBlockArray_1.next(); !ecBlockArray_1_1.done; ecBlockArray_1_1 = ecBlockArray_1.next()) {\n                var ecBlock = ecBlockArray_1_1.value;\n                totalBlocks += ecBlock.getCount();\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (ecBlockArray_1_1 && !ecBlockArray_1_1.done && (_a = ecBlockArray_1.return)) _a.call(ecBlockArray_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        // Now establish DataBlocks of the appropriate size and number of data codewords\n        var result = new Array(totalBlocks);\n        var numResultBlocks = 0;\n        try {\n            for (var ecBlockArray_2 = __values(ecBlockArray), ecBlockArray_2_1 = ecBlockArray_2.next(); !ecBlockArray_2_1.done; ecBlockArray_2_1 = ecBlockArray_2.next()) {\n                var ecBlock = ecBlockArray_2_1.value;\n                for (var i = 0; i < ecBlock.getCount(); i++) {\n                    var numDataCodewords = ecBlock.getDataCodewords();\n                    var numBlockCodewords = ecBlocks.getECCodewords() + numDataCodewords;\n                    result[numResultBlocks++] = new DataBlock(numDataCodewords, new Uint8Array(numBlockCodewords));\n                }\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (ecBlockArray_2_1 && !ecBlockArray_2_1.done && (_b = ecBlockArray_2.return)) _b.call(ecBlockArray_2);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        // All blocks have the same amount of data, except that the last n\n        // (where n may be 0) have 1 less byte. Figure out where these start.\n        // TODO(bbrown): There is only one case where there is a difference for Data Matrix for size 144\n        var longerBlocksTotalCodewords = result[0].codewords.length;\n        // int shorterBlocksTotalCodewords = longerBlocksTotalCodewords - 1;\n        var longerBlocksNumDataCodewords = longerBlocksTotalCodewords - ecBlocks.getECCodewords();\n        var shorterBlocksNumDataCodewords = longerBlocksNumDataCodewords - 1;\n        // The last elements of result may be 1 element shorter for 144 matrix\n        // first fill out as many elements as all of them have minus 1\n        var rawCodewordsOffset = 0;\n        for (var i = 0; i < shorterBlocksNumDataCodewords; i++) {\n            for (var j = 0; j < numResultBlocks; j++) {\n                result[j].codewords[i] = rawCodewords[rawCodewordsOffset++];\n            }\n        }\n        // Fill out the last data block in the longer ones\n        var specialVersion = version.getVersionNumber() === 24;\n        var numLongerBlocks = specialVersion ? 8 : numResultBlocks;\n        for (var j = 0; j < numLongerBlocks; j++) {\n            result[j].codewords[longerBlocksNumDataCodewords - 1] = rawCodewords[rawCodewordsOffset++];\n        }\n        // Now add in error correction blocks\n        var max = result[0].codewords.length;\n        for (var i = longerBlocksNumDataCodewords; i < max; i++) {\n            for (var j = 0; j < numResultBlocks; j++) {\n                var jOffset = specialVersion ? (j + 8) % numResultBlocks : j;\n                var iOffset = specialVersion && jOffset > 7 ? i - 1 : i;\n                result[jOffset].codewords[iOffset] = rawCodewords[rawCodewordsOffset++];\n            }\n        }\n        if (rawCodewordsOffset !== rawCodewords.length) {\n            throw new IllegalArgumentException();\n        }\n        return result;\n    };\n    DataBlock.prototype.getNumDataCodewords = function () {\n        return this.numDataCodewords;\n    };\n    DataBlock.prototype.getCodewords = function () {\n        return this.codewords;\n    };\n    return DataBlock;\n}());\nexport default DataBlock;\n"], "names": [], "mappings": ";;;AAWA;AAXA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;AAEA;;;;;;;;;;;;;;CAcC,GACD;;;;;;CAMC,GACD,IAAI,YAA2B;IAC3B,SAAS,UAAU,gBAAgB,EAAE,SAAS;QAC1C,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,SAAS,GAAG;IACrB;IACA;;;;;;;;;KASC,GACD,UAAU,aAAa,GAAG,SAAU,YAAY,EAAE,OAAO;QACrD,IAAI,KAAK,IAAI,KAAK;QAClB,qEAAqE;QACrE,IAAI,WAAW,QAAQ,WAAW;QAClC,8CAA8C;QAC9C,IAAI,cAAc;QAClB,IAAI,eAAe,SAAS,WAAW;QACvC,IAAI;YACA,IAAK,IAAI,iBAAiB,SAAS,eAAe,mBAAmB,eAAe,IAAI,IAAI,CAAC,iBAAiB,IAAI,EAAE,mBAAmB,eAAe,IAAI,GAAI;gBAC1J,IAAI,UAAU,iBAAiB,KAAK;gBACpC,eAAe,QAAQ,QAAQ;YACnC;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,oBAAoB,CAAC,iBAAiB,IAAI,IAAI,CAAC,KAAK,eAAe,MAAM,GAAG,GAAG,IAAI,CAAC;YAC5F,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,gFAAgF;QAChF,IAAI,SAAS,IAAI,MAAM;QACvB,IAAI,kBAAkB;QACtB,IAAI;YACA,IAAK,IAAI,iBAAiB,SAAS,eAAe,mBAAmB,eAAe,IAAI,IAAI,CAAC,iBAAiB,IAAI,EAAE,mBAAmB,eAAe,IAAI,GAAI;gBAC1J,IAAI,UAAU,iBAAiB,KAAK;gBACpC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,IAAK;oBACzC,IAAI,mBAAmB,QAAQ,gBAAgB;oBAC/C,IAAI,oBAAoB,SAAS,cAAc,KAAK;oBACpD,MAAM,CAAC,kBAAkB,GAAG,IAAI,UAAU,kBAAkB,IAAI,WAAW;gBAC/E;YACJ;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,oBAAoB,CAAC,iBAAiB,IAAI,IAAI,CAAC,KAAK,eAAe,MAAM,GAAG,GAAG,IAAI,CAAC;YAC5F,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,kEAAkE;QAClE,qEAAqE;QACrE,gGAAgG;QAChG,IAAI,6BAA6B,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM;QAC3D,oEAAoE;QACpE,IAAI,+BAA+B,6BAA6B,SAAS,cAAc;QACvF,IAAI,gCAAgC,+BAA+B;QACnE,sEAAsE;QACtE,8DAA8D;QAC9D,IAAI,qBAAqB;QACzB,IAAK,IAAI,IAAI,GAAG,IAAI,+BAA+B,IAAK;YACpD,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,IAAK;gBACtC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,GAAG,YAAY,CAAC,qBAAqB;YAC/D;QACJ;QACA,kDAAkD;QAClD,IAAI,iBAAiB,QAAQ,gBAAgB,OAAO;QACpD,IAAI,kBAAkB,iBAAiB,IAAI;QAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,IAAK;YACtC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,+BAA+B,EAAE,GAAG,YAAY,CAAC,qBAAqB;QAC9F;QACA,qCAAqC;QACrC,IAAI,MAAM,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM;QACpC,IAAK,IAAI,IAAI,8BAA8B,IAAI,KAAK,IAAK;YACrD,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,IAAK;gBACtC,IAAI,UAAU,iBAAiB,CAAC,IAAI,CAAC,IAAI,kBAAkB;gBAC3D,IAAI,UAAU,kBAAkB,UAAU,IAAI,IAAI,IAAI;gBACtD,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,GAAG,YAAY,CAAC,qBAAqB;YAC3E;QACJ;QACA,IAAI,uBAAuB,aAAa,MAAM,EAAE;YAC5C,MAAM,IAAI,gPAAA,CAAA,UAAwB;QACtC;QACA,OAAO;IACX;IACA,UAAU,SAAS,CAAC,mBAAmB,GAAG;QACtC,OAAO,IAAI,CAAC,gBAAgB;IAChC;IACA,UAAU,SAAS,CAAC,YAAY,GAAG;QAC/B,OAAO,IAAI,CAAC,SAAS;IACzB;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 774, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/core/datamatrix/decoder/DecodedBitStreamParser.js"], "sourcesContent": ["import DecoderResult from '../../common/DecoderResult';\nimport BitSource from '../../common/BitSource';\nimport StringBuilder from '../../util/StringBuilder';\nimport StringEncoding from '../../util/StringEncoding';\nimport StringUtils from '../../common/StringUtils';\nimport FormatException from '../../FormatException';\nimport IllegalStateException from '../../IllegalStateException';\n/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar Mode;\n(function (Mode) {\n    Mode[Mode[\"PAD_ENCODE\"] = 0] = \"PAD_ENCODE\";\n    Mode[Mode[\"ASCII_ENCODE\"] = 1] = \"ASCII_ENCODE\";\n    Mode[Mode[\"C40_ENCODE\"] = 2] = \"C40_ENCODE\";\n    Mode[Mode[\"TEXT_ENCODE\"] = 3] = \"TEXT_ENCODE\";\n    Mode[Mode[\"ANSIX12_ENCODE\"] = 4] = \"ANSIX12_ENCODE\";\n    Mode[Mode[\"EDIFACT_ENCODE\"] = 5] = \"EDIFACT_ENCODE\";\n    Mode[Mode[\"BASE256_ENCODE\"] = 6] = \"BASE256_ENCODE\";\n})(Mode || (Mode = {}));\n/**\n * <p>Data Matrix Codes can encode text as bits in one of several modes, and can use multiple modes\n * in one Data Matrix Code. This class decodes the bits back into text.</p>\n *\n * <p>See ISO 16022:2006, 5.2.1 - *******</p>\n *\n * <AUTHOR> (Brian Brown)\n * <AUTHOR> Owen\n */\nvar DecodedBitStreamParser = /** @class */ (function () {\n    function DecodedBitStreamParser() {\n    }\n    DecodedBitStreamParser.decode = function (bytes) {\n        var bits = new BitSource(bytes);\n        var result = new StringBuilder();\n        var resultTrailer = new StringBuilder();\n        var byteSegments = new Array();\n        var mode = Mode.ASCII_ENCODE;\n        do {\n            if (mode === Mode.ASCII_ENCODE) {\n                mode = this.decodeAsciiSegment(bits, result, resultTrailer);\n            }\n            else {\n                switch (mode) {\n                    case Mode.C40_ENCODE:\n                        this.decodeC40Segment(bits, result);\n                        break;\n                    case Mode.TEXT_ENCODE:\n                        this.decodeTextSegment(bits, result);\n                        break;\n                    case Mode.ANSIX12_ENCODE:\n                        this.decodeAnsiX12Segment(bits, result);\n                        break;\n                    case Mode.EDIFACT_ENCODE:\n                        this.decodeEdifactSegment(bits, result);\n                        break;\n                    case Mode.BASE256_ENCODE:\n                        this.decodeBase256Segment(bits, result, byteSegments);\n                        break;\n                    default:\n                        throw new FormatException();\n                }\n                mode = Mode.ASCII_ENCODE;\n            }\n        } while (mode !== Mode.PAD_ENCODE && bits.available() > 0);\n        if (resultTrailer.length() > 0) {\n            result.append(resultTrailer.toString());\n        }\n        return new DecoderResult(bytes, result.toString(), byteSegments.length === 0 ? null : byteSegments, null);\n    };\n    /**\n     * See ISO 16022:2006, 5.2.3 and Annex C, Table C.2\n     */\n    DecodedBitStreamParser.decodeAsciiSegment = function (bits, result, resultTrailer) {\n        var upperShift = false;\n        do {\n            var oneByte = bits.readBits(8);\n            if (oneByte === 0) {\n                throw new FormatException();\n            }\n            else if (oneByte <= 128) { // ASCII data (ASCII value + 1)\n                if (upperShift) {\n                    oneByte += 128;\n                    // upperShift = false;\n                }\n                result.append(String.fromCharCode(oneByte - 1));\n                return Mode.ASCII_ENCODE;\n            }\n            else if (oneByte === 129) { // Pad\n                return Mode.PAD_ENCODE;\n            }\n            else if (oneByte <= 229) { // 2-digit data 00-99 (Numeric Value + 130)\n                var value = oneByte - 130;\n                if (value < 10) { // pad with '0' for single digit values\n                    result.append('0');\n                }\n                result.append('' + value);\n            }\n            else {\n                switch (oneByte) {\n                    case 230: // Latch to C40 encodation\n                        return Mode.C40_ENCODE;\n                    case 231: // Latch to Base 256 encodation\n                        return Mode.BASE256_ENCODE;\n                    case 232: // FNC1\n                        result.append(String.fromCharCode(29)); // translate as ASCII 29\n                        break;\n                    case 233: // Structured Append\n                    case 234: // Reader Programming\n                        // Ignore these symbols for now\n                        // throw ReaderException.getInstance();\n                        break;\n                    case 235: // Upper Shift (shift to Extended ASCII)\n                        upperShift = true;\n                        break;\n                    case 236: // 05 Macro\n                        result.append('[)>\\u001E05\\u001D');\n                        resultTrailer.insert(0, '\\u001E\\u0004');\n                        break;\n                    case 237: // 06 Macro\n                        result.append('[)>\\u001E06\\u001D');\n                        resultTrailer.insert(0, '\\u001E\\u0004');\n                        break;\n                    case 238: // Latch to ANSI X12 encodation\n                        return Mode.ANSIX12_ENCODE;\n                    case 239: // Latch to Text encodation\n                        return Mode.TEXT_ENCODE;\n                    case 240: // Latch to EDIFACT encodation\n                        return Mode.EDIFACT_ENCODE;\n                    case 241: // ECI Character\n                        // TODO(bbrown): I think we need to support ECI\n                        // throw ReaderException.getInstance();\n                        // Ignore this symbol for now\n                        break;\n                    default:\n                        // Not to be used in ASCII encodation\n                        // but work around encoders that end with 254, latch back to ASCII\n                        if (oneByte !== 254 || bits.available() !== 0) {\n                            throw new FormatException();\n                        }\n                        break;\n                }\n            }\n        } while (bits.available() > 0);\n        return Mode.ASCII_ENCODE;\n    };\n    /**\n     * See ISO 16022:2006, 5.2.5 and Annex C, Table C.1\n     */\n    DecodedBitStreamParser.decodeC40Segment = function (bits, result) {\n        // Three C40 values are encoded in a 16-bit value as\n        // (1600 * C1) + (40 * C2) + C3 + 1\n        // TODO(bbrown): The Upper Shift with C40 doesn't work in the 4 value scenario all the time\n        var upperShift = false;\n        var cValues = [];\n        var shift = 0;\n        do {\n            // If there is only one byte left then it will be encoded as ASCII\n            if (bits.available() === 8) {\n                return;\n            }\n            var firstByte = bits.readBits(8);\n            if (firstByte === 254) { // Unlatch codeword\n                return;\n            }\n            this.parseTwoBytes(firstByte, bits.readBits(8), cValues);\n            for (var i = 0; i < 3; i++) {\n                var cValue = cValues[i];\n                switch (shift) {\n                    case 0:\n                        if (cValue < 3) {\n                            shift = cValue + 1;\n                        }\n                        else if (cValue < this.C40_BASIC_SET_CHARS.length) {\n                            var c40char = this.C40_BASIC_SET_CHARS[cValue];\n                            if (upperShift) {\n                                result.append(String.fromCharCode(c40char.charCodeAt(0) + 128));\n                                upperShift = false;\n                            }\n                            else {\n                                result.append(c40char);\n                            }\n                        }\n                        else {\n                            throw new FormatException();\n                        }\n                        break;\n                    case 1:\n                        if (upperShift) {\n                            result.append(String.fromCharCode(cValue + 128));\n                            upperShift = false;\n                        }\n                        else {\n                            result.append(String.fromCharCode(cValue));\n                        }\n                        shift = 0;\n                        break;\n                    case 2:\n                        if (cValue < this.C40_SHIFT2_SET_CHARS.length) {\n                            var c40char = this.C40_SHIFT2_SET_CHARS[cValue];\n                            if (upperShift) {\n                                result.append(String.fromCharCode(c40char.charCodeAt(0) + 128));\n                                upperShift = false;\n                            }\n                            else {\n                                result.append(c40char);\n                            }\n                        }\n                        else {\n                            switch (cValue) {\n                                case 27: // FNC1\n                                    result.append(String.fromCharCode(29)); // translate as ASCII 29\n                                    break;\n                                case 30: // Upper Shift\n                                    upperShift = true;\n                                    break;\n                                default:\n                                    throw new FormatException();\n                            }\n                        }\n                        shift = 0;\n                        break;\n                    case 3:\n                        if (upperShift) {\n                            result.append(String.fromCharCode(cValue + 224));\n                            upperShift = false;\n                        }\n                        else {\n                            result.append(String.fromCharCode(cValue + 96));\n                        }\n                        shift = 0;\n                        break;\n                    default:\n                        throw new FormatException();\n                }\n            }\n        } while (bits.available() > 0);\n    };\n    /**\n     * See ISO 16022:2006, 5.2.6 and Annex C, Table C.2\n     */\n    DecodedBitStreamParser.decodeTextSegment = function (bits, result) {\n        // Three Text values are encoded in a 16-bit value as\n        // (1600 * C1) + (40 * C2) + C3 + 1\n        // TODO(bbrown): The Upper Shift with Text doesn't work in the 4 value scenario all the time\n        var upperShift = false;\n        var cValues = [];\n        var shift = 0;\n        do {\n            // If there is only one byte left then it will be encoded as ASCII\n            if (bits.available() === 8) {\n                return;\n            }\n            var firstByte = bits.readBits(8);\n            if (firstByte === 254) { // Unlatch codeword\n                return;\n            }\n            this.parseTwoBytes(firstByte, bits.readBits(8), cValues);\n            for (var i = 0; i < 3; i++) {\n                var cValue = cValues[i];\n                switch (shift) {\n                    case 0:\n                        if (cValue < 3) {\n                            shift = cValue + 1;\n                        }\n                        else if (cValue < this.TEXT_BASIC_SET_CHARS.length) {\n                            var textChar = this.TEXT_BASIC_SET_CHARS[cValue];\n                            if (upperShift) {\n                                result.append(String.fromCharCode(textChar.charCodeAt(0) + 128));\n                                upperShift = false;\n                            }\n                            else {\n                                result.append(textChar);\n                            }\n                        }\n                        else {\n                            throw new FormatException();\n                        }\n                        break;\n                    case 1:\n                        if (upperShift) {\n                            result.append(String.fromCharCode(cValue + 128));\n                            upperShift = false;\n                        }\n                        else {\n                            result.append(String.fromCharCode(cValue));\n                        }\n                        shift = 0;\n                        break;\n                    case 2:\n                        // Shift 2 for Text is the same encoding as C40\n                        if (cValue < this.TEXT_SHIFT2_SET_CHARS.length) {\n                            var textChar = this.TEXT_SHIFT2_SET_CHARS[cValue];\n                            if (upperShift) {\n                                result.append(String.fromCharCode(textChar.charCodeAt(0) + 128));\n                                upperShift = false;\n                            }\n                            else {\n                                result.append(textChar);\n                            }\n                        }\n                        else {\n                            switch (cValue) {\n                                case 27: // FNC1\n                                    result.append(String.fromCharCode(29)); // translate as ASCII 29\n                                    break;\n                                case 30: // Upper Shift\n                                    upperShift = true;\n                                    break;\n                                default:\n                                    throw new FormatException();\n                            }\n                        }\n                        shift = 0;\n                        break;\n                    case 3:\n                        if (cValue < this.TEXT_SHIFT3_SET_CHARS.length) {\n                            var textChar = this.TEXT_SHIFT3_SET_CHARS[cValue];\n                            if (upperShift) {\n                                result.append(String.fromCharCode(textChar.charCodeAt(0) + 128));\n                                upperShift = false;\n                            }\n                            else {\n                                result.append(textChar);\n                            }\n                            shift = 0;\n                        }\n                        else {\n                            throw new FormatException();\n                        }\n                        break;\n                    default:\n                        throw new FormatException();\n                }\n            }\n        } while (bits.available() > 0);\n    };\n    /**\n     * See ISO 16022:2006, 5.2.7\n     */\n    DecodedBitStreamParser.decodeAnsiX12Segment = function (bits, result) {\n        // Three ANSI X12 values are encoded in a 16-bit value as\n        // (1600 * C1) + (40 * C2) + C3 + 1\n        var cValues = [];\n        do {\n            // If there is only one byte left then it will be encoded as ASCII\n            if (bits.available() === 8) {\n                return;\n            }\n            var firstByte = bits.readBits(8);\n            if (firstByte === 254) { // Unlatch codeword\n                return;\n            }\n            this.parseTwoBytes(firstByte, bits.readBits(8), cValues);\n            for (var i = 0; i < 3; i++) {\n                var cValue = cValues[i];\n                switch (cValue) {\n                    case 0: // X12 segment terminator <CR>\n                        result.append('\\r');\n                        break;\n                    case 1: // X12 segment separator *\n                        result.append('*');\n                        break;\n                    case 2: // X12 sub-element separator >\n                        result.append('>');\n                        break;\n                    case 3: // space\n                        result.append(' ');\n                        break;\n                    default:\n                        if (cValue < 14) { // 0 - 9\n                            result.append(String.fromCharCode(cValue + 44));\n                        }\n                        else if (cValue < 40) { // A - Z\n                            result.append(String.fromCharCode(cValue + 51));\n                        }\n                        else {\n                            throw new FormatException();\n                        }\n                        break;\n                }\n            }\n        } while (bits.available() > 0);\n    };\n    DecodedBitStreamParser.parseTwoBytes = function (firstByte, secondByte, result) {\n        var fullBitValue = (firstByte << 8) + secondByte - 1;\n        var temp = Math.floor(fullBitValue / 1600);\n        result[0] = temp;\n        fullBitValue -= temp * 1600;\n        temp = Math.floor(fullBitValue / 40);\n        result[1] = temp;\n        result[2] = fullBitValue - temp * 40;\n    };\n    /**\n     * See ISO 16022:2006, 5.2.8 and Annex C Table C.3\n     */\n    DecodedBitStreamParser.decodeEdifactSegment = function (bits, result) {\n        do {\n            // If there is only two or less bytes left then it will be encoded as ASCII\n            if (bits.available() <= 16) {\n                return;\n            }\n            for (var i = 0; i < 4; i++) {\n                var edifactValue = bits.readBits(6);\n                // Check for the unlatch character\n                if (edifactValue === 0x1F) { // 011111\n                    // Read rest of byte, which should be 0, and stop\n                    var bitsLeft = 8 - bits.getBitOffset();\n                    if (bitsLeft !== 8) {\n                        bits.readBits(bitsLeft);\n                    }\n                    return;\n                }\n                if ((edifactValue & 0x20) === 0) { // no 1 in the leading (6th) bit\n                    edifactValue |= 0x40; // Add a leading 01 to the 6 bit binary value\n                }\n                result.append(String.fromCharCode(edifactValue));\n            }\n        } while (bits.available() > 0);\n    };\n    /**\n     * See ISO 16022:2006, 5.2.9 and Annex B, B.2\n     */\n    DecodedBitStreamParser.decodeBase256Segment = function (bits, result, byteSegments) {\n        // Figure out how long the Base 256 Segment is.\n        var codewordPosition = 1 + bits.getByteOffset(); // position is 1-indexed\n        var d1 = this.unrandomize255State(bits.readBits(8), codewordPosition++);\n        var count;\n        if (d1 === 0) { // Read the remainder of the symbol\n            count = bits.available() / 8 | 0;\n        }\n        else if (d1 < 250) {\n            count = d1;\n        }\n        else {\n            count = 250 * (d1 - 249) + this.unrandomize255State(bits.readBits(8), codewordPosition++);\n        }\n        // We're seeing NegativeArraySizeException errors from users.\n        if (count < 0) {\n            throw new FormatException();\n        }\n        var bytes = new Uint8Array(count);\n        for (var i = 0; i < count; i++) {\n            // Have seen this particular error in the wild, such as at\n            // http://www.bcgen.com/demo/IDAutomationStreamingDataMatrix.aspx?MODE=3&D=Fred&PFMT=3&PT=F&X=0.3&O=0&LM=0.2\n            if (bits.available() < 8) {\n                throw new FormatException();\n            }\n            bytes[i] = this.unrandomize255State(bits.readBits(8), codewordPosition++);\n        }\n        byteSegments.push(bytes);\n        try {\n            result.append(StringEncoding.decode(bytes, StringUtils.ISO88591));\n        }\n        catch (uee) {\n            throw new IllegalStateException('Platform does not support required encoding: ' + uee.message);\n        }\n    };\n    /**\n     * See ISO 16022:2006, Annex B, B.2\n     */\n    DecodedBitStreamParser.unrandomize255State = function (randomizedBase256Codeword, base256CodewordPosition) {\n        var pseudoRandomNumber = ((149 * base256CodewordPosition) % 255) + 1;\n        var tempVariable = randomizedBase256Codeword - pseudoRandomNumber;\n        return tempVariable >= 0 ? tempVariable : tempVariable + 256;\n    };\n    /**\n     * See ISO 16022:2006, Annex C Table C.1\n     * The C40 Basic Character Set (*'s used for placeholders for the shift values)\n     */\n    DecodedBitStreamParser.C40_BASIC_SET_CHARS = [\n        '*', '*', '*', ' ', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',\n        'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N',\n        'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'\n    ];\n    DecodedBitStreamParser.C40_SHIFT2_SET_CHARS = [\n        '!', '\"', '#', '$', '%', '&', '\\'', '(', ')', '*', '+', ',', '-', '.',\n        '/', ':', ';', '<', '=', '>', '?', '@', '[', '\\\\', ']', '^', '_'\n    ];\n    /**\n     * See ISO 16022:2006, Annex C Table C.2\n     * The Text Basic Character Set (*'s used for placeholders for the shift values)\n     */\n    DecodedBitStreamParser.TEXT_BASIC_SET_CHARS = [\n        '*', '*', '*', ' ', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',\n        'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n',\n        'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'\n    ];\n    // Shift 2 for Text is the same encoding as C40\n    DecodedBitStreamParser.TEXT_SHIFT2_SET_CHARS = DecodedBitStreamParser.C40_SHIFT2_SET_CHARS;\n    DecodedBitStreamParser.TEXT_SHIFT3_SET_CHARS = [\n        '`', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N',\n        'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '{', '|', '}', '~', String.fromCharCode(127)\n    ];\n    return DecodedBitStreamParser;\n}());\nexport default DecodedBitStreamParser;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AACA;;;;;;;;;;;;;;CAcC,GACD,IAAI;AACJ,CAAC,SAAU,IAAI;IACX,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,EAAE,GAAG;IAC/B,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,EAAE,GAAG;IACjC,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,EAAE,GAAG;IAC/B,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,EAAE,GAAG;IAChC,IAAI,CAAC,IAAI,CAAC,iBAAiB,GAAG,EAAE,GAAG;IACnC,IAAI,CAAC,IAAI,CAAC,iBAAiB,GAAG,EAAE,GAAG;IACnC,IAAI,CAAC,IAAI,CAAC,iBAAiB,GAAG,EAAE,GAAG;AACvC,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACrB;;;;;;;;CAQC,GACD,IAAI,yBAAwC;IACxC,SAAS,0BACT;IACA,uBAAuB,MAAM,GAAG,SAAU,KAAK;QAC3C,IAAI,OAAO,IAAI,2OAAA,CAAA,UAAS,CAAC;QACzB,IAAI,SAAS,IAAI,6OAAA,CAAA,UAAa;QAC9B,IAAI,gBAAgB,IAAI,6OAAA,CAAA,UAAa;QACrC,IAAI,eAAe,IAAI;QACvB,IAAI,OAAO,KAAK,YAAY;QAC5B,GAAG;YACC,IAAI,SAAS,KAAK,YAAY,EAAE;gBAC5B,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,QAAQ;YACjD,OACK;gBACD,OAAQ;oBACJ,KAAK,KAAK,UAAU;wBAChB,IAAI,CAAC,gBAAgB,CAAC,MAAM;wBAC5B;oBACJ,KAAK,KAAK,WAAW;wBACjB,IAAI,CAAC,iBAAiB,CAAC,MAAM;wBAC7B;oBACJ,KAAK,KAAK,cAAc;wBACpB,IAAI,CAAC,oBAAoB,CAAC,MAAM;wBAChC;oBACJ,KAAK,KAAK,cAAc;wBACpB,IAAI,CAAC,oBAAoB,CAAC,MAAM;wBAChC;oBACJ,KAAK,KAAK,cAAc;wBACpB,IAAI,CAAC,oBAAoB,CAAC,MAAM,QAAQ;wBACxC;oBACJ;wBACI,MAAM,IAAI,uOAAA,CAAA,UAAe;gBACjC;gBACA,OAAO,KAAK,YAAY;YAC5B;QACJ,QAAS,SAAS,KAAK,UAAU,IAAI,KAAK,SAAS,KAAK,EAAG;QAC3D,IAAI,cAAc,MAAM,KAAK,GAAG;YAC5B,OAAO,MAAM,CAAC,cAAc,QAAQ;QACxC;QACA,OAAO,IAAI,+OAAA,CAAA,UAAa,CAAC,OAAO,OAAO,QAAQ,IAAI,aAAa,MAAM,KAAK,IAAI,OAAO,cAAc;IACxG;IACA;;KAEC,GACD,uBAAuB,kBAAkB,GAAG,SAAU,IAAI,EAAE,MAAM,EAAE,aAAa;QAC7E,IAAI,aAAa;QACjB,GAAG;YACC,IAAI,UAAU,KAAK,QAAQ,CAAC;YAC5B,IAAI,YAAY,GAAG;gBACf,MAAM,IAAI,uOAAA,CAAA,UAAe;YAC7B,OACK,IAAI,WAAW,KAAK;gBACrB,IAAI,YAAY;oBACZ,WAAW;gBACX,sBAAsB;gBAC1B;gBACA,OAAO,MAAM,CAAC,OAAO,YAAY,CAAC,UAAU;gBAC5C,OAAO,KAAK,YAAY;YAC5B,OACK,IAAI,YAAY,KAAK;gBACtB,OAAO,KAAK,UAAU;YAC1B,OACK,IAAI,WAAW,KAAK;gBACrB,IAAI,QAAQ,UAAU;gBACtB,IAAI,QAAQ,IAAI;oBACZ,OAAO,MAAM,CAAC;gBAClB;gBACA,OAAO,MAAM,CAAC,KAAK;YACvB,OACK;gBACD,OAAQ;oBACJ,KAAK;wBACD,OAAO,KAAK,UAAU;oBAC1B,KAAK;wBACD,OAAO,KAAK,cAAc;oBAC9B,KAAK;wBACD,OAAO,MAAM,CAAC,OAAO,YAAY,CAAC,MAAM,wBAAwB;wBAChE;oBACJ,KAAK;oBACL,KAAK;wBAGD;oBACJ,KAAK;wBACD,aAAa;wBACb;oBACJ,KAAK;wBACD,OAAO,MAAM,CAAC;wBACd,cAAc,MAAM,CAAC,GAAG;wBACxB;oBACJ,KAAK;wBACD,OAAO,MAAM,CAAC;wBACd,cAAc,MAAM,CAAC,GAAG;wBACxB;oBACJ,KAAK;wBACD,OAAO,KAAK,cAAc;oBAC9B,KAAK;wBACD,OAAO,KAAK,WAAW;oBAC3B,KAAK;wBACD,OAAO,KAAK,cAAc;oBAC9B,KAAK;wBAID;oBACJ;wBACI,qCAAqC;wBACrC,kEAAkE;wBAClE,IAAI,YAAY,OAAO,KAAK,SAAS,OAAO,GAAG;4BAC3C,MAAM,IAAI,uOAAA,CAAA,UAAe;wBAC7B;wBACA;gBACR;YACJ;QACJ,QAAS,KAAK,SAAS,KAAK,EAAG;QAC/B,OAAO,KAAK,YAAY;IAC5B;IACA;;KAEC,GACD,uBAAuB,gBAAgB,GAAG,SAAU,IAAI,EAAE,MAAM;QAC5D,oDAAoD;QACpD,mCAAmC;QACnC,2FAA2F;QAC3F,IAAI,aAAa;QACjB,IAAI,UAAU,EAAE;QAChB,IAAI,QAAQ;QACZ,GAAG;YACC,kEAAkE;YAClE,IAAI,KAAK,SAAS,OAAO,GAAG;gBACxB;YACJ;YACA,IAAI,YAAY,KAAK,QAAQ,CAAC;YAC9B,IAAI,cAAc,KAAK;gBACnB;YACJ;YACA,IAAI,CAAC,aAAa,CAAC,WAAW,KAAK,QAAQ,CAAC,IAAI;YAChD,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;gBACxB,IAAI,SAAS,OAAO,CAAC,EAAE;gBACvB,OAAQ;oBACJ,KAAK;wBACD,IAAI,SAAS,GAAG;4BACZ,QAAQ,SAAS;wBACrB,OACK,IAAI,SAAS,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE;4BAC/C,IAAI,UAAU,IAAI,CAAC,mBAAmB,CAAC,OAAO;4BAC9C,IAAI,YAAY;gCACZ,OAAO,MAAM,CAAC,OAAO,YAAY,CAAC,QAAQ,UAAU,CAAC,KAAK;gCAC1D,aAAa;4BACjB,OACK;gCACD,OAAO,MAAM,CAAC;4BAClB;wBACJ,OACK;4BACD,MAAM,IAAI,uOAAA,CAAA,UAAe;wBAC7B;wBACA;oBACJ,KAAK;wBACD,IAAI,YAAY;4BACZ,OAAO,MAAM,CAAC,OAAO,YAAY,CAAC,SAAS;4BAC3C,aAAa;wBACjB,OACK;4BACD,OAAO,MAAM,CAAC,OAAO,YAAY,CAAC;wBACtC;wBACA,QAAQ;wBACR;oBACJ,KAAK;wBACD,IAAI,SAAS,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE;4BAC3C,IAAI,UAAU,IAAI,CAAC,oBAAoB,CAAC,OAAO;4BAC/C,IAAI,YAAY;gCACZ,OAAO,MAAM,CAAC,OAAO,YAAY,CAAC,QAAQ,UAAU,CAAC,KAAK;gCAC1D,aAAa;4BACjB,OACK;gCACD,OAAO,MAAM,CAAC;4BAClB;wBACJ,OACK;4BACD,OAAQ;gCACJ,KAAK;oCACD,OAAO,MAAM,CAAC,OAAO,YAAY,CAAC,MAAM,wBAAwB;oCAChE;gCACJ,KAAK;oCACD,aAAa;oCACb;gCACJ;oCACI,MAAM,IAAI,uOAAA,CAAA,UAAe;4BACjC;wBACJ;wBACA,QAAQ;wBACR;oBACJ,KAAK;wBACD,IAAI,YAAY;4BACZ,OAAO,MAAM,CAAC,OAAO,YAAY,CAAC,SAAS;4BAC3C,aAAa;wBACjB,OACK;4BACD,OAAO,MAAM,CAAC,OAAO,YAAY,CAAC,SAAS;wBAC/C;wBACA,QAAQ;wBACR;oBACJ;wBACI,MAAM,IAAI,uOAAA,CAAA,UAAe;gBACjC;YACJ;QACJ,QAAS,KAAK,SAAS,KAAK,EAAG;IACnC;IACA;;KAEC,GACD,uBAAuB,iBAAiB,GAAG,SAAU,IAAI,EAAE,MAAM;QAC7D,qDAAqD;QACrD,mCAAmC;QACnC,4FAA4F;QAC5F,IAAI,aAAa;QACjB,IAAI,UAAU,EAAE;QAChB,IAAI,QAAQ;QACZ,GAAG;YACC,kEAAkE;YAClE,IAAI,KAAK,SAAS,OAAO,GAAG;gBACxB;YACJ;YACA,IAAI,YAAY,KAAK,QAAQ,CAAC;YAC9B,IAAI,cAAc,KAAK;gBACnB;YACJ;YACA,IAAI,CAAC,aAAa,CAAC,WAAW,KAAK,QAAQ,CAAC,IAAI;YAChD,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;gBACxB,IAAI,SAAS,OAAO,CAAC,EAAE;gBACvB,OAAQ;oBACJ,KAAK;wBACD,IAAI,SAAS,GAAG;4BACZ,QAAQ,SAAS;wBACrB,OACK,IAAI,SAAS,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE;4BAChD,IAAI,WAAW,IAAI,CAAC,oBAAoB,CAAC,OAAO;4BAChD,IAAI,YAAY;gCACZ,OAAO,MAAM,CAAC,OAAO,YAAY,CAAC,SAAS,UAAU,CAAC,KAAK;gCAC3D,aAAa;4BACjB,OACK;gCACD,OAAO,MAAM,CAAC;4BAClB;wBACJ,OACK;4BACD,MAAM,IAAI,uOAAA,CAAA,UAAe;wBAC7B;wBACA;oBACJ,KAAK;wBACD,IAAI,YAAY;4BACZ,OAAO,MAAM,CAAC,OAAO,YAAY,CAAC,SAAS;4BAC3C,aAAa;wBACjB,OACK;4BACD,OAAO,MAAM,CAAC,OAAO,YAAY,CAAC;wBACtC;wBACA,QAAQ;wBACR;oBACJ,KAAK;wBACD,+CAA+C;wBAC/C,IAAI,SAAS,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE;4BAC5C,IAAI,WAAW,IAAI,CAAC,qBAAqB,CAAC,OAAO;4BACjD,IAAI,YAAY;gCACZ,OAAO,MAAM,CAAC,OAAO,YAAY,CAAC,SAAS,UAAU,CAAC,KAAK;gCAC3D,aAAa;4BACjB,OACK;gCACD,OAAO,MAAM,CAAC;4BAClB;wBACJ,OACK;4BACD,OAAQ;gCACJ,KAAK;oCACD,OAAO,MAAM,CAAC,OAAO,YAAY,CAAC,MAAM,wBAAwB;oCAChE;gCACJ,KAAK;oCACD,aAAa;oCACb;gCACJ;oCACI,MAAM,IAAI,uOAAA,CAAA,UAAe;4BACjC;wBACJ;wBACA,QAAQ;wBACR;oBACJ,KAAK;wBACD,IAAI,SAAS,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE;4BAC5C,IAAI,WAAW,IAAI,CAAC,qBAAqB,CAAC,OAAO;4BACjD,IAAI,YAAY;gCACZ,OAAO,MAAM,CAAC,OAAO,YAAY,CAAC,SAAS,UAAU,CAAC,KAAK;gCAC3D,aAAa;4BACjB,OACK;gCACD,OAAO,MAAM,CAAC;4BAClB;4BACA,QAAQ;wBACZ,OACK;4BACD,MAAM,IAAI,uOAAA,CAAA,UAAe;wBAC7B;wBACA;oBACJ;wBACI,MAAM,IAAI,uOAAA,CAAA,UAAe;gBACjC;YACJ;QACJ,QAAS,KAAK,SAAS,KAAK,EAAG;IACnC;IACA;;KAEC,GACD,uBAAuB,oBAAoB,GAAG,SAAU,IAAI,EAAE,MAAM;QAChE,yDAAyD;QACzD,mCAAmC;QACnC,IAAI,UAAU,EAAE;QAChB,GAAG;YACC,kEAAkE;YAClE,IAAI,KAAK,SAAS,OAAO,GAAG;gBACxB;YACJ;YACA,IAAI,YAAY,KAAK,QAAQ,CAAC;YAC9B,IAAI,cAAc,KAAK;gBACnB;YACJ;YACA,IAAI,CAAC,aAAa,CAAC,WAAW,KAAK,QAAQ,CAAC,IAAI;YAChD,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;gBACxB,IAAI,SAAS,OAAO,CAAC,EAAE;gBACvB,OAAQ;oBACJ,KAAK;wBACD,OAAO,MAAM,CAAC;wBACd;oBACJ,KAAK;wBACD,OAAO,MAAM,CAAC;wBACd;oBACJ,KAAK;wBACD,OAAO,MAAM,CAAC;wBACd;oBACJ,KAAK;wBACD,OAAO,MAAM,CAAC;wBACd;oBACJ;wBACI,IAAI,SAAS,IAAI;4BACb,OAAO,MAAM,CAAC,OAAO,YAAY,CAAC,SAAS;wBAC/C,OACK,IAAI,SAAS,IAAI;4BAClB,OAAO,MAAM,CAAC,OAAO,YAAY,CAAC,SAAS;wBAC/C,OACK;4BACD,MAAM,IAAI,uOAAA,CAAA,UAAe;wBAC7B;wBACA;gBACR;YACJ;QACJ,QAAS,KAAK,SAAS,KAAK,EAAG;IACnC;IACA,uBAAuB,aAAa,GAAG,SAAU,SAAS,EAAE,UAAU,EAAE,MAAM;QAC1E,IAAI,eAAe,CAAC,aAAa,CAAC,IAAI,aAAa;QACnD,IAAI,OAAO,KAAK,KAAK,CAAC,eAAe;QACrC,MAAM,CAAC,EAAE,GAAG;QACZ,gBAAgB,OAAO;QACvB,OAAO,KAAK,KAAK,CAAC,eAAe;QACjC,MAAM,CAAC,EAAE,GAAG;QACZ,MAAM,CAAC,EAAE,GAAG,eAAe,OAAO;IACtC;IACA;;KAEC,GACD,uBAAuB,oBAAoB,GAAG,SAAU,IAAI,EAAE,MAAM;QAChE,GAAG;YACC,2EAA2E;YAC3E,IAAI,KAAK,SAAS,MAAM,IAAI;gBACxB;YACJ;YACA,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;gBACxB,IAAI,eAAe,KAAK,QAAQ,CAAC;gBACjC,kCAAkC;gBAClC,IAAI,iBAAiB,MAAM;oBACvB,iDAAiD;oBACjD,IAAI,WAAW,IAAI,KAAK,YAAY;oBACpC,IAAI,aAAa,GAAG;wBAChB,KAAK,QAAQ,CAAC;oBAClB;oBACA;gBACJ;gBACA,IAAI,CAAC,eAAe,IAAI,MAAM,GAAG;oBAC7B,gBAAgB,MAAM,6CAA6C;gBACvE;gBACA,OAAO,MAAM,CAAC,OAAO,YAAY,CAAC;YACtC;QACJ,QAAS,KAAK,SAAS,KAAK,EAAG;IACnC;IACA;;KAEC,GACD,uBAAuB,oBAAoB,GAAG,SAAU,IAAI,EAAE,MAAM,EAAE,YAAY;QAC9E,+CAA+C;QAC/C,IAAI,mBAAmB,IAAI,KAAK,aAAa,IAAI,wBAAwB;QACzE,IAAI,KAAK,IAAI,CAAC,mBAAmB,CAAC,KAAK,QAAQ,CAAC,IAAI;QACpD,IAAI;QACJ,IAAI,OAAO,GAAG;YACV,QAAQ,KAAK,SAAS,KAAK,IAAI;QACnC,OACK,IAAI,KAAK,KAAK;YACf,QAAQ;QACZ,OACK;YACD,QAAQ,MAAM,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK,QAAQ,CAAC,IAAI;QAC1E;QACA,6DAA6D;QAC7D,IAAI,QAAQ,GAAG;YACX,MAAM,IAAI,uOAAA,CAAA,UAAe;QAC7B;QACA,IAAI,QAAQ,IAAI,WAAW;QAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;YAC5B,0DAA0D;YAC1D,4GAA4G;YAC5G,IAAI,KAAK,SAAS,KAAK,GAAG;gBACtB,MAAM,IAAI,uOAAA,CAAA,UAAe;YAC7B;YACA,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,QAAQ,CAAC,IAAI;QAC1D;QACA,aAAa,IAAI,CAAC;QAClB,IAAI;YACA,OAAO,MAAM,CAAC,8OAAA,CAAA,UAAc,CAAC,MAAM,CAAC,OAAO,6OAAA,CAAA,UAAW,CAAC,QAAQ;QACnE,EACA,OAAO,KAAK;YACR,MAAM,IAAI,6OAAA,CAAA,UAAqB,CAAC,kDAAkD,IAAI,OAAO;QACjG;IACJ;IACA;;KAEC,GACD,uBAAuB,mBAAmB,GAAG,SAAU,yBAAyB,EAAE,uBAAuB;QACrG,IAAI,qBAAqB,AAAE,MAAM,0BAA2B,MAAO;QACnE,IAAI,eAAe,4BAA4B;QAC/C,OAAO,gBAAgB,IAAI,eAAe,eAAe;IAC7D;IACA;;;KAGC,GACD,uBAAuB,mBAAmB,GAAG;QACzC;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QACjE;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QACjE;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;KAC1D;IACD,uBAAuB,oBAAoB,GAAG;QAC1C;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAM;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAClE;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAM;QAAK;QAAK;KAChE;IACD;;;KAGC,GACD,uBAAuB,oBAAoB,GAAG;QAC1C;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QACjE;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QACjE;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;KAC1D;IACD,+CAA+C;IAC/C,uBAAuB,qBAAqB,GAAG,uBAAuB,oBAAoB;IAC1F,uBAAuB,qBAAqB,GAAG;QAC3C;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QACtE;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK,OAAO,YAAY,CAAC;KACvG;IACD,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1384, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/core/datamatrix/decoder/Decoder.js"], "sourcesContent": ["var __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport ChecksumException from '../../ChecksumException';\nimport GenericGF from '../../common/reedsolomon/GenericGF';\nimport ReedSolomonDecoder from '../../common/reedsolomon/ReedSolomonDecoder';\nimport BitMatrixParser from './BitMatrixParser';\nimport DataBlock from './DataBlock';\nimport DecodedBitStreamParser from './DecodedBitStreamParser';\n/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * <p>The main class which implements Data Matrix Code decoding -- as opposed to locating and extracting\n * the Data Matrix Code from an image.</p>\n *\n * <AUTHOR> (Brian Brown)\n */\nvar Decoder = /** @class */ (function () {\n    function Decoder() {\n        this.rsDecoder = new ReedSolomonDecoder(GenericGF.DATA_MATRIX_FIELD_256);\n    }\n    /**\n     * <p>Decodes a Data Matrix Code represented as a {@link BitMatrix}. A 1 or \"true\" is taken\n     * to mean a black module.</p>\n     *\n     * @param bits booleans representing white/black Data Matrix Code modules\n     * @return text and bytes encoded within the Data Matrix Code\n     * @throws FormatException if the Data Matrix Code cannot be decoded\n     * @throws ChecksumException if error correction fails\n     */\n    Decoder.prototype.decode = function (bits) {\n        var e_1, _a;\n        // Construct a parser and read version, error-correction level\n        var parser = new BitMatrixParser(bits);\n        var version = parser.getVersion();\n        // Read codewords\n        var codewords = parser.readCodewords();\n        // Separate into data blocks\n        var dataBlocks = DataBlock.getDataBlocks(codewords, version);\n        // Count total number of data bytes\n        var totalBytes = 0;\n        try {\n            for (var dataBlocks_1 = __values(dataBlocks), dataBlocks_1_1 = dataBlocks_1.next(); !dataBlocks_1_1.done; dataBlocks_1_1 = dataBlocks_1.next()) {\n                var db = dataBlocks_1_1.value;\n                totalBytes += db.getNumDataCodewords();\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (dataBlocks_1_1 && !dataBlocks_1_1.done && (_a = dataBlocks_1.return)) _a.call(dataBlocks_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        var resultBytes = new Uint8Array(totalBytes);\n        var dataBlocksCount = dataBlocks.length;\n        // Error-correct and copy data blocks together into a stream of bytes\n        for (var j = 0; j < dataBlocksCount; j++) {\n            var dataBlock = dataBlocks[j];\n            var codewordBytes = dataBlock.getCodewords();\n            var numDataCodewords = dataBlock.getNumDataCodewords();\n            this.correctErrors(codewordBytes, numDataCodewords);\n            for (var i = 0; i < numDataCodewords; i++) {\n                // De-interlace data blocks.\n                resultBytes[i * dataBlocksCount + j] = codewordBytes[i];\n            }\n        }\n        // Decode the contents of that stream of bytes\n        return DecodedBitStreamParser.decode(resultBytes);\n    };\n    /**\n     * <p>Given data and error-correction codewords received, possibly corrupted by errors, attempts to\n     * correct the errors in-place using Reed-Solomon error correction.</p>\n     *\n     * @param codewordBytes data and error correction codewords\n     * @param numDataCodewords number of codewords that are data bytes\n     * @throws ChecksumException if error correction fails\n     */\n    Decoder.prototype.correctErrors = function (codewordBytes, numDataCodewords) {\n        // const numCodewords = codewordBytes.length;\n        // First read into an array of ints\n        var codewordsInts = new Int32Array(codewordBytes);\n        // for (let i = 0; i < numCodewords; i++) {\n        //   codewordsInts[i] = codewordBytes[i] & 0xFF;\n        // }\n        try {\n            this.rsDecoder.decode(codewordsInts, codewordBytes.length - numDataCodewords);\n        }\n        catch (ignored /* ReedSolomonException */) {\n            throw new ChecksumException();\n        }\n        // Copy back into array of bytes -- only need to worry about the bytes that were data\n        // We don't care about errors in the error-correction codewords\n        for (var i = 0; i < numDataCodewords; i++) {\n            codewordBytes[i] = codewordsInts[i];\n        }\n    };\n    return Decoder;\n}());\nexport default Decoder;\n"], "names": [], "mappings": ";;;AAWA;AACA;AACA;AACA;AACA;AACA;AAhBA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;;;;;AAOA;;;;;;;;;;;;;;CAcC,GACD;;;;;CAKC,GACD,IAAI,UAAyB;IACzB,SAAS;QACL,IAAI,CAAC,SAAS,GAAG,IAAI,mQAAA,CAAA,UAAkB,CAAC,0PAAA,CAAA,UAAS,CAAC,qBAAqB;IAC3E;IACA;;;;;;;;KAQC,GACD,QAAQ,SAAS,CAAC,MAAM,GAAG,SAAU,IAAI;QACrC,IAAI,KAAK;QACT,8DAA8D;QAC9D,IAAI,SAAS,IAAI,gQAAA,CAAA,UAAe,CAAC;QACjC,IAAI,UAAU,OAAO,UAAU;QAC/B,iBAAiB;QACjB,IAAI,YAAY,OAAO,aAAa;QACpC,4BAA4B;QAC5B,IAAI,aAAa,0PAAA,CAAA,UAAS,CAAC,aAAa,CAAC,WAAW;QACpD,mCAAmC;QACnC,IAAI,aAAa;QACjB,IAAI;YACA,IAAK,IAAI,eAAe,SAAS,aAAa,iBAAiB,aAAa,IAAI,IAAI,CAAC,eAAe,IAAI,EAAE,iBAAiB,aAAa,IAAI,GAAI;gBAC5I,IAAI,KAAK,eAAe,KAAK;gBAC7B,cAAc,GAAG,mBAAmB;YACxC;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,kBAAkB,CAAC,eAAe,IAAI,IAAI,CAAC,KAAK,aAAa,MAAM,GAAG,GAAG,IAAI,CAAC;YACtF,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,IAAI,cAAc,IAAI,WAAW;QACjC,IAAI,kBAAkB,WAAW,MAAM;QACvC,qEAAqE;QACrE,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,IAAK;YACtC,IAAI,YAAY,UAAU,CAAC,EAAE;YAC7B,IAAI,gBAAgB,UAAU,YAAY;YAC1C,IAAI,mBAAmB,UAAU,mBAAmB;YACpD,IAAI,CAAC,aAAa,CAAC,eAAe;YAClC,IAAK,IAAI,IAAI,GAAG,IAAI,kBAAkB,IAAK;gBACvC,4BAA4B;gBAC5B,WAAW,CAAC,IAAI,kBAAkB,EAAE,GAAG,aAAa,CAAC,EAAE;YAC3D;QACJ;QACA,8CAA8C;QAC9C,OAAO,uQAAA,CAAA,UAAsB,CAAC,MAAM,CAAC;IACzC;IACA;;;;;;;KAOC,GACD,QAAQ,SAAS,CAAC,aAAa,GAAG,SAAU,aAAa,EAAE,gBAAgB;QACvE,6CAA6C;QAC7C,mCAAmC;QACnC,IAAI,gBAAgB,IAAI,WAAW;QACnC,2CAA2C;QAC3C,gDAAgD;QAChD,IAAI;QACJ,IAAI;YACA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,eAAe,cAAc,MAAM,GAAG;QAChE,EACA,OAAO,QAAQ,wBAAwB,KAAI;YACvC,MAAM,IAAI,yOAAA,CAAA,UAAiB;QAC/B;QACA,qFAAqF;QACrF,+DAA+D;QAC/D,IAAK,IAAI,IAAI,GAAG,IAAI,kBAAkB,IAAK;YACvC,aAAa,CAAC,EAAE,GAAG,aAAa,CAAC,EAAE;QACvC;IACJ;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1519, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/core/datamatrix/detector/Detector.js"], "sourcesContent": ["import WhiteRectangleDetector from '../../common/detector/WhiteRectangleDetector';\nimport DetectorResult from '../../common/DetectorResult';\nimport GridSamplerInstance from '../../common/GridSamplerInstance';\nimport NotFoundException from '../../NotFoundException';\nimport ResultPoint from '../../ResultPoint';\n/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * <p>Encapsulates logic that can detect a Data Matrix Code in an image, even if the Data Matrix Code\n * is rotated or skewed, or partially obscured.</p>\n *\n * <AUTHOR>\n */\nvar Detector = /** @class */ (function () {\n    function Detector(image) {\n        this.image = image;\n        this.rectangleDetector = new WhiteRectangleDetector(this.image);\n    }\n    /**\n     * <p>Detects a Data Matrix Code in an image.</p>\n     *\n     * @return {@link DetectorResult} encapsulating results of detecting a Data Matrix Code\n     * @throws NotFoundException if no Data Matrix Code can be found\n     */\n    Detector.prototype.detect = function () {\n        var cornerPoints = this.rectangleDetector.detect();\n        var points = this.detectSolid1(cornerPoints);\n        points = this.detectSolid2(points);\n        points[3] = this.correctTopRight(points);\n        if (!points[3]) {\n            throw new NotFoundException();\n        }\n        points = this.shiftToModuleCenter(points);\n        var topLeft = points[0];\n        var bottomLeft = points[1];\n        var bottomRight = points[2];\n        var topRight = points[3];\n        var dimensionTop = this.transitionsBetween(topLeft, topRight) + 1;\n        var dimensionRight = this.transitionsBetween(bottomRight, topRight) + 1;\n        if ((dimensionTop & 0x01) === 1) {\n            dimensionTop += 1;\n        }\n        if ((dimensionRight & 0x01) === 1) {\n            dimensionRight += 1;\n        }\n        if (4 * dimensionTop < 7 * dimensionRight && 4 * dimensionRight < 7 * dimensionTop) {\n            // The matrix is square\n            dimensionTop = dimensionRight = Math.max(dimensionTop, dimensionRight);\n        }\n        var bits = Detector.sampleGrid(this.image, topLeft, bottomLeft, bottomRight, topRight, dimensionTop, dimensionRight);\n        return new DetectorResult(bits, [topLeft, bottomLeft, bottomRight, topRight]);\n    };\n    Detector.shiftPoint = function (point, to, div) {\n        var x = (to.getX() - point.getX()) / (div + 1);\n        var y = (to.getY() - point.getY()) / (div + 1);\n        return new ResultPoint(point.getX() + x, point.getY() + y);\n    };\n    Detector.moveAway = function (point, fromX, fromY) {\n        var x = point.getX();\n        var y = point.getY();\n        if (x < fromX) {\n            x -= 1;\n        }\n        else {\n            x += 1;\n        }\n        if (y < fromY) {\n            y -= 1;\n        }\n        else {\n            y += 1;\n        }\n        return new ResultPoint(x, y);\n    };\n    /**\n     * Detect a solid side which has minimum transition.\n     */\n    Detector.prototype.detectSolid1 = function (cornerPoints) {\n        // 0  2\n        // 1  3\n        var pointA = cornerPoints[0];\n        var pointB = cornerPoints[1];\n        var pointC = cornerPoints[3];\n        var pointD = cornerPoints[2];\n        var trAB = this.transitionsBetween(pointA, pointB);\n        var trBC = this.transitionsBetween(pointB, pointC);\n        var trCD = this.transitionsBetween(pointC, pointD);\n        var trDA = this.transitionsBetween(pointD, pointA);\n        // 0..3\n        // :  :\n        // 1--2\n        var min = trAB;\n        var points = [pointD, pointA, pointB, pointC];\n        if (min > trBC) {\n            min = trBC;\n            points[0] = pointA;\n            points[1] = pointB;\n            points[2] = pointC;\n            points[3] = pointD;\n        }\n        if (min > trCD) {\n            min = trCD;\n            points[0] = pointB;\n            points[1] = pointC;\n            points[2] = pointD;\n            points[3] = pointA;\n        }\n        if (min > trDA) {\n            points[0] = pointC;\n            points[1] = pointD;\n            points[2] = pointA;\n            points[3] = pointB;\n        }\n        return points;\n    };\n    /**\n     * Detect a second solid side next to first solid side.\n     */\n    Detector.prototype.detectSolid2 = function (points) {\n        // A..D\n        // :  :\n        // B--C\n        var pointA = points[0];\n        var pointB = points[1];\n        var pointC = points[2];\n        var pointD = points[3];\n        // Transition detection on the edge is not stable.\n        // To safely detect, shift the points to the module center.\n        var tr = this.transitionsBetween(pointA, pointD);\n        var pointBs = Detector.shiftPoint(pointB, pointC, (tr + 1) * 4);\n        var pointCs = Detector.shiftPoint(pointC, pointB, (tr + 1) * 4);\n        var trBA = this.transitionsBetween(pointBs, pointA);\n        var trCD = this.transitionsBetween(pointCs, pointD);\n        // 0..3\n        // |  :\n        // 1--2\n        if (trBA < trCD) {\n            // solid sides: A-B-C\n            points[0] = pointA;\n            points[1] = pointB;\n            points[2] = pointC;\n            points[3] = pointD;\n        }\n        else {\n            // solid sides: B-C-D\n            points[0] = pointB;\n            points[1] = pointC;\n            points[2] = pointD;\n            points[3] = pointA;\n        }\n        return points;\n    };\n    /**\n     * Calculates the corner position of the white top right module.\n     */\n    Detector.prototype.correctTopRight = function (points) {\n        // A..D\n        // |  :\n        // B--C\n        var pointA = points[0];\n        var pointB = points[1];\n        var pointC = points[2];\n        var pointD = points[3];\n        // shift points for safe transition detection.\n        var trTop = this.transitionsBetween(pointA, pointD);\n        var trRight = this.transitionsBetween(pointB, pointD);\n        var pointAs = Detector.shiftPoint(pointA, pointB, (trRight + 1) * 4);\n        var pointCs = Detector.shiftPoint(pointC, pointB, (trTop + 1) * 4);\n        trTop = this.transitionsBetween(pointAs, pointD);\n        trRight = this.transitionsBetween(pointCs, pointD);\n        var candidate1 = new ResultPoint(pointD.getX() + (pointC.getX() - pointB.getX()) / (trTop + 1), pointD.getY() + (pointC.getY() - pointB.getY()) / (trTop + 1));\n        var candidate2 = new ResultPoint(pointD.getX() + (pointA.getX() - pointB.getX()) / (trRight + 1), pointD.getY() + (pointA.getY() - pointB.getY()) / (trRight + 1));\n        if (!this.isValid(candidate1)) {\n            if (this.isValid(candidate2)) {\n                return candidate2;\n            }\n            return null;\n        }\n        if (!this.isValid(candidate2)) {\n            return candidate1;\n        }\n        var sumc1 = this.transitionsBetween(pointAs, candidate1) + this.transitionsBetween(pointCs, candidate1);\n        var sumc2 = this.transitionsBetween(pointAs, candidate2) + this.transitionsBetween(pointCs, candidate2);\n        if (sumc1 > sumc2) {\n            return candidate1;\n        }\n        else {\n            return candidate2;\n        }\n    };\n    /**\n     * Shift the edge points to the module center.\n     */\n    Detector.prototype.shiftToModuleCenter = function (points) {\n        // A..D\n        // |  :\n        // B--C\n        var pointA = points[0];\n        var pointB = points[1];\n        var pointC = points[2];\n        var pointD = points[3];\n        // calculate pseudo dimensions\n        var dimH = this.transitionsBetween(pointA, pointD) + 1;\n        var dimV = this.transitionsBetween(pointC, pointD) + 1;\n        // shift points for safe dimension detection\n        var pointAs = Detector.shiftPoint(pointA, pointB, dimV * 4);\n        var pointCs = Detector.shiftPoint(pointC, pointB, dimH * 4);\n        //  calculate more precise dimensions\n        dimH = this.transitionsBetween(pointAs, pointD) + 1;\n        dimV = this.transitionsBetween(pointCs, pointD) + 1;\n        if ((dimH & 0x01) === 1) {\n            dimH += 1;\n        }\n        if ((dimV & 0x01) === 1) {\n            dimV += 1;\n        }\n        // WhiteRectangleDetector returns points inside of the rectangle.\n        // I want points on the edges.\n        var centerX = (pointA.getX() + pointB.getX() + pointC.getX() + pointD.getX()) / 4;\n        var centerY = (pointA.getY() + pointB.getY() + pointC.getY() + pointD.getY()) / 4;\n        pointA = Detector.moveAway(pointA, centerX, centerY);\n        pointB = Detector.moveAway(pointB, centerX, centerY);\n        pointC = Detector.moveAway(pointC, centerX, centerY);\n        pointD = Detector.moveAway(pointD, centerX, centerY);\n        var pointBs;\n        var pointDs;\n        // shift points to the center of each modules\n        pointAs = Detector.shiftPoint(pointA, pointB, dimV * 4);\n        pointAs = Detector.shiftPoint(pointAs, pointD, dimH * 4);\n        pointBs = Detector.shiftPoint(pointB, pointA, dimV * 4);\n        pointBs = Detector.shiftPoint(pointBs, pointC, dimH * 4);\n        pointCs = Detector.shiftPoint(pointC, pointD, dimV * 4);\n        pointCs = Detector.shiftPoint(pointCs, pointB, dimH * 4);\n        pointDs = Detector.shiftPoint(pointD, pointC, dimV * 4);\n        pointDs = Detector.shiftPoint(pointDs, pointA, dimH * 4);\n        return [pointAs, pointBs, pointCs, pointDs];\n    };\n    Detector.prototype.isValid = function (p) {\n        return p.getX() >= 0 && p.getX() < this.image.getWidth() && p.getY() > 0 && p.getY() < this.image.getHeight();\n    };\n    Detector.sampleGrid = function (image, topLeft, bottomLeft, bottomRight, topRight, dimensionX, dimensionY) {\n        var sampler = GridSamplerInstance.getInstance();\n        return sampler.sampleGrid(image, dimensionX, dimensionY, 0.5, 0.5, dimensionX - 0.5, 0.5, dimensionX - 0.5, dimensionY - 0.5, 0.5, dimensionY - 0.5, topLeft.getX(), topLeft.getY(), topRight.getX(), topRight.getY(), bottomRight.getX(), bottomRight.getY(), bottomLeft.getX(), bottomLeft.getY());\n    };\n    /**\n     * Counts the number of black/white transitions between two points, using something like Bresenham's algorithm.\n     */\n    Detector.prototype.transitionsBetween = function (from, to) {\n        // See QR Code Detector, sizeOfBlackWhiteBlackRun()\n        var fromX = Math.trunc(from.getX());\n        var fromY = Math.trunc(from.getY());\n        var toX = Math.trunc(to.getX());\n        var toY = Math.trunc(to.getY());\n        var steep = Math.abs(toY - fromY) > Math.abs(toX - fromX);\n        if (steep) {\n            var temp = fromX;\n            fromX = fromY;\n            fromY = temp;\n            temp = toX;\n            toX = toY;\n            toY = temp;\n        }\n        var dx = Math.abs(toX - fromX);\n        var dy = Math.abs(toY - fromY);\n        var error = -dx / 2;\n        var ystep = fromY < toY ? 1 : -1;\n        var xstep = fromX < toX ? 1 : -1;\n        var transitions = 0;\n        var inBlack = this.image.get(steep ? fromY : fromX, steep ? fromX : fromY);\n        for (var x = fromX, y = fromY; x !== toX; x += xstep) {\n            var isBlack = this.image.get(steep ? y : x, steep ? x : y);\n            if (isBlack !== inBlack) {\n                transitions++;\n                inBlack = isBlack;\n            }\n            error += dy;\n            if (error > 0) {\n                if (y === toY) {\n                    break;\n                }\n                y += ystep;\n                error -= dx;\n            }\n        }\n        return transitions;\n    };\n    return Detector;\n}());\nexport default Detector;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AACA;;;;;;;;;;;;;;CAcC,GACD;;;;;CAKC,GACD,IAAI,WAA0B;IAC1B,SAAS,SAAS,KAAK;QACnB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,iBAAiB,GAAG,IAAI,oQAAA,CAAA,UAAsB,CAAC,IAAI,CAAC,KAAK;IAClE;IACA;;;;;KAKC,GACD,SAAS,SAAS,CAAC,MAAM,GAAG;QACxB,IAAI,eAAe,IAAI,CAAC,iBAAiB,CAAC,MAAM;QAChD,IAAI,SAAS,IAAI,CAAC,YAAY,CAAC;QAC/B,SAAS,IAAI,CAAC,YAAY,CAAC;QAC3B,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC;QACjC,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;YACZ,MAAM,IAAI,yOAAA,CAAA,UAAiB;QAC/B;QACA,SAAS,IAAI,CAAC,mBAAmB,CAAC;QAClC,IAAI,UAAU,MAAM,CAAC,EAAE;QACvB,IAAI,aAAa,MAAM,CAAC,EAAE;QAC1B,IAAI,cAAc,MAAM,CAAC,EAAE;QAC3B,IAAI,WAAW,MAAM,CAAC,EAAE;QACxB,IAAI,eAAe,IAAI,CAAC,kBAAkB,CAAC,SAAS,YAAY;QAChE,IAAI,iBAAiB,IAAI,CAAC,kBAAkB,CAAC,aAAa,YAAY;QACtE,IAAI,CAAC,eAAe,IAAI,MAAM,GAAG;YAC7B,gBAAgB;QACpB;QACA,IAAI,CAAC,iBAAiB,IAAI,MAAM,GAAG;YAC/B,kBAAkB;QACtB;QACA,IAAI,IAAI,eAAe,IAAI,kBAAkB,IAAI,iBAAiB,IAAI,cAAc;YAChF,uBAAuB;YACvB,eAAe,iBAAiB,KAAK,GAAG,CAAC,cAAc;QAC3D;QACA,IAAI,OAAO,SAAS,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,YAAY,aAAa,UAAU,cAAc;QACrG,OAAO,IAAI,gPAAA,CAAA,UAAc,CAAC,MAAM;YAAC;YAAS;YAAY;YAAa;SAAS;IAChF;IACA,SAAS,UAAU,GAAG,SAAU,KAAK,EAAE,EAAE,EAAE,GAAG;QAC1C,IAAI,IAAI,CAAC,GAAG,IAAI,KAAK,MAAM,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;QAC7C,IAAI,IAAI,CAAC,GAAG,IAAI,KAAK,MAAM,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;QAC7C,OAAO,IAAI,mOAAA,CAAA,UAAW,CAAC,MAAM,IAAI,KAAK,GAAG,MAAM,IAAI,KAAK;IAC5D;IACA,SAAS,QAAQ,GAAG,SAAU,KAAK,EAAE,KAAK,EAAE,KAAK;QAC7C,IAAI,IAAI,MAAM,IAAI;QAClB,IAAI,IAAI,MAAM,IAAI;QAClB,IAAI,IAAI,OAAO;YACX,KAAK;QACT,OACK;YACD,KAAK;QACT;QACA,IAAI,IAAI,OAAO;YACX,KAAK;QACT,OACK;YACD,KAAK;QACT;QACA,OAAO,IAAI,mOAAA,CAAA,UAAW,CAAC,GAAG;IAC9B;IACA;;KAEC,GACD,SAAS,SAAS,CAAC,YAAY,GAAG,SAAU,YAAY;QACpD,OAAO;QACP,OAAO;QACP,IAAI,SAAS,YAAY,CAAC,EAAE;QAC5B,IAAI,SAAS,YAAY,CAAC,EAAE;QAC5B,IAAI,SAAS,YAAY,CAAC,EAAE;QAC5B,IAAI,SAAS,YAAY,CAAC,EAAE;QAC5B,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,QAAQ;QAC3C,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,QAAQ;QAC3C,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,QAAQ;QAC3C,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,QAAQ;QAC3C,OAAO;QACP,OAAO;QACP,OAAO;QACP,IAAI,MAAM;QACV,IAAI,SAAS;YAAC;YAAQ;YAAQ;YAAQ;SAAO;QAC7C,IAAI,MAAM,MAAM;YACZ,MAAM;YACN,MAAM,CAAC,EAAE,GAAG;YACZ,MAAM,CAAC,EAAE,GAAG;YACZ,MAAM,CAAC,EAAE,GAAG;YACZ,MAAM,CAAC,EAAE,GAAG;QAChB;QACA,IAAI,MAAM,MAAM;YACZ,MAAM;YACN,MAAM,CAAC,EAAE,GAAG;YACZ,MAAM,CAAC,EAAE,GAAG;YACZ,MAAM,CAAC,EAAE,GAAG;YACZ,MAAM,CAAC,EAAE,GAAG;QAChB;QACA,IAAI,MAAM,MAAM;YACZ,MAAM,CAAC,EAAE,GAAG;YACZ,MAAM,CAAC,EAAE,GAAG;YACZ,MAAM,CAAC,EAAE,GAAG;YACZ,MAAM,CAAC,EAAE,GAAG;QAChB;QACA,OAAO;IACX;IACA;;KAEC,GACD,SAAS,SAAS,CAAC,YAAY,GAAG,SAAU,MAAM;QAC9C,OAAO;QACP,OAAO;QACP,OAAO;QACP,IAAI,SAAS,MAAM,CAAC,EAAE;QACtB,IAAI,SAAS,MAAM,CAAC,EAAE;QACtB,IAAI,SAAS,MAAM,CAAC,EAAE;QACtB,IAAI,SAAS,MAAM,CAAC,EAAE;QACtB,kDAAkD;QAClD,2DAA2D;QAC3D,IAAI,KAAK,IAAI,CAAC,kBAAkB,CAAC,QAAQ;QACzC,IAAI,UAAU,SAAS,UAAU,CAAC,QAAQ,QAAQ,CAAC,KAAK,CAAC,IAAI;QAC7D,IAAI,UAAU,SAAS,UAAU,CAAC,QAAQ,QAAQ,CAAC,KAAK,CAAC,IAAI;QAC7D,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,SAAS;QAC5C,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,SAAS;QAC5C,OAAO;QACP,OAAO;QACP,OAAO;QACP,IAAI,OAAO,MAAM;YACb,qBAAqB;YACrB,MAAM,CAAC,EAAE,GAAG;YACZ,MAAM,CAAC,EAAE,GAAG;YACZ,MAAM,CAAC,EAAE,GAAG;YACZ,MAAM,CAAC,EAAE,GAAG;QAChB,OACK;YACD,qBAAqB;YACrB,MAAM,CAAC,EAAE,GAAG;YACZ,MAAM,CAAC,EAAE,GAAG;YACZ,MAAM,CAAC,EAAE,GAAG;YACZ,MAAM,CAAC,EAAE,GAAG;QAChB;QACA,OAAO;IACX;IACA;;KAEC,GACD,SAAS,SAAS,CAAC,eAAe,GAAG,SAAU,MAAM;QACjD,OAAO;QACP,OAAO;QACP,OAAO;QACP,IAAI,SAAS,MAAM,CAAC,EAAE;QACtB,IAAI,SAAS,MAAM,CAAC,EAAE;QACtB,IAAI,SAAS,MAAM,CAAC,EAAE;QACtB,IAAI,SAAS,MAAM,CAAC,EAAE;QACtB,8CAA8C;QAC9C,IAAI,QAAQ,IAAI,CAAC,kBAAkB,CAAC,QAAQ;QAC5C,IAAI,UAAU,IAAI,CAAC,kBAAkB,CAAC,QAAQ;QAC9C,IAAI,UAAU,SAAS,UAAU,CAAC,QAAQ,QAAQ,CAAC,UAAU,CAAC,IAAI;QAClE,IAAI,UAAU,SAAS,UAAU,CAAC,QAAQ,QAAQ,CAAC,QAAQ,CAAC,IAAI;QAChE,QAAQ,IAAI,CAAC,kBAAkB,CAAC,SAAS;QACzC,UAAU,IAAI,CAAC,kBAAkB,CAAC,SAAS;QAC3C,IAAI,aAAa,IAAI,mOAAA,CAAA,UAAW,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,OAAO,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,OAAO,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,OAAO,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC;QAC5J,IAAI,aAAa,IAAI,mOAAA,CAAA,UAAW,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,OAAO,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,OAAO,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,OAAO,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC;QAChK,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa;YAC3B,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa;gBAC1B,OAAO;YACX;YACA,OAAO;QACX;QACA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa;YAC3B,OAAO;QACX;QACA,IAAI,QAAQ,IAAI,CAAC,kBAAkB,CAAC,SAAS,cAAc,IAAI,CAAC,kBAAkB,CAAC,SAAS;QAC5F,IAAI,QAAQ,IAAI,CAAC,kBAAkB,CAAC,SAAS,cAAc,IAAI,CAAC,kBAAkB,CAAC,SAAS;QAC5F,IAAI,QAAQ,OAAO;YACf,OAAO;QACX,OACK;YACD,OAAO;QACX;IACJ;IACA;;KAEC,GACD,SAAS,SAAS,CAAC,mBAAmB,GAAG,SAAU,MAAM;QACrD,OAAO;QACP,OAAO;QACP,OAAO;QACP,IAAI,SAAS,MAAM,CAAC,EAAE;QACtB,IAAI,SAAS,MAAM,CAAC,EAAE;QACtB,IAAI,SAAS,MAAM,CAAC,EAAE;QACtB,IAAI,SAAS,MAAM,CAAC,EAAE;QACtB,8BAA8B;QAC9B,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,QAAQ,UAAU;QACrD,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,QAAQ,UAAU;QACrD,4CAA4C;QAC5C,IAAI,UAAU,SAAS,UAAU,CAAC,QAAQ,QAAQ,OAAO;QACzD,IAAI,UAAU,SAAS,UAAU,CAAC,QAAQ,QAAQ,OAAO;QACzD,qCAAqC;QACrC,OAAO,IAAI,CAAC,kBAAkB,CAAC,SAAS,UAAU;QAClD,OAAO,IAAI,CAAC,kBAAkB,CAAC,SAAS,UAAU;QAClD,IAAI,CAAC,OAAO,IAAI,MAAM,GAAG;YACrB,QAAQ;QACZ;QACA,IAAI,CAAC,OAAO,IAAI,MAAM,GAAG;YACrB,QAAQ;QACZ;QACA,iEAAiE;QACjE,8BAA8B;QAC9B,IAAI,UAAU,CAAC,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,IAAI,EAAE,IAAI;QAChF,IAAI,UAAU,CAAC,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,IAAI,EAAE,IAAI;QAChF,SAAS,SAAS,QAAQ,CAAC,QAAQ,SAAS;QAC5C,SAAS,SAAS,QAAQ,CAAC,QAAQ,SAAS;QAC5C,SAAS,SAAS,QAAQ,CAAC,QAAQ,SAAS;QAC5C,SAAS,SAAS,QAAQ,CAAC,QAAQ,SAAS;QAC5C,IAAI;QACJ,IAAI;QACJ,6CAA6C;QAC7C,UAAU,SAAS,UAAU,CAAC,QAAQ,QAAQ,OAAO;QACrD,UAAU,SAAS,UAAU,CAAC,SAAS,QAAQ,OAAO;QACtD,UAAU,SAAS,UAAU,CAAC,QAAQ,QAAQ,OAAO;QACrD,UAAU,SAAS,UAAU,CAAC,SAAS,QAAQ,OAAO;QACtD,UAAU,SAAS,UAAU,CAAC,QAAQ,QAAQ,OAAO;QACrD,UAAU,SAAS,UAAU,CAAC,SAAS,QAAQ,OAAO;QACtD,UAAU,SAAS,UAAU,CAAC,QAAQ,QAAQ,OAAO;QACrD,UAAU,SAAS,UAAU,CAAC,SAAS,QAAQ,OAAO;QACtD,OAAO;YAAC;YAAS;YAAS;YAAS;SAAQ;IAC/C;IACA,SAAS,SAAS,CAAC,OAAO,GAAG,SAAU,CAAC;QACpC,OAAO,EAAE,IAAI,MAAM,KAAK,EAAE,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,QAAQ,MAAM,EAAE,IAAI,KAAK,KAAK,EAAE,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,SAAS;IAC/G;IACA,SAAS,UAAU,GAAG,SAAU,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU;QACrG,IAAI,UAAU,qPAAA,CAAA,UAAmB,CAAC,WAAW;QAC7C,OAAO,QAAQ,UAAU,CAAC,OAAO,YAAY,YAAY,KAAK,KAAK,aAAa,KAAK,KAAK,aAAa,KAAK,aAAa,KAAK,KAAK,aAAa,KAAK,QAAQ,IAAI,IAAI,QAAQ,IAAI,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,IAAI,YAAY,IAAI,IAAI,YAAY,IAAI,IAAI,WAAW,IAAI,IAAI,WAAW,IAAI;IACrS;IACA;;KAEC,GACD,SAAS,SAAS,CAAC,kBAAkB,GAAG,SAAU,IAAI,EAAE,EAAE;QACtD,mDAAmD;QACnD,IAAI,QAAQ,KAAK,KAAK,CAAC,KAAK,IAAI;QAChC,IAAI,QAAQ,KAAK,KAAK,CAAC,KAAK,IAAI;QAChC,IAAI,MAAM,KAAK,KAAK,CAAC,GAAG,IAAI;QAC5B,IAAI,MAAM,KAAK,KAAK,CAAC,GAAG,IAAI;QAC5B,IAAI,QAAQ,KAAK,GAAG,CAAC,MAAM,SAAS,KAAK,GAAG,CAAC,MAAM;QACnD,IAAI,OAAO;YACP,IAAI,OAAO;YACX,QAAQ;YACR,QAAQ;YACR,OAAO;YACP,MAAM;YACN,MAAM;QACV;QACA,IAAI,KAAK,KAAK,GAAG,CAAC,MAAM;QACxB,IAAI,KAAK,KAAK,GAAG,CAAC,MAAM;QACxB,IAAI,QAAQ,CAAC,KAAK;QAClB,IAAI,QAAQ,QAAQ,MAAM,IAAI,CAAC;QAC/B,IAAI,QAAQ,QAAQ,MAAM,IAAI,CAAC;QAC/B,IAAI,cAAc;QAClB,IAAI,UAAU,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,QAAQ,OAAO,QAAQ,QAAQ;QACpE,IAAK,IAAI,IAAI,OAAO,IAAI,OAAO,MAAM,KAAK,KAAK,MAAO;YAClD,IAAI,UAAU,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,IAAI,GAAG,QAAQ,IAAI;YACxD,IAAI,YAAY,SAAS;gBACrB;gBACA,UAAU;YACd;YACA,SAAS;YACT,IAAI,QAAQ,GAAG;gBACX,IAAI,MAAM,KAAK;oBACX;gBACJ;gBACA,KAAK;gBACL,SAAS;YACb;QACJ;QACA,OAAO;IACX;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1836, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/core/datamatrix/DataMatrixReader.js"], "sourcesContent": ["import BarcodeFormat from '../BarcodeFormat';\nimport BitMatrix from '../common/BitMatrix';\nimport DecodeHintType from '../DecodeHintType';\nimport NotFoundException from '../NotFoundException';\nimport Result from '../Result';\nimport ResultMetadataType from '../ResultMetadataType';\nimport System from '../util/System';\nimport Decoder from './decoder/Decoder';\nimport Detector from './detector/Detector';\n/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * This implementation can detect and decode Data Matrix codes in an image.\n *\n * <AUTHOR> (<PERSON>)\n */\nvar DataMatrixReader = /** @class */ (function () {\n    function DataMatrixReader() {\n        this.decoder = new Decoder();\n    }\n    /**\n     * Locates and decodes a Data Matrix code in an image.\n     *\n     * @return a String representing the content encoded by the Data Matrix code\n     * @throws NotFoundException if a Data Matrix code cannot be found\n     * @throws FormatException if a Data Matrix code cannot be decoded\n     * @throws ChecksumException if error correction fails\n     */\n    // @Override\n    // public Result decode(BinaryBitmap image) throws NotFoundException, ChecksumException, FormatException {\n    //   return decode(image, null);\n    // }\n    // @Override\n    DataMatrixReader.prototype.decode = function (image, hints) {\n        if (hints === void 0) { hints = null; }\n        var decoderResult;\n        var points;\n        if (hints != null && hints.has(DecodeHintType.PURE_BARCODE)) {\n            var bits = DataMatrixReader.extractPureBits(image.getBlackMatrix());\n            decoderResult = this.decoder.decode(bits);\n            points = DataMatrixReader.NO_POINTS;\n        }\n        else {\n            var detectorResult = new Detector(image.getBlackMatrix()).detect();\n            decoderResult = this.decoder.decode(detectorResult.getBits());\n            points = detectorResult.getPoints();\n        }\n        var rawBytes = decoderResult.getRawBytes();\n        var result = new Result(decoderResult.getText(), rawBytes, 8 * rawBytes.length, points, BarcodeFormat.DATA_MATRIX, System.currentTimeMillis());\n        var byteSegments = decoderResult.getByteSegments();\n        if (byteSegments != null) {\n            result.putMetadata(ResultMetadataType.BYTE_SEGMENTS, byteSegments);\n        }\n        var ecLevel = decoderResult.getECLevel();\n        if (ecLevel != null) {\n            result.putMetadata(ResultMetadataType.ERROR_CORRECTION_LEVEL, ecLevel);\n        }\n        return result;\n    };\n    // @Override\n    DataMatrixReader.prototype.reset = function () {\n        // do nothing\n    };\n    /**\n     * This method detects a code in a \"pure\" image -- that is, pure monochrome image\n     * which contains only an unrotated, unskewed, image of a code, with some white border\n     * around it. This is a specialized method that works exceptionally fast in this special\n     * case.\n     *\n     * @see com.google.zxing.qrcode.QRCodeReader#extractPureBits(BitMatrix)\n     */\n    DataMatrixReader.extractPureBits = function (image) {\n        var leftTopBlack = image.getTopLeftOnBit();\n        var rightBottomBlack = image.getBottomRightOnBit();\n        if (leftTopBlack == null || rightBottomBlack == null) {\n            throw new NotFoundException();\n        }\n        var moduleSize = this.moduleSize(leftTopBlack, image);\n        var top = leftTopBlack[1];\n        var bottom = rightBottomBlack[1];\n        var left = leftTopBlack[0];\n        var right = rightBottomBlack[0];\n        var matrixWidth = (right - left + 1) / moduleSize;\n        var matrixHeight = (bottom - top + 1) / moduleSize;\n        if (matrixWidth <= 0 || matrixHeight <= 0) {\n            throw new NotFoundException();\n        }\n        // Push in the \"border\" by half the module width so that we start\n        // sampling in the middle of the module. Just in case the image is a\n        // little off, this will help recover.\n        var nudge = moduleSize / 2;\n        top += nudge;\n        left += nudge;\n        // Now just read off the bits\n        var bits = new BitMatrix(matrixWidth, matrixHeight);\n        for (var y = 0; y < matrixHeight; y++) {\n            var iOffset = top + y * moduleSize;\n            for (var x = 0; x < matrixWidth; x++) {\n                if (image.get(left + x * moduleSize, iOffset)) {\n                    bits.set(x, y);\n                }\n            }\n        }\n        return bits;\n    };\n    DataMatrixReader.moduleSize = function (leftTopBlack, image) {\n        var width = image.getWidth();\n        var x = leftTopBlack[0];\n        var y = leftTopBlack[1];\n        while (x < width && image.get(x, y)) {\n            x++;\n        }\n        if (x === width) {\n            throw new NotFoundException();\n        }\n        var moduleSize = x - leftTopBlack[0];\n        if (moduleSize === 0) {\n            throw new NotFoundException();\n        }\n        return moduleSize;\n    };\n    DataMatrixReader.NO_POINTS = [];\n    return DataMatrixReader;\n}());\nexport default DataMatrixReader;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AACA;;;;;;;;;;;;;;CAcC,GACD;;;;CAIC,GACD,IAAI,mBAAkC;IAClC,SAAS;QACL,IAAI,CAAC,OAAO,GAAG,IAAI,wPAAA,CAAA,UAAO;IAC9B;IACA;;;;;;;KAOC,GACD,YAAY;IACZ,0GAA0G;IAC1G,gCAAgC;IAChC,IAAI;IACJ,YAAY;IACZ,iBAAiB,SAAS,CAAC,MAAM,GAAG,SAAU,KAAK,EAAE,KAAK;QACtD,IAAI,UAAU,KAAK,GAAG;YAAE,QAAQ;QAAM;QACtC,IAAI;QACJ,IAAI;QACJ,IAAI,SAAS,QAAQ,MAAM,GAAG,CAAC,sOAAA,CAAA,UAAc,CAAC,YAAY,GAAG;YACzD,IAAI,OAAO,iBAAiB,eAAe,CAAC,MAAM,cAAc;YAChE,gBAAgB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;YACpC,SAAS,iBAAiB,SAAS;QACvC,OACK;YACD,IAAI,iBAAiB,IAAI,0PAAA,CAAA,UAAQ,CAAC,MAAM,cAAc,IAAI,MAAM;YAChE,gBAAgB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,OAAO;YAC1D,SAAS,eAAe,SAAS;QACrC;QACA,IAAI,WAAW,cAAc,WAAW;QACxC,IAAI,SAAS,IAAI,8NAAA,CAAA,UAAM,CAAC,cAAc,OAAO,IAAI,UAAU,IAAI,SAAS,MAAM,EAAE,QAAQ,qOAAA,CAAA,UAAa,CAAC,WAAW,EAAE,sOAAA,CAAA,UAAM,CAAC,iBAAiB;QAC3I,IAAI,eAAe,cAAc,eAAe;QAChD,IAAI,gBAAgB,MAAM;YACtB,OAAO,WAAW,CAAC,0OAAA,CAAA,UAAkB,CAAC,aAAa,EAAE;QACzD;QACA,IAAI,UAAU,cAAc,UAAU;QACtC,IAAI,WAAW,MAAM;YACjB,OAAO,WAAW,CAAC,0OAAA,CAAA,UAAkB,CAAC,sBAAsB,EAAE;QAClE;QACA,OAAO;IACX;IACA,YAAY;IACZ,iBAAiB,SAAS,CAAC,KAAK,GAAG;IAC/B,aAAa;IACjB;IACA;;;;;;;KAOC,GACD,iBAAiB,eAAe,GAAG,SAAU,KAAK;QAC9C,IAAI,eAAe,MAAM,eAAe;QACxC,IAAI,mBAAmB,MAAM,mBAAmB;QAChD,IAAI,gBAAgB,QAAQ,oBAAoB,MAAM;YAClD,MAAM,IAAI,yOAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,aAAa,IAAI,CAAC,UAAU,CAAC,cAAc;QAC/C,IAAI,MAAM,YAAY,CAAC,EAAE;QACzB,IAAI,SAAS,gBAAgB,CAAC,EAAE;QAChC,IAAI,OAAO,YAAY,CAAC,EAAE;QAC1B,IAAI,QAAQ,gBAAgB,CAAC,EAAE;QAC/B,IAAI,cAAc,CAAC,QAAQ,OAAO,CAAC,IAAI;QACvC,IAAI,eAAe,CAAC,SAAS,MAAM,CAAC,IAAI;QACxC,IAAI,eAAe,KAAK,gBAAgB,GAAG;YACvC,MAAM,IAAI,yOAAA,CAAA,UAAiB;QAC/B;QACA,iEAAiE;QACjE,oEAAoE;QACpE,sCAAsC;QACtC,IAAI,QAAQ,aAAa;QACzB,OAAO;QACP,QAAQ;QACR,6BAA6B;QAC7B,IAAI,OAAO,IAAI,2OAAA,CAAA,UAAS,CAAC,aAAa;QACtC,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,IAAK;YACnC,IAAI,UAAU,MAAM,IAAI;YACxB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;gBAClC,IAAI,MAAM,GAAG,CAAC,OAAO,IAAI,YAAY,UAAU;oBAC3C,KAAK,GAAG,CAAC,GAAG;gBAChB;YACJ;QACJ;QACA,OAAO;IACX;IACA,iBAAiB,UAAU,GAAG,SAAU,YAAY,EAAE,KAAK;QACvD,IAAI,QAAQ,MAAM,QAAQ;QAC1B,IAAI,IAAI,YAAY,CAAC,EAAE;QACvB,IAAI,IAAI,YAAY,CAAC,EAAE;QACvB,MAAO,IAAI,SAAS,MAAM,GAAG,CAAC,GAAG,GAAI;YACjC;QACJ;QACA,IAAI,MAAM,OAAO;YACb,MAAM,IAAI,yOAAA,CAAA,UAAiB;QAC/B;QACA,IAAI,aAAa,IAAI,YAAY,CAAC,EAAE;QACpC,IAAI,eAAe,GAAG;YAClB,MAAM,IAAI,yOAAA,CAAA,UAAiB;QAC/B;QACA,OAAO;IACX;IACA,iBAAiB,SAAS,GAAG,EAAE;IAC/B,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1987, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/core/datamatrix/encoder/DefaultPlacement.js"], "sourcesContent": ["import Arrays from '../../util/Arrays';\n/**\n * Symbol Character Placement Program. Adapted from Annex M.1 in ISO/IEC 16022:2000(E).\n */\nvar DefaultPlacement = /** @class */ (function () {\n    /**\n     * Main constructor\n     *\n     * @param codewords the codewords to place\n     * @param numcols   the number of columns\n     * @param numrows   the number of rows\n     */\n    function DefaultPlacement(codewords, numcols, numrows) {\n        this.codewords = codewords;\n        this.numcols = numcols;\n        this.numrows = numrows;\n        this.bits = new Uint8Array(numcols * numrows);\n        Arrays.fill(this.bits, 2); // Initialize with \"not set\" value\n    }\n    DefaultPlacement.prototype.getNumrows = function () {\n        return this.numrows;\n    };\n    DefaultPlacement.prototype.getNumcols = function () {\n        return this.numcols;\n    };\n    DefaultPlacement.prototype.getBits = function () {\n        return this.bits;\n    };\n    DefaultPlacement.prototype.getBit = function (col, row) {\n        return this.bits[row * this.numcols + col] === 1;\n    };\n    DefaultPlacement.prototype.setBit = function (col, row, bit) {\n        this.bits[row * this.numcols + col] = bit ? 1 : 0;\n    };\n    DefaultPlacement.prototype.noBit = function (col, row) {\n        return this.bits[row * this.numcols + col] === 2;\n    };\n    DefaultPlacement.prototype.place = function () {\n        var pos = 0;\n        var row = 4;\n        var col = 0;\n        do {\n            // repeatedly first check for one of the special corner cases, then...\n            if (row === this.numrows && col === 0) {\n                this.corner1(pos++);\n            }\n            if (row === this.numrows - 2 && col === 0 && this.numcols % 4 !== 0) {\n                this.corner2(pos++);\n            }\n            if (row === this.numrows - 2 && col === 0 && this.numcols % 8 === 4) {\n                this.corner3(pos++);\n            }\n            if (row === this.numrows + 4 && col === 2 && this.numcols % 8 === 0) {\n                this.corner4(pos++);\n            }\n            // sweep upward diagonally, inserting successive characters...\n            do {\n                if (row < this.numrows && col >= 0 && this.noBit(col, row)) {\n                    this.utah(row, col, pos++);\n                }\n                row -= 2;\n                col += 2;\n            } while (row >= 0 && col < this.numcols);\n            row++;\n            col += 3;\n            // and then sweep downward diagonally, inserting successive characters, ...\n            do {\n                if (row >= 0 && col < this.numcols && this.noBit(col, row)) {\n                    this.utah(row, col, pos++);\n                }\n                row += 2;\n                col -= 2;\n            } while (row < this.numrows && col >= 0);\n            row += 3;\n            col++;\n            // ...until the entire array is scanned\n        } while (row < this.numrows || col < this.numcols);\n        // Lastly, if the lower right-hand corner is untouched, fill in fixed pattern\n        if (this.noBit(this.numcols - 1, this.numrows - 1)) {\n            this.setBit(this.numcols - 1, this.numrows - 1, true);\n            this.setBit(this.numcols - 2, this.numrows - 2, true);\n        }\n    };\n    DefaultPlacement.prototype.module = function (row, col, pos, bit) {\n        if (row < 0) {\n            row += this.numrows;\n            col += 4 - ((this.numrows + 4) % 8);\n        }\n        if (col < 0) {\n            col += this.numcols;\n            row += 4 - ((this.numcols + 4) % 8);\n        }\n        // Note the conversion:\n        var v = this.codewords.charCodeAt(pos);\n        v &= 1 << (8 - bit);\n        this.setBit(col, row, v !== 0);\n    };\n    /**\n     * Places the 8 bits of a utah-shaped symbol character in ECC200.\n     *\n     * @param row the row\n     * @param col the column\n     * @param pos character position\n     */\n    DefaultPlacement.prototype.utah = function (row, col, pos) {\n        this.module(row - 2, col - 2, pos, 1);\n        this.module(row - 2, col - 1, pos, 2);\n        this.module(row - 1, col - 2, pos, 3);\n        this.module(row - 1, col - 1, pos, 4);\n        this.module(row - 1, col, pos, 5);\n        this.module(row, col - 2, pos, 6);\n        this.module(row, col - 1, pos, 7);\n        this.module(row, col, pos, 8);\n    };\n    DefaultPlacement.prototype.corner1 = function (pos) {\n        this.module(this.numrows - 1, 0, pos, 1);\n        this.module(this.numrows - 1, 1, pos, 2);\n        this.module(this.numrows - 1, 2, pos, 3);\n        this.module(0, this.numcols - 2, pos, 4);\n        this.module(0, this.numcols - 1, pos, 5);\n        this.module(1, this.numcols - 1, pos, 6);\n        this.module(2, this.numcols - 1, pos, 7);\n        this.module(3, this.numcols - 1, pos, 8);\n    };\n    DefaultPlacement.prototype.corner2 = function (pos) {\n        this.module(this.numrows - 3, 0, pos, 1);\n        this.module(this.numrows - 2, 0, pos, 2);\n        this.module(this.numrows - 1, 0, pos, 3);\n        this.module(0, this.numcols - 4, pos, 4);\n        this.module(0, this.numcols - 3, pos, 5);\n        this.module(0, this.numcols - 2, pos, 6);\n        this.module(0, this.numcols - 1, pos, 7);\n        this.module(1, this.numcols - 1, pos, 8);\n    };\n    DefaultPlacement.prototype.corner3 = function (pos) {\n        this.module(this.numrows - 3, 0, pos, 1);\n        this.module(this.numrows - 2, 0, pos, 2);\n        this.module(this.numrows - 1, 0, pos, 3);\n        this.module(0, this.numcols - 2, pos, 4);\n        this.module(0, this.numcols - 1, pos, 5);\n        this.module(1, this.numcols - 1, pos, 6);\n        this.module(2, this.numcols - 1, pos, 7);\n        this.module(3, this.numcols - 1, pos, 8);\n    };\n    DefaultPlacement.prototype.corner4 = function (pos) {\n        this.module(this.numrows - 1, 0, pos, 1);\n        this.module(this.numrows - 1, this.numcols - 1, pos, 2);\n        this.module(0, this.numcols - 3, pos, 3);\n        this.module(0, this.numcols - 2, pos, 4);\n        this.module(0, this.numcols - 1, pos, 5);\n        this.module(1, this.numcols - 3, pos, 6);\n        this.module(1, this.numcols - 2, pos, 7);\n        this.module(1, this.numcols - 1, pos, 8);\n    };\n    return DefaultPlacement;\n}());\nexport default DefaultPlacement;\n"], "names": [], "mappings": ";;;AAAA;;AACA;;CAEC,GACD,IAAI,mBAAkC;IAClC;;;;;;KAMC,GACD,SAAS,iBAAiB,SAAS,EAAE,OAAO,EAAE,OAAO;QACjD,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,IAAI,GAAG,IAAI,WAAW,UAAU;QACrC,sOAAA,CAAA,UAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,kCAAkC;IACjE;IACA,iBAAiB,SAAS,CAAC,UAAU,GAAG;QACpC,OAAO,IAAI,CAAC,OAAO;IACvB;IACA,iBAAiB,SAAS,CAAC,UAAU,GAAG;QACpC,OAAO,IAAI,CAAC,OAAO;IACvB;IACA,iBAAiB,SAAS,CAAC,OAAO,GAAG;QACjC,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,iBAAiB,SAAS,CAAC,MAAM,GAAG,SAAU,GAAG,EAAE,GAAG;QAClD,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,OAAO,GAAG,IAAI,KAAK;IACnD;IACA,iBAAiB,SAAS,CAAC,MAAM,GAAG,SAAU,GAAG,EAAE,GAAG,EAAE,GAAG;QACvD,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,MAAM,IAAI;IACpD;IACA,iBAAiB,SAAS,CAAC,KAAK,GAAG,SAAU,GAAG,EAAE,GAAG;QACjD,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,OAAO,GAAG,IAAI,KAAK;IACnD;IACA,iBAAiB,SAAS,CAAC,KAAK,GAAG;QAC/B,IAAI,MAAM;QACV,IAAI,MAAM;QACV,IAAI,MAAM;QACV,GAAG;YACC,sEAAsE;YACtE,IAAI,QAAQ,IAAI,CAAC,OAAO,IAAI,QAAQ,GAAG;gBACnC,IAAI,CAAC,OAAO,CAAC;YACjB;YACA,IAAI,QAAQ,IAAI,CAAC,OAAO,GAAG,KAAK,QAAQ,KAAK,IAAI,CAAC,OAAO,GAAG,MAAM,GAAG;gBACjE,IAAI,CAAC,OAAO,CAAC;YACjB;YACA,IAAI,QAAQ,IAAI,CAAC,OAAO,GAAG,KAAK,QAAQ,KAAK,IAAI,CAAC,OAAO,GAAG,MAAM,GAAG;gBACjE,IAAI,CAAC,OAAO,CAAC;YACjB;YACA,IAAI,QAAQ,IAAI,CAAC,OAAO,GAAG,KAAK,QAAQ,KAAK,IAAI,CAAC,OAAO,GAAG,MAAM,GAAG;gBACjE,IAAI,CAAC,OAAO,CAAC;YACjB;YACA,8DAA8D;YAC9D,GAAG;gBACC,IAAI,MAAM,IAAI,CAAC,OAAO,IAAI,OAAO,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,MAAM;oBACxD,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK;gBACxB;gBACA,OAAO;gBACP,OAAO;YACX,QAAS,OAAO,KAAK,MAAM,IAAI,CAAC,OAAO,CAAE;YACzC;YACA,OAAO;YACP,2EAA2E;YAC3E,GAAG;gBACC,IAAI,OAAO,KAAK,MAAM,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,MAAM;oBACxD,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK;gBACxB;gBACA,OAAO;gBACP,OAAO;YACX,QAAS,MAAM,IAAI,CAAC,OAAO,IAAI,OAAO,EAAG;YACzC,OAAO;YACP;QACA,uCAAuC;QAC3C,QAAS,MAAM,IAAI,CAAC,OAAO,IAAI,MAAM,IAAI,CAAC,OAAO,CAAE;QACnD,6EAA6E;QAC7E,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI;YAChD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG;YAChD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG;QACpD;IACJ;IACA,iBAAiB,SAAS,CAAC,MAAM,GAAG,SAAU,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;QAC5D,IAAI,MAAM,GAAG;YACT,OAAO,IAAI,CAAC,OAAO;YACnB,OAAO,IAAK,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI;QACrC;QACA,IAAI,MAAM,GAAG;YACT,OAAO,IAAI,CAAC,OAAO;YACnB,OAAO,IAAK,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI;QACrC;QACA,uBAAuB;QACvB,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;QAClC,KAAK,KAAM,IAAI;QACf,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,MAAM;IAChC;IACA;;;;;;KAMC,GACD,iBAAiB,SAAS,CAAC,IAAI,GAAG,SAAU,GAAG,EAAE,GAAG,EAAE,GAAG;QACrD,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,GAAG,KAAK;QACnC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,GAAG,KAAK;QACnC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,GAAG,KAAK;QACnC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,GAAG,KAAK;QACnC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK,KAAK;QAC/B,IAAI,CAAC,MAAM,CAAC,KAAK,MAAM,GAAG,KAAK;QAC/B,IAAI,CAAC,MAAM,CAAC,KAAK,MAAM,GAAG,KAAK;QAC/B,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,KAAK;IAC/B;IACA,iBAAiB,SAAS,CAAC,OAAO,GAAG,SAAU,GAAG;QAC9C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,GAAG,KAAK;QACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,GAAG,KAAK;QACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,GAAG,KAAK;QACtC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,KAAK;QACtC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,KAAK;QACtC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,KAAK;QACtC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,KAAK;QACtC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,KAAK;IAC1C;IACA,iBAAiB,SAAS,CAAC,OAAO,GAAG,SAAU,GAAG;QAC9C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,GAAG,KAAK;QACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,GAAG,KAAK;QACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,GAAG,KAAK;QACtC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,KAAK;QACtC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,KAAK;QACtC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,KAAK;QACtC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,KAAK;QACtC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,KAAK;IAC1C;IACA,iBAAiB,SAAS,CAAC,OAAO,GAAG,SAAU,GAAG;QAC9C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,GAAG,KAAK;QACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,GAAG,KAAK;QACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,GAAG,KAAK;QACtC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,KAAK;QACtC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,KAAK;QACtC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,KAAK;QACtC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,KAAK;QACtC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,KAAK;IAC1C;IACA,iBAAiB,SAAS,CAAC,OAAO,GAAG,SAAU,GAAG;QAC9C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,GAAG,KAAK;QACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,KAAK;QACrD,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,KAAK;QACtC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,KAAK;QACtC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,KAAK;QACtC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,KAAK;QACtC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,KAAK;QACtC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,GAAG,KAAK;IAC1C;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2149, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/core/datamatrix/encoder/constants.js"], "sourcesContent": ["var _a;\n/**\n * Lookup table which factors to use for which number of error correction codewords.\n * See FACTORS.\n */\nexport var FACTOR_SETS = [\n    5, 7, 10, 11, 12, 14, 18, 20, 24, 28, 36, 42, 48, 56, 62, 68,\n];\n/**\n * Precomputed polynomial factors for ECC 200.\n */\nexport var FACTORS = [\n    [228, 48, 15, 111, 62],\n    [23, 68, 144, 134, 240, 92, 254],\n    [28, 24, 185, 166, 223, 248, 116, 255, 110, 61],\n    [175, 138, 205, 12, 194, 168, 39, 245, 60, 97, 120],\n    [41, 153, 158, 91, 61, 42, 142, 213, 97, 178, 100, 242],\n    [156, 97, 192, 252, 95, 9, 157, 119, 138, 45, 18, 186, 83, 185],\n    [\n        83, 195, 100, 39, 188, 75, 66, 61, 241, 213, 109, 129, 94, 254, 225, 48, 90,\n        188,\n    ],\n    [\n        15, 195, 244, 9, 233, 71, 168, 2, 188, 160, 153, 145, 253, 79, 108, 82, 27,\n        174, 186, 172,\n    ],\n    [\n        52, 190, 88, 205, 109, 39, 176, 21, 155, 197, 251, 223, 155, 21, 5, 172,\n        254, 124, 12, 181, 184, 96, 50, 193,\n    ],\n    [\n        211, 231, 43, 97, 71, 96, 103, 174, 37, 151, 170, 53, 75, 34, 249, 121, 17,\n        138, 110, 213, 141, 136, 120, 151, 233, 168, 93, 255,\n    ],\n    [\n        245, 127, 242, 218, 130, 250, 162, 181, 102, 120, 84, 179, 220, 251, 80,\n        182, 229, 18, 2, 4, 68, 33, 101, 137, 95, 119, 115, 44, 175, 184, 59, 25,\n        225, 98, 81, 112,\n    ],\n    [\n        77, 193, 137, 31, 19, 38, 22, 153, 247, 105, 122, 2, 245, 133, 242, 8, 175,\n        95, 100, 9, 167, 105, 214, 111, 57, 121, 21, 1, 253, 57, 54, 101, 248, 202,\n        69, 50, 150, 177, 226, 5, 9, 5,\n    ],\n    [\n        245, 132, 172, 223, 96, 32, 117, 22, 238, 133, 238, 231, 205, 188, 237, 87,\n        191, 106, 16, 147, 118, 23, 37, 90, 170, 205, 131, 88, 120, 100, 66, 138,\n        186, 240, 82, 44, 176, 87, 187, 147, 160, 175, 69, 213, 92, 253, 225, 19,\n    ],\n    [\n        175, 9, 223, 238, 12, 17, 220, 208, 100, 29, 175, 170, 230, 192, 215, 235,\n        150, 159, 36, 223, 38, 200, 132, 54, 228, 146, 218, 234, 117, 203, 29, 232,\n        144, 238, 22, 150, 201, 117, 62, 207, 164, 13, 137, 245, 127, 67, 247, 28,\n        155, 43, 203, 107, 233, 53, 143, 46,\n    ],\n    [\n        242, 93, 169, 50, 144, 210, 39, 118, 202, 188, 201, 189, 143, 108, 196, 37,\n        185, 112, 134, 230, 245, 63, 197, 190, 250, 106, 185, 221, 175, 64, 114, 71,\n        161, 44, 147, 6, 27, 218, 51, 63, 87, 10, 40, 130, 188, 17, 163, 31, 176,\n        170, 4, 107, 232, 7, 94, 166, 224, 124, 86, 47, 11, 204,\n    ],\n    [\n        220, 228, 173, 89, 251, 149, 159, 56, 89, 33, 147, 244, 154, 36, 73, 127,\n        213, 136, 248, 180, 234, 197, 158, 177, 68, 122, 93, 213, 15, 160, 227, 236,\n        66, 139, 153, 185, 202, 167, 179, 25, 220, 232, 96, 210, 231, 136, 223, 239,\n        181, 241, 59, 52, 172, 25, 49, 232, 211, 189, 64, 54, 108, 153, 132, 63, 96,\n        103, 82, 186,\n    ],\n];\nexport var /*final*/ MODULO_VALUE = 0x12d;\nvar static_LOG = function (LOG, ALOG) {\n    var p = 1;\n    for (var i = 0; i < 255; i++) {\n        ALOG[i] = p;\n        LOG[p] = i;\n        p *= 2;\n        if (p >= 256) {\n            p ^= MODULO_VALUE;\n        }\n    }\n    return {\n        LOG: LOG,\n        ALOG: ALOG,\n    };\n};\nexport var LOG = (_a = static_LOG([], []), _a.LOG), ALOG = _a.ALOG;\nexport var SymbolShapeHint;\n(function (SymbolShapeHint) {\n    SymbolShapeHint[SymbolShapeHint[\"FORCE_NONE\"] = 0] = \"FORCE_NONE\";\n    SymbolShapeHint[SymbolShapeHint[\"FORCE_SQUARE\"] = 1] = \"FORCE_SQUARE\";\n    SymbolShapeHint[SymbolShapeHint[\"FORCE_RECTANGLE\"] = 2] = \"FORCE_RECTANGLE\";\n})(SymbolShapeHint || (SymbolShapeHint = {}));\n/**\n * Padding character\n */\nexport var PAD = 129;\n/**\n * mode latch to C40 encodation mode\n */\nexport var LATCH_TO_C40 = 230;\n/**\n * mode latch to Base 256 encodation mode\n */\nexport var LATCH_TO_BASE256 = 231;\n/**\n * FNC1 Codeword\n */\n// private static FNC1 = 232;\n/**\n * Structured Append Codeword\n */\n// private static STRUCTURED_APPEND = 233;\n/**\n * Reader Programming\n */\n// private static READER_PROGRAMMING = 234;\n/**\n * Upper Shift\n */\nexport var UPPER_SHIFT = 235;\n/**\n * 05 Macro\n */\nexport var MACRO_05 = 236;\n/**\n * 06 Macro\n */\nexport var MACRO_06 = 237;\n/**\n * mode latch to ANSI X.12 encodation mode\n */\nexport var LATCH_TO_ANSIX12 = 238;\n/**\n * mode latch to Text encodation mode\n */\nexport var LATCH_TO_TEXT = 239;\n/**\n * mode latch to EDIFACT encodation mode\n */\nexport var LATCH_TO_EDIFACT = 240;\n/**\n * ECI character (Extended Channel Interpretation)\n */\n// private export const ECI = 241;\n/**\n * Unlatch from C40 encodation\n */\nexport var C40_UNLATCH = 254;\n/**\n * Unlatch from X12 encodation\n */\nexport var X12_UNLATCH = 254;\n/**\n * 05 Macro header\n */\nexport var MACRO_05_HEADER = '[)>\\u001E05\\u001D';\n/**\n * 06 Macro header\n */\nexport var MACRO_06_HEADER = '[)>\\u001E06\\u001D';\n/**\n * Macro trailer\n */\nexport var MACRO_TRAILER = '\\u001E\\u0004';\nexport var ASCII_ENCODATION = 0;\nexport var C40_ENCODATION = 1;\nexport var TEXT_ENCODATION = 2;\nexport var X12_ENCODATION = 3;\nexport var EDIFACT_ENCODATION = 4;\nexport var BASE256_ENCODATION = 5;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAI;AAKG,IAAI,cAAc;IACrB;IAAG;IAAG;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;CAC7D;AAIM,IAAI,UAAU;IACjB;QAAC;QAAK;QAAI;QAAI;QAAK;KAAG;IACtB;QAAC;QAAI;QAAI;QAAK;QAAK;QAAK;QAAI;KAAI;IAChC;QAAC;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;KAAG;IAC/C;QAAC;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAI;QAAI;KAAI;IACnD;QAAC;QAAI;QAAK;QAAK;QAAI;QAAI;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;KAAI;IACvD;QAAC;QAAK;QAAI;QAAK;QAAK;QAAI;QAAG;QAAK;QAAK;QAAK;QAAI;QAAI;QAAK;QAAI;KAAI;IAC/D;QACI;QAAI;QAAK;QAAK;QAAI;QAAK;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QACzE;KACH;IACD;QACI;QAAI;QAAK;QAAK;QAAG;QAAK;QAAI;QAAK;QAAG;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAI;QACxE;QAAK;QAAK;KACb;IACD;QACI;QAAI;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAG;QACpE;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAI;KACnC;IACD;QACI;QAAK;QAAK;QAAI;QAAI;QAAI;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAI;QAAI;QAAK;QAAK;QACxE;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;KACpD;IACD;QACI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QACrE;QAAK;QAAK;QAAI;QAAG;QAAG;QAAI;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QACtE;QAAK;QAAI;QAAI;KAChB;IACD;QACI;QAAI;QAAK;QAAK;QAAI;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAG;QAAK;QAAK;QAAK;QAAG;QACvE;QAAI;QAAK;QAAG;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAI;QAAG;QAAK;QAAI;QAAI;QAAK;QAAK;QACvE;QAAI;QAAI;QAAK;QAAK;QAAK;QAAG;QAAG;KAChC;IACD;QACI;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QACxE;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QACrE;QAAK;QAAK;QAAI;QAAI;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAI;QAAK;QAAK;KACzE;IACD;QACI;QAAK;QAAG;QAAK;QAAK;QAAI;QAAI;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QACtE;QAAK;QAAK;QAAI;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QACvE;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAI;QAAK;QACvE;QAAK;QAAI;QAAK;QAAK;QAAK;QAAI;QAAK;KACpC;IACD;QACI;QAAK;QAAI;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QACxE;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QACzE;QAAK;QAAI;QAAK;QAAG;QAAI;QAAK;QAAI;QAAI;QAAI;QAAI;QAAI;QAAK;QAAK;QAAI;QAAK;QAAI;QACrE;QAAK;QAAG;QAAK;QAAK;QAAG;QAAI;QAAK;QAAK;QAAK;QAAI;QAAI;QAAI;KACvD;IACD;QACI;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAI;QAAI;QACrE;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAI;QAAK;QAAI;QAAK;QAAK;QACxE;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QACxE;QAAK;QAAK;QAAI;QAAI;QAAK;QAAI;QAAI;QAAK;QAAK;QAAK;QAAI;QAAI;QAAK;QAAK;QAAK;QAAI;QACzE;QAAK;QAAI;KACZ;CACJ;AACM,IAAI,OAAO,GAAG,eAAe;AACpC,IAAI,aAAa,SAAU,GAAG,EAAE,IAAI;IAChC,IAAI,IAAI;IACR,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;QAC1B,IAAI,CAAC,EAAE,GAAG;QACV,GAAG,CAAC,EAAE,GAAG;QACT,KAAK;QACL,IAAI,KAAK,KAAK;YACV,KAAK;QACT;IACJ;IACA,OAAO;QACH,KAAK;QACL,MAAM;IACV;AACJ;AACO,IAAI,MAAM,CAAC,KAAK,WAAW,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,OAAO,GAAG,IAAI;AAC3D,IAAI;AACX,CAAC,SAAU,eAAe;IACtB,eAAe,CAAC,eAAe,CAAC,aAAa,GAAG,EAAE,GAAG;IACrD,eAAe,CAAC,eAAe,CAAC,eAAe,GAAG,EAAE,GAAG;IACvD,eAAe,CAAC,eAAe,CAAC,kBAAkB,GAAG,EAAE,GAAG;AAC9D,CAAC,EAAE,mBAAmB,CAAC,kBAAkB,CAAC,CAAC;AAIpC,IAAI,MAAM;AAIV,IAAI,eAAe;AAInB,IAAI,mBAAmB;AAgBvB,IAAI,cAAc;AAIlB,IAAI,WAAW;AAIf,IAAI,WAAW;AAIf,IAAI,mBAAmB;AAIvB,IAAI,gBAAgB;AAIpB,IAAI,mBAAmB;AAQvB,IAAI,cAAc;AAIlB,IAAI,cAAc;AAIlB,IAAI,kBAAkB;AAItB,IAAI,kBAAkB;AAItB,IAAI,gBAAgB;AACpB,IAAI,mBAAmB;AACvB,IAAI,iBAAiB;AACrB,IAAI,kBAAkB;AACtB,IAAI,iBAAiB;AACrB,IAAI,qBAAqB;AACzB,IAAI,qBAAqB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2738, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/core/datamatrix/encoder/ErrorCorrection.js"], "sourcesContent": ["import StringBuilder from '../../util/StringBuilder';\nimport { ALOG, FACTORS, FACTOR_SETS, LOG } from './constants';\n/**\n * Error Correction Code for ECC200.\n */\nvar ErrorCorrection = /** @class */ (function () {\n    function ErrorCorrection() {\n    }\n    /**\n     * Creates the ECC200 error correction for an encoded message.\n     *\n     * @param codewords  the codewords\n     * @param symbolInfo information about the symbol to be encoded\n     * @return the codewords with interleaved error correction.\n     */\n    ErrorCorrection.encodeECC200 = function (codewords, symbolInfo) {\n        if (codewords.length !== symbolInfo.getDataCapacity()) {\n            throw new Error('The number of codewords does not match the selected symbol');\n        }\n        var sb = new StringBuilder();\n        sb.append(codewords);\n        var blockCount = symbolInfo.getInterleavedBlockCount();\n        if (blockCount === 1) {\n            var ecc = this.createECCBlock(codewords, symbolInfo.getErrorCodewords());\n            sb.append(ecc);\n        }\n        else {\n            // sb.setLength(sb.capacity());\n            var dataSizes = [];\n            var errorSizes = [];\n            for (var i = 0; i < blockCount; i++) {\n                dataSizes[i] = symbolInfo.getDataLengthForInterleavedBlock(i + 1);\n                errorSizes[i] = symbolInfo.getErrorLengthForInterleavedBlock(i + 1);\n            }\n            for (var block = 0; block < blockCount; block++) {\n                var temp = new StringBuilder();\n                for (var d = block; d < symbolInfo.getDataCapacity(); d += blockCount) {\n                    temp.append(codewords.charAt(d));\n                }\n                var ecc = this.createECCBlock(temp.toString(), errorSizes[block]);\n                var pos = 0;\n                for (var e = block; e < errorSizes[block] * blockCount; e += blockCount) {\n                    sb.setCharAt(symbolInfo.getDataCapacity() + e, ecc.charAt(pos++));\n                }\n            }\n        }\n        return sb.toString();\n    };\n    ErrorCorrection.createECCBlock = function (codewords, numECWords) {\n        var table = -1;\n        for (var i = 0; i < FACTOR_SETS.length; i++) {\n            if (FACTOR_SETS[i] === numECWords) {\n                table = i;\n                break;\n            }\n        }\n        if (table < 0) {\n            throw new Error('Illegal number of error correction codewords specified: ' + numECWords);\n        }\n        var poly = FACTORS[table];\n        var ecc = [];\n        for (var i = 0; i < numECWords; i++) {\n            ecc[i] = 0;\n        }\n        for (var i = 0; i < codewords.length; i++) {\n            var m = ecc[numECWords - 1] ^ codewords.charAt(i).charCodeAt(0);\n            for (var k = numECWords - 1; k > 0; k--) {\n                if (m !== 0 && poly[k] !== 0) {\n                    ecc[k] = ecc[k - 1] ^ ALOG[(LOG[m] + LOG[poly[k]]) % 255];\n                }\n                else {\n                    ecc[k] = ecc[k - 1];\n                }\n            }\n            if (m !== 0 && poly[0] !== 0) {\n                ecc[0] = ALOG[(LOG[m] + LOG[poly[0]]) % 255];\n            }\n            else {\n                ecc[0] = 0;\n            }\n        }\n        var eccReversed = [];\n        for (var i = 0; i < numECWords; i++) {\n            eccReversed[i] = ecc[numECWords - i - 1];\n        }\n        return eccReversed.map(function (c) { return String.fromCharCode(c); }).join('');\n    };\n    return ErrorCorrection;\n}());\nexport default ErrorCorrection;\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACA;;CAEC,GACD,IAAI,kBAAiC;IACjC,SAAS,mBACT;IACA;;;;;;KAMC,GACD,gBAAgB,YAAY,GAAG,SAAU,SAAS,EAAE,UAAU;QAC1D,IAAI,UAAU,MAAM,KAAK,WAAW,eAAe,IAAI;YACnD,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,KAAK,IAAI,6OAAA,CAAA,UAAa;QAC1B,GAAG,MAAM,CAAC;QACV,IAAI,aAAa,WAAW,wBAAwB;QACpD,IAAI,eAAe,GAAG;YAClB,IAAI,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,WAAW,iBAAiB;YACrE,GAAG,MAAM,CAAC;QACd,OACK;YACD,+BAA+B;YAC/B,IAAI,YAAY,EAAE;YAClB,IAAI,aAAa,EAAE;YACnB,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;gBACjC,SAAS,CAAC,EAAE,GAAG,WAAW,gCAAgC,CAAC,IAAI;gBAC/D,UAAU,CAAC,EAAE,GAAG,WAAW,iCAAiC,CAAC,IAAI;YACrE;YACA,IAAK,IAAI,QAAQ,GAAG,QAAQ,YAAY,QAAS;gBAC7C,IAAI,OAAO,IAAI,6OAAA,CAAA,UAAa;gBAC5B,IAAK,IAAI,IAAI,OAAO,IAAI,WAAW,eAAe,IAAI,KAAK,WAAY;oBACnE,KAAK,MAAM,CAAC,UAAU,MAAM,CAAC;gBACjC;gBACA,IAAI,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,QAAQ,IAAI,UAAU,CAAC,MAAM;gBAChE,IAAI,MAAM;gBACV,IAAK,IAAI,IAAI,OAAO,IAAI,UAAU,CAAC,MAAM,GAAG,YAAY,KAAK,WAAY;oBACrE,GAAG,SAAS,CAAC,WAAW,eAAe,KAAK,GAAG,IAAI,MAAM,CAAC;gBAC9D;YACJ;QACJ;QACA,OAAO,GAAG,QAAQ;IACtB;IACA,gBAAgB,cAAc,GAAG,SAAU,SAAS,EAAE,UAAU;QAC5D,IAAI,QAAQ,CAAC;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,0PAAA,CAAA,cAAW,CAAC,MAAM,EAAE,IAAK;YACzC,IAAI,0PAAA,CAAA,cAAW,CAAC,EAAE,KAAK,YAAY;gBAC/B,QAAQ;gBACR;YACJ;QACJ;QACA,IAAI,QAAQ,GAAG;YACX,MAAM,IAAI,MAAM,6DAA6D;QACjF;QACA,IAAI,OAAO,0PAAA,CAAA,UAAO,CAAC,MAAM;QACzB,IAAI,MAAM,EAAE;QACZ,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;YACjC,GAAG,CAAC,EAAE,GAAG;QACb;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;YACvC,IAAI,IAAI,GAAG,CAAC,aAAa,EAAE,GAAG,UAAU,MAAM,CAAC,GAAG,UAAU,CAAC;YAC7D,IAAK,IAAI,IAAI,aAAa,GAAG,IAAI,GAAG,IAAK;gBACrC,IAAI,MAAM,KAAK,IAAI,CAAC,EAAE,KAAK,GAAG;oBAC1B,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,0PAAA,CAAA,OAAI,CAAC,CAAC,0PAAA,CAAA,MAAG,CAAC,EAAE,GAAG,0PAAA,CAAA,MAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI;gBAC7D,OACK;oBACD,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE;gBACvB;YACJ;YACA,IAAI,MAAM,KAAK,IAAI,CAAC,EAAE,KAAK,GAAG;gBAC1B,GAAG,CAAC,EAAE,GAAG,0PAAA,CAAA,OAAI,CAAC,CAAC,0PAAA,CAAA,MAAG,CAAC,EAAE,GAAG,0PAAA,CAAA,MAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI;YAChD,OACK;gBACD,GAAG,CAAC,EAAE,GAAG;YACb;QACJ;QACA,IAAI,cAAc,EAAE;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;YACjC,WAAW,CAAC,EAAE,GAAG,GAAG,CAAC,aAAa,IAAI,EAAE;QAC5C;QACA,OAAO,YAAY,GAAG,CAAC,SAAU,CAAC;YAAI,OAAO,OAAO,YAAY,CAAC;QAAI,GAAG,IAAI,CAAC;IACjF;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2833, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/core/datamatrix/encoder/ASCIIEncoder.js"], "sourcesContent": ["import { ASCII_ENCODATION, BASE256_ENCODATION, C40_ENCODATION, EDIFACT_ENCODATION, LATCH_TO_ANSIX12, LATCH_TO_BASE256, LATCH_TO_C40, LATCH_TO_EDIFACT, LATCH_TO_TEXT, TEXT_ENCODATION, UPPER_SHIFT, X12_ENCODATION, } from './constants';\n// tslint:disable-next-line:no-circular-imports\nimport HighLevelEncoder from './HighLevelEncoder';\nvar ASCIIEncoder = /** @class */ (function () {\n    function ASCIIEncoder() {\n    }\n    ASCIIEncoder.prototype.getEncodingMode = function () {\n        return ASCII_ENCODATION;\n    };\n    ASCIIEncoder.prototype.encode = function (context) {\n        // step B\n        var n = HighLevelEncoder.determineConsecutiveDigitCount(context.getMessage(), context.pos);\n        if (n >= 2) {\n            context.writeCodeword(this.encodeASCIIDigits(context.getMessage().charCodeAt(context.pos), context.getMessage().charCodeAt(context.pos + 1)));\n            context.pos += 2;\n        }\n        else {\n            var c = context.getCurrentChar();\n            var newMode = HighLevelEncoder.lookAheadTest(context.getMessage(), context.pos, this.getEncodingMode());\n            if (newMode !== this.getEncodingMode()) {\n                switch (newMode) {\n                    case BASE256_ENCODATION:\n                        context.writeCodeword(LATCH_TO_BASE256);\n                        context.signalEncoderChange(BASE256_ENCODATION);\n                        return;\n                    case C40_ENCODATION:\n                        context.writeCodeword(LATCH_TO_C40);\n                        context.signalEncoderChange(C40_ENCODATION);\n                        return;\n                    case X12_ENCODATION:\n                        context.writeCodeword(LATCH_TO_ANSIX12);\n                        context.signalEncoderChange(X12_ENCODATION);\n                        break;\n                    case TEXT_ENCODATION:\n                        context.writeCodeword(LATCH_TO_TEXT);\n                        context.signalEncoderChange(TEXT_ENCODATION);\n                        break;\n                    case EDIFACT_ENCODATION:\n                        context.writeCodeword(LATCH_TO_EDIFACT);\n                        context.signalEncoderChange(EDIFACT_ENCODATION);\n                        break;\n                    default:\n                        throw new Error('Illegal mode: ' + newMode);\n                }\n            }\n            else if (HighLevelEncoder.isExtendedASCII(c)) {\n                context.writeCodeword(UPPER_SHIFT);\n                context.writeCodeword(c - 128 + 1);\n                context.pos++;\n            }\n            else {\n                context.writeCodeword(c + 1);\n                context.pos++;\n            }\n        }\n    };\n    ASCIIEncoder.prototype.encodeASCIIDigits = function (digit1, digit2) {\n        if (HighLevelEncoder.isDigit(digit1) && HighLevelEncoder.isDigit(digit2)) {\n            var num = (digit1 - 48) * 10 + (digit2 - 48);\n            return num + 130;\n        }\n        throw new Error('not digits: ' + digit1 + digit2);\n    };\n    return ASCIIEncoder;\n}());\nexport { ASCIIEncoder };\n"], "names": [], "mappings": ";;;AAAA;AACA,+CAA+C;AAC/C;;;AACA,IAAI,eAA8B;IAC9B,SAAS,gBACT;IACA,aAAa,SAAS,CAAC,eAAe,GAAG;QACrC,OAAO,0PAAA,CAAA,mBAAgB;IAC3B;IACA,aAAa,SAAS,CAAC,MAAM,GAAG,SAAU,OAAO;QAC7C,SAAS;QACT,IAAI,IAAI,iQAAA,CAAA,UAAgB,CAAC,8BAA8B,CAAC,QAAQ,UAAU,IAAI,QAAQ,GAAG;QACzF,IAAI,KAAK,GAAG;YACR,QAAQ,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,UAAU,GAAG,UAAU,CAAC,QAAQ,GAAG,GAAG,QAAQ,UAAU,GAAG,UAAU,CAAC,QAAQ,GAAG,GAAG;YACzI,QAAQ,GAAG,IAAI;QACnB,OACK;YACD,IAAI,IAAI,QAAQ,cAAc;YAC9B,IAAI,UAAU,iQAAA,CAAA,UAAgB,CAAC,aAAa,CAAC,QAAQ,UAAU,IAAI,QAAQ,GAAG,EAAE,IAAI,CAAC,eAAe;YACpG,IAAI,YAAY,IAAI,CAAC,eAAe,IAAI;gBACpC,OAAQ;oBACJ,KAAK,0PAAA,CAAA,qBAAkB;wBACnB,QAAQ,aAAa,CAAC,0PAAA,CAAA,mBAAgB;wBACtC,QAAQ,mBAAmB,CAAC,0PAAA,CAAA,qBAAkB;wBAC9C;oBACJ,KAAK,0PAAA,CAAA,iBAAc;wBACf,QAAQ,aAAa,CAAC,0PAAA,CAAA,eAAY;wBAClC,QAAQ,mBAAmB,CAAC,0PAAA,CAAA,iBAAc;wBAC1C;oBACJ,KAAK,0PAAA,CAAA,iBAAc;wBACf,QAAQ,aAAa,CAAC,0PAAA,CAAA,mBAAgB;wBACtC,QAAQ,mBAAmB,CAAC,0PAAA,CAAA,iBAAc;wBAC1C;oBACJ,KAAK,0PAAA,CAAA,kBAAe;wBAChB,QAAQ,aAAa,CAAC,0PAAA,CAAA,gBAAa;wBACnC,QAAQ,mBAAmB,CAAC,0PAAA,CAAA,kBAAe;wBAC3C;oBACJ,KAAK,0PAAA,CAAA,qBAAkB;wBACnB,QAAQ,aAAa,CAAC,0PAAA,CAAA,mBAAgB;wBACtC,QAAQ,mBAAmB,CAAC,0PAAA,CAAA,qBAAkB;wBAC9C;oBACJ;wBACI,MAAM,IAAI,MAAM,mBAAmB;gBAC3C;YACJ,OACK,IAAI,iQAAA,CAAA,UAAgB,CAAC,eAAe,CAAC,IAAI;gBAC1C,QAAQ,aAAa,CAAC,0PAAA,CAAA,cAAW;gBACjC,QAAQ,aAAa,CAAC,IAAI,MAAM;gBAChC,QAAQ,GAAG;YACf,OACK;gBACD,QAAQ,aAAa,CAAC,IAAI;gBAC1B,QAAQ,GAAG;YACf;QACJ;IACJ;IACA,aAAa,SAAS,CAAC,iBAAiB,GAAG,SAAU,MAAM,EAAE,MAAM;QAC/D,IAAI,iQAAA,CAAA,UAAgB,CAAC,OAAO,CAAC,WAAW,iQAAA,CAAA,UAAgB,CAAC,OAAO,CAAC,SAAS;YACtE,IAAI,MAAM,CAAC,SAAS,EAAE,IAAI,KAAK,CAAC,SAAS,EAAE;YAC3C,OAAO,MAAM;QACjB;QACA,MAAM,IAAI,MAAM,iBAAiB,SAAS;IAC9C;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2904, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/core/datamatrix/encoder/Base256Encoder.js"], "sourcesContent": ["import StringUtils from '../../common/StringUtils';\nimport StringBuilder from '../../util/StringBuilder';\nimport HighLevelEncoder from './HighLevelEncoder';\nimport { BASE256_ENCODATION, ASCII_ENCODATION } from './constants';\nvar Base256Encoder = /** @class */ (function () {\n    function Base256Encoder() {\n    }\n    Base256Encoder.prototype.getEncodingMode = function () {\n        return BASE256_ENCODATION;\n    };\n    Base256Encoder.prototype.encode = function (context) {\n        var buffer = new StringBuilder();\n        buffer.append(0); // Initialize length field\n        while (context.hasMoreCharacters()) {\n            var c = context.getCurrentChar();\n            buffer.append(c);\n            context.pos++;\n            var newMode = HighLevelEncoder.lookAheadTest(context.getMessage(), context.pos, this.getEncodingMode());\n            if (newMode !== this.getEncodingMode()) {\n                // Return to ASCII encodation, which will actually handle latch to new mode\n                context.signalEncoderChange(ASCII_ENCODATION);\n                break;\n            }\n        }\n        var dataCount = buffer.length() - 1;\n        var lengthFieldSize = 1;\n        var currentSize = context.getCodewordCount() + dataCount + lengthFieldSize;\n        context.updateSymbolInfo(currentSize);\n        var mustPad = context.getSymbolInfo().getDataCapacity() - currentSize > 0;\n        if (context.hasMoreCharacters() || mustPad) {\n            if (dataCount <= 249) {\n                buffer.setCharAt(0, StringUtils.getCharAt(dataCount));\n            }\n            else if (dataCount <= 1555) {\n                buffer.setCharAt(0, StringUtils.getCharAt(Math.floor(dataCount / 250) + 249));\n                buffer.insert(1, StringUtils.getCharAt(dataCount % 250));\n            }\n            else {\n                throw new Error('Message length not in valid ranges: ' + dataCount);\n            }\n        }\n        for (var i = 0, c = buffer.length(); i < c; i++) {\n            context.writeCodeword(this.randomize255State(buffer.charAt(i).charCodeAt(0), context.getCodewordCount() + 1));\n        }\n    };\n    Base256Encoder.prototype.randomize255State = function (ch, codewordPosition) {\n        var pseudoRandom = ((149 * codewordPosition) % 255) + 1;\n        var tempVariable = ch + pseudoRandom;\n        if (tempVariable <= 255) {\n            return tempVariable;\n        }\n        else {\n            return tempVariable - 256;\n        }\n    };\n    return Base256Encoder;\n}());\nexport { Base256Encoder };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACA,IAAI,iBAAgC;IAChC,SAAS,kBACT;IACA,eAAe,SAAS,CAAC,eAAe,GAAG;QACvC,OAAO,0PAAA,CAAA,qBAAkB;IAC7B;IACA,eAAe,SAAS,CAAC,MAAM,GAAG,SAAU,OAAO;QAC/C,IAAI,SAAS,IAAI,6OAAA,CAAA,UAAa;QAC9B,OAAO,MAAM,CAAC,IAAI,0BAA0B;QAC5C,MAAO,QAAQ,iBAAiB,GAAI;YAChC,IAAI,IAAI,QAAQ,cAAc;YAC9B,OAAO,MAAM,CAAC;YACd,QAAQ,GAAG;YACX,IAAI,UAAU,iQAAA,CAAA,UAAgB,CAAC,aAAa,CAAC,QAAQ,UAAU,IAAI,QAAQ,GAAG,EAAE,IAAI,CAAC,eAAe;YACpG,IAAI,YAAY,IAAI,CAAC,eAAe,IAAI;gBACpC,2EAA2E;gBAC3E,QAAQ,mBAAmB,CAAC,0PAAA,CAAA,mBAAgB;gBAC5C;YACJ;QACJ;QACA,IAAI,YAAY,OAAO,MAAM,KAAK;QAClC,IAAI,kBAAkB;QACtB,IAAI,cAAc,QAAQ,gBAAgB,KAAK,YAAY;QAC3D,QAAQ,gBAAgB,CAAC;QACzB,IAAI,UAAU,QAAQ,aAAa,GAAG,eAAe,KAAK,cAAc;QACxE,IAAI,QAAQ,iBAAiB,MAAM,SAAS;YACxC,IAAI,aAAa,KAAK;gBAClB,OAAO,SAAS,CAAC,GAAG,6OAAA,CAAA,UAAW,CAAC,SAAS,CAAC;YAC9C,OACK,IAAI,aAAa,MAAM;gBACxB,OAAO,SAAS,CAAC,GAAG,6OAAA,CAAA,UAAW,CAAC,SAAS,CAAC,KAAK,KAAK,CAAC,YAAY,OAAO;gBACxE,OAAO,MAAM,CAAC,GAAG,6OAAA,CAAA,UAAW,CAAC,SAAS,CAAC,YAAY;YACvD,OACK;gBACD,MAAM,IAAI,MAAM,yCAAyC;YAC7D;QACJ;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,IAAI,IAAI,GAAG,IAAK;YAC7C,QAAQ,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,MAAM,CAAC,GAAG,UAAU,CAAC,IAAI,QAAQ,gBAAgB,KAAK;QAC9G;IACJ;IACA,eAAe,SAAS,CAAC,iBAAiB,GAAG,SAAU,EAAE,EAAE,gBAAgB;QACvE,IAAI,eAAe,AAAE,MAAM,mBAAoB,MAAO;QACtD,IAAI,eAAe,KAAK;QACxB,IAAI,gBAAgB,KAAK;YACrB,OAAO;QACX,OACK;YACD,OAAO,eAAe;QAC1B;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2969, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/core/datamatrix/encoder/C40Encoder.js"], "sourcesContent": ["import StringBuilder from '../../util/StringBuilder';\nimport HighLevelEncoder from './HighLevelEncoder';\nimport { C40_ENCODATION, LATCH_TO_C40, ASCII_ENCODATION, C40_UNLATCH, } from './constants';\nvar C40Encoder = /** @class */ (function () {\n    function C40Encoder() {\n    }\n    C40Encoder.prototype.getEncodingMode = function () {\n        return C40_ENCODATION;\n    };\n    C40Encoder.prototype.encodeMaximal = function (context) {\n        var buffer = new StringBuilder();\n        var lastCharSize = 0;\n        var backtrackStartPosition = context.pos;\n        var backtrackBufferLength = 0;\n        while (context.hasMoreCharacters()) {\n            var c = context.getCurrentChar();\n            context.pos++;\n            lastCharSize = this.encodeChar(c, buffer);\n            if (buffer.length() % 3 === 0) {\n                backtrackStartPosition = context.pos;\n                backtrackBufferLength = buffer.length();\n            }\n        }\n        if (backtrackBufferLength !== buffer.length()) {\n            var unwritten = Math.floor((buffer.length() / 3) * 2);\n            var curCodewordCount = Math.floor(context.getCodewordCount() + unwritten + 1); // +1 for the latch to C40\n            context.updateSymbolInfo(curCodewordCount);\n            var available = context.getSymbolInfo().getDataCapacity() - curCodewordCount;\n            var rest = Math.floor(buffer.length() % 3);\n            if ((rest === 2 && available !== 2) ||\n                (rest === 1 && (lastCharSize > 3 || available !== 1))) {\n                // buffer.setLength(backtrackBufferLength);\n                context.pos = backtrackStartPosition;\n            }\n        }\n        if (buffer.length() > 0) {\n            context.writeCodeword(LATCH_TO_C40);\n        }\n        this.handleEOD(context, buffer);\n    };\n    C40Encoder.prototype.encode = function (context) {\n        // step C\n        var buffer = new StringBuilder();\n        while (context.hasMoreCharacters()) {\n            var c = context.getCurrentChar();\n            context.pos++;\n            var lastCharSize = this.encodeChar(c, buffer);\n            var unwritten = Math.floor(buffer.length() / 3) * 2;\n            var curCodewordCount = context.getCodewordCount() + unwritten;\n            context.updateSymbolInfo(curCodewordCount);\n            var available = context.getSymbolInfo().getDataCapacity() - curCodewordCount;\n            if (!context.hasMoreCharacters()) {\n                // Avoid having a single C40 value in the last triplet\n                var removed = new StringBuilder();\n                if (buffer.length() % 3 === 2 && available !== 2) {\n                    lastCharSize = this.backtrackOneCharacter(context, buffer, removed, lastCharSize);\n                }\n                while (buffer.length() % 3 === 1 &&\n                    (lastCharSize > 3 || available !== 1)) {\n                    lastCharSize = this.backtrackOneCharacter(context, buffer, removed, lastCharSize);\n                }\n                break;\n            }\n            var count = buffer.length();\n            if (count % 3 === 0) {\n                var newMode = HighLevelEncoder.lookAheadTest(context.getMessage(), context.pos, this.getEncodingMode());\n                if (newMode !== this.getEncodingMode()) {\n                    // Return to ASCII encodation, which will actually handle latch to new mode\n                    context.signalEncoderChange(ASCII_ENCODATION);\n                    break;\n                }\n            }\n        }\n        this.handleEOD(context, buffer);\n    };\n    C40Encoder.prototype.backtrackOneCharacter = function (context, buffer, removed, lastCharSize) {\n        var count = buffer.length();\n        var test = buffer.toString().substring(0, count - lastCharSize);\n        buffer.setLengthToZero();\n        buffer.append(test);\n        // buffer.delete(count - lastCharSize, count);\n        /*for (let i = count - lastCharSize; i < count; i++) {\n          buffer.deleteCharAt(i);\n        }*/\n        context.pos--;\n        var c = context.getCurrentChar();\n        lastCharSize = this.encodeChar(c, removed);\n        context.resetSymbolInfo(); // Deal with possible reduction in symbol size\n        return lastCharSize;\n    };\n    C40Encoder.prototype.writeNextTriplet = function (context, buffer) {\n        context.writeCodewords(this.encodeToCodewords(buffer.toString()));\n        var test = buffer.toString().substring(3);\n        buffer.setLengthToZero();\n        buffer.append(test);\n        // buffer.delete(0, 3);\n        /*for (let i = 0; i < 3; i++) {\n          buffer.deleteCharAt(i);\n        }*/\n    };\n    /**\n     * Handle \"end of data\" situations\n     *\n     * @param context the encoder context\n     * @param buffer  the buffer with the remaining encoded characters\n     */\n    C40Encoder.prototype.handleEOD = function (context, buffer) {\n        var unwritten = Math.floor((buffer.length() / 3) * 2);\n        var rest = buffer.length() % 3;\n        var curCodewordCount = context.getCodewordCount() + unwritten;\n        context.updateSymbolInfo(curCodewordCount);\n        var available = context.getSymbolInfo().getDataCapacity() - curCodewordCount;\n        if (rest === 2) {\n            buffer.append('\\0'); // Shift 1\n            while (buffer.length() >= 3) {\n                this.writeNextTriplet(context, buffer);\n            }\n            if (context.hasMoreCharacters()) {\n                context.writeCodeword(C40_UNLATCH);\n            }\n        }\n        else if (available === 1 && rest === 1) {\n            while (buffer.length() >= 3) {\n                this.writeNextTriplet(context, buffer);\n            }\n            if (context.hasMoreCharacters()) {\n                context.writeCodeword(C40_UNLATCH);\n            }\n            // else no unlatch\n            context.pos--;\n        }\n        else if (rest === 0) {\n            while (buffer.length() >= 3) {\n                this.writeNextTriplet(context, buffer);\n            }\n            if (available > 0 || context.hasMoreCharacters()) {\n                context.writeCodeword(C40_UNLATCH);\n            }\n        }\n        else {\n            throw new Error('Unexpected case. Please report!');\n        }\n        context.signalEncoderChange(ASCII_ENCODATION);\n    };\n    C40Encoder.prototype.encodeChar = function (c, sb) {\n        if (c === ' '.charCodeAt(0)) {\n            sb.append(3);\n            return 1;\n        }\n        if (c >= '0'.charCodeAt(0) && c <= '9'.charCodeAt(0)) {\n            sb.append(c - 48 + 4);\n            return 1;\n        }\n        if (c >= 'A'.charCodeAt(0) && c <= 'Z'.charCodeAt(0)) {\n            sb.append(c - 65 + 14);\n            return 1;\n        }\n        if (c < ' '.charCodeAt(0)) {\n            sb.append(0); // Shift 1 Set\n            sb.append(c);\n            return 2;\n        }\n        if (c <= '/'.charCodeAt(0)) {\n            sb.append(1); // Shift 2 Set\n            sb.append(c - 33);\n            return 2;\n        }\n        if (c <= '@'.charCodeAt(0)) {\n            sb.append(1); // Shift 2 Set\n            sb.append(c - 58 + 15);\n            return 2;\n        }\n        if (c <= '_'.charCodeAt(0)) {\n            sb.append(1); // Shift 2 Set\n            sb.append(c - 91 + 22);\n            return 2;\n        }\n        if (c <= 127) {\n            sb.append(2); // Shift 3 Set\n            sb.append(c - 96);\n            return 2;\n        }\n        sb.append(1 + \"\\u001E\"); // Shift 2, Upper Shift\n        var len = 2;\n        len += this.encodeChar(c - 128, sb);\n        return len;\n    };\n    C40Encoder.prototype.encodeToCodewords = function (sb) {\n        var v = 1600 * sb.charCodeAt(0) + 40 * sb.charCodeAt(1) + sb.charCodeAt(2) + 1;\n        var cw1 = v / 256;\n        var cw2 = v % 256;\n        var result = new StringBuilder();\n        result.append(cw1);\n        result.append(cw2);\n        return result.toString();\n    };\n    return C40Encoder;\n}());\nexport { C40Encoder };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACA,IAAI,aAA4B;IAC5B,SAAS,cACT;IACA,WAAW,SAAS,CAAC,eAAe,GAAG;QACnC,OAAO,0PAAA,CAAA,iBAAc;IACzB;IACA,WAAW,SAAS,CAAC,aAAa,GAAG,SAAU,OAAO;QAClD,IAAI,SAAS,IAAI,6OAAA,CAAA,UAAa;QAC9B,IAAI,eAAe;QACnB,IAAI,yBAAyB,QAAQ,GAAG;QACxC,IAAI,wBAAwB;QAC5B,MAAO,QAAQ,iBAAiB,GAAI;YAChC,IAAI,IAAI,QAAQ,cAAc;YAC9B,QAAQ,GAAG;YACX,eAAe,IAAI,CAAC,UAAU,CAAC,GAAG;YAClC,IAAI,OAAO,MAAM,KAAK,MAAM,GAAG;gBAC3B,yBAAyB,QAAQ,GAAG;gBACpC,wBAAwB,OAAO,MAAM;YACzC;QACJ;QACA,IAAI,0BAA0B,OAAO,MAAM,IAAI;YAC3C,IAAI,YAAY,KAAK,KAAK,CAAC,AAAC,OAAO,MAAM,KAAK,IAAK;YACnD,IAAI,mBAAmB,KAAK,KAAK,CAAC,QAAQ,gBAAgB,KAAK,YAAY,IAAI,0BAA0B;YACzG,QAAQ,gBAAgB,CAAC;YACzB,IAAI,YAAY,QAAQ,aAAa,GAAG,eAAe,KAAK;YAC5D,IAAI,OAAO,KAAK,KAAK,CAAC,OAAO,MAAM,KAAK;YACxC,IAAI,AAAC,SAAS,KAAK,cAAc,KAC5B,SAAS,KAAK,CAAC,eAAe,KAAK,cAAc,CAAC,GAAI;gBACvD,2CAA2C;gBAC3C,QAAQ,GAAG,GAAG;YAClB;QACJ;QACA,IAAI,OAAO,MAAM,KAAK,GAAG;YACrB,QAAQ,aAAa,CAAC,0PAAA,CAAA,eAAY;QACtC;QACA,IAAI,CAAC,SAAS,CAAC,SAAS;IAC5B;IACA,WAAW,SAAS,CAAC,MAAM,GAAG,SAAU,OAAO;QAC3C,SAAS;QACT,IAAI,SAAS,IAAI,6OAAA,CAAA,UAAa;QAC9B,MAAO,QAAQ,iBAAiB,GAAI;YAChC,IAAI,IAAI,QAAQ,cAAc;YAC9B,QAAQ,GAAG;YACX,IAAI,eAAe,IAAI,CAAC,UAAU,CAAC,GAAG;YACtC,IAAI,YAAY,KAAK,KAAK,CAAC,OAAO,MAAM,KAAK,KAAK;YAClD,IAAI,mBAAmB,QAAQ,gBAAgB,KAAK;YACpD,QAAQ,gBAAgB,CAAC;YACzB,IAAI,YAAY,QAAQ,aAAa,GAAG,eAAe,KAAK;YAC5D,IAAI,CAAC,QAAQ,iBAAiB,IAAI;gBAC9B,sDAAsD;gBACtD,IAAI,UAAU,IAAI,6OAAA,CAAA,UAAa;gBAC/B,IAAI,OAAO,MAAM,KAAK,MAAM,KAAK,cAAc,GAAG;oBAC9C,eAAe,IAAI,CAAC,qBAAqB,CAAC,SAAS,QAAQ,SAAS;gBACxE;gBACA,MAAO,OAAO,MAAM,KAAK,MAAM,KAC3B,CAAC,eAAe,KAAK,cAAc,CAAC,EAAG;oBACvC,eAAe,IAAI,CAAC,qBAAqB,CAAC,SAAS,QAAQ,SAAS;gBACxE;gBACA;YACJ;YACA,IAAI,QAAQ,OAAO,MAAM;YACzB,IAAI,QAAQ,MAAM,GAAG;gBACjB,IAAI,UAAU,iQAAA,CAAA,UAAgB,CAAC,aAAa,CAAC,QAAQ,UAAU,IAAI,QAAQ,GAAG,EAAE,IAAI,CAAC,eAAe;gBACpG,IAAI,YAAY,IAAI,CAAC,eAAe,IAAI;oBACpC,2EAA2E;oBAC3E,QAAQ,mBAAmB,CAAC,0PAAA,CAAA,mBAAgB;oBAC5C;gBACJ;YACJ;QACJ;QACA,IAAI,CAAC,SAAS,CAAC,SAAS;IAC5B;IACA,WAAW,SAAS,CAAC,qBAAqB,GAAG,SAAU,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY;QACzF,IAAI,QAAQ,OAAO,MAAM;QACzB,IAAI,OAAO,OAAO,QAAQ,GAAG,SAAS,CAAC,GAAG,QAAQ;QAClD,OAAO,eAAe;QACtB,OAAO,MAAM,CAAC;QACd,8CAA8C;QAC9C;;SAEC,GACD,QAAQ,GAAG;QACX,IAAI,IAAI,QAAQ,cAAc;QAC9B,eAAe,IAAI,CAAC,UAAU,CAAC,GAAG;QAClC,QAAQ,eAAe,IAAI,8CAA8C;QACzE,OAAO;IACX;IACA,WAAW,SAAS,CAAC,gBAAgB,GAAG,SAAU,OAAO,EAAE,MAAM;QAC7D,QAAQ,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,QAAQ;QAC7D,IAAI,OAAO,OAAO,QAAQ,GAAG,SAAS,CAAC;QACvC,OAAO,eAAe;QACtB,OAAO,MAAM,CAAC;IACd,uBAAuB;IACvB;;SAEC,GACL;IACA;;;;;KAKC,GACD,WAAW,SAAS,CAAC,SAAS,GAAG,SAAU,OAAO,EAAE,MAAM;QACtD,IAAI,YAAY,KAAK,KAAK,CAAC,AAAC,OAAO,MAAM,KAAK,IAAK;QACnD,IAAI,OAAO,OAAO,MAAM,KAAK;QAC7B,IAAI,mBAAmB,QAAQ,gBAAgB,KAAK;QACpD,QAAQ,gBAAgB,CAAC;QACzB,IAAI,YAAY,QAAQ,aAAa,GAAG,eAAe,KAAK;QAC5D,IAAI,SAAS,GAAG;YACZ,OAAO,MAAM,CAAC,OAAO,UAAU;YAC/B,MAAO,OAAO,MAAM,MAAM,EAAG;gBACzB,IAAI,CAAC,gBAAgB,CAAC,SAAS;YACnC;YACA,IAAI,QAAQ,iBAAiB,IAAI;gBAC7B,QAAQ,aAAa,CAAC,0PAAA,CAAA,cAAW;YACrC;QACJ,OACK,IAAI,cAAc,KAAK,SAAS,GAAG;YACpC,MAAO,OAAO,MAAM,MAAM,EAAG;gBACzB,IAAI,CAAC,gBAAgB,CAAC,SAAS;YACnC;YACA,IAAI,QAAQ,iBAAiB,IAAI;gBAC7B,QAAQ,aAAa,CAAC,0PAAA,CAAA,cAAW;YACrC;YACA,kBAAkB;YAClB,QAAQ,GAAG;QACf,OACK,IAAI,SAAS,GAAG;YACjB,MAAO,OAAO,MAAM,MAAM,EAAG;gBACzB,IAAI,CAAC,gBAAgB,CAAC,SAAS;YACnC;YACA,IAAI,YAAY,KAAK,QAAQ,iBAAiB,IAAI;gBAC9C,QAAQ,aAAa,CAAC,0PAAA,CAAA,cAAW;YACrC;QACJ,OACK;YACD,MAAM,IAAI,MAAM;QACpB;QACA,QAAQ,mBAAmB,CAAC,0PAAA,CAAA,mBAAgB;IAChD;IACA,WAAW,SAAS,CAAC,UAAU,GAAG,SAAU,CAAC,EAAE,EAAE;QAC7C,IAAI,MAAM,IAAI,UAAU,CAAC,IAAI;YACzB,GAAG,MAAM,CAAC;YACV,OAAO;QACX;QACA,IAAI,KAAK,IAAI,UAAU,CAAC,MAAM,KAAK,IAAI,UAAU,CAAC,IAAI;YAClD,GAAG,MAAM,CAAC,IAAI,KAAK;YACnB,OAAO;QACX;QACA,IAAI,KAAK,IAAI,UAAU,CAAC,MAAM,KAAK,IAAI,UAAU,CAAC,IAAI;YAClD,GAAG,MAAM,CAAC,IAAI,KAAK;YACnB,OAAO;QACX;QACA,IAAI,IAAI,IAAI,UAAU,CAAC,IAAI;YACvB,GAAG,MAAM,CAAC,IAAI,cAAc;YAC5B,GAAG,MAAM,CAAC;YACV,OAAO;QACX;QACA,IAAI,KAAK,IAAI,UAAU,CAAC,IAAI;YACxB,GAAG,MAAM,CAAC,IAAI,cAAc;YAC5B,GAAG,MAAM,CAAC,IAAI;YACd,OAAO;QACX;QACA,IAAI,KAAK,IAAI,UAAU,CAAC,IAAI;YACxB,GAAG,MAAM,CAAC,IAAI,cAAc;YAC5B,GAAG,MAAM,CAAC,IAAI,KAAK;YACnB,OAAO;QACX;QACA,IAAI,KAAK,IAAI,UAAU,CAAC,IAAI;YACxB,GAAG,MAAM,CAAC,IAAI,cAAc;YAC5B,GAAG,MAAM,CAAC,IAAI,KAAK;YACnB,OAAO;QACX;QACA,IAAI,KAAK,KAAK;YACV,GAAG,MAAM,CAAC,IAAI,cAAc;YAC5B,GAAG,MAAM,CAAC,IAAI;YACd,OAAO;QACX;QACA,GAAG,MAAM,CAAC,IAAI,WAAW,uBAAuB;QAChD,IAAI,MAAM;QACV,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK;QAChC,OAAO;IACX;IACA,WAAW,SAAS,CAAC,iBAAiB,GAAG,SAAU,EAAE;QACjD,IAAI,IAAI,OAAO,GAAG,UAAU,CAAC,KAAK,KAAK,GAAG,UAAU,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK;QAC7E,IAAI,MAAM,IAAI;QACd,IAAI,MAAM,IAAI;QACd,IAAI,SAAS,IAAI,6OAAA,CAAA,UAAa;QAC9B,OAAO,MAAM,CAAC;QACd,OAAO,MAAM,CAAC;QACd,OAAO,OAAO,QAAQ;IAC1B;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3169, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/core/datamatrix/encoder/EdifactEncoder.js"], "sourcesContent": ["import StringUtils from '../../common/StringUtils';\nimport StringBuilder from '../../util/StringBuilder';\nimport { EDIFACT_ENCODATION, ASCII_ENCODATION } from './constants';\nimport HighLevelEncoder from './HighLevelEncoder';\nvar EdifactEncoder = /** @class */ (function () {\n    function EdifactEncoder() {\n    }\n    EdifactEncoder.prototype.getEncodingMode = function () {\n        return EDIFACT_ENCODATION;\n    };\n    EdifactEncoder.prototype.encode = function (context) {\n        // step F\n        var buffer = new StringBuilder();\n        while (context.hasMoreCharacters()) {\n            var c = context.getCurrentChar();\n            this.encodeChar(c, buffer);\n            context.pos++;\n            var count = buffer.length();\n            if (count >= 4) {\n                context.writeCodewords(this.encodeToCodewords(buffer.toString()));\n                var test_1 = buffer.toString().substring(4);\n                buffer.setLengthToZero();\n                buffer.append(test_1);\n                // buffer.delete(0, 4);\n                // for (let i = 0; i < 4; i++) {\n                //  buffer.deleteCharAt(i);\n                // }\n                var newMode = HighLevelEncoder.lookAheadTest(context.getMessage(), context.pos, this.getEncodingMode());\n                if (newMode !== this.getEncodingMode()) {\n                    // Return to ASCII encodation, which will actually handle latch to new mode\n                    context.signalEncoderChange(ASCII_ENCODATION);\n                    break;\n                }\n            }\n        }\n        buffer.append(StringUtils.getCharAt(31)); // Unlatch\n        this.handleEOD(context, buffer);\n    };\n    /**\n     * Handle \"end of data\" situations\n     *\n     * @param context the encoder context\n     * @param buffer  the buffer with the remaining encoded characters\n     */\n    EdifactEncoder.prototype.handleEOD = function (context, buffer) {\n        try {\n            var count = buffer.length();\n            if (count === 0) {\n                return; // Already finished\n            }\n            if (count === 1) {\n                // Only an unlatch at the end\n                context.updateSymbolInfo();\n                var available = context.getSymbolInfo().getDataCapacity() -\n                    context.getCodewordCount();\n                var remaining = context.getRemainingCharacters();\n                // The following two lines are a hack inspired by the 'fix' from https://sourceforge.net/p/barcode4j/svn/221/\n                if (remaining > available) {\n                    context.updateSymbolInfo(context.getCodewordCount() + 1);\n                    available =\n                        context.getSymbolInfo().getDataCapacity() -\n                            context.getCodewordCount();\n                }\n                if (remaining <= available && available <= 2) {\n                    return; // No unlatch\n                }\n            }\n            if (count > 4) {\n                throw new Error('Count must not exceed 4');\n            }\n            var restChars = count - 1;\n            var encoded = this.encodeToCodewords(buffer.toString());\n            var endOfSymbolReached = !context.hasMoreCharacters();\n            var restInAscii = endOfSymbolReached && restChars <= 2;\n            if (restChars <= 2) {\n                context.updateSymbolInfo(context.getCodewordCount() + restChars);\n                var available = context.getSymbolInfo().getDataCapacity() -\n                    context.getCodewordCount();\n                if (available >= 3) {\n                    restInAscii = false;\n                    context.updateSymbolInfo(context.getCodewordCount() + encoded.length);\n                    // available = context.symbolInfo.dataCapacity - context.getCodewordCount();\n                }\n            }\n            if (restInAscii) {\n                context.resetSymbolInfo();\n                context.pos -= restChars;\n            }\n            else {\n                context.writeCodewords(encoded);\n            }\n        }\n        finally {\n            context.signalEncoderChange(ASCII_ENCODATION);\n        }\n    };\n    EdifactEncoder.prototype.encodeChar = function (c, sb) {\n        if (c >= ' '.charCodeAt(0) && c <= '?'.charCodeAt(0)) {\n            sb.append(c);\n        }\n        else if (c >= '@'.charCodeAt(0) && c <= '^'.charCodeAt(0)) {\n            sb.append(StringUtils.getCharAt(c - 64));\n        }\n        else {\n            HighLevelEncoder.illegalCharacter(StringUtils.getCharAt(c));\n        }\n    };\n    EdifactEncoder.prototype.encodeToCodewords = function (sb) {\n        var len = sb.length;\n        if (len === 0) {\n            throw new Error('StringBuilder must not be empty');\n        }\n        var c1 = sb.charAt(0).charCodeAt(0);\n        var c2 = len >= 2 ? sb.charAt(1).charCodeAt(0) : 0;\n        var c3 = len >= 3 ? sb.charAt(2).charCodeAt(0) : 0;\n        var c4 = len >= 4 ? sb.charAt(3).charCodeAt(0) : 0;\n        var v = (c1 << 18) + (c2 << 12) + (c3 << 6) + c4;\n        var cw1 = (v >> 16) & 255;\n        var cw2 = (v >> 8) & 255;\n        var cw3 = v & 255;\n        var res = new StringBuilder();\n        res.append(cw1);\n        if (len >= 2) {\n            res.append(cw2);\n        }\n        if (len >= 3) {\n            res.append(cw3);\n        }\n        return res.toString();\n    };\n    return EdifactEncoder;\n}());\nexport { EdifactEncoder };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACA,IAAI,iBAAgC;IAChC,SAAS,kBACT;IACA,eAAe,SAAS,CAAC,eAAe,GAAG;QACvC,OAAO,0PAAA,CAAA,qBAAkB;IAC7B;IACA,eAAe,SAAS,CAAC,MAAM,GAAG,SAAU,OAAO;QAC/C,SAAS;QACT,IAAI,SAAS,IAAI,6OAAA,CAAA,UAAa;QAC9B,MAAO,QAAQ,iBAAiB,GAAI;YAChC,IAAI,IAAI,QAAQ,cAAc;YAC9B,IAAI,CAAC,UAAU,CAAC,GAAG;YACnB,QAAQ,GAAG;YACX,IAAI,QAAQ,OAAO,MAAM;YACzB,IAAI,SAAS,GAAG;gBACZ,QAAQ,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,QAAQ;gBAC7D,IAAI,SAAS,OAAO,QAAQ,GAAG,SAAS,CAAC;gBACzC,OAAO,eAAe;gBACtB,OAAO,MAAM,CAAC;gBACd,uBAAuB;gBACvB,gCAAgC;gBAChC,2BAA2B;gBAC3B,IAAI;gBACJ,IAAI,UAAU,iQAAA,CAAA,UAAgB,CAAC,aAAa,CAAC,QAAQ,UAAU,IAAI,QAAQ,GAAG,EAAE,IAAI,CAAC,eAAe;gBACpG,IAAI,YAAY,IAAI,CAAC,eAAe,IAAI;oBACpC,2EAA2E;oBAC3E,QAAQ,mBAAmB,CAAC,0PAAA,CAAA,mBAAgB;oBAC5C;gBACJ;YACJ;QACJ;QACA,OAAO,MAAM,CAAC,6OAAA,CAAA,UAAW,CAAC,SAAS,CAAC,MAAM,UAAU;QACpD,IAAI,CAAC,SAAS,CAAC,SAAS;IAC5B;IACA;;;;;KAKC,GACD,eAAe,SAAS,CAAC,SAAS,GAAG,SAAU,OAAO,EAAE,MAAM;QAC1D,IAAI;YACA,IAAI,QAAQ,OAAO,MAAM;YACzB,IAAI,UAAU,GAAG;gBACb,QAAQ,mBAAmB;YAC/B;YACA,IAAI,UAAU,GAAG;gBACb,6BAA6B;gBAC7B,QAAQ,gBAAgB;gBACxB,IAAI,YAAY,QAAQ,aAAa,GAAG,eAAe,KACnD,QAAQ,gBAAgB;gBAC5B,IAAI,YAAY,QAAQ,sBAAsB;gBAC9C,6GAA6G;gBAC7G,IAAI,YAAY,WAAW;oBACvB,QAAQ,gBAAgB,CAAC,QAAQ,gBAAgB,KAAK;oBACtD,YACI,QAAQ,aAAa,GAAG,eAAe,KACnC,QAAQ,gBAAgB;gBACpC;gBACA,IAAI,aAAa,aAAa,aAAa,GAAG;oBAC1C,QAAQ,aAAa;gBACzB;YACJ;YACA,IAAI,QAAQ,GAAG;gBACX,MAAM,IAAI,MAAM;YACpB;YACA,IAAI,YAAY,QAAQ;YACxB,IAAI,UAAU,IAAI,CAAC,iBAAiB,CAAC,OAAO,QAAQ;YACpD,IAAI,qBAAqB,CAAC,QAAQ,iBAAiB;YACnD,IAAI,cAAc,sBAAsB,aAAa;YACrD,IAAI,aAAa,GAAG;gBAChB,QAAQ,gBAAgB,CAAC,QAAQ,gBAAgB,KAAK;gBACtD,IAAI,YAAY,QAAQ,aAAa,GAAG,eAAe,KACnD,QAAQ,gBAAgB;gBAC5B,IAAI,aAAa,GAAG;oBAChB,cAAc;oBACd,QAAQ,gBAAgB,CAAC,QAAQ,gBAAgB,KAAK,QAAQ,MAAM;gBACpE,4EAA4E;gBAChF;YACJ;YACA,IAAI,aAAa;gBACb,QAAQ,eAAe;gBACvB,QAAQ,GAAG,IAAI;YACnB,OACK;gBACD,QAAQ,cAAc,CAAC;YAC3B;QACJ,SACQ;YACJ,QAAQ,mBAAmB,CAAC,0PAAA,CAAA,mBAAgB;QAChD;IACJ;IACA,eAAe,SAAS,CAAC,UAAU,GAAG,SAAU,CAAC,EAAE,EAAE;QACjD,IAAI,KAAK,IAAI,UAAU,CAAC,MAAM,KAAK,IAAI,UAAU,CAAC,IAAI;YAClD,GAAG,MAAM,CAAC;QACd,OACK,IAAI,KAAK,IAAI,UAAU,CAAC,MAAM,KAAK,IAAI,UAAU,CAAC,IAAI;YACvD,GAAG,MAAM,CAAC,6OAAA,CAAA,UAAW,CAAC,SAAS,CAAC,IAAI;QACxC,OACK;YACD,iQAAA,CAAA,UAAgB,CAAC,gBAAgB,CAAC,6OAAA,CAAA,UAAW,CAAC,SAAS,CAAC;QAC5D;IACJ;IACA,eAAe,SAAS,CAAC,iBAAiB,GAAG,SAAU,EAAE;QACrD,IAAI,MAAM,GAAG,MAAM;QACnB,IAAI,QAAQ,GAAG;YACX,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,KAAK,GAAG,MAAM,CAAC,GAAG,UAAU,CAAC;QACjC,IAAI,KAAK,OAAO,IAAI,GAAG,MAAM,CAAC,GAAG,UAAU,CAAC,KAAK;QACjD,IAAI,KAAK,OAAO,IAAI,GAAG,MAAM,CAAC,GAAG,UAAU,CAAC,KAAK;QACjD,IAAI,KAAK,OAAO,IAAI,GAAG,MAAM,CAAC,GAAG,UAAU,CAAC,KAAK;QACjD,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;QAC9C,IAAI,MAAM,AAAC,KAAK,KAAM;QACtB,IAAI,MAAM,AAAC,KAAK,IAAK;QACrB,IAAI,MAAM,IAAI;QACd,IAAI,MAAM,IAAI,6OAAA,CAAA,UAAa;QAC3B,IAAI,MAAM,CAAC;QACX,IAAI,OAAO,GAAG;YACV,IAAI,MAAM,CAAC;QACf;QACA,IAAI,OAAO,GAAG;YACV,IAAI,MAAM,CAAC;QACf;QACA,OAAO,IAAI,QAAQ;IACvB;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3303, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/core/datamatrix/encoder/SymbolInfo.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/**\n * Symbol info table for DataMatrix.\n */\nvar SymbolInfo = /** @class */ (function () {\n    function SymbolInfo(rectangular, dataCapacity, errorCodewords, matrixWidth, matrixHeight, dataRegions, rsBlockData, rsBlockError) {\n        if (rsBlockData === void 0) { rsBlockData = 0; }\n        if (rsBlockError === void 0) { rsBlockError = 0; }\n        this.rectangular = rectangular;\n        this.dataCapacity = dataCapacity;\n        this.errorCodewords = errorCodewords;\n        this.matrixWidth = matrixWidth;\n        this.matrixHeight = matrixHeight;\n        this.dataRegions = dataRegions;\n        this.rsBlockData = rsBlockData;\n        this.rsBlockError = rsBlockError;\n    }\n    SymbolInfo.lookup = function (dataCodewords, shape, minSize, maxSize, fail) {\n        var e_1, _a;\n        if (shape === void 0) { shape = 0 /* FORCE_NONE */; }\n        if (minSize === void 0) { minSize = null; }\n        if (maxSize === void 0) { maxSize = null; }\n        if (fail === void 0) { fail = true; }\n        try {\n            for (var PROD_SYMBOLS_1 = __values(PROD_SYMBOLS), PROD_SYMBOLS_1_1 = PROD_SYMBOLS_1.next(); !PROD_SYMBOLS_1_1.done; PROD_SYMBOLS_1_1 = PROD_SYMBOLS_1.next()) {\n                var symbol = PROD_SYMBOLS_1_1.value;\n                if (shape === 1 /* FORCE_SQUARE */ && symbol.rectangular) {\n                    continue;\n                }\n                if (shape === 2 /* FORCE_RECTANGLE */ && !symbol.rectangular) {\n                    continue;\n                }\n                if (minSize != null &&\n                    (symbol.getSymbolWidth() < minSize.getWidth() ||\n                        symbol.getSymbolHeight() < minSize.getHeight())) {\n                    continue;\n                }\n                if (maxSize != null &&\n                    (symbol.getSymbolWidth() > maxSize.getWidth() ||\n                        symbol.getSymbolHeight() > maxSize.getHeight())) {\n                    continue;\n                }\n                if (dataCodewords <= symbol.dataCapacity) {\n                    return symbol;\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (PROD_SYMBOLS_1_1 && !PROD_SYMBOLS_1_1.done && (_a = PROD_SYMBOLS_1.return)) _a.call(PROD_SYMBOLS_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        if (fail) {\n            throw new Error(\"Can't find a symbol arrangement that matches the message. Data codewords: \" +\n                dataCodewords);\n        }\n        return null;\n    };\n    SymbolInfo.prototype.getHorizontalDataRegions = function () {\n        switch (this.dataRegions) {\n            case 1:\n                return 1;\n            case 2:\n            case 4:\n                return 2;\n            case 16:\n                return 4;\n            case 36:\n                return 6;\n            default:\n                throw new Error('Cannot handle this number of data regions');\n        }\n    };\n    SymbolInfo.prototype.getVerticalDataRegions = function () {\n        switch (this.dataRegions) {\n            case 1:\n            case 2:\n                return 1;\n            case 4:\n                return 2;\n            case 16:\n                return 4;\n            case 36:\n                return 6;\n            default:\n                throw new Error('Cannot handle this number of data regions');\n        }\n    };\n    SymbolInfo.prototype.getSymbolDataWidth = function () {\n        return this.getHorizontalDataRegions() * this.matrixWidth;\n    };\n    SymbolInfo.prototype.getSymbolDataHeight = function () {\n        return this.getVerticalDataRegions() * this.matrixHeight;\n    };\n    SymbolInfo.prototype.getSymbolWidth = function () {\n        return this.getSymbolDataWidth() + this.getHorizontalDataRegions() * 2;\n    };\n    SymbolInfo.prototype.getSymbolHeight = function () {\n        return this.getSymbolDataHeight() + this.getVerticalDataRegions() * 2;\n    };\n    SymbolInfo.prototype.getCodewordCount = function () {\n        return this.dataCapacity + this.errorCodewords;\n    };\n    SymbolInfo.prototype.getInterleavedBlockCount = function () {\n        if (!this.rsBlockData)\n            return 1;\n        return this.dataCapacity / this.rsBlockData;\n    };\n    SymbolInfo.prototype.getDataCapacity = function () {\n        return this.dataCapacity;\n    };\n    SymbolInfo.prototype.getErrorCodewords = function () {\n        return this.errorCodewords;\n    };\n    SymbolInfo.prototype.getDataLengthForInterleavedBlock = function (index) {\n        return this.rsBlockData;\n    };\n    SymbolInfo.prototype.getErrorLengthForInterleavedBlock = function (index) {\n        return this.rsBlockError;\n    };\n    return SymbolInfo;\n}());\nexport default SymbolInfo;\nvar DataMatrixSymbolInfo144 = /** @class */ (function (_super) {\n    __extends(DataMatrixSymbolInfo144, _super);\n    function DataMatrixSymbolInfo144() {\n        return _super.call(this, false, 1558, 620, 22, 22, 36, -1, 62) || this;\n    }\n    DataMatrixSymbolInfo144.prototype.getInterleavedBlockCount = function () {\n        return 10;\n    };\n    DataMatrixSymbolInfo144.prototype.getDataLengthForInterleavedBlock = function (index) {\n        return index <= 8 ? 156 : 155;\n    };\n    return DataMatrixSymbolInfo144;\n}(SymbolInfo));\nexport var PROD_SYMBOLS = [\n    new SymbolInfo(false, 3, 5, 8, 8, 1),\n    new SymbolInfo(false, 5, 7, 10, 10, 1),\n    /*rect*/ new SymbolInfo(true, 5, 7, 16, 6, 1),\n    new SymbolInfo(false, 8, 10, 12, 12, 1),\n    /*rect*/ new SymbolInfo(true, 10, 11, 14, 6, 2),\n    new SymbolInfo(false, 12, 12, 14, 14, 1),\n    /*rect*/ new SymbolInfo(true, 16, 14, 24, 10, 1),\n    new SymbolInfo(false, 18, 14, 16, 16, 1),\n    new SymbolInfo(false, 22, 18, 18, 18, 1),\n    /*rect*/ new SymbolInfo(true, 22, 18, 16, 10, 2),\n    new SymbolInfo(false, 30, 20, 20, 20, 1),\n    /*rect*/ new SymbolInfo(true, 32, 24, 16, 14, 2),\n    new SymbolInfo(false, 36, 24, 22, 22, 1),\n    new SymbolInfo(false, 44, 28, 24, 24, 1),\n    /*rect*/ new SymbolInfo(true, 49, 28, 22, 14, 2),\n    new SymbolInfo(false, 62, 36, 14, 14, 4),\n    new SymbolInfo(false, 86, 42, 16, 16, 4),\n    new SymbolInfo(false, 114, 48, 18, 18, 4),\n    new SymbolInfo(false, 144, 56, 20, 20, 4),\n    new SymbolInfo(false, 174, 68, 22, 22, 4),\n    new SymbolInfo(false, 204, 84, 24, 24, 4, 102, 42),\n    new SymbolInfo(false, 280, 112, 14, 14, 16, 140, 56),\n    new SymbolInfo(false, 368, 144, 16, 16, 16, 92, 36),\n    new SymbolInfo(false, 456, 192, 18, 18, 16, 114, 48),\n    new SymbolInfo(false, 576, 224, 20, 20, 16, 144, 56),\n    new SymbolInfo(false, 696, 272, 22, 22, 16, 174, 68),\n    new SymbolInfo(false, 816, 336, 24, 24, 16, 136, 56),\n    new SymbolInfo(false, 1050, 408, 18, 18, 36, 175, 68),\n    new SymbolInfo(false, 1304, 496, 20, 20, 36, 163, 62),\n    new DataMatrixSymbolInfo144(),\n];\n"], "names": [], "mappings": ";;;;AAAA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;AACA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;AACA;;CAEC,GACD,IAAI,aAA4B;IAC5B,SAAS,WAAW,WAAW,EAAE,YAAY,EAAE,cAAc,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY;QAC5H,IAAI,gBAAgB,KAAK,GAAG;YAAE,cAAc;QAAG;QAC/C,IAAI,iBAAiB,KAAK,GAAG;YAAE,eAAe;QAAG;QACjD,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,YAAY,GAAG;IACxB;IACA,WAAW,MAAM,GAAG,SAAU,aAAa,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI;QACtE,IAAI,KAAK;QACT,IAAI,UAAU,KAAK,GAAG;YAAE,QAAQ,EAAE,cAAc;QAAI;QACpD,IAAI,YAAY,KAAK,GAAG;YAAE,UAAU;QAAM;QAC1C,IAAI,YAAY,KAAK,GAAG;YAAE,UAAU;QAAM;QAC1C,IAAI,SAAS,KAAK,GAAG;YAAE,OAAO;QAAM;QACpC,IAAI;YACA,IAAK,IAAI,iBAAiB,SAAS,eAAe,mBAAmB,eAAe,IAAI,IAAI,CAAC,iBAAiB,IAAI,EAAE,mBAAmB,eAAe,IAAI,GAAI;gBAC1J,IAAI,SAAS,iBAAiB,KAAK;gBACnC,IAAI,UAAU,EAAE,gBAAgB,OAAM,OAAO,WAAW,EAAE;oBACtD;gBACJ;gBACA,IAAI,UAAU,EAAE,mBAAmB,OAAM,CAAC,OAAO,WAAW,EAAE;oBAC1D;gBACJ;gBACA,IAAI,WAAW,QACX,CAAC,OAAO,cAAc,KAAK,QAAQ,QAAQ,MACvC,OAAO,eAAe,KAAK,QAAQ,SAAS,EAAE,GAAG;oBACrD;gBACJ;gBACA,IAAI,WAAW,QACX,CAAC,OAAO,cAAc,KAAK,QAAQ,QAAQ,MACvC,OAAO,eAAe,KAAK,QAAQ,SAAS,EAAE,GAAG;oBACrD;gBACJ;gBACA,IAAI,iBAAiB,OAAO,YAAY,EAAE;oBACtC,OAAO;gBACX;YACJ;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,oBAAoB,CAAC,iBAAiB,IAAI,IAAI,CAAC,KAAK,eAAe,MAAM,GAAG,GAAG,IAAI,CAAC;YAC5F,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,IAAI,MAAM;YACN,MAAM,IAAI,MAAM,+EACZ;QACR;QACA,OAAO;IACX;IACA,WAAW,SAAS,CAAC,wBAAwB,GAAG;QAC5C,OAAQ,IAAI,CAAC,WAAW;YACpB,KAAK;gBACD,OAAO;YACX,KAAK;YACL,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX;gBACI,MAAM,IAAI,MAAM;QACxB;IACJ;IACA,WAAW,SAAS,CAAC,sBAAsB,GAAG;QAC1C,OAAQ,IAAI,CAAC,WAAW;YACpB,KAAK;YACL,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX;gBACI,MAAM,IAAI,MAAM;QACxB;IACJ;IACA,WAAW,SAAS,CAAC,kBAAkB,GAAG;QACtC,OAAO,IAAI,CAAC,wBAAwB,KAAK,IAAI,CAAC,WAAW;IAC7D;IACA,WAAW,SAAS,CAAC,mBAAmB,GAAG;QACvC,OAAO,IAAI,CAAC,sBAAsB,KAAK,IAAI,CAAC,YAAY;IAC5D;IACA,WAAW,SAAS,CAAC,cAAc,GAAG;QAClC,OAAO,IAAI,CAAC,kBAAkB,KAAK,IAAI,CAAC,wBAAwB,KAAK;IACzE;IACA,WAAW,SAAS,CAAC,eAAe,GAAG;QACnC,OAAO,IAAI,CAAC,mBAAmB,KAAK,IAAI,CAAC,sBAAsB,KAAK;IACxE;IACA,WAAW,SAAS,CAAC,gBAAgB,GAAG;QACpC,OAAO,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,cAAc;IAClD;IACA,WAAW,SAAS,CAAC,wBAAwB,GAAG;QAC5C,IAAI,CAAC,IAAI,CAAC,WAAW,EACjB,OAAO;QACX,OAAO,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,WAAW;IAC/C;IACA,WAAW,SAAS,CAAC,eAAe,GAAG;QACnC,OAAO,IAAI,CAAC,YAAY;IAC5B;IACA,WAAW,SAAS,CAAC,iBAAiB,GAAG;QACrC,OAAO,IAAI,CAAC,cAAc;IAC9B;IACA,WAAW,SAAS,CAAC,gCAAgC,GAAG,SAAU,KAAK;QACnE,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA,WAAW,SAAS,CAAC,iCAAiC,GAAG,SAAU,KAAK;QACpE,OAAO,IAAI,CAAC,YAAY;IAC5B;IACA,OAAO;AACX;uCACe;AACf,IAAI,0BAAyC,SAAU,MAAM;IACzD,UAAU,yBAAyB;IACnC,SAAS;QACL,OAAO,OAAO,IAAI,CAAC,IAAI,EAAE,OAAO,MAAM,KAAK,IAAI,IAAI,IAAI,CAAC,GAAG,OAAO,IAAI;IAC1E;IACA,wBAAwB,SAAS,CAAC,wBAAwB,GAAG;QACzD,OAAO;IACX;IACA,wBAAwB,SAAS,CAAC,gCAAgC,GAAG,SAAU,KAAK;QAChF,OAAO,SAAS,IAAI,MAAM;IAC9B;IACA,OAAO;AACX,EAAE;AACK,IAAI,eAAe;IACtB,IAAI,WAAW,OAAO,GAAG,GAAG,GAAG,GAAG;IAClC,IAAI,WAAW,OAAO,GAAG,GAAG,IAAI,IAAI;IACpC,MAAM,GAAG,IAAI,WAAW,MAAM,GAAG,GAAG,IAAI,GAAG;IAC3C,IAAI,WAAW,OAAO,GAAG,IAAI,IAAI,IAAI;IACrC,MAAM,GAAG,IAAI,WAAW,MAAM,IAAI,IAAI,IAAI,GAAG;IAC7C,IAAI,WAAW,OAAO,IAAI,IAAI,IAAI,IAAI;IACtC,MAAM,GAAG,IAAI,WAAW,MAAM,IAAI,IAAI,IAAI,IAAI;IAC9C,IAAI,WAAW,OAAO,IAAI,IAAI,IAAI,IAAI;IACtC,IAAI,WAAW,OAAO,IAAI,IAAI,IAAI,IAAI;IACtC,MAAM,GAAG,IAAI,WAAW,MAAM,IAAI,IAAI,IAAI,IAAI;IAC9C,IAAI,WAAW,OAAO,IAAI,IAAI,IAAI,IAAI;IACtC,MAAM,GAAG,IAAI,WAAW,MAAM,IAAI,IAAI,IAAI,IAAI;IAC9C,IAAI,WAAW,OAAO,IAAI,IAAI,IAAI,IAAI;IACtC,IAAI,WAAW,OAAO,IAAI,IAAI,IAAI,IAAI;IACtC,MAAM,GAAG,IAAI,WAAW,MAAM,IAAI,IAAI,IAAI,IAAI;IAC9C,IAAI,WAAW,OAAO,IAAI,IAAI,IAAI,IAAI;IACtC,IAAI,WAAW,OAAO,IAAI,IAAI,IAAI,IAAI;IACtC,IAAI,WAAW,OAAO,KAAK,IAAI,IAAI,IAAI;IACvC,IAAI,WAAW,OAAO,KAAK,IAAI,IAAI,IAAI;IACvC,IAAI,WAAW,OAAO,KAAK,IAAI,IAAI,IAAI;IACvC,IAAI,WAAW,OAAO,KAAK,IAAI,IAAI,IAAI,GAAG,KAAK;IAC/C,IAAI,WAAW,OAAO,KAAK,KAAK,IAAI,IAAI,IAAI,KAAK;IACjD,IAAI,WAAW,OAAO,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI;IAChD,IAAI,WAAW,OAAO,KAAK,KAAK,IAAI,IAAI,IAAI,KAAK;IACjD,IAAI,WAAW,OAAO,KAAK,KAAK,IAAI,IAAI,IAAI,KAAK;IACjD,IAAI,WAAW,OAAO,KAAK,KAAK,IAAI,IAAI,IAAI,KAAK;IACjD,IAAI,WAAW,OAAO,KAAK,KAAK,IAAI,IAAI,IAAI,KAAK;IACjD,IAAI,WAAW,OAAO,MAAM,KAAK,IAAI,IAAI,IAAI,KAAK;IAClD,IAAI,WAAW,OAAO,MAAM,KAAK,IAAI,IAAI,IAAI,KAAK;IAClD,IAAI;CACP", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3521, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/core/datamatrix/encoder/EncoderContext.js"], "sourcesContent": ["import StringBuilder from '../../util/StringBuilder';\nimport SymbolInfo from './SymbolInfo';\nvar EncoderContext = /** @class */ (function () {\n    function EncoderContext(msg) {\n        this.msg = msg;\n        this.pos = 0;\n        this.skipAtEnd = 0;\n        // From this point on Strings are not Unicode anymore!\n        var msgBinary = msg.split('').map(function (c) { return c.charCodeAt(0); });\n        var sb = new StringBuilder();\n        for (var i = 0, c = msgBinary.length; i < c; i++) {\n            var ch = String.fromCharCode(msgBinary[i] & 0xff);\n            if (ch === '?' && msg.charAt(i) !== '?') {\n                throw new Error('Message contains characters outside ISO-8859-1 encoding.');\n            }\n            sb.append(ch);\n        }\n        this.msg = sb.toString(); // Not Unicode here!\n        this.shape = 0 /* FORCE_NONE */;\n        this.codewords = new StringBuilder();\n        this.newEncoding = -1;\n    }\n    EncoderContext.prototype.setSymbolShape = function (shape) {\n        this.shape = shape;\n    };\n    EncoderContext.prototype.setSizeConstraints = function (minSize, maxSize) {\n        this.minSize = minSize;\n        this.maxSize = maxSize;\n    };\n    EncoderContext.prototype.getMessage = function () {\n        return this.msg;\n    };\n    EncoderContext.prototype.setSkipAtEnd = function (count) {\n        this.skipAtEnd = count;\n    };\n    EncoderContext.prototype.getCurrentChar = function () {\n        return this.msg.charCodeAt(this.pos);\n    };\n    EncoderContext.prototype.getCurrent = function () {\n        return this.msg.charCodeAt(this.pos);\n    };\n    EncoderContext.prototype.getCodewords = function () {\n        return this.codewords;\n    };\n    EncoderContext.prototype.writeCodewords = function (codewords) {\n        this.codewords.append(codewords);\n    };\n    EncoderContext.prototype.writeCodeword = function (codeword) {\n        this.codewords.append(codeword);\n    };\n    EncoderContext.prototype.getCodewordCount = function () {\n        return this.codewords.length();\n    };\n    EncoderContext.prototype.getNewEncoding = function () {\n        return this.newEncoding;\n    };\n    EncoderContext.prototype.signalEncoderChange = function (encoding) {\n        this.newEncoding = encoding;\n    };\n    EncoderContext.prototype.resetEncoderSignal = function () {\n        this.newEncoding = -1;\n    };\n    EncoderContext.prototype.hasMoreCharacters = function () {\n        return this.pos < this.getTotalMessageCharCount();\n    };\n    EncoderContext.prototype.getTotalMessageCharCount = function () {\n        return this.msg.length - this.skipAtEnd;\n    };\n    EncoderContext.prototype.getRemainingCharacters = function () {\n        return this.getTotalMessageCharCount() - this.pos;\n    };\n    EncoderContext.prototype.getSymbolInfo = function () {\n        return this.symbolInfo;\n    };\n    EncoderContext.prototype.updateSymbolInfo = function (len) {\n        if (len === void 0) { len = this.getCodewordCount(); }\n        if (this.symbolInfo == null || len > this.symbolInfo.getDataCapacity()) {\n            this.symbolInfo = SymbolInfo.lookup(len, this.shape, this.minSize, this.maxSize, true);\n        }\n    };\n    EncoderContext.prototype.resetSymbolInfo = function () {\n        this.symbolInfo = null;\n    };\n    return EncoderContext;\n}());\nexport { EncoderContext };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACA,IAAI,iBAAgC;IAChC,SAAS,eAAe,GAAG;QACvB,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,SAAS,GAAG;QACjB,sDAAsD;QACtD,IAAI,YAAY,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,SAAU,CAAC;YAAI,OAAO,EAAE,UAAU,CAAC;QAAI;QACzE,IAAI,KAAK,IAAI,6OAAA,CAAA,UAAa;QAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAI,GAAG,IAAK;YAC9C,IAAI,KAAK,OAAO,YAAY,CAAC,SAAS,CAAC,EAAE,GAAG;YAC5C,IAAI,OAAO,OAAO,IAAI,MAAM,CAAC,OAAO,KAAK;gBACrC,MAAM,IAAI,MAAM;YACpB;YACA,GAAG,MAAM,CAAC;QACd;QACA,IAAI,CAAC,GAAG,GAAG,GAAG,QAAQ,IAAI,oBAAoB;QAC9C,IAAI,CAAC,KAAK,GAAG,EAAE,cAAc;QAC7B,IAAI,CAAC,SAAS,GAAG,IAAI,6OAAA,CAAA,UAAa;QAClC,IAAI,CAAC,WAAW,GAAG,CAAC;IACxB;IACA,eAAe,SAAS,CAAC,cAAc,GAAG,SAAU,KAAK;QACrD,IAAI,CAAC,KAAK,GAAG;IACjB;IACA,eAAe,SAAS,CAAC,kBAAkB,GAAG,SAAU,OAAO,EAAE,OAAO;QACpE,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,OAAO,GAAG;IACnB;IACA,eAAe,SAAS,CAAC,UAAU,GAAG;QAClC,OAAO,IAAI,CAAC,GAAG;IACnB;IACA,eAAe,SAAS,CAAC,YAAY,GAAG,SAAU,KAAK;QACnD,IAAI,CAAC,SAAS,GAAG;IACrB;IACA,eAAe,SAAS,CAAC,cAAc,GAAG;QACtC,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG;IACvC;IACA,eAAe,SAAS,CAAC,UAAU,GAAG;QAClC,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG;IACvC;IACA,eAAe,SAAS,CAAC,YAAY,GAAG;QACpC,OAAO,IAAI,CAAC,SAAS;IACzB;IACA,eAAe,SAAS,CAAC,cAAc,GAAG,SAAU,SAAS;QACzD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;IAC1B;IACA,eAAe,SAAS,CAAC,aAAa,GAAG,SAAU,QAAQ;QACvD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;IAC1B;IACA,eAAe,SAAS,CAAC,gBAAgB,GAAG;QACxC,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM;IAChC;IACA,eAAe,SAAS,CAAC,cAAc,GAAG;QACtC,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA,eAAe,SAAS,CAAC,mBAAmB,GAAG,SAAU,QAAQ;QAC7D,IAAI,CAAC,WAAW,GAAG;IACvB;IACA,eAAe,SAAS,CAAC,kBAAkB,GAAG;QAC1C,IAAI,CAAC,WAAW,GAAG,CAAC;IACxB;IACA,eAAe,SAAS,CAAC,iBAAiB,GAAG;QACzC,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,wBAAwB;IACnD;IACA,eAAe,SAAS,CAAC,wBAAwB,GAAG;QAChD,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS;IAC3C;IACA,eAAe,SAAS,CAAC,sBAAsB,GAAG;QAC9C,OAAO,IAAI,CAAC,wBAAwB,KAAK,IAAI,CAAC,GAAG;IACrD;IACA,eAAe,SAAS,CAAC,aAAa,GAAG;QACrC,OAAO,IAAI,CAAC,UAAU;IAC1B;IACA,eAAe,SAAS,CAAC,gBAAgB,GAAG,SAAU,GAAG;QACrD,IAAI,QAAQ,KAAK,GAAG;YAAE,MAAM,IAAI,CAAC,gBAAgB;QAAI;QACrD,IAAI,IAAI,CAAC,UAAU,IAAI,QAAQ,MAAM,IAAI,CAAC,UAAU,CAAC,eAAe,IAAI;YACpE,IAAI,CAAC,UAAU,GAAG,2PAAA,CAAA,UAAU,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;QACrF;IACJ;IACA,eAAe,SAAS,CAAC,eAAe,GAAG;QACvC,IAAI,CAAC,UAAU,GAAG;IACtB;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3620, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/core/datamatrix/encoder/X12Encoder.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport StringUtils from '../../common/StringUtils';\nimport StringBuilder from '../../util/StringBuilder';\nimport { C40Encoder } from './C40Encoder';\nimport HighLevelEncoder from './HighLevelEncoder';\nimport { X12_ENCODATION, ASCII_ENCODATION, X12_UNLATCH } from './constants';\nvar X12Encoder = /** @class */ (function (_super) {\n    __extends(X12Encoder, _super);\n    function X12Encoder() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    X12Encoder.prototype.getEncodingMode = function () {\n        return X12_ENCODATION;\n    };\n    X12Encoder.prototype.encode = function (context) {\n        // step C\n        var buffer = new StringBuilder();\n        while (context.hasMoreCharacters()) {\n            var c = context.getCurrentChar();\n            context.pos++;\n            this.encodeChar(c, buffer);\n            var count = buffer.length();\n            if (count % 3 === 0) {\n                this.writeNextTriplet(context, buffer);\n                var newMode = HighLevelEncoder.lookAheadTest(context.getMessage(), context.pos, this.getEncodingMode());\n                if (newMode !== this.getEncodingMode()) {\n                    // Return to ASCII encodation, which will actually handle latch to new mode\n                    context.signalEncoderChange(ASCII_ENCODATION);\n                    break;\n                }\n            }\n        }\n        this.handleEOD(context, buffer);\n    };\n    X12Encoder.prototype.encodeChar = function (c, sb) {\n        switch (c) {\n            case 13: // CR (Carriage return)\n                sb.append(0);\n                break;\n            case '*'.charCodeAt(0):\n                sb.append(1);\n                break;\n            case '>'.charCodeAt(0):\n                sb.append(2);\n                break;\n            case ' '.charCodeAt(0):\n                sb.append(3);\n                break;\n            default:\n                if (c >= '0'.charCodeAt(0) && c <= '9'.charCodeAt(0)) {\n                    sb.append(c - 48 + 4);\n                }\n                else if (c >= 'A'.charCodeAt(0) && c <= 'Z'.charCodeAt(0)) {\n                    sb.append(c - 65 + 14);\n                }\n                else {\n                    HighLevelEncoder.illegalCharacter(StringUtils.getCharAt(c));\n                }\n                break;\n        }\n        return 1;\n    };\n    X12Encoder.prototype.handleEOD = function (context, buffer) {\n        context.updateSymbolInfo();\n        var available = context.getSymbolInfo().getDataCapacity() - context.getCodewordCount();\n        var count = buffer.length();\n        context.pos -= count;\n        if (context.getRemainingCharacters() > 1 ||\n            available > 1 ||\n            context.getRemainingCharacters() !== available) {\n            context.writeCodeword(X12_UNLATCH);\n        }\n        if (context.getNewEncoding() < 0) {\n            context.signalEncoderChange(ASCII_ENCODATION);\n        }\n    };\n    return X12Encoder;\n}(C40Encoder));\nexport { X12Encoder };\n"], "names": [], "mappings": ";;;AAaA;AACA;AACA;AACA;AACA;AAjBA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;;;;AAMA,IAAI,aAA4B,SAAU,MAAM;IAC5C,UAAU,YAAY;IACtB,SAAS;QACL,OAAO,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;IACnE;IACA,WAAW,SAAS,CAAC,eAAe,GAAG;QACnC,OAAO,0PAAA,CAAA,iBAAc;IACzB;IACA,WAAW,SAAS,CAAC,MAAM,GAAG,SAAU,OAAO;QAC3C,SAAS;QACT,IAAI,SAAS,IAAI,6OAAA,CAAA,UAAa;QAC9B,MAAO,QAAQ,iBAAiB,GAAI;YAChC,IAAI,IAAI,QAAQ,cAAc;YAC9B,QAAQ,GAAG;YACX,IAAI,CAAC,UAAU,CAAC,GAAG;YACnB,IAAI,QAAQ,OAAO,MAAM;YACzB,IAAI,QAAQ,MAAM,GAAG;gBACjB,IAAI,CAAC,gBAAgB,CAAC,SAAS;gBAC/B,IAAI,UAAU,iQAAA,CAAA,UAAgB,CAAC,aAAa,CAAC,QAAQ,UAAU,IAAI,QAAQ,GAAG,EAAE,IAAI,CAAC,eAAe;gBACpG,IAAI,YAAY,IAAI,CAAC,eAAe,IAAI;oBACpC,2EAA2E;oBAC3E,QAAQ,mBAAmB,CAAC,0PAAA,CAAA,mBAAgB;oBAC5C;gBACJ;YACJ;QACJ;QACA,IAAI,CAAC,SAAS,CAAC,SAAS;IAC5B;IACA,WAAW,SAAS,CAAC,UAAU,GAAG,SAAU,CAAC,EAAE,EAAE;QAC7C,OAAQ;YACJ,KAAK;gBACD,GAAG,MAAM,CAAC;gBACV;YACJ,KAAK,IAAI,UAAU,CAAC;gBAChB,GAAG,MAAM,CAAC;gBACV;YACJ,KAAK,IAAI,UAAU,CAAC;gBAChB,GAAG,MAAM,CAAC;gBACV;YACJ,KAAK,IAAI,UAAU,CAAC;gBAChB,GAAG,MAAM,CAAC;gBACV;YACJ;gBACI,IAAI,KAAK,IAAI,UAAU,CAAC,MAAM,KAAK,IAAI,UAAU,CAAC,IAAI;oBAClD,GAAG,MAAM,CAAC,IAAI,KAAK;gBACvB,OACK,IAAI,KAAK,IAAI,UAAU,CAAC,MAAM,KAAK,IAAI,UAAU,CAAC,IAAI;oBACvD,GAAG,MAAM,CAAC,IAAI,KAAK;gBACvB,OACK;oBACD,iQAAA,CAAA,UAAgB,CAAC,gBAAgB,CAAC,6OAAA,CAAA,UAAW,CAAC,SAAS,CAAC;gBAC5D;gBACA;QACR;QACA,OAAO;IACX;IACA,WAAW,SAAS,CAAC,SAAS,GAAG,SAAU,OAAO,EAAE,MAAM;QACtD,QAAQ,gBAAgB;QACxB,IAAI,YAAY,QAAQ,aAAa,GAAG,eAAe,KAAK,QAAQ,gBAAgB;QACpF,IAAI,QAAQ,OAAO,MAAM;QACzB,QAAQ,GAAG,IAAI;QACf,IAAI,QAAQ,sBAAsB,KAAK,KACnC,YAAY,KACZ,QAAQ,sBAAsB,OAAO,WAAW;YAChD,QAAQ,aAAa,CAAC,0PAAA,CAAA,cAAW;QACrC;QACA,IAAI,QAAQ,cAAc,KAAK,GAAG;YAC9B,QAAQ,mBAAmB,CAAC,0PAAA,CAAA,mBAAgB;QAChD;IACJ;IACA,OAAO;AACX,EAAE,2PAAA,CAAA,aAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3725, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/core/datamatrix/encoder/TextEncoder.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport { C40Encoder } from './C40Encoder';\nimport { TEXT_ENCODATION } from './constants';\nvar TextEncoder = /** @class */ (function (_super) {\n    __extends(TextEncoder, _super);\n    function TextEncoder() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    TextEncoder.prototype.getEncodingMode = function () {\n        return TEXT_ENCODATION;\n    };\n    TextEncoder.prototype.encodeChar = function (c, sb) {\n        if (c === ' '.charCodeAt(0)) {\n            sb.append(3);\n            return 1;\n        }\n        if (c >= '0'.charCodeAt(0) && c <= '9'.charCodeAt(0)) {\n            sb.append(c - 48 + 4);\n            return 1;\n        }\n        if (c >= 'a'.charCodeAt(0) && c <= 'z'.charCodeAt(0)) {\n            sb.append(c - 97 + 14);\n            return 1;\n        }\n        if (c < ' '.charCodeAt(0)) {\n            sb.append(0); // Shift 1 Set\n            sb.append(c);\n            return 2;\n        }\n        if (c <= '/'.charCodeAt(0)) {\n            sb.append(1); // Shift 2 Set\n            sb.append(c - 33);\n            return 2;\n        }\n        if (c <= '@'.charCodeAt(0)) {\n            sb.append(1); // Shift 2 Set\n            sb.append(c - 58 + 15);\n            return 2;\n        }\n        if (c >= '['.charCodeAt(0) && c <= '_'.charCodeAt(0)) {\n            sb.append(1); // Shift 2 Set\n            sb.append(c - 91 + 22);\n            return 2;\n        }\n        if (c === '`'.charCodeAt(0)) {\n            sb.append(2); // Shift 3 Set\n            sb.append(0); // '`' - 96 == 0\n            return 2;\n        }\n        if (c <= 'Z'.charCodeAt(0)) {\n            sb.append(2); // Shift 3 Set\n            sb.append(c - 65 + 1);\n            return 2;\n        }\n        if (c <= 127) {\n            sb.append(2); // Shift 3 Set\n            sb.append(c - 123 + 27);\n            return 2;\n        }\n        sb.append(1 + \"\\u001E\"); // Shift 2, Upper Shift\n        var len = 2;\n        len += this.encodeChar(c - 128, sb);\n        return len;\n    };\n    return TextEncoder;\n}(C40Encoder));\nexport { TextEncoder };\n"], "names": [], "mappings": ";;;AAaA;AACA;AAdA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;AAGA,IAAI,cAA6B,SAAU,MAAM;IAC7C,UAAU,aAAa;IACvB,SAAS;QACL,OAAO,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;IACnE;IACA,YAAY,SAAS,CAAC,eAAe,GAAG;QACpC,OAAO,0PAAA,CAAA,kBAAe;IAC1B;IACA,YAAY,SAAS,CAAC,UAAU,GAAG,SAAU,CAAC,EAAE,EAAE;QAC9C,IAAI,MAAM,IAAI,UAAU,CAAC,IAAI;YACzB,GAAG,MAAM,CAAC;YACV,OAAO;QACX;QACA,IAAI,KAAK,IAAI,UAAU,CAAC,MAAM,KAAK,IAAI,UAAU,CAAC,IAAI;YAClD,GAAG,MAAM,CAAC,IAAI,KAAK;YACnB,OAAO;QACX;QACA,IAAI,KAAK,IAAI,UAAU,CAAC,MAAM,KAAK,IAAI,UAAU,CAAC,IAAI;YAClD,GAAG,MAAM,CAAC,IAAI,KAAK;YACnB,OAAO;QACX;QACA,IAAI,IAAI,IAAI,UAAU,CAAC,IAAI;YACvB,GAAG,MAAM,CAAC,IAAI,cAAc;YAC5B,GAAG,MAAM,CAAC;YACV,OAAO;QACX;QACA,IAAI,KAAK,IAAI,UAAU,CAAC,IAAI;YACxB,GAAG,MAAM,CAAC,IAAI,cAAc;YAC5B,GAAG,MAAM,CAAC,IAAI;YACd,OAAO;QACX;QACA,IAAI,KAAK,IAAI,UAAU,CAAC,IAAI;YACxB,GAAG,MAAM,CAAC,IAAI,cAAc;YAC5B,GAAG,MAAM,CAAC,IAAI,KAAK;YACnB,OAAO;QACX;QACA,IAAI,KAAK,IAAI,UAAU,CAAC,MAAM,KAAK,IAAI,UAAU,CAAC,IAAI;YAClD,GAAG,MAAM,CAAC,IAAI,cAAc;YAC5B,GAAG,MAAM,CAAC,IAAI,KAAK;YACnB,OAAO;QACX;QACA,IAAI,MAAM,IAAI,UAAU,CAAC,IAAI;YACzB,GAAG,MAAM,CAAC,IAAI,cAAc;YAC5B,GAAG,MAAM,CAAC,IAAI,gBAAgB;YAC9B,OAAO;QACX;QACA,IAAI,KAAK,IAAI,UAAU,CAAC,IAAI;YACxB,GAAG,MAAM,CAAC,IAAI,cAAc;YAC5B,GAAG,MAAM,CAAC,IAAI,KAAK;YACnB,OAAO;QACX;QACA,IAAI,KAAK,KAAK;YACV,GAAG,MAAM,CAAC,IAAI,cAAc;YAC5B,GAAG,MAAM,CAAC,IAAI,MAAM;YACpB,OAAO;QACX;QACA,GAAG,MAAM,CAAC,IAAI,WAAW,uBAAuB;QAChD,IAAI,MAAM;QACV,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK;QAChC,OAAO;IACX;IACA,OAAO;AACX,EAAE,2PAAA,CAAA,aAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3819, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/core/datamatrix/encoder/HighLevelEncoder.js"], "sourcesContent": ["// tslint:disable-next-line:no-circular-imports\nimport { ASCIIEncoder } from './ASCIIEncoder';\n// tslint:disable-next-line:no-circular-imports\nimport { Base256Encoder } from './Base256Encoder';\n// tslint:disable-next-line:no-circular-imports\nimport { C40Encoder } from './C40Encoder';\nimport { ASCII_ENCODATION, BASE256_ENCODATION, C40_ENCODATION, EDIFACT_ENCODATION, MACRO_05, MACRO_05_HEADER, MACRO_06, MACRO_06_HEADER, MACRO_TRAILER, PAD, TEXT_ENCODATION, X12_ENCODATION, } from './constants';\n// tslint:disable-next-line:no-circular-imports\nimport { EdifactEncoder } from './EdifactEncoder';\nimport { EncoderContext } from './EncoderContext';\n// tslint:disable-next-line:no-circular-imports\nimport { X12Encoder } from './X12Encoder';\n// tslint:disable-next-line:no-circular-imports\nimport { TextEncoder } from './TextEncoder';\nimport Arrays from '../../util/Arrays';\nimport Integer from '../../util/Integer';\n/**\n * DataMatrix ECC 200 data encoder following the algorithm described in ISO/IEC 16022:200(E) in\n * annex S.\n */\nvar HighLevelEncoder = /** @class */ (function () {\n    function HighLevelEncoder() {\n    }\n    HighLevelEncoder.randomize253State = function (codewordPosition) {\n        var pseudoRandom = ((149 * codewordPosition) % 253) + 1;\n        var tempVariable = PAD + pseudoRandom;\n        return tempVariable <= 254 ? tempVariable : tempVariable - 254;\n    };\n    /**\n     * Performs message encoding of a DataMatrix message using the algorithm described in annex P\n     * of ISO/IEC 16022:2000(E).\n     *\n     * @param msg     the message\n     * @param shape   requested shape. May be {@code SymbolShapeHint.FORCE_NONE},\n     *                {@code SymbolShapeHint.FORCE_SQUARE} or {@code SymbolShapeHint.FORCE_RECTANGLE}.\n     * @param minSize the minimum symbol size constraint or null for no constraint\n     * @param maxSize the maximum symbol size constraint or null for no constraint\n     * @param forceC40 enforce C40 encoding\n     * @return the encoded message (the char values range from 0 to 255)\n     */\n    HighLevelEncoder.encodeHighLevel = function (msg, shape, minSize, maxSize, forceC40) {\n        if (shape === void 0) { shape = 0 /* FORCE_NONE */; }\n        if (minSize === void 0) { minSize = null; }\n        if (maxSize === void 0) { maxSize = null; }\n        if (forceC40 === void 0) { forceC40 = false; }\n        // the codewords 0..255 are encoded as Unicode characters\n        var c40Encoder = new C40Encoder();\n        var encoders = [\n            new ASCIIEncoder(),\n            c40Encoder,\n            new TextEncoder(),\n            new X12Encoder(),\n            new EdifactEncoder(),\n            new Base256Encoder(),\n        ];\n        var context = new EncoderContext(msg);\n        context.setSymbolShape(shape);\n        context.setSizeConstraints(minSize, maxSize);\n        if (msg.startsWith(MACRO_05_HEADER) && msg.endsWith(MACRO_TRAILER)) {\n            context.writeCodeword(MACRO_05);\n            context.setSkipAtEnd(2);\n            context.pos += MACRO_05_HEADER.length;\n        }\n        else if (msg.startsWith(MACRO_06_HEADER) && msg.endsWith(MACRO_TRAILER)) {\n            context.writeCodeword(MACRO_06);\n            context.setSkipAtEnd(2);\n            context.pos += MACRO_06_HEADER.length;\n        }\n        var encodingMode = ASCII_ENCODATION; // Default mode\n        if (forceC40) {\n            c40Encoder.encodeMaximal(context);\n            encodingMode = context.getNewEncoding();\n            context.resetEncoderSignal();\n        }\n        while (context.hasMoreCharacters()) {\n            encoders[encodingMode].encode(context);\n            if (context.getNewEncoding() >= 0) {\n                encodingMode = context.getNewEncoding();\n                context.resetEncoderSignal();\n            }\n        }\n        var len = context.getCodewordCount();\n        context.updateSymbolInfo();\n        var capacity = context.getSymbolInfo().getDataCapacity();\n        if (len < capacity &&\n            encodingMode !== ASCII_ENCODATION &&\n            encodingMode !== BASE256_ENCODATION &&\n            encodingMode !== EDIFACT_ENCODATION) {\n            context.writeCodeword('\\u00fe'); // Unlatch (254)\n        }\n        // Padding\n        var codewords = context.getCodewords();\n        if (codewords.length() < capacity) {\n            codewords.append(PAD);\n        }\n        while (codewords.length() < capacity) {\n            codewords.append(this.randomize253State(codewords.length() + 1));\n        }\n        return context.getCodewords().toString();\n    };\n    HighLevelEncoder.lookAheadTest = function (msg, startpos, currentMode) {\n        var newMode = this.lookAheadTestIntern(msg, startpos, currentMode);\n        if (currentMode === X12_ENCODATION && newMode === X12_ENCODATION) {\n            var endpos = Math.min(startpos + 3, msg.length);\n            for (var i = startpos; i < endpos; i++) {\n                if (!this.isNativeX12(msg.charCodeAt(i))) {\n                    return ASCII_ENCODATION;\n                }\n            }\n        }\n        else if (currentMode === EDIFACT_ENCODATION &&\n            newMode === EDIFACT_ENCODATION) {\n            var endpos = Math.min(startpos + 4, msg.length);\n            for (var i = startpos; i < endpos; i++) {\n                if (!this.isNativeEDIFACT(msg.charCodeAt(i))) {\n                    return ASCII_ENCODATION;\n                }\n            }\n        }\n        return newMode;\n    };\n    HighLevelEncoder.lookAheadTestIntern = function (msg, startpos, currentMode) {\n        if (startpos >= msg.length) {\n            return currentMode;\n        }\n        var charCounts;\n        // step J\n        if (currentMode === ASCII_ENCODATION) {\n            charCounts = [0, 1, 1, 1, 1, 1.25];\n        }\n        else {\n            charCounts = [1, 2, 2, 2, 2, 2.25];\n            charCounts[currentMode] = 0;\n        }\n        var charsProcessed = 0;\n        var mins = new Uint8Array(6);\n        var intCharCounts = [];\n        while (true) {\n            // step K\n            if (startpos + charsProcessed === msg.length) {\n                Arrays.fill(mins, 0);\n                Arrays.fill(intCharCounts, 0);\n                var min = this.findMinimums(charCounts, intCharCounts, Integer.MAX_VALUE, mins);\n                var minCount = this.getMinimumCount(mins);\n                if (intCharCounts[ASCII_ENCODATION] === min) {\n                    return ASCII_ENCODATION;\n                }\n                if (minCount === 1) {\n                    if (mins[BASE256_ENCODATION] > 0) {\n                        return BASE256_ENCODATION;\n                    }\n                    if (mins[EDIFACT_ENCODATION] > 0) {\n                        return EDIFACT_ENCODATION;\n                    }\n                    if (mins[TEXT_ENCODATION] > 0) {\n                        return TEXT_ENCODATION;\n                    }\n                    if (mins[X12_ENCODATION] > 0) {\n                        return X12_ENCODATION;\n                    }\n                }\n                return C40_ENCODATION;\n            }\n            var c = msg.charCodeAt(startpos + charsProcessed);\n            charsProcessed++;\n            // step L\n            if (this.isDigit(c)) {\n                charCounts[ASCII_ENCODATION] += 0.5;\n            }\n            else if (this.isExtendedASCII(c)) {\n                charCounts[ASCII_ENCODATION] = Math.ceil(charCounts[ASCII_ENCODATION]);\n                charCounts[ASCII_ENCODATION] += 2.0;\n            }\n            else {\n                charCounts[ASCII_ENCODATION] = Math.ceil(charCounts[ASCII_ENCODATION]);\n                charCounts[ASCII_ENCODATION]++;\n            }\n            // step M\n            if (this.isNativeC40(c)) {\n                charCounts[C40_ENCODATION] += 2.0 / 3.0;\n            }\n            else if (this.isExtendedASCII(c)) {\n                charCounts[C40_ENCODATION] += 8.0 / 3.0;\n            }\n            else {\n                charCounts[C40_ENCODATION] += 4.0 / 3.0;\n            }\n            // step N\n            if (this.isNativeText(c)) {\n                charCounts[TEXT_ENCODATION] += 2.0 / 3.0;\n            }\n            else if (this.isExtendedASCII(c)) {\n                charCounts[TEXT_ENCODATION] += 8.0 / 3.0;\n            }\n            else {\n                charCounts[TEXT_ENCODATION] += 4.0 / 3.0;\n            }\n            // step O\n            if (this.isNativeX12(c)) {\n                charCounts[X12_ENCODATION] += 2.0 / 3.0;\n            }\n            else if (this.isExtendedASCII(c)) {\n                charCounts[X12_ENCODATION] += 13.0 / 3.0;\n            }\n            else {\n                charCounts[X12_ENCODATION] += 10.0 / 3.0;\n            }\n            // step P\n            if (this.isNativeEDIFACT(c)) {\n                charCounts[EDIFACT_ENCODATION] += 3.0 / 4.0;\n            }\n            else if (this.isExtendedASCII(c)) {\n                charCounts[EDIFACT_ENCODATION] += 17.0 / 4.0;\n            }\n            else {\n                charCounts[EDIFACT_ENCODATION] += 13.0 / 4.0;\n            }\n            // step Q\n            if (this.isSpecialB256(c)) {\n                charCounts[BASE256_ENCODATION] += 4.0;\n            }\n            else {\n                charCounts[BASE256_ENCODATION]++;\n            }\n            // step R\n            if (charsProcessed >= 4) {\n                Arrays.fill(mins, 0);\n                Arrays.fill(intCharCounts, 0);\n                this.findMinimums(charCounts, intCharCounts, Integer.MAX_VALUE, mins);\n                if (intCharCounts[ASCII_ENCODATION] <\n                    this.min(intCharCounts[BASE256_ENCODATION], intCharCounts[C40_ENCODATION], intCharCounts[TEXT_ENCODATION], intCharCounts[X12_ENCODATION], intCharCounts[EDIFACT_ENCODATION])) {\n                    return ASCII_ENCODATION;\n                }\n                if (intCharCounts[BASE256_ENCODATION] < intCharCounts[ASCII_ENCODATION] ||\n                    intCharCounts[BASE256_ENCODATION] + 1 <\n                        this.min(intCharCounts[C40_ENCODATION], intCharCounts[TEXT_ENCODATION], intCharCounts[X12_ENCODATION], intCharCounts[EDIFACT_ENCODATION])) {\n                    return BASE256_ENCODATION;\n                }\n                if (intCharCounts[EDIFACT_ENCODATION] + 1 <\n                    this.min(intCharCounts[BASE256_ENCODATION], intCharCounts[C40_ENCODATION], intCharCounts[TEXT_ENCODATION], intCharCounts[X12_ENCODATION], intCharCounts[ASCII_ENCODATION])) {\n                    return EDIFACT_ENCODATION;\n                }\n                if (intCharCounts[TEXT_ENCODATION] + 1 <\n                    this.min(intCharCounts[BASE256_ENCODATION], intCharCounts[C40_ENCODATION], intCharCounts[EDIFACT_ENCODATION], intCharCounts[X12_ENCODATION], intCharCounts[ASCII_ENCODATION])) {\n                    return TEXT_ENCODATION;\n                }\n                if (intCharCounts[X12_ENCODATION] + 1 <\n                    this.min(intCharCounts[BASE256_ENCODATION], intCharCounts[C40_ENCODATION], intCharCounts[EDIFACT_ENCODATION], intCharCounts[TEXT_ENCODATION], intCharCounts[ASCII_ENCODATION])) {\n                    return X12_ENCODATION;\n                }\n                if (intCharCounts[C40_ENCODATION] + 1 <\n                    this.min(intCharCounts[ASCII_ENCODATION], intCharCounts[BASE256_ENCODATION], intCharCounts[EDIFACT_ENCODATION], intCharCounts[TEXT_ENCODATION])) {\n                    if (intCharCounts[C40_ENCODATION] < intCharCounts[X12_ENCODATION]) {\n                        return C40_ENCODATION;\n                    }\n                    if (intCharCounts[C40_ENCODATION] === intCharCounts[X12_ENCODATION]) {\n                        var p = startpos + charsProcessed + 1;\n                        while (p < msg.length) {\n                            var tc = msg.charCodeAt(p);\n                            if (this.isX12TermSep(tc)) {\n                                return X12_ENCODATION;\n                            }\n                            if (!this.isNativeX12(tc)) {\n                                break;\n                            }\n                            p++;\n                        }\n                        return C40_ENCODATION;\n                    }\n                }\n            }\n        }\n    };\n    HighLevelEncoder.min = function (f1, f2, f3, f4, f5) {\n        var val = Math.min(f1, Math.min(f2, Math.min(f3, f4)));\n        if (f5 === undefined) {\n            return val;\n        }\n        else {\n            return Math.min(val, f5);\n        }\n    };\n    HighLevelEncoder.findMinimums = function (charCounts, intCharCounts, min, mins) {\n        for (var i = 0; i < 6; i++) {\n            var current = (intCharCounts[i] = Math.ceil(charCounts[i]));\n            if (min > current) {\n                min = current;\n                Arrays.fill(mins, 0);\n            }\n            if (min === current) {\n                mins[i] = mins[i] + 1;\n            }\n        }\n        return min;\n    };\n    HighLevelEncoder.getMinimumCount = function (mins) {\n        var minCount = 0;\n        for (var i = 0; i < 6; i++) {\n            minCount += mins[i];\n        }\n        return minCount || 0;\n    };\n    HighLevelEncoder.isDigit = function (ch) {\n        return ch >= '0'.charCodeAt(0) && ch <= '9'.charCodeAt(0);\n    };\n    HighLevelEncoder.isExtendedASCII = function (ch) {\n        return ch >= 128 && ch <= 255;\n    };\n    HighLevelEncoder.isNativeC40 = function (ch) {\n        return (ch === ' '.charCodeAt(0) ||\n            (ch >= '0'.charCodeAt(0) && ch <= '9'.charCodeAt(0)) ||\n            (ch >= 'A'.charCodeAt(0) && ch <= 'Z'.charCodeAt(0)));\n    };\n    HighLevelEncoder.isNativeText = function (ch) {\n        return (ch === ' '.charCodeAt(0) ||\n            (ch >= '0'.charCodeAt(0) && ch <= '9'.charCodeAt(0)) ||\n            (ch >= 'a'.charCodeAt(0) && ch <= 'z'.charCodeAt(0)));\n    };\n    HighLevelEncoder.isNativeX12 = function (ch) {\n        return (this.isX12TermSep(ch) ||\n            ch === ' '.charCodeAt(0) ||\n            (ch >= '0'.charCodeAt(0) && ch <= '9'.charCodeAt(0)) ||\n            (ch >= 'A'.charCodeAt(0) && ch <= 'Z'.charCodeAt(0)));\n    };\n    HighLevelEncoder.isX12TermSep = function (ch) {\n        return (ch === 13 || // CR\n            ch === '*'.charCodeAt(0) ||\n            ch === '>'.charCodeAt(0));\n    };\n    HighLevelEncoder.isNativeEDIFACT = function (ch) {\n        return ch >= ' '.charCodeAt(0) && ch <= '^'.charCodeAt(0);\n    };\n    HighLevelEncoder.isSpecialB256 = function (ch) {\n        return false; // TODO NOT IMPLEMENTED YET!!!\n    };\n    /**\n     * Determines the number of consecutive characters that are encodable using numeric compaction.\n     *\n     * @param msg      the message\n     * @param startpos the start position within the message\n     * @return the requested character count\n     */\n    HighLevelEncoder.determineConsecutiveDigitCount = function (msg, startpos) {\n        if (startpos === void 0) { startpos = 0; }\n        var len = msg.length;\n        var idx = startpos;\n        while (idx < len && this.isDigit(msg.charCodeAt(idx))) {\n            idx++;\n        }\n        return idx - startpos;\n    };\n    HighLevelEncoder.illegalCharacter = function (singleCharacter) {\n        var hex = Integer.toHexString(singleCharacter.charCodeAt(0));\n        hex = '0000'.substring(0, 4 - hex.length) + hex;\n        throw new Error('Illegal character: ' + singleCharacter + ' (0x' + hex + ')');\n    };\n    return HighLevelEncoder;\n}());\nexport default HighLevelEncoder;\n"], "names": [], "mappings": "AAAA,+CAA+C;;;;AAC/C;AACA,+CAA+C;AAC/C;AACA,+CAA+C;AAC/C;AACA;AACA,+CAA+C;AAC/C;AACA;AACA,+CAA+C;AAC/C;AACA,+CAA+C;AAC/C;AACA;AACA;;;;;;;;;;;AACA;;;CAGC,GACD,IAAI,mBAAkC;IAClC,SAAS,oBACT;IACA,iBAAiB,iBAAiB,GAAG,SAAU,gBAAgB;QAC3D,IAAI,eAAe,AAAE,MAAM,mBAAoB,MAAO;QACtD,IAAI,eAAe,0PAAA,CAAA,MAAG,GAAG;QACzB,OAAO,gBAAgB,MAAM,eAAe,eAAe;IAC/D;IACA;;;;;;;;;;;KAWC,GACD,iBAAiB,eAAe,GAAG,SAAU,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ;QAC/E,IAAI,UAAU,KAAK,GAAG;YAAE,QAAQ,EAAE,cAAc;QAAI;QACpD,IAAI,YAAY,KAAK,GAAG;YAAE,UAAU;QAAM;QAC1C,IAAI,YAAY,KAAK,GAAG;YAAE,UAAU;QAAM;QAC1C,IAAI,aAAa,KAAK,GAAG;YAAE,WAAW;QAAO;QAC7C,yDAAyD;QACzD,IAAI,aAAa,IAAI,2PAAA,CAAA,aAAU;QAC/B,IAAI,WAAW;YACX,IAAI,6PAAA,CAAA,eAAY;YAChB;YACA,IAAI,4PAAA,CAAA,cAAW;YACf,IAAI,2PAAA,CAAA,aAAU;YACd,IAAI,+PAAA,CAAA,iBAAc;YAClB,IAAI,+PAAA,CAAA,iBAAc;SACrB;QACD,IAAI,UAAU,IAAI,+PAAA,CAAA,iBAAc,CAAC;QACjC,QAAQ,cAAc,CAAC;QACvB,QAAQ,kBAAkB,CAAC,SAAS;QACpC,IAAI,IAAI,UAAU,CAAC,0PAAA,CAAA,kBAAe,KAAK,IAAI,QAAQ,CAAC,0PAAA,CAAA,gBAAa,GAAG;YAChE,QAAQ,aAAa,CAAC,0PAAA,CAAA,WAAQ;YAC9B,QAAQ,YAAY,CAAC;YACrB,QAAQ,GAAG,IAAI,0PAAA,CAAA,kBAAe,CAAC,MAAM;QACzC,OACK,IAAI,IAAI,UAAU,CAAC,0PAAA,CAAA,kBAAe,KAAK,IAAI,QAAQ,CAAC,0PAAA,CAAA,gBAAa,GAAG;YACrE,QAAQ,aAAa,CAAC,0PAAA,CAAA,WAAQ;YAC9B,QAAQ,YAAY,CAAC;YACrB,QAAQ,GAAG,IAAI,0PAAA,CAAA,kBAAe,CAAC,MAAM;QACzC;QACA,IAAI,eAAe,0PAAA,CAAA,mBAAgB,EAAE,eAAe;QACpD,IAAI,UAAU;YACV,WAAW,aAAa,CAAC;YACzB,eAAe,QAAQ,cAAc;YACrC,QAAQ,kBAAkB;QAC9B;QACA,MAAO,QAAQ,iBAAiB,GAAI;YAChC,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC;YAC9B,IAAI,QAAQ,cAAc,MAAM,GAAG;gBAC/B,eAAe,QAAQ,cAAc;gBACrC,QAAQ,kBAAkB;YAC9B;QACJ;QACA,IAAI,MAAM,QAAQ,gBAAgB;QAClC,QAAQ,gBAAgB;QACxB,IAAI,WAAW,QAAQ,aAAa,GAAG,eAAe;QACtD,IAAI,MAAM,YACN,iBAAiB,0PAAA,CAAA,mBAAgB,IACjC,iBAAiB,0PAAA,CAAA,qBAAkB,IACnC,iBAAiB,0PAAA,CAAA,qBAAkB,EAAE;YACrC,QAAQ,aAAa,CAAC,WAAW,gBAAgB;QACrD;QACA,UAAU;QACV,IAAI,YAAY,QAAQ,YAAY;QACpC,IAAI,UAAU,MAAM,KAAK,UAAU;YAC/B,UAAU,MAAM,CAAC,0PAAA,CAAA,MAAG;QACxB;QACA,MAAO,UAAU,MAAM,KAAK,SAAU;YAClC,UAAU,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,MAAM,KAAK;QACjE;QACA,OAAO,QAAQ,YAAY,GAAG,QAAQ;IAC1C;IACA,iBAAiB,aAAa,GAAG,SAAU,GAAG,EAAE,QAAQ,EAAE,WAAW;QACjE,IAAI,UAAU,IAAI,CAAC,mBAAmB,CAAC,KAAK,UAAU;QACtD,IAAI,gBAAgB,0PAAA,CAAA,iBAAc,IAAI,YAAY,0PAAA,CAAA,iBAAc,EAAE;YAC9D,IAAI,SAAS,KAAK,GAAG,CAAC,WAAW,GAAG,IAAI,MAAM;YAC9C,IAAK,IAAI,IAAI,UAAU,IAAI,QAAQ,IAAK;gBACpC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,UAAU,CAAC,KAAK;oBACtC,OAAO,0PAAA,CAAA,mBAAgB;gBAC3B;YACJ;QACJ,OACK,IAAI,gBAAgB,0PAAA,CAAA,qBAAkB,IACvC,YAAY,0PAAA,CAAA,qBAAkB,EAAE;YAChC,IAAI,SAAS,KAAK,GAAG,CAAC,WAAW,GAAG,IAAI,MAAM;YAC9C,IAAK,IAAI,IAAI,UAAU,IAAI,QAAQ,IAAK;gBACpC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,UAAU,CAAC,KAAK;oBAC1C,OAAO,0PAAA,CAAA,mBAAgB;gBAC3B;YACJ;QACJ;QACA,OAAO;IACX;IACA,iBAAiB,mBAAmB,GAAG,SAAU,GAAG,EAAE,QAAQ,EAAE,WAAW;QACvE,IAAI,YAAY,IAAI,MAAM,EAAE;YACxB,OAAO;QACX;QACA,IAAI;QACJ,SAAS;QACT,IAAI,gBAAgB,0PAAA,CAAA,mBAAgB,EAAE;YAClC,aAAa;gBAAC;gBAAG;gBAAG;gBAAG;gBAAG;gBAAG;aAAK;QACtC,OACK;YACD,aAAa;gBAAC;gBAAG;gBAAG;gBAAG;gBAAG;gBAAG;aAAK;YAClC,UAAU,CAAC,YAAY,GAAG;QAC9B;QACA,IAAI,iBAAiB;QACrB,IAAI,OAAO,IAAI,WAAW;QAC1B,IAAI,gBAAgB,EAAE;QACtB,MAAO,KAAM;YACT,SAAS;YACT,IAAI,WAAW,mBAAmB,IAAI,MAAM,EAAE;gBAC1C,sOAAA,CAAA,UAAM,CAAC,IAAI,CAAC,MAAM;gBAClB,sOAAA,CAAA,UAAM,CAAC,IAAI,CAAC,eAAe;gBAC3B,IAAI,MAAM,IAAI,CAAC,YAAY,CAAC,YAAY,eAAe,uOAAA,CAAA,UAAO,CAAC,SAAS,EAAE;gBAC1E,IAAI,WAAW,IAAI,CAAC,eAAe,CAAC;gBACpC,IAAI,aAAa,CAAC,0PAAA,CAAA,mBAAgB,CAAC,KAAK,KAAK;oBACzC,OAAO,0PAAA,CAAA,mBAAgB;gBAC3B;gBACA,IAAI,aAAa,GAAG;oBAChB,IAAI,IAAI,CAAC,0PAAA,CAAA,qBAAkB,CAAC,GAAG,GAAG;wBAC9B,OAAO,0PAAA,CAAA,qBAAkB;oBAC7B;oBACA,IAAI,IAAI,CAAC,0PAAA,CAAA,qBAAkB,CAAC,GAAG,GAAG;wBAC9B,OAAO,0PAAA,CAAA,qBAAkB;oBAC7B;oBACA,IAAI,IAAI,CAAC,0PAAA,CAAA,kBAAe,CAAC,GAAG,GAAG;wBAC3B,OAAO,0PAAA,CAAA,kBAAe;oBAC1B;oBACA,IAAI,IAAI,CAAC,0PAAA,CAAA,iBAAc,CAAC,GAAG,GAAG;wBAC1B,OAAO,0PAAA,CAAA,iBAAc;oBACzB;gBACJ;gBACA,OAAO,0PAAA,CAAA,iBAAc;YACzB;YACA,IAAI,IAAI,IAAI,UAAU,CAAC,WAAW;YAClC;YACA,SAAS;YACT,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI;gBACjB,UAAU,CAAC,0PAAA,CAAA,mBAAgB,CAAC,IAAI;YACpC,OACK,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI;gBAC9B,UAAU,CAAC,0PAAA,CAAA,mBAAgB,CAAC,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC,0PAAA,CAAA,mBAAgB,CAAC;gBACrE,UAAU,CAAC,0PAAA,CAAA,mBAAgB,CAAC,IAAI;YACpC,OACK;gBACD,UAAU,CAAC,0PAAA,CAAA,mBAAgB,CAAC,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC,0PAAA,CAAA,mBAAgB,CAAC;gBACrE,UAAU,CAAC,0PAAA,CAAA,mBAAgB,CAAC;YAChC;YACA,SAAS;YACT,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI;gBACrB,UAAU,CAAC,0PAAA,CAAA,iBAAc,CAAC,IAAI,MAAM;YACxC,OACK,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI;gBAC9B,UAAU,CAAC,0PAAA,CAAA,iBAAc,CAAC,IAAI,MAAM;YACxC,OACK;gBACD,UAAU,CAAC,0PAAA,CAAA,iBAAc,CAAC,IAAI,MAAM;YACxC;YACA,SAAS;YACT,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI;gBACtB,UAAU,CAAC,0PAAA,CAAA,kBAAe,CAAC,IAAI,MAAM;YACzC,OACK,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI;gBAC9B,UAAU,CAAC,0PAAA,CAAA,kBAAe,CAAC,IAAI,MAAM;YACzC,OACK;gBACD,UAAU,CAAC,0PAAA,CAAA,kBAAe,CAAC,IAAI,MAAM;YACzC;YACA,SAAS;YACT,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI;gBACrB,UAAU,CAAC,0PAAA,CAAA,iBAAc,CAAC,IAAI,MAAM;YACxC,OACK,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI;gBAC9B,UAAU,CAAC,0PAAA,CAAA,iBAAc,CAAC,IAAI,OAAO;YACzC,OACK;gBACD,UAAU,CAAC,0PAAA,CAAA,iBAAc,CAAC,IAAI,OAAO;YACzC;YACA,SAAS;YACT,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI;gBACzB,UAAU,CAAC,0PAAA,CAAA,qBAAkB,CAAC,IAAI,MAAM;YAC5C,OACK,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI;gBAC9B,UAAU,CAAC,0PAAA,CAAA,qBAAkB,CAAC,IAAI,OAAO;YAC7C,OACK;gBACD,UAAU,CAAC,0PAAA,CAAA,qBAAkB,CAAC,IAAI,OAAO;YAC7C;YACA,SAAS;YACT,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI;gBACvB,UAAU,CAAC,0PAAA,CAAA,qBAAkB,CAAC,IAAI;YACtC,OACK;gBACD,UAAU,CAAC,0PAAA,CAAA,qBAAkB,CAAC;YAClC;YACA,SAAS;YACT,IAAI,kBAAkB,GAAG;gBACrB,sOAAA,CAAA,UAAM,CAAC,IAAI,CAAC,MAAM;gBAClB,sOAAA,CAAA,UAAM,CAAC,IAAI,CAAC,eAAe;gBAC3B,IAAI,CAAC,YAAY,CAAC,YAAY,eAAe,uOAAA,CAAA,UAAO,CAAC,SAAS,EAAE;gBAChE,IAAI,aAAa,CAAC,0PAAA,CAAA,mBAAgB,CAAC,GAC/B,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,0PAAA,CAAA,qBAAkB,CAAC,EAAE,aAAa,CAAC,0PAAA,CAAA,iBAAc,CAAC,EAAE,aAAa,CAAC,0PAAA,CAAA,kBAAe,CAAC,EAAE,aAAa,CAAC,0PAAA,CAAA,iBAAc,CAAC,EAAE,aAAa,CAAC,0PAAA,CAAA,qBAAkB,CAAC,GAAG;oBAC9K,OAAO,0PAAA,CAAA,mBAAgB;gBAC3B;gBACA,IAAI,aAAa,CAAC,0PAAA,CAAA,qBAAkB,CAAC,GAAG,aAAa,CAAC,0PAAA,CAAA,mBAAgB,CAAC,IACnE,aAAa,CAAC,0PAAA,CAAA,qBAAkB,CAAC,GAAG,IAChC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,0PAAA,CAAA,iBAAc,CAAC,EAAE,aAAa,CAAC,0PAAA,CAAA,kBAAe,CAAC,EAAE,aAAa,CAAC,0PAAA,CAAA,iBAAc,CAAC,EAAE,aAAa,CAAC,0PAAA,CAAA,qBAAkB,CAAC,GAAG;oBAC/I,OAAO,0PAAA,CAAA,qBAAkB;gBAC7B;gBACA,IAAI,aAAa,CAAC,0PAAA,CAAA,qBAAkB,CAAC,GAAG,IACpC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,0PAAA,CAAA,qBAAkB,CAAC,EAAE,aAAa,CAAC,0PAAA,CAAA,iBAAc,CAAC,EAAE,aAAa,CAAC,0PAAA,CAAA,kBAAe,CAAC,EAAE,aAAa,CAAC,0PAAA,CAAA,iBAAc,CAAC,EAAE,aAAa,CAAC,0PAAA,CAAA,mBAAgB,CAAC,GAAG;oBAC5K,OAAO,0PAAA,CAAA,qBAAkB;gBAC7B;gBACA,IAAI,aAAa,CAAC,0PAAA,CAAA,kBAAe,CAAC,GAAG,IACjC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,0PAAA,CAAA,qBAAkB,CAAC,EAAE,aAAa,CAAC,0PAAA,CAAA,iBAAc,CAAC,EAAE,aAAa,CAAC,0PAAA,CAAA,qBAAkB,CAAC,EAAE,aAAa,CAAC,0PAAA,CAAA,iBAAc,CAAC,EAAE,aAAa,CAAC,0PAAA,CAAA,mBAAgB,CAAC,GAAG;oBAC/K,OAAO,0PAAA,CAAA,kBAAe;gBAC1B;gBACA,IAAI,aAAa,CAAC,0PAAA,CAAA,iBAAc,CAAC,GAAG,IAChC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,0PAAA,CAAA,qBAAkB,CAAC,EAAE,aAAa,CAAC,0PAAA,CAAA,iBAAc,CAAC,EAAE,aAAa,CAAC,0PAAA,CAAA,qBAAkB,CAAC,EAAE,aAAa,CAAC,0PAAA,CAAA,kBAAe,CAAC,EAAE,aAAa,CAAC,0PAAA,CAAA,mBAAgB,CAAC,GAAG;oBAChL,OAAO,0PAAA,CAAA,iBAAc;gBACzB;gBACA,IAAI,aAAa,CAAC,0PAAA,CAAA,iBAAc,CAAC,GAAG,IAChC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,0PAAA,CAAA,mBAAgB,CAAC,EAAE,aAAa,CAAC,0PAAA,CAAA,qBAAkB,CAAC,EAAE,aAAa,CAAC,0PAAA,CAAA,qBAAkB,CAAC,EAAE,aAAa,CAAC,0PAAA,CAAA,kBAAe,CAAC,GAAG;oBACjJ,IAAI,aAAa,CAAC,0PAAA,CAAA,iBAAc,CAAC,GAAG,aAAa,CAAC,0PAAA,CAAA,iBAAc,CAAC,EAAE;wBAC/D,OAAO,0PAAA,CAAA,iBAAc;oBACzB;oBACA,IAAI,aAAa,CAAC,0PAAA,CAAA,iBAAc,CAAC,KAAK,aAAa,CAAC,0PAAA,CAAA,iBAAc,CAAC,EAAE;wBACjE,IAAI,IAAI,WAAW,iBAAiB;wBACpC,MAAO,IAAI,IAAI,MAAM,CAAE;4BACnB,IAAI,KAAK,IAAI,UAAU,CAAC;4BACxB,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK;gCACvB,OAAO,0PAAA,CAAA,iBAAc;4BACzB;4BACA,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK;gCACvB;4BACJ;4BACA;wBACJ;wBACA,OAAO,0PAAA,CAAA,iBAAc;oBACzB;gBACJ;YACJ;QACJ;IACJ;IACA,iBAAiB,GAAG,GAAG,SAAU,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;QAC/C,IAAI,MAAM,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI;QACjD,IAAI,OAAO,WAAW;YAClB,OAAO;QACX,OACK;YACD,OAAO,KAAK,GAAG,CAAC,KAAK;QACzB;IACJ;IACA,iBAAiB,YAAY,GAAG,SAAU,UAAU,EAAE,aAAa,EAAE,GAAG,EAAE,IAAI;QAC1E,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YACxB,IAAI,UAAW,aAAa,CAAC,EAAE,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC,EAAE;YACzD,IAAI,MAAM,SAAS;gBACf,MAAM;gBACN,sOAAA,CAAA,UAAM,CAAC,IAAI,CAAC,MAAM;YACtB;YACA,IAAI,QAAQ,SAAS;gBACjB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG;YACxB;QACJ;QACA,OAAO;IACX;IACA,iBAAiB,eAAe,GAAG,SAAU,IAAI;QAC7C,IAAI,WAAW;QACf,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YACxB,YAAY,IAAI,CAAC,EAAE;QACvB;QACA,OAAO,YAAY;IACvB;IACA,iBAAiB,OAAO,GAAG,SAAU,EAAE;QACnC,OAAO,MAAM,IAAI,UAAU,CAAC,MAAM,MAAM,IAAI,UAAU,CAAC;IAC3D;IACA,iBAAiB,eAAe,GAAG,SAAU,EAAE;QAC3C,OAAO,MAAM,OAAO,MAAM;IAC9B;IACA,iBAAiB,WAAW,GAAG,SAAU,EAAE;QACvC,OAAQ,OAAO,IAAI,UAAU,CAAC,MACzB,MAAM,IAAI,UAAU,CAAC,MAAM,MAAM,IAAI,UAAU,CAAC,MAChD,MAAM,IAAI,UAAU,CAAC,MAAM,MAAM,IAAI,UAAU,CAAC;IACzD;IACA,iBAAiB,YAAY,GAAG,SAAU,EAAE;QACxC,OAAQ,OAAO,IAAI,UAAU,CAAC,MACzB,MAAM,IAAI,UAAU,CAAC,MAAM,MAAM,IAAI,UAAU,CAAC,MAChD,MAAM,IAAI,UAAU,CAAC,MAAM,MAAM,IAAI,UAAU,CAAC;IACzD;IACA,iBAAiB,WAAW,GAAG,SAAU,EAAE;QACvC,OAAQ,IAAI,CAAC,YAAY,CAAC,OACtB,OAAO,IAAI,UAAU,CAAC,MACrB,MAAM,IAAI,UAAU,CAAC,MAAM,MAAM,IAAI,UAAU,CAAC,MAChD,MAAM,IAAI,UAAU,CAAC,MAAM,MAAM,IAAI,UAAU,CAAC;IACzD;IACA,iBAAiB,YAAY,GAAG,SAAU,EAAE;QACxC,OAAQ,OAAO,MAAM,KAAK;QACtB,OAAO,IAAI,UAAU,CAAC,MACtB,OAAO,IAAI,UAAU,CAAC;IAC9B;IACA,iBAAiB,eAAe,GAAG,SAAU,EAAE;QAC3C,OAAO,MAAM,IAAI,UAAU,CAAC,MAAM,MAAM,IAAI,UAAU,CAAC;IAC3D;IACA,iBAAiB,aAAa,GAAG,SAAU,EAAE;QACzC,OAAO,OAAO,8BAA8B;IAChD;IACA;;;;;;KAMC,GACD,iBAAiB,8BAA8B,GAAG,SAAU,GAAG,EAAE,QAAQ;QACrE,IAAI,aAAa,KAAK,GAAG;YAAE,WAAW;QAAG;QACzC,IAAI,MAAM,IAAI,MAAM;QACpB,IAAI,MAAM;QACV,MAAO,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,UAAU,CAAC,MAAO;YACnD;QACJ;QACA,OAAO,MAAM;IACjB;IACA,iBAAiB,gBAAgB,GAAG,SAAU,eAAe;QACzD,IAAI,MAAM,uOAAA,CAAA,UAAO,CAAC,WAAW,CAAC,gBAAgB,UAAU,CAAC;QACzD,MAAM,OAAO,SAAS,CAAC,GAAG,IAAI,IAAI,MAAM,IAAI;QAC5C,MAAM,IAAI,MAAM,wBAAwB,kBAAkB,SAAS,MAAM;IAC7E;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4181, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/core/datamatrix/encoder/MinimalEncoder.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spread = (this && this.__spread) || function () {\n    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));\n    return ar;\n};\nimport { MACRO_05_HEADER, MACRO_06_HEADER, MACRO_TRAILER, } from './constants';\nimport HighLevelEncoder from './HighLevelEncoder';\nimport { MinimalECIInput } from '../../common/MinimalECIInput';\nimport Integer from '../../util/Integer';\nvar Mode;\n(function (Mode) {\n    Mode[Mode[\"ASCII\"] = 0] = \"ASCII\";\n    Mode[Mode[\"C40\"] = 1] = \"C40\";\n    Mode[Mode[\"TEXT\"] = 2] = \"TEXT\";\n    Mode[Mode[\"X12\"] = 3] = \"X12\";\n    Mode[Mode[\"EDF\"] = 4] = \"EDF\";\n    Mode[Mode[\"B256\"] = 5] = \"B256\";\n})(Mode || (Mode = {}));\nvar C40_SHIFT2_CHARS = [\n    '!',\n    '\"',\n    '#',\n    '$',\n    '%',\n    '&',\n    \"'\",\n    '(',\n    ')',\n    '*',\n    '+',\n    ',',\n    '-',\n    '.',\n    '/',\n    ':',\n    ';',\n    '<',\n    '=',\n    '>',\n    '?',\n    '@',\n    '[',\n    '\\\\',\n    ']',\n    '^',\n    '_',\n];\nvar MinimalEncoder = /** @class */ (function () {\n    function MinimalEncoder() {\n    }\n    MinimalEncoder.isExtendedASCII = function (ch, fnc1) {\n        return ch !== fnc1 && ch >= 128 && ch <= 255;\n    };\n    MinimalEncoder.isInC40Shift1Set = function (ch) {\n        return ch <= 31;\n    };\n    MinimalEncoder.isInC40Shift2Set = function (ch, fnc1) {\n        var e_1, _a;\n        try {\n            for (var C40_SHIFT2_CHARS_1 = __values(C40_SHIFT2_CHARS), C40_SHIFT2_CHARS_1_1 = C40_SHIFT2_CHARS_1.next(); !C40_SHIFT2_CHARS_1_1.done; C40_SHIFT2_CHARS_1_1 = C40_SHIFT2_CHARS_1.next()) {\n                var c40Shift2Char = C40_SHIFT2_CHARS_1_1.value;\n                if (c40Shift2Char.charCodeAt(0) === ch) {\n                    return true;\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (C40_SHIFT2_CHARS_1_1 && !C40_SHIFT2_CHARS_1_1.done && (_a = C40_SHIFT2_CHARS_1.return)) _a.call(C40_SHIFT2_CHARS_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return ch === fnc1;\n    };\n    MinimalEncoder.isInTextShift1Set = function (ch) {\n        return this.isInC40Shift1Set(ch);\n    };\n    MinimalEncoder.isInTextShift2Set = function (ch, fnc1) {\n        return this.isInC40Shift2Set(ch, fnc1);\n    };\n    /**\n     * Performs message encoding of a DataMatrix message\n     *\n     * @param msg the message\n     * @param priorityCharset The preferred {@link Charset}. When the value of the argument is null, the algorithm\n     *   chooses charsets that leads to a minimal representation. Otherwise the algorithm will use the priority\n     *   charset to encode any character in the input that can be encoded by it if the charset is among the\n     *   supported charsets.\n     * @param fnc1 denotes the character in the input that represents the FNC1 character or -1 if this is not a GS1\n     *   bar code. If the value is not -1 then a FNC1 is also prepended.\n     * @param shape requested shape.\n     * @return the encoded message (the char values range from 0 to 255)\n     */\n    MinimalEncoder.encodeHighLevel = function (msg, priorityCharset, fnc1, shape) {\n        if (priorityCharset === void 0) { priorityCharset = null; }\n        if (fnc1 === void 0) { fnc1 = -1; }\n        if (shape === void 0) { shape = 0 /* FORCE_NONE */; }\n        var macroId = 0;\n        if (msg.startsWith(MACRO_05_HEADER) && msg.endsWith(MACRO_TRAILER)) {\n            macroId = 5;\n            msg = msg.substring(MACRO_05_HEADER.length, msg.length - 2);\n        }\n        else if (msg.startsWith(MACRO_06_HEADER) && msg.endsWith(MACRO_TRAILER)) {\n            macroId = 6;\n            msg = msg.substring(MACRO_06_HEADER.length, msg.length - 2);\n        }\n        return decodeURIComponent(escape(String.fromCharCode.apply(String, __spread(this.encode(msg, priorityCharset, fnc1, shape, macroId)))));\n    };\n    /**\n     * Encodes input minimally and returns an array of the codewords\n     *\n     * @param input The string to encode\n     * @param priorityCharset The preferred {@link Charset}. When the value of the argument is null, the algorithm\n     *   chooses charsets that leads to a minimal representation. Otherwise the algorithm will use the priority\n     *   charset to encode any character in the input that can be encoded by it if the charset is among the\n     *   supported charsets.\n     * @param fnc1 denotes the character in the input that represents the FNC1 character or -1 if this is not a GS1\n     *   bar code. If the value is not -1 then a FNC1 is also prepended.\n     * @param shape requested shape.\n     * @param macroId Prepends the specified macro function in case that a value of 5 or 6 is specified.\n     * @return An array of bytes representing the codewords of a minimal encoding.\n     */\n    MinimalEncoder.encode = function (input, priorityCharset, fnc1, shape, macroId) {\n        return this.encodeMinimally(new Input(input, priorityCharset, fnc1, shape, macroId)).getBytes();\n    };\n    MinimalEncoder.addEdge = function (edges, edge) {\n        var vertexIndex = edge.fromPosition + edge.characterLength;\n        if (edges[vertexIndex][edge.getEndMode()] === null ||\n            edges[vertexIndex][edge.getEndMode()].cachedTotalSize >\n                edge.cachedTotalSize) {\n            edges[vertexIndex][edge.getEndMode()] = edge;\n        }\n    };\n    /** @return the number of words in which the string starting at from can be encoded in c40 or text mode.\n     *  The number of characters encoded is returned in characterLength.\n     *  The number of characters encoded is also minimal in the sense that the algorithm stops as soon\n     *  as a character encoding fills a C40 word competely (three C40 values). An exception is at the\n     *  end of the string where two C40 values are allowed (according to the spec the third c40 value\n     *  is filled  with 0 (Shift 1) in this case).\n     */\n    MinimalEncoder.getNumberOfC40Words = function (input, from, c40, characterLength) {\n        var thirdsCount = 0;\n        for (var i = from; i < input.length(); i++) {\n            if (input.isECI(i)) {\n                characterLength[0] = 0;\n                return 0;\n            }\n            var ci = input.charAt(i);\n            if ((c40 && HighLevelEncoder.isNativeC40(ci)) ||\n                (!c40 && HighLevelEncoder.isNativeText(ci))) {\n                thirdsCount++; // native\n            }\n            else if (!MinimalEncoder.isExtendedASCII(ci, input.getFNC1Character())) {\n                thirdsCount += 2; // shift\n            }\n            else {\n                var asciiValue = ci & 0xff;\n                if (asciiValue >= 128 &&\n                    ((c40 && HighLevelEncoder.isNativeC40(asciiValue - 128)) ||\n                        (!c40 && HighLevelEncoder.isNativeText(asciiValue - 128)))) {\n                    thirdsCount += 3; // shift, Upper shift\n                }\n                else {\n                    thirdsCount += 4; // shift, Upper shift, shift\n                }\n            }\n            if (thirdsCount % 3 === 0 ||\n                ((thirdsCount - 2) % 3 === 0 && i + 1 === input.length())) {\n                characterLength[0] = i - from + 1;\n                return Math.ceil(thirdsCount / 3.0);\n            }\n        }\n        characterLength[0] = 0;\n        return 0;\n    };\n    MinimalEncoder.addEdges = function (input, edges, from, previous) {\n        var e_2, _a;\n        if (input.isECI(from)) {\n            this.addEdge(edges, new Edge(input, Mode.ASCII, from, 1, previous));\n            return;\n        }\n        var ch = input.charAt(from);\n        if (previous === null || previous.getEndMode() !== Mode.EDF) {\n            // not possible to unlatch a full EDF edge to something\n            // else\n            if (HighLevelEncoder.isDigit(ch) &&\n                input.haveNCharacters(from, 2) &&\n                HighLevelEncoder.isDigit(input.charAt(from + 1))) {\n                // two digits ASCII encoded\n                this.addEdge(edges, new Edge(input, Mode.ASCII, from, 2, previous));\n            }\n            else {\n                // one ASCII encoded character or an extended character via Upper Shift\n                this.addEdge(edges, new Edge(input, Mode.ASCII, from, 1, previous));\n            }\n            var modes = [Mode.C40, Mode.TEXT];\n            try {\n                for (var modes_1 = __values(modes), modes_1_1 = modes_1.next(); !modes_1_1.done; modes_1_1 = modes_1.next()) {\n                    var mode = modes_1_1.value;\n                    var characterLength = [];\n                    if (MinimalEncoder.getNumberOfC40Words(input, from, mode === Mode.C40, characterLength) > 0) {\n                        this.addEdge(edges, new Edge(input, mode, from, characterLength[0], previous));\n                    }\n                }\n            }\n            catch (e_2_1) { e_2 = { error: e_2_1 }; }\n            finally {\n                try {\n                    if (modes_1_1 && !modes_1_1.done && (_a = modes_1.return)) _a.call(modes_1);\n                }\n                finally { if (e_2) throw e_2.error; }\n            }\n            if (input.haveNCharacters(from, 3) &&\n                HighLevelEncoder.isNativeX12(input.charAt(from)) &&\n                HighLevelEncoder.isNativeX12(input.charAt(from + 1)) &&\n                HighLevelEncoder.isNativeX12(input.charAt(from + 2))) {\n                this.addEdge(edges, new Edge(input, Mode.X12, from, 3, previous));\n            }\n            this.addEdge(edges, new Edge(input, Mode.B256, from, 1, previous));\n        }\n        // We create 4 EDF edges,  with 1, 2 3 or 4 characters length. The fourth normally doesn't have a latch to ASCII\n        // unless it is 2 characters away from the end of the input.\n        var i;\n        for (i = 0; i < 3; i++) {\n            var pos = from + i;\n            if (input.haveNCharacters(pos, 1) &&\n                HighLevelEncoder.isNativeEDIFACT(input.charAt(pos))) {\n                this.addEdge(edges, new Edge(input, Mode.EDF, from, i + 1, previous));\n            }\n            else {\n                break;\n            }\n        }\n        if (i === 3 &&\n            input.haveNCharacters(from, 4) &&\n            HighLevelEncoder.isNativeEDIFACT(input.charAt(from + 3))) {\n            this.addEdge(edges, new Edge(input, Mode.EDF, from, 4, previous));\n        }\n    };\n    MinimalEncoder.encodeMinimally = function (input) {\n        /* The minimal encoding is computed by Dijkstra. The acyclic graph is modeled as follows:\n         * A vertex represents a combination of a position in the input and an encoding mode where position 0\n         * denotes the position left of the first character, 1 the position left of the second character and so on.\n         * Likewise the end vertices are located after the last character at position input.length().\n         * For any position there might be up to six vertices, one for each of the encoding types ASCII, C40, TEXT, X12,\n         * EDF and B256.\n         *\n         * As an example consider the input string \"ABC123\" then at position 0 there is only one vertex with the default\n         * ASCII encodation. At position 3 there might be vertices for the types ASCII, C40, X12, EDF and B256.\n         *\n         * An edge leading to such a vertex encodes one or more of the characters left of the position that the vertex\n         * represents. It encodes the characters in the encoding mode of the vertex that it ends on. In other words,\n         * all edges leading to a particular vertex encode the same characters (the length of the suffix can vary) using the same\n         * encoding mode.\n         * As an example consider the input string \"ABC123\" and the vertex (4,EDF). Possible edges leading to this vertex\n         * are:\n         *   (0,ASCII)  --EDF(ABC1)--> (4,EDF)\n         *   (1,ASCII)  --EDF(BC1)-->  (4,EDF)\n         *   (1,B256)   --EDF(BC1)-->  (4,EDF)\n         *   (1,EDF)    --EDF(BC1)-->  (4,EDF)\n         *   (2,ASCII)  --EDF(C1)-->   (4,EDF)\n         *   (2,B256)   --EDF(C1)-->   (4,EDF)\n         *   (2,EDF)    --EDF(C1)-->   (4,EDF)\n         *   (3,ASCII)  --EDF(1)-->    (4,EDF)\n         *   (3,B256)   --EDF(1)-->    (4,EDF)\n         *   (3,EDF)    --EDF(1)-->    (4,EDF)\n         *   (3,C40)    --EDF(1)-->    (4,EDF)\n         *   (3,X12)    --EDF(1)-->    (4,EDF)\n         *\n         * The edges leading to a vertex are stored in such a way that there is a fast way to enumerate the edges ending\n         * on a particular vertex.\n         *\n         * The algorithm processes the vertices in order of their position thereby performing the following:\n         *\n         * For every vertex at position i the algorithm enumerates the edges ending on the vertex and removes all but the\n         * shortest from that list.\n         * Then it processes the vertices for the position i+1. If i+1 == input.length() then the algorithm ends\n         * and chooses the the edge with the smallest size from any of the edges leading to vertices at this position.\n         * Otherwise the algorithm computes all possible outgoing edges for the vertices at the position i+1\n         *\n         * Examples:\n         * The process is illustrated by showing the graph (edges) after each iteration from left to right over the input:\n         * An edge is drawn as follows \"(\" + fromVertex + \") -- \" + encodingMode + \"(\" + encodedInput + \") (\" +\n         * accumulatedSize + \") --> (\" + toVertex + \")\"\n         *\n         * Example 1 encoding the string \"ABCDEFG\":\n         *\n         *\n         * Situation after adding edges to the start vertex (0,ASCII)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII)\n         * (0,ASCII) B256(A) (3) --> (1,B256)\n         * (0,ASCII) EDF(AB) (4) --> (2,EDF)\n         * (0,ASCII) C40(ABC) (3) --> (3,C40)\n         * (0,ASCII) TEXT(ABC) (5) --> (3,TEXT)\n         * (0,ASCII) X12(ABC) (3) --> (3,X12)\n         * (0,ASCII) EDF(ABC) (4) --> (3,EDF)\n         * (0,ASCII) EDF(ABCD) (4) --> (4,EDF)\n         *\n         * Situation after adding edges to vertices at position 1\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII)\n         * (0,ASCII) B256(A) (3) --> (1,B256)\n         * (0,ASCII) EDF(AB) (4) --> (2,EDF)\n         * (0,ASCII) C40(ABC) (3) --> (3,C40)\n         * (0,ASCII) TEXT(ABC) (5) --> (3,TEXT)\n         * (0,ASCII) X12(ABC) (3) --> (3,X12)\n         * (0,ASCII) EDF(ABC) (4) --> (3,EDF)\n         * (0,ASCII) EDF(ABCD) (4) --> (4,EDF)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) B256(B) (4) --> (2,B256)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) EDF(BC) (5) --> (3,EDF)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) C40(BCD) (4) --> (4,C40)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) TEXT(BCD) (6) --> (4,TEXT)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) X12(BCD) (4) --> (4,X12)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) EDF(BCD) (5) --> (4,EDF)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) EDF(BCDE) (5) --> (5,EDF)\n         * (0,ASCII) B256(A) (3) --> (1,B256) ASCII(B) (4) --> (2,ASCII)\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256)\n         * (0,ASCII) B256(A) (3) --> (1,B256) EDF(BC) (6) --> (3,EDF)\n         * (0,ASCII) B256(A) (3) --> (1,B256) C40(BCD) (5) --> (4,C40)\n         * (0,ASCII) B256(A) (3) --> (1,B256) TEXT(BCD) (7) --> (4,TEXT)\n         * (0,ASCII) B256(A) (3) --> (1,B256) X12(BCD) (5) --> (4,X12)\n         * (0,ASCII) B256(A) (3) --> (1,B256) EDF(BCD) (6) --> (4,EDF)\n         * (0,ASCII) B256(A) (3) --> (1,B256) EDF(BCDE) (6) --> (5,EDF)\n         *\n         * Edge \"(1,ASCII) ASCII(B) (2) --> (2,ASCII)\" is minimal for the vertex (2,ASCII) so that edge \"(1,B256) ASCII(B) (4) --> (2,ASCII)\" is removed.\n         * Edge \"(1,B256) B256(B) (3) --> (2,B256)\" is minimal for the vertext (2,B256) so that the edge \"(1,ASCII) B256(B) (4) --> (2,B256)\" is removed.\n         *\n         * Situation after adding edges to vertices at position 2\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII)\n         * (0,ASCII) B256(A) (3) --> (1,B256)\n         * (0,ASCII) EDF(AB) (4) --> (2,EDF)\n         * (0,ASCII) C40(ABC) (3) --> (3,C40)\n         * (0,ASCII) TEXT(ABC) (5) --> (3,TEXT)\n         * (0,ASCII) X12(ABC) (3) --> (3,X12)\n         * (0,ASCII) EDF(ABC) (4) --> (3,EDF)\n         * (0,ASCII) EDF(ABCD) (4) --> (4,EDF)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) EDF(BC) (5) --> (3,EDF)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) C40(BCD) (4) --> (4,C40)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) TEXT(BCD) (6) --> (4,TEXT)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) X12(BCD) (4) --> (4,X12)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) EDF(BCD) (5) --> (4,EDF)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) EDF(BCDE) (5) --> (5,EDF)\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256)\n         * (0,ASCII) B256(A) (3) --> (1,B256) EDF(BC) (6) --> (3,EDF)\n         * (0,ASCII) B256(A) (3) --> (1,B256) C40(BCD) (5) --> (4,C40)\n         * (0,ASCII) B256(A) (3) --> (1,B256) TEXT(BCD) (7) --> (4,TEXT)\n         * (0,ASCII) B256(A) (3) --> (1,B256) X12(BCD) (5) --> (4,X12)\n         * (0,ASCII) B256(A) (3) --> (1,B256) EDF(BCD) (6) --> (4,EDF)\n         * (0,ASCII) B256(A) (3) --> (1,B256) EDF(BCDE) (6) --> (5,EDF)\n         * (0,ASCII) EDF(AB) (4) --> (2,EDF) ASCII(C) (5) --> (3,ASCII)\n         * (0,ASCII) EDF(AB) (4) --> (2,EDF) B256(C) (6) --> (3,B256)\n         * (0,ASCII) EDF(AB) (4) --> (2,EDF) EDF(CD) (7) --> (4,EDF)\n         * (0,ASCII) EDF(AB) (4) --> (2,EDF) C40(CDE) (6) --> (5,C40)\n         * (0,ASCII) EDF(AB) (4) --> (2,EDF) TEXT(CDE) (8) --> (5,TEXT)\n         * (0,ASCII) EDF(AB) (4) --> (2,EDF) X12(CDE) (6) --> (5,X12)\n         * (0,ASCII) EDF(AB) (4) --> (2,EDF) EDF(CDE) (7) --> (5,EDF)\n         * (0,ASCII) EDF(AB) (4) --> (2,EDF) EDF(CDEF) (7) --> (6,EDF)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) B256(C) (5) --> (3,B256)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) EDF(CD) (6) --> (4,EDF)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) C40(CDE) (5) --> (5,C40)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) TEXT(CDE) (7) --> (5,TEXT)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) X12(CDE) (5) --> (5,X12)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) EDF(CDE) (6) --> (5,EDF)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) EDF(CDEF) (6) --> (6,EDF)\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) ASCII(C) (4) --> (3,ASCII)\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) B256(C) (4) --> (3,B256)\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) EDF(CD) (6) --> (4,EDF)\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) C40(CDE) (5) --> (5,C40)\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) TEXT(CDE) (7) --> (5,TEXT)\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) X12(CDE) (5) --> (5,X12)\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) EDF(CDE) (6) --> (5,EDF)\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) EDF(CDEF) (6) --> (6,EDF)\n         *\n         * Edge \"(2,ASCII) ASCII(C) (3) --> (3,ASCII)\" is minimal for the vertex (3,ASCII) so that edges \"(2,EDF) ASCII(C) (5) --> (3,ASCII)\"\n         * and \"(2,B256) ASCII(C) (4) --> (3,ASCII)\" can be removed.\n         * Edge \"(0,ASCII) EDF(ABC) (4) --> (3,EDF)\" is minimal for the vertex (3,EDF) so that edges \"(1,ASCII) EDF(BC) (5) --> (3,EDF)\"\n         * and \"(1,B256) EDF(BC) (6) --> (3,EDF)\" can be removed.\n         * Edge \"(2,B256) B256(C) (4) --> (3,B256)\" is minimal for the vertex (3,B256) so that edges \"(2,ASCII) B256(C) (5) --> (3,B256)\"\n         * and \"(2,EDF) B256(C) (6) --> (3,B256)\" can be removed.\n         *\n         * This continues for vertices 3 thru 7\n         *\n         * Situation after adding edges to vertices at position 7\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII)\n         * (0,ASCII) B256(A) (3) --> (1,B256)\n         * (0,ASCII) EDF(AB) (4) --> (2,EDF)\n         * (0,ASCII) C40(ABC) (3) --> (3,C40)\n         * (0,ASCII) TEXT(ABC) (5) --> (3,TEXT)\n         * (0,ASCII) X12(ABC) (3) --> (3,X12)\n         * (0,ASCII) EDF(ABC) (4) --> (3,EDF)\n         * (0,ASCII) EDF(ABCD) (4) --> (4,EDF)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) C40(BCD) (4) --> (4,C40)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) TEXT(BCD) (6) --> (4,TEXT)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) X12(BCD) (4) --> (4,X12)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) EDF(BCDE) (5) --> (5,EDF)\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256)\n         * (0,ASCII) C40(ABC) (3) --> (3,C40) C40(DEF) (5) --> (6,C40)\n         * (0,ASCII) X12(ABC) (3) --> (3,X12) X12(DEF) (5) --> (6,X12)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) C40(CDE) (5) --> (5,C40)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) TEXT(CDE) (7) --> (5,TEXT)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) X12(CDE) (5) --> (5,X12)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) EDF(CDEF) (6) --> (6,EDF)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) C40(BCD) (4) --> (4,C40) C40(EFG) (6) --> (7,C40)    //Solution 1\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) X12(BCD) (4) --> (4,X12) X12(EFG) (6) --> (7,X12)    //Solution 2\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) B256(C) (4) --> (3,B256)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII) ASCII(D) (4) --> (4,ASCII)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII) TEXT(DEF) (8) --> (6,TEXT)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII) EDF(DEFG) (7) --> (7,EDF)\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) B256(C) (4) --> (3,B256) B256(D) (5) --> (4,B256)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII) ASCII(D) (4) --> (4,ASCII) ASCII(E) (5) --> (5,ASCII)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII) ASCII(D) (4) --> (4,ASCII) TEXT(EFG) (9) --> (7,TEXT)\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) B256(C) (4) --> (3,B256) B256(D) (5) --> (4,B256) B256(E) (6) --> (5,B256)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII) ASCII(D) (4) --> (4,ASCII) ASCII(E) (5) --> (5,ASCII) ASCII(F) (6) --> (6,ASCII)\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) B256(C) (4) --> (3,B256) B256(D) (5) --> (4,B256) B256(E) (6) --> (5,B256) B256(F) (7) --> (6,B256)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII) ASCII(D) (4) --> (4,ASCII) ASCII(E) (5) --> (5,ASCII) ASCII(F) (6) --> (6,ASCII) ASCII(G) (7) --> (7,ASCII)\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) B256(C) (4) --> (3,B256) B256(D) (5) --> (4,B256) B256(E) (6) --> (5,B256) B256(F) (7) --> (6,B256) B256(G) (8) --> (7,B256)\n         *\n         * Hence a minimal encoding of \"ABCDEFG\" is either ASCII(A),C40(BCDEFG) or ASCII(A), X12(BCDEFG) with a size of 5 bytes.\n         */\n        var inputLength = input.length();\n        // Array that represents vertices. There is a vertex for every character and mode.\n        // The last dimension in the array below encodes the 6 modes ASCII, C40, TEXT, X12, EDF and B256\n        var edges = Array(inputLength + 1)\n            .fill(null)\n            .map(function () { return Array(6).fill(0); });\n        this.addEdges(input, edges, 0, null);\n        for (var i = 1; i <= inputLength; i++) {\n            for (var j = 0; j < 6; j++) {\n                if (edges[i][j] !== null && i < inputLength) {\n                    this.addEdges(input, edges, i, edges[i][j]);\n                }\n            }\n            // optimize memory by removing edges that have been passed.\n            for (var j = 0; j < 6; j++) {\n                edges[i - 1][j] = null;\n            }\n        }\n        var minimalJ = -1;\n        var minimalSize = Integer.MAX_VALUE;\n        for (var j = 0; j < 6; j++) {\n            if (edges[inputLength][j] !== null) {\n                var edge = edges[inputLength][j];\n                var size = j >= 1 && j <= 3 ? edge.cachedTotalSize + 1 : edge.cachedTotalSize; // C40, TEXT and X12 need an\n                // extra unlatch at the end\n                if (size < minimalSize) {\n                    minimalSize = size;\n                    minimalJ = j;\n                }\n            }\n        }\n        if (minimalJ < 0) {\n            throw new Error('Failed to encode \"' + input + '\"');\n        }\n        return new Result(edges[inputLength][minimalJ]);\n    };\n    return MinimalEncoder;\n}());\nexport { MinimalEncoder };\nvar Result = /** @class */ (function () {\n    function Result(solution) {\n        var input = solution.input;\n        var size = 0;\n        var bytesAL = [];\n        var randomizePostfixLength = [];\n        var randomizeLengths = [];\n        if ((solution.mode === Mode.C40 ||\n            solution.mode === Mode.TEXT ||\n            solution.mode === Mode.X12) &&\n            solution.getEndMode() !== Mode.ASCII) {\n            size += this.prepend(Edge.getBytes(254), bytesAL);\n        }\n        var current = solution;\n        while (current !== null) {\n            size += this.prepend(current.getDataBytes(), bytesAL);\n            if (current.previous === null ||\n                current.getPreviousStartMode() !== current.getMode()) {\n                if (current.getMode() === Mode.B256) {\n                    if (size <= 249) {\n                        bytesAL.unshift(size);\n                        size++;\n                    }\n                    else {\n                        bytesAL.unshift(size % 250);\n                        bytesAL.unshift(size / 250 + 249);\n                        size += 2;\n                    }\n                    randomizePostfixLength.push(bytesAL.length);\n                    randomizeLengths.push(size);\n                }\n                this.prepend(current.getLatchBytes(), bytesAL);\n                size = 0;\n            }\n            current = current.previous;\n        }\n        if (input.getMacroId() === 5) {\n            size += this.prepend(Edge.getBytes(236), bytesAL);\n        }\n        else if (input.getMacroId() === 6) {\n            size += this.prepend(Edge.getBytes(237), bytesAL);\n        }\n        if (input.getFNC1Character() > 0) {\n            size += this.prepend(Edge.getBytes(232), bytesAL);\n        }\n        for (var i = 0; i < randomizePostfixLength.length; i++) {\n            this.applyRandomPattern(bytesAL, bytesAL.length - randomizePostfixLength[i], randomizeLengths[i]);\n        }\n        // add padding\n        var capacity = solution.getMinSymbolSize(bytesAL.length);\n        if (bytesAL.length < capacity) {\n            bytesAL.push(129);\n        }\n        while (bytesAL.length < capacity) {\n            bytesAL.push(this.randomize253State(bytesAL.length + 1));\n        }\n        this.bytes = new Uint8Array(bytesAL.length);\n        for (var i = 0; i < this.bytes.length; i++) {\n            this.bytes[i] = bytesAL[i];\n        }\n    }\n    Result.prototype.prepend = function (bytes, into) {\n        for (var i = bytes.length - 1; i >= 0; i--) {\n            into.unshift(bytes[i]);\n        }\n        return bytes.length;\n    };\n    Result.prototype.randomize253State = function (codewordPosition) {\n        var pseudoRandom = ((149 * codewordPosition) % 253) + 1;\n        var tempVariable = 129 + pseudoRandom;\n        return tempVariable <= 254 ? tempVariable : tempVariable - 254;\n    };\n    Result.prototype.applyRandomPattern = function (bytesAL, startPosition, length) {\n        for (var i = 0; i < length; i++) {\n            // See \"B.1 253-state algorithm\n            var Pad_codeword_position = startPosition + i;\n            var Pad_codeword_value = bytesAL[Pad_codeword_position] & 0xff;\n            var pseudo_random_number = ((149 * (Pad_codeword_position + 1)) % 255) + 1;\n            var temp_variable = Pad_codeword_value + pseudo_random_number;\n            bytesAL[Pad_codeword_position] =\n                temp_variable <= 255 ? temp_variable : temp_variable - 256;\n        }\n    };\n    Result.prototype.getBytes = function () {\n        return this.bytes;\n    };\n    return Result;\n}());\nvar Edge = /** @class */ (function () {\n    function Edge(input, mode, fromPosition, characterLength, previous) {\n        this.input = input;\n        this.mode = mode;\n        this.fromPosition = fromPosition;\n        this.characterLength = characterLength;\n        this.previous = previous;\n        this.allCodewordCapacities = [\n            3, 5, 8, 10, 12, 16, 18, 22, 30, 32, 36, 44, 49, 62, 86, 114, 144, 174, 204,\n            280, 368, 456, 576, 696, 816, 1050, 1304, 1558,\n        ];\n        this.squareCodewordCapacities = [\n            3, 5, 8, 12, 18, 22, 30, 36, 44, 62, 86, 114, 144, 174, 204, 280, 368, 456,\n            576, 696, 816, 1050, 1304, 1558,\n        ];\n        this.rectangularCodewordCapacities = [5, 10, 16, 33, 32, 49];\n        if (!(fromPosition + characterLength <= input.length())) {\n            throw new Error('Invalid edge');\n        }\n        var size = previous !== null ? previous.cachedTotalSize : 0;\n        var previousMode = this.getPreviousMode();\n        /*\n         * Switching modes\n         * ASCII -> C40: latch 230\n         * ASCII -> TEXT: latch 239\n         * ASCII -> X12: latch 238\n         * ASCII -> EDF: latch 240\n         * ASCII -> B256: latch 231\n         * C40 -> ASCII: word(c1,c2,c3), 254\n         * TEXT -> ASCII: word(c1,c2,c3), 254\n         * X12 -> ASCII: word(c1,c2,c3), 254\n         * EDIFACT -> ASCII: Unlatch character,0,0,0 or c1,Unlatch character,0,0 or c1,c2,Unlatch character,0 or\n         * c1,c2,c3,Unlatch character\n         * B256 -> ASCII: without latch after n bytes\n         */\n        switch (mode) {\n            case Mode.ASCII:\n                size++;\n                if (input.isECI(fromPosition) ||\n                    MinimalEncoder.isExtendedASCII(input.charAt(fromPosition), input.getFNC1Character())) {\n                    size++;\n                }\n                if (previousMode === Mode.C40 ||\n                    previousMode === Mode.TEXT ||\n                    previousMode === Mode.X12) {\n                    size++; // unlatch 254 to ASCII\n                }\n                break;\n            case Mode.B256:\n                size++;\n                if (previousMode !== Mode.B256) {\n                    size++; // byte count\n                }\n                else if (this.getB256Size() === 250) {\n                    size++; // extra byte count\n                }\n                if (previousMode === Mode.ASCII) {\n                    size++; // latch to B256\n                }\n                else if (previousMode === Mode.C40 ||\n                    previousMode === Mode.TEXT ||\n                    previousMode === Mode.X12) {\n                    size += 2; // unlatch to ASCII, latch to B256\n                }\n                break;\n            case Mode.C40:\n            case Mode.TEXT:\n            case Mode.X12:\n                if (mode === Mode.X12) {\n                    size += 2;\n                }\n                else {\n                    var charLen = [];\n                    size +=\n                        MinimalEncoder.getNumberOfC40Words(input, fromPosition, mode === Mode.C40, charLen) * 2;\n                }\n                if (previousMode === Mode.ASCII || previousMode === Mode.B256) {\n                    size++; // additional byte for latch from ASCII to this mode\n                }\n                else if (previousMode !== mode &&\n                    (previousMode === Mode.C40 ||\n                        previousMode === Mode.TEXT ||\n                        previousMode === Mode.X12)) {\n                    size += 2; // unlatch 254 to ASCII followed by latch to this mode\n                }\n                break;\n            case Mode.EDF:\n                size += 3;\n                if (previousMode === Mode.ASCII || previousMode === Mode.B256) {\n                    size++; // additional byte for latch from ASCII to this mode\n                }\n                else if (previousMode === Mode.C40 ||\n                    previousMode === Mode.TEXT ||\n                    previousMode === Mode.X12) {\n                    size += 2; // unlatch 254 to ASCII followed by latch to this mode\n                }\n                break;\n        }\n        this.cachedTotalSize = size;\n    }\n    // does not count beyond 250\n    Edge.prototype.getB256Size = function () {\n        var cnt = 0;\n        var current = this;\n        while (current !== null && current.mode === Mode.B256 && cnt <= 250) {\n            cnt++;\n            current = current.previous;\n        }\n        return cnt;\n    };\n    Edge.prototype.getPreviousStartMode = function () {\n        return this.previous === null ? Mode.ASCII : this.previous.mode;\n    };\n    Edge.prototype.getPreviousMode = function () {\n        return this.previous === null ? Mode.ASCII : this.previous.getEndMode();\n    };\n    /** Returns Mode.ASCII in case that:\n     *  - Mode is EDIFACT and characterLength is less than 4 or the remaining characters can be encoded in at most 2\n     *    ASCII bytes.\n     *  - Mode is C40, TEXT or X12 and the remaining characters can be encoded in at most 1 ASCII byte.\n     *  Returns mode in all other cases.\n     * */\n    Edge.prototype.getEndMode = function () {\n        if (this.mode === Mode.EDF) {\n            if (this.characterLength < 4) {\n                return Mode.ASCII;\n            }\n            var lastASCII = this.getLastASCII(); // see 5.2.8.2 EDIFACT encodation Rules\n            if (lastASCII > 0 &&\n                this.getCodewordsRemaining(this.cachedTotalSize + lastASCII) <=\n                    2 - lastASCII) {\n                return Mode.ASCII;\n            }\n        }\n        if (this.mode === Mode.C40 ||\n            this.mode === Mode.TEXT ||\n            this.mode === Mode.X12) {\n            // see 5.2.5.2 C40 encodation rules and 5.2.7.2 ANSI X12 encodation rules\n            if (this.fromPosition + this.characterLength >= this.input.length() &&\n                this.getCodewordsRemaining(this.cachedTotalSize) === 0) {\n                return Mode.ASCII;\n            }\n            var lastASCII = this.getLastASCII();\n            if (lastASCII === 1 &&\n                this.getCodewordsRemaining(this.cachedTotalSize + 1) === 0) {\n                return Mode.ASCII;\n            }\n        }\n        return this.mode;\n    };\n    Edge.prototype.getMode = function () {\n        return this.mode;\n    };\n    /** Peeks ahead and returns 1 if the postfix consists of exactly two digits, 2 if the postfix consists of exactly\n     *  two consecutive digits and a non extended character or of 4 digits.\n     *  Returns 0 in any other case\n     **/\n    Edge.prototype.getLastASCII = function () {\n        var length = this.input.length();\n        var from = this.fromPosition + this.characterLength;\n        if (length - from > 4 || from >= length) {\n            return 0;\n        }\n        if (length - from === 1) {\n            if (MinimalEncoder.isExtendedASCII(this.input.charAt(from), this.input.getFNC1Character())) {\n                return 0;\n            }\n            return 1;\n        }\n        if (length - from === 2) {\n            if (MinimalEncoder.isExtendedASCII(this.input.charAt(from), this.input.getFNC1Character()) ||\n                MinimalEncoder.isExtendedASCII(this.input.charAt(from + 1), this.input.getFNC1Character())) {\n                return 0;\n            }\n            if (HighLevelEncoder.isDigit(this.input.charAt(from)) &&\n                HighLevelEncoder.isDigit(this.input.charAt(from + 1))) {\n                return 1;\n            }\n            return 2;\n        }\n        if (length - from === 3) {\n            if (HighLevelEncoder.isDigit(this.input.charAt(from)) &&\n                HighLevelEncoder.isDigit(this.input.charAt(from + 1)) &&\n                !MinimalEncoder.isExtendedASCII(this.input.charAt(from + 2), this.input.getFNC1Character())) {\n                return 2;\n            }\n            if (HighLevelEncoder.isDigit(this.input.charAt(from + 1)) &&\n                HighLevelEncoder.isDigit(this.input.charAt(from + 2)) &&\n                !MinimalEncoder.isExtendedASCII(this.input.charAt(from), this.input.getFNC1Character())) {\n                return 2;\n            }\n            return 0;\n        }\n        if (HighLevelEncoder.isDigit(this.input.charAt(from)) &&\n            HighLevelEncoder.isDigit(this.input.charAt(from + 1)) &&\n            HighLevelEncoder.isDigit(this.input.charAt(from + 2)) &&\n            HighLevelEncoder.isDigit(this.input.charAt(from + 3))) {\n            return 2;\n        }\n        return 0;\n    };\n    /** Returns the capacity in codewords of the smallest symbol that has enough capacity to fit the given minimal\n     * number of codewords.\n     **/\n    Edge.prototype.getMinSymbolSize = function (minimum) {\n        var e_3, _a, e_4, _b, e_5, _c;\n        switch (this.input.getShapeHint()) {\n            case 1 /* FORCE_SQUARE */:\n                try {\n                    for (var _d = __values(this.squareCodewordCapacities), _e = _d.next(); !_e.done; _e = _d.next()) {\n                        var capacity = _e.value;\n                        if (capacity >= minimum) {\n                            return capacity;\n                        }\n                    }\n                }\n                catch (e_3_1) { e_3 = { error: e_3_1 }; }\n                finally {\n                    try {\n                        if (_e && !_e.done && (_a = _d.return)) _a.call(_d);\n                    }\n                    finally { if (e_3) throw e_3.error; }\n                }\n                break;\n            case 2 /* FORCE_RECTANGLE */:\n                try {\n                    for (var _f = __values(this.rectangularCodewordCapacities), _g = _f.next(); !_g.done; _g = _f.next()) {\n                        var capacity = _g.value;\n                        if (capacity >= minimum) {\n                            return capacity;\n                        }\n                    }\n                }\n                catch (e_4_1) { e_4 = { error: e_4_1 }; }\n                finally {\n                    try {\n                        if (_g && !_g.done && (_b = _f.return)) _b.call(_f);\n                    }\n                    finally { if (e_4) throw e_4.error; }\n                }\n                break;\n        }\n        try {\n            for (var _h = __values(this.allCodewordCapacities), _j = _h.next(); !_j.done; _j = _h.next()) {\n                var capacity = _j.value;\n                if (capacity >= minimum) {\n                    return capacity;\n                }\n            }\n        }\n        catch (e_5_1) { e_5 = { error: e_5_1 }; }\n        finally {\n            try {\n                if (_j && !_j.done && (_c = _h.return)) _c.call(_h);\n            }\n            finally { if (e_5) throw e_5.error; }\n        }\n        return this.allCodewordCapacities[this.allCodewordCapacities.length - 1];\n    };\n    /** Returns the remaining capacity in codewords of the smallest symbol that has enough capacity to fit the given\n     * minimal number of codewords.\n     **/\n    Edge.prototype.getCodewordsRemaining = function (minimum) {\n        return this.getMinSymbolSize(minimum) - minimum;\n    };\n    Edge.getBytes = function (c1, c2) {\n        var result = new Uint8Array(c2 ? 2 : 1);\n        result[0] = c1;\n        if (c2) {\n            result[1] = c2;\n        }\n        return result;\n    };\n    Edge.prototype.setC40Word = function (bytes, offset, c1, c2, c3) {\n        var val16 = 1600 * (c1 & 0xff) + 40 * (c2 & 0xff) + (c3 & 0xff) + 1;\n        bytes[offset] = val16 / 256;\n        bytes[offset + 1] = val16 % 256;\n    };\n    Edge.prototype.getX12Value = function (c) {\n        return c === 13\n            ? 0\n            : c === 42\n                ? 1\n                : c === 62\n                    ? 2\n                    : c === 32\n                        ? 3\n                        : c >= 48 && c <= 57\n                            ? c - 44\n                            : c >= 65 && c <= 90\n                                ? c - 51\n                                : c;\n    };\n    Edge.prototype.getX12Words = function () {\n        if (!(this.characterLength % 3 === 0)) {\n            throw new Error('X12 words must be a multiple of 3');\n        }\n        var result = new Uint8Array((this.characterLength / 3) * 2);\n        for (var i = 0; i < result.length; i += 2) {\n            this.setC40Word(result, i, this.getX12Value(this.input.charAt(this.fromPosition + (i / 2) * 3)), this.getX12Value(this.input.charAt(this.fromPosition + (i / 2) * 3 + 1)), this.getX12Value(this.input.charAt(this.fromPosition + (i / 2) * 3 + 2)));\n        }\n        return result;\n    };\n    Edge.prototype.getShiftValue = function (c, c40, fnc1) {\n        return (c40 && MinimalEncoder.isInC40Shift1Set(c)) ||\n            (!c40 && MinimalEncoder.isInTextShift1Set(c))\n            ? 0\n            : (c40 && MinimalEncoder.isInC40Shift2Set(c, fnc1)) ||\n                (!c40 && MinimalEncoder.isInTextShift2Set(c, fnc1))\n                ? 1\n                : 2;\n    };\n    Edge.prototype.getC40Value = function (c40, setIndex, c, fnc1) {\n        if (c === fnc1) {\n            if (!(setIndex === 2)) {\n                throw new Error('FNC1 cannot be used in C40 shift 2');\n            }\n            return 27;\n        }\n        if (c40) {\n            return c <= 31\n                ? c\n                : c === 32\n                    ? 3\n                    : c <= 47\n                        ? c - 33\n                        : c <= 57\n                            ? c - 44\n                            : c <= 64\n                                ? c - 43\n                                : c <= 90\n                                    ? c - 51\n                                    : c <= 95\n                                        ? c - 69\n                                        : c <= 127\n                                            ? c - 96\n                                            : c;\n        }\n        else {\n            return c === 0\n                ? 0\n                : setIndex === 0 && c <= 3\n                    ? c - 1 // is this a bug in the spec?\n                    : setIndex === 1 && c <= 31\n                        ? c\n                        : c === 32\n                            ? 3\n                            : c >= 33 && c <= 47\n                                ? c - 33\n                                : c >= 48 && c <= 57\n                                    ? c - 44\n                                    : c >= 58 && c <= 64\n                                        ? c - 43\n                                        : c >= 65 && c <= 90\n                                            ? c - 64\n                                            : c >= 91 && c <= 95\n                                                ? c - 69\n                                                : c === 96\n                                                    ? 0\n                                                    : c >= 97 && c <= 122\n                                                        ? c - 83\n                                                        : c >= 123 && c <= 127\n                                                            ? c - 96\n                                                            : c;\n        }\n    };\n    Edge.prototype.getC40Words = function (c40, fnc1) {\n        var c40Values = [];\n        for (var i = 0; i < this.characterLength; i++) {\n            var ci = this.input.charAt(this.fromPosition + i);\n            if ((c40 && HighLevelEncoder.isNativeC40(ci)) ||\n                (!c40 && HighLevelEncoder.isNativeText(ci))) {\n                c40Values.push(this.getC40Value(c40, 0, ci, fnc1));\n            }\n            else if (!MinimalEncoder.isExtendedASCII(ci, fnc1)) {\n                var shiftValue = this.getShiftValue(ci, c40, fnc1);\n                c40Values.push(shiftValue); // Shift[123]\n                c40Values.push(this.getC40Value(c40, shiftValue, ci, fnc1));\n            }\n            else {\n                var asciiValue = (ci & 0xff) - 128;\n                if ((c40 && HighLevelEncoder.isNativeC40(asciiValue)) ||\n                    (!c40 && HighLevelEncoder.isNativeText(asciiValue))) {\n                    c40Values.push(1); // Shift 2\n                    c40Values.push(30); // Upper Shift\n                    c40Values.push(this.getC40Value(c40, 0, asciiValue, fnc1));\n                }\n                else {\n                    c40Values.push(1); // Shift 2\n                    c40Values.push(30); // Upper Shift\n                    var shiftValue = this.getShiftValue(asciiValue, c40, fnc1);\n                    c40Values.push(shiftValue); // Shift[123]\n                    c40Values.push(this.getC40Value(c40, shiftValue, asciiValue, fnc1));\n                }\n            }\n        }\n        if (c40Values.length % 3 !== 0) {\n            if (!((c40Values.length - 2) % 3 === 0 &&\n                this.fromPosition + this.characterLength === this.input.length())) {\n                throw new Error('C40 words must be a multiple of 3');\n            }\n            c40Values.push(0); // pad with 0 (Shift 1)\n        }\n        var result = new Uint8Array((c40Values.length / 3) * 2);\n        var byteIndex = 0;\n        for (var i = 0; i < c40Values.length; i += 3) {\n            this.setC40Word(result, byteIndex, c40Values[i] & 0xff, c40Values[i + 1] & 0xff, c40Values[i + 2] & 0xff);\n            byteIndex += 2;\n        }\n        return result;\n    };\n    Edge.prototype.getEDFBytes = function () {\n        var numberOfThirds = Math.ceil(this.characterLength / 4.0);\n        var result = new Uint8Array(numberOfThirds * 3);\n        var pos = this.fromPosition;\n        var endPos = Math.min(this.fromPosition + this.characterLength - 1, this.input.length() - 1);\n        for (var i = 0; i < numberOfThirds; i += 3) {\n            var edfValues = [];\n            for (var j = 0; j < 4; j++) {\n                if (pos <= endPos) {\n                    edfValues[j] = this.input.charAt(pos++) & 0x3f;\n                }\n                else {\n                    edfValues[j] = pos === endPos + 1 ? 0x1f : 0;\n                }\n            }\n            var val24 = edfValues[0] << 18;\n            val24 |= edfValues[1] << 12;\n            val24 |= edfValues[2] << 6;\n            val24 |= edfValues[3];\n            result[i] = (val24 >> 16) & 0xff;\n            result[i + 1] = (val24 >> 8) & 0xff;\n            result[i + 2] = val24 & 0xff;\n        }\n        return result;\n    };\n    Edge.prototype.getLatchBytes = function () {\n        switch (this.getPreviousMode()) {\n            case Mode.ASCII:\n            case Mode.B256: // after B256 ends (via length) we are back to ASCII\n                switch (this.mode) {\n                    case Mode.B256:\n                        return Edge.getBytes(231);\n                    case Mode.C40:\n                        return Edge.getBytes(230);\n                    case Mode.TEXT:\n                        return Edge.getBytes(239);\n                    case Mode.X12:\n                        return Edge.getBytes(238);\n                    case Mode.EDF:\n                        return Edge.getBytes(240);\n                }\n                break;\n            case Mode.C40:\n            case Mode.TEXT:\n            case Mode.X12:\n                if (this.mode !== this.getPreviousMode()) {\n                    switch (this.mode) {\n                        case Mode.ASCII:\n                            return Edge.getBytes(254);\n                        case Mode.B256:\n                            return Edge.getBytes(254, 231);\n                        case Mode.C40:\n                            return Edge.getBytes(254, 230);\n                        case Mode.TEXT:\n                            return Edge.getBytes(254, 239);\n                        case Mode.X12:\n                            return Edge.getBytes(254, 238);\n                        case Mode.EDF:\n                            return Edge.getBytes(254, 240);\n                    }\n                }\n                break;\n            case Mode.EDF:\n                // The rightmost EDIFACT edge always contains an unlatch character\n                if (this.mode !== Mode.EDF) {\n                    throw new Error('Cannot switch from EDF to ' + this.mode);\n                }\n                break;\n        }\n        return new Uint8Array(0);\n    };\n    // Important: The function does not return the length bytes (one or two) in case of B256 encoding\n    Edge.prototype.getDataBytes = function () {\n        switch (this.mode) {\n            case Mode.ASCII:\n                if (this.input.isECI(this.fromPosition)) {\n                    return Edge.getBytes(241, this.input.getECIValue(this.fromPosition) + 1);\n                }\n                else if (MinimalEncoder.isExtendedASCII(this.input.charAt(this.fromPosition), this.input.getFNC1Character())) {\n                    return Edge.getBytes(235, this.input.charAt(this.fromPosition) - 127);\n                }\n                else if (this.characterLength === 2) {\n                    return Edge.getBytes(this.input.charAt(this.fromPosition) * 10 +\n                        this.input.charAt(this.fromPosition + 1) +\n                        130);\n                }\n                else if (this.input.isFNC1(this.fromPosition)) {\n                    return Edge.getBytes(232);\n                }\n                else {\n                    return Edge.getBytes(this.input.charAt(this.fromPosition) + 1);\n                }\n            case Mode.B256:\n                return Edge.getBytes(this.input.charAt(this.fromPosition));\n            case Mode.C40:\n                return this.getC40Words(true, this.input.getFNC1Character());\n            case Mode.TEXT:\n                return this.getC40Words(false, this.input.getFNC1Character());\n            case Mode.X12:\n                return this.getX12Words();\n            case Mode.EDF:\n                return this.getEDFBytes();\n        }\n    };\n    return Edge;\n}());\nvar Input = /** @class */ (function (_super) {\n    __extends(Input, _super);\n    function Input(stringToEncode, priorityCharset, fnc1, shape, macroId) {\n        var _this = _super.call(this, stringToEncode, priorityCharset, fnc1) || this;\n        _this.shape = shape;\n        _this.macroId = macroId;\n        return _this;\n    }\n    Input.prototype.getMacroId = function () {\n        return this.macroId;\n    };\n    Input.prototype.getShapeHint = function () {\n        return this.shape;\n    };\n    return Input;\n}(MinimalECIInput));\n"], "names": [], "mappings": ";;;AA4CA;AACA;AACA;AACA;AA/CA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;AACA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;AACA,IAAI,SAAS,4CAAS,yCAAK,MAAM,IAAK,SAAU,CAAC,EAAE,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,CAAC,CAAC,OAAO,QAAQ,CAAC;IAC1D,IAAI,CAAC,GAAG,OAAO;IACf,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,KAAK,EAAE,EAAE;IAC/B,IAAI;QACA,MAAO,CAAC,MAAM,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,IAAI,CAAE,GAAG,IAAI,CAAC,EAAE,KAAK;IAC7E,EACA,OAAO,OAAO;QAAE,IAAI;YAAE,OAAO;QAAM;IAAG,SAC9B;QACJ,IAAI;YACA,IAAI,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,EAAE,IAAI,CAAC;QAClD,SACQ;YAAE,IAAI,GAAG,MAAM,EAAE,KAAK;QAAE;IACpC;IACA,OAAO;AACX;AACA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK;IACtC,IAAK,IAAI,KAAK,EAAE,EAAE,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK,KAAK,GAAG,MAAM,CAAC,OAAO,SAAS,CAAC,EAAE;IACtF,OAAO;AACX;;;;;AAKA,IAAI;AACJ,CAAC,SAAU,IAAI;IACX,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,EAAE,GAAG;IAC1B,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG;IACxB,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG;IACzB,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG;IACxB,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG;IACxB,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG;AAC7B,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACrB,IAAI,mBAAmB;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACD,IAAI,iBAAgC;IAChC,SAAS,kBACT;IACA,eAAe,eAAe,GAAG,SAAU,EAAE,EAAE,IAAI;QAC/C,OAAO,OAAO,QAAQ,MAAM,OAAO,MAAM;IAC7C;IACA,eAAe,gBAAgB,GAAG,SAAU,EAAE;QAC1C,OAAO,MAAM;IACjB;IACA,eAAe,gBAAgB,GAAG,SAAU,EAAE,EAAE,IAAI;QAChD,IAAI,KAAK;QACT,IAAI;YACA,IAAK,IAAI,qBAAqB,SAAS,mBAAmB,uBAAuB,mBAAmB,IAAI,IAAI,CAAC,qBAAqB,IAAI,EAAE,uBAAuB,mBAAmB,IAAI,GAAI;gBACtL,IAAI,gBAAgB,qBAAqB,KAAK;gBAC9C,IAAI,cAAc,UAAU,CAAC,OAAO,IAAI;oBACpC,OAAO;gBACX;YACJ;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,wBAAwB,CAAC,qBAAqB,IAAI,IAAI,CAAC,KAAK,mBAAmB,MAAM,GAAG,GAAG,IAAI,CAAC;YACxG,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,OAAO,OAAO;IAClB;IACA,eAAe,iBAAiB,GAAG,SAAU,EAAE;QAC3C,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC;IACA,eAAe,iBAAiB,GAAG,SAAU,EAAE,EAAE,IAAI;QACjD,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI;IACrC;IACA;;;;;;;;;;;;KAYC,GACD,eAAe,eAAe,GAAG,SAAU,GAAG,EAAE,eAAe,EAAE,IAAI,EAAE,KAAK;QACxE,IAAI,oBAAoB,KAAK,GAAG;YAAE,kBAAkB;QAAM;QAC1D,IAAI,SAAS,KAAK,GAAG;YAAE,OAAO,CAAC;QAAG;QAClC,IAAI,UAAU,KAAK,GAAG;YAAE,QAAQ,EAAE,cAAc;QAAI;QACpD,IAAI,UAAU;QACd,IAAI,IAAI,UAAU,CAAC,0PAAA,CAAA,kBAAe,KAAK,IAAI,QAAQ,CAAC,0PAAA,CAAA,gBAAa,GAAG;YAChE,UAAU;YACV,MAAM,IAAI,SAAS,CAAC,0PAAA,CAAA,kBAAe,CAAC,MAAM,EAAE,IAAI,MAAM,GAAG;QAC7D,OACK,IAAI,IAAI,UAAU,CAAC,0PAAA,CAAA,kBAAe,KAAK,IAAI,QAAQ,CAAC,0PAAA,CAAA,gBAAa,GAAG;YACrE,UAAU;YACV,MAAM,IAAI,SAAS,CAAC,0PAAA,CAAA,kBAAe,CAAC,MAAM,EAAE,IAAI,MAAM,GAAG;QAC7D;QACA,OAAO,mBAAmB,OAAO,OAAO,YAAY,CAAC,KAAK,CAAC,QAAQ,SAAS,IAAI,CAAC,MAAM,CAAC,KAAK,iBAAiB,MAAM,OAAO;IAC/H;IACA;;;;;;;;;;;;;KAaC,GACD,eAAe,MAAM,GAAG,SAAU,KAAK,EAAE,eAAe,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO;QAC1E,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,MAAM,OAAO,iBAAiB,MAAM,OAAO,UAAU,QAAQ;IACjG;IACA,eAAe,OAAO,GAAG,SAAU,KAAK,EAAE,IAAI;QAC1C,IAAI,cAAc,KAAK,YAAY,GAAG,KAAK,eAAe;QAC1D,IAAI,KAAK,CAAC,YAAY,CAAC,KAAK,UAAU,GAAG,KAAK,QAC1C,KAAK,CAAC,YAAY,CAAC,KAAK,UAAU,GAAG,CAAC,eAAe,GACjD,KAAK,eAAe,EAAE;YAC1B,KAAK,CAAC,YAAY,CAAC,KAAK,UAAU,GAAG,GAAG;QAC5C;IACJ;IACA;;;;;;KAMC,GACD,eAAe,mBAAmB,GAAG,SAAU,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,eAAe;QAC5E,IAAI,cAAc;QAClB,IAAK,IAAI,IAAI,MAAM,IAAI,MAAM,MAAM,IAAI,IAAK;YACxC,IAAI,MAAM,KAAK,CAAC,IAAI;gBAChB,eAAe,CAAC,EAAE,GAAG;gBACrB,OAAO;YACX;YACA,IAAI,KAAK,MAAM,MAAM,CAAC;YACtB,IAAI,AAAC,OAAO,iQAAA,CAAA,UAAgB,CAAC,WAAW,CAAC,OACpC,CAAC,OAAO,iQAAA,CAAA,UAAgB,CAAC,YAAY,CAAC,KAAM;gBAC7C,eAAe,SAAS;YAC5B,OACK,IAAI,CAAC,eAAe,eAAe,CAAC,IAAI,MAAM,gBAAgB,KAAK;gBACpE,eAAe,GAAG,QAAQ;YAC9B,OACK;gBACD,IAAI,aAAa,KAAK;gBACtB,IAAI,cAAc,OACd,CAAC,AAAC,OAAO,iQAAA,CAAA,UAAgB,CAAC,WAAW,CAAC,aAAa,QAC9C,CAAC,OAAO,iQAAA,CAAA,UAAgB,CAAC,YAAY,CAAC,aAAa,IAAK,GAAG;oBAChE,eAAe,GAAG,qBAAqB;gBAC3C,OACK;oBACD,eAAe,GAAG,4BAA4B;gBAClD;YACJ;YACA,IAAI,cAAc,MAAM,KACnB,CAAC,cAAc,CAAC,IAAI,MAAM,KAAK,IAAI,MAAM,MAAM,MAAM,IAAK;gBAC3D,eAAe,CAAC,EAAE,GAAG,IAAI,OAAO;gBAChC,OAAO,KAAK,IAAI,CAAC,cAAc;YACnC;QACJ;QACA,eAAe,CAAC,EAAE,GAAG;QACrB,OAAO;IACX;IACA,eAAe,QAAQ,GAAG,SAAU,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ;QAC5D,IAAI,KAAK;QACT,IAAI,MAAM,KAAK,CAAC,OAAO;YACnB,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,KAAK,OAAO,KAAK,KAAK,EAAE,MAAM,GAAG;YACzD;QACJ;QACA,IAAI,KAAK,MAAM,MAAM,CAAC;QACtB,IAAI,aAAa,QAAQ,SAAS,UAAU,OAAO,KAAK,GAAG,EAAE;YACzD,uDAAuD;YACvD,OAAO;YACP,IAAI,iQAAA,CAAA,UAAgB,CAAC,OAAO,CAAC,OACzB,MAAM,eAAe,CAAC,MAAM,MAC5B,iQAAA,CAAA,UAAgB,CAAC,OAAO,CAAC,MAAM,MAAM,CAAC,OAAO,KAAK;gBAClD,2BAA2B;gBAC3B,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,KAAK,OAAO,KAAK,KAAK,EAAE,MAAM,GAAG;YAC7D,OACK;gBACD,uEAAuE;gBACvE,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,KAAK,OAAO,KAAK,KAAK,EAAE,MAAM,GAAG;YAC7D;YACA,IAAI,QAAQ;gBAAC,KAAK,GAAG;gBAAE,KAAK,IAAI;aAAC;YACjC,IAAI;gBACA,IAAK,IAAI,UAAU,SAAS,QAAQ,YAAY,QAAQ,IAAI,IAAI,CAAC,UAAU,IAAI,EAAE,YAAY,QAAQ,IAAI,GAAI;oBACzG,IAAI,OAAO,UAAU,KAAK;oBAC1B,IAAI,kBAAkB,EAAE;oBACxB,IAAI,eAAe,mBAAmB,CAAC,OAAO,MAAM,SAAS,KAAK,GAAG,EAAE,mBAAmB,GAAG;wBACzF,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,KAAK,OAAO,MAAM,MAAM,eAAe,CAAC,EAAE,EAAE;oBACxE;gBACJ;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,aAAa,CAAC,UAAU,IAAI,IAAI,CAAC,KAAK,QAAQ,MAAM,GAAG,GAAG,IAAI,CAAC;gBACvE,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;YACA,IAAI,MAAM,eAAe,CAAC,MAAM,MAC5B,iQAAA,CAAA,UAAgB,CAAC,WAAW,CAAC,MAAM,MAAM,CAAC,UAC1C,iQAAA,CAAA,UAAgB,CAAC,WAAW,CAAC,MAAM,MAAM,CAAC,OAAO,OACjD,iQAAA,CAAA,UAAgB,CAAC,WAAW,CAAC,MAAM,MAAM,CAAC,OAAO,KAAK;gBACtD,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,KAAK,OAAO,KAAK,GAAG,EAAE,MAAM,GAAG;YAC3D;YACA,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,KAAK,OAAO,KAAK,IAAI,EAAE,MAAM,GAAG;QAC5D;QACA,gHAAgH;QAChH,4DAA4D;QAC5D,IAAI;QACJ,IAAK,IAAI,GAAG,IAAI,GAAG,IAAK;YACpB,IAAI,MAAM,OAAO;YACjB,IAAI,MAAM,eAAe,CAAC,KAAK,MAC3B,iQAAA,CAAA,UAAgB,CAAC,eAAe,CAAC,MAAM,MAAM,CAAC,OAAO;gBACrD,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,KAAK,OAAO,KAAK,GAAG,EAAE,MAAM,IAAI,GAAG;YAC/D,OACK;gBACD;YACJ;QACJ;QACA,IAAI,MAAM,KACN,MAAM,eAAe,CAAC,MAAM,MAC5B,iQAAA,CAAA,UAAgB,CAAC,eAAe,CAAC,MAAM,MAAM,CAAC,OAAO,KAAK;YAC1D,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,KAAK,OAAO,KAAK,GAAG,EAAE,MAAM,GAAG;QAC3D;IACJ;IACA,eAAe,eAAe,GAAG,SAAU,KAAK;QAC5C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SAsLC,GACD,IAAI,cAAc,MAAM,MAAM;QAC9B,kFAAkF;QAClF,gGAAgG;QAChG,IAAI,QAAQ,MAAM,cAAc,GAC3B,IAAI,CAAC,MACL,GAAG,CAAC;YAAc,OAAO,MAAM,GAAG,IAAI,CAAC;QAAI;QAChD,IAAI,CAAC,QAAQ,CAAC,OAAO,OAAO,GAAG;QAC/B,IAAK,IAAI,IAAI,GAAG,KAAK,aAAa,IAAK;YACnC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;gBACxB,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,QAAQ,IAAI,aAAa;oBACzC,IAAI,CAAC,QAAQ,CAAC,OAAO,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,EAAE;gBAC9C;YACJ;YACA,2DAA2D;YAC3D,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;gBACxB,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG;YACtB;QACJ;QACA,IAAI,WAAW,CAAC;QAChB,IAAI,cAAc,uOAAA,CAAA,UAAO,CAAC,SAAS;QACnC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YACxB,IAAI,KAAK,CAAC,YAAY,CAAC,EAAE,KAAK,MAAM;gBAChC,IAAI,OAAO,KAAK,CAAC,YAAY,CAAC,EAAE;gBAChC,IAAI,OAAO,KAAK,KAAK,KAAK,IAAI,KAAK,eAAe,GAAG,IAAI,KAAK,eAAe,EAAE,4BAA4B;gBAC3G,2BAA2B;gBAC3B,IAAI,OAAO,aAAa;oBACpB,cAAc;oBACd,WAAW;gBACf;YACJ;QACJ;QACA,IAAI,WAAW,GAAG;YACd,MAAM,IAAI,MAAM,uBAAuB,QAAQ;QACnD;QACA,OAAO,IAAI,OAAO,KAAK,CAAC,YAAY,CAAC,SAAS;IAClD;IACA,OAAO;AACX;;AAEA,IAAI,SAAwB;IACxB,SAAS,OAAO,QAAQ;QACpB,IAAI,QAAQ,SAAS,KAAK;QAC1B,IAAI,OAAO;QACX,IAAI,UAAU,EAAE;QAChB,IAAI,yBAAyB,EAAE;QAC/B,IAAI,mBAAmB,EAAE;QACzB,IAAI,CAAC,SAAS,IAAI,KAAK,KAAK,GAAG,IAC3B,SAAS,IAAI,KAAK,KAAK,IAAI,IAC3B,SAAS,IAAI,KAAK,KAAK,GAAG,KAC1B,SAAS,UAAU,OAAO,KAAK,KAAK,EAAE;YACtC,QAAQ,IAAI,CAAC,OAAO,CAAC,KAAK,QAAQ,CAAC,MAAM;QAC7C;QACA,IAAI,UAAU;QACd,MAAO,YAAY,KAAM;YACrB,QAAQ,IAAI,CAAC,OAAO,CAAC,QAAQ,YAAY,IAAI;YAC7C,IAAI,QAAQ,QAAQ,KAAK,QACrB,QAAQ,oBAAoB,OAAO,QAAQ,OAAO,IAAI;gBACtD,IAAI,QAAQ,OAAO,OAAO,KAAK,IAAI,EAAE;oBACjC,IAAI,QAAQ,KAAK;wBACb,QAAQ,OAAO,CAAC;wBAChB;oBACJ,OACK;wBACD,QAAQ,OAAO,CAAC,OAAO;wBACvB,QAAQ,OAAO,CAAC,OAAO,MAAM;wBAC7B,QAAQ;oBACZ;oBACA,uBAAuB,IAAI,CAAC,QAAQ,MAAM;oBAC1C,iBAAiB,IAAI,CAAC;gBAC1B;gBACA,IAAI,CAAC,OAAO,CAAC,QAAQ,aAAa,IAAI;gBACtC,OAAO;YACX;YACA,UAAU,QAAQ,QAAQ;QAC9B;QACA,IAAI,MAAM,UAAU,OAAO,GAAG;YAC1B,QAAQ,IAAI,CAAC,OAAO,CAAC,KAAK,QAAQ,CAAC,MAAM;QAC7C,OACK,IAAI,MAAM,UAAU,OAAO,GAAG;YAC/B,QAAQ,IAAI,CAAC,OAAO,CAAC,KAAK,QAAQ,CAAC,MAAM;QAC7C;QACA,IAAI,MAAM,gBAAgB,KAAK,GAAG;YAC9B,QAAQ,IAAI,CAAC,OAAO,CAAC,KAAK,QAAQ,CAAC,MAAM;QAC7C;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,uBAAuB,MAAM,EAAE,IAAK;YACpD,IAAI,CAAC,kBAAkB,CAAC,SAAS,QAAQ,MAAM,GAAG,sBAAsB,CAAC,EAAE,EAAE,gBAAgB,CAAC,EAAE;QACpG;QACA,cAAc;QACd,IAAI,WAAW,SAAS,gBAAgB,CAAC,QAAQ,MAAM;QACvD,IAAI,QAAQ,MAAM,GAAG,UAAU;YAC3B,QAAQ,IAAI,CAAC;QACjB;QACA,MAAO,QAAQ,MAAM,GAAG,SAAU;YAC9B,QAAQ,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,MAAM,GAAG;QACzD;QACA,IAAI,CAAC,KAAK,GAAG,IAAI,WAAW,QAAQ,MAAM;QAC1C,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAK;YACxC,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE;QAC9B;IACJ;IACA,OAAO,SAAS,CAAC,OAAO,GAAG,SAAU,KAAK,EAAE,IAAI;QAC5C,IAAK,IAAI,IAAI,MAAM,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;YACxC,KAAK,OAAO,CAAC,KAAK,CAAC,EAAE;QACzB;QACA,OAAO,MAAM,MAAM;IACvB;IACA,OAAO,SAAS,CAAC,iBAAiB,GAAG,SAAU,gBAAgB;QAC3D,IAAI,eAAe,AAAE,MAAM,mBAAoB,MAAO;QACtD,IAAI,eAAe,MAAM;QACzB,OAAO,gBAAgB,MAAM,eAAe,eAAe;IAC/D;IACA,OAAO,SAAS,CAAC,kBAAkB,GAAG,SAAU,OAAO,EAAE,aAAa,EAAE,MAAM;QAC1E,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;YAC7B,+BAA+B;YAC/B,IAAI,wBAAwB,gBAAgB;YAC5C,IAAI,qBAAqB,OAAO,CAAC,sBAAsB,GAAG;YAC1D,IAAI,uBAAuB,AAAE,MAAM,CAAC,wBAAwB,CAAC,IAAK,MAAO;YACzE,IAAI,gBAAgB,qBAAqB;YACzC,OAAO,CAAC,sBAAsB,GAC1B,iBAAiB,MAAM,gBAAgB,gBAAgB;QAC/D;IACJ;IACA,OAAO,SAAS,CAAC,QAAQ,GAAG;QACxB,OAAO,IAAI,CAAC,KAAK;IACrB;IACA,OAAO;AACX;AACA,IAAI,OAAsB;IACtB,SAAS,KAAK,KAAK,EAAE,IAAI,EAAE,YAAY,EAAE,eAAe,EAAE,QAAQ;QAC9D,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,qBAAqB,GAAG;YACzB;YAAG;YAAG;YAAG;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAK;YAAK;YAAK;YACxE;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAM;YAAM;SAC7C;QACD,IAAI,CAAC,wBAAwB,GAAG;YAC5B;YAAG;YAAG;YAAG;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YACvE;YAAK;YAAK;YAAK;YAAM;YAAM;SAC9B;QACD,IAAI,CAAC,6BAA6B,GAAG;YAAC;YAAG;YAAI;YAAI;YAAI;YAAI;SAAG;QAC5D,IAAI,CAAC,CAAC,eAAe,mBAAmB,MAAM,MAAM,EAAE,GAAG;YACrD,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,OAAO,aAAa,OAAO,SAAS,eAAe,GAAG;QAC1D,IAAI,eAAe,IAAI,CAAC,eAAe;QACvC;;;;;;;;;;;;;SAaC,GACD,OAAQ;YACJ,KAAK,KAAK,KAAK;gBACX;gBACA,IAAI,MAAM,KAAK,CAAC,iBACZ,eAAe,eAAe,CAAC,MAAM,MAAM,CAAC,eAAe,MAAM,gBAAgB,KAAK;oBACtF;gBACJ;gBACA,IAAI,iBAAiB,KAAK,GAAG,IACzB,iBAAiB,KAAK,IAAI,IAC1B,iBAAiB,KAAK,GAAG,EAAE;oBAC3B,QAAQ,uBAAuB;gBACnC;gBACA;YACJ,KAAK,KAAK,IAAI;gBACV;gBACA,IAAI,iBAAiB,KAAK,IAAI,EAAE;oBAC5B,QAAQ,aAAa;gBACzB,OACK,IAAI,IAAI,CAAC,WAAW,OAAO,KAAK;oBACjC,QAAQ,mBAAmB;gBAC/B;gBACA,IAAI,iBAAiB,KAAK,KAAK,EAAE;oBAC7B,QAAQ,gBAAgB;gBAC5B,OACK,IAAI,iBAAiB,KAAK,GAAG,IAC9B,iBAAiB,KAAK,IAAI,IAC1B,iBAAiB,KAAK,GAAG,EAAE;oBAC3B,QAAQ,GAAG,kCAAkC;gBACjD;gBACA;YACJ,KAAK,KAAK,GAAG;YACb,KAAK,KAAK,IAAI;YACd,KAAK,KAAK,GAAG;gBACT,IAAI,SAAS,KAAK,GAAG,EAAE;oBACnB,QAAQ;gBACZ,OACK;oBACD,IAAI,UAAU,EAAE;oBAChB,QACI,eAAe,mBAAmB,CAAC,OAAO,cAAc,SAAS,KAAK,GAAG,EAAE,WAAW;gBAC9F;gBACA,IAAI,iBAAiB,KAAK,KAAK,IAAI,iBAAiB,KAAK,IAAI,EAAE;oBAC3D,QAAQ,oDAAoD;gBAChE,OACK,IAAI,iBAAiB,QACtB,CAAC,iBAAiB,KAAK,GAAG,IACtB,iBAAiB,KAAK,IAAI,IAC1B,iBAAiB,KAAK,GAAG,GAAG;oBAChC,QAAQ,GAAG,sDAAsD;gBACrE;gBACA;YACJ,KAAK,KAAK,GAAG;gBACT,QAAQ;gBACR,IAAI,iBAAiB,KAAK,KAAK,IAAI,iBAAiB,KAAK,IAAI,EAAE;oBAC3D,QAAQ,oDAAoD;gBAChE,OACK,IAAI,iBAAiB,KAAK,GAAG,IAC9B,iBAAiB,KAAK,IAAI,IAC1B,iBAAiB,KAAK,GAAG,EAAE;oBAC3B,QAAQ,GAAG,sDAAsD;gBACrE;gBACA;QACR;QACA,IAAI,CAAC,eAAe,GAAG;IAC3B;IACA,4BAA4B;IAC5B,KAAK,SAAS,CAAC,WAAW,GAAG;QACzB,IAAI,MAAM;QACV,IAAI,UAAU,IAAI;QAClB,MAAO,YAAY,QAAQ,QAAQ,IAAI,KAAK,KAAK,IAAI,IAAI,OAAO,IAAK;YACjE;YACA,UAAU,QAAQ,QAAQ;QAC9B;QACA,OAAO;IACX;IACA,KAAK,SAAS,CAAC,oBAAoB,GAAG;QAClC,OAAO,IAAI,CAAC,QAAQ,KAAK,OAAO,KAAK,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI;IACnE;IACA,KAAK,SAAS,CAAC,eAAe,GAAG;QAC7B,OAAO,IAAI,CAAC,QAAQ,KAAK,OAAO,KAAK,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU;IACzE;IACA;;;;;OAKG,GACH,KAAK,SAAS,CAAC,UAAU,GAAG;QACxB,IAAI,IAAI,CAAC,IAAI,KAAK,KAAK,GAAG,EAAE;YACxB,IAAI,IAAI,CAAC,eAAe,GAAG,GAAG;gBAC1B,OAAO,KAAK,KAAK;YACrB;YACA,IAAI,YAAY,IAAI,CAAC,YAAY,IAAI,uCAAuC;YAC5E,IAAI,YAAY,KACZ,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,eAAe,GAAG,cAC9C,IAAI,WAAW;gBACnB,OAAO,KAAK,KAAK;YACrB;QACJ;QACA,IAAI,IAAI,CAAC,IAAI,KAAK,KAAK,GAAG,IACtB,IAAI,CAAC,IAAI,KAAK,KAAK,IAAI,IACvB,IAAI,CAAC,IAAI,KAAK,KAAK,GAAG,EAAE;YACxB,yEAAyE;YACzE,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,MAC7D,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,eAAe,MAAM,GAAG;gBACxD,OAAO,KAAK,KAAK;YACrB;YACA,IAAI,YAAY,IAAI,CAAC,YAAY;YACjC,IAAI,cAAc,KACd,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,eAAe,GAAG,OAAO,GAAG;gBAC5D,OAAO,KAAK,KAAK;YACrB;QACJ;QACA,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,KAAK,SAAS,CAAC,OAAO,GAAG;QACrB,OAAO,IAAI,CAAC,IAAI;IACpB;IACA;;;MAGE,GACF,KAAK,SAAS,CAAC,YAAY,GAAG;QAC1B,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM;QAC9B,IAAI,OAAO,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe;QACnD,IAAI,SAAS,OAAO,KAAK,QAAQ,QAAQ;YACrC,OAAO;QACX;QACA,IAAI,SAAS,SAAS,GAAG;YACrB,IAAI,eAAe,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK;gBACxF,OAAO;YACX;YACA,OAAO;QACX;QACA,IAAI,SAAS,SAAS,GAAG;YACrB,IAAI,eAAe,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,OACnF,eAAe,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK;gBAC5F,OAAO;YACX;YACA,IAAI,iQAAA,CAAA,UAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAC3C,iQAAA,CAAA,UAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,KAAK;gBACvD,OAAO;YACX;YACA,OAAO;QACX;QACA,IAAI,SAAS,SAAS,GAAG;YACrB,IAAI,iQAAA,CAAA,UAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAC3C,iQAAA,CAAA,UAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,OAClD,CAAC,eAAe,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK;gBAC7F,OAAO;YACX;YACA,IAAI,iQAAA,CAAA,UAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,OAClD,iQAAA,CAAA,UAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,OAClD,CAAC,eAAe,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK;gBACzF,OAAO;YACX;YACA,OAAO;QACX;QACA,IAAI,iQAAA,CAAA,UAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAC3C,iQAAA,CAAA,UAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,OAClD,iQAAA,CAAA,UAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,OAClD,iQAAA,CAAA,UAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,KAAK;YACvD,OAAO;QACX;QACA,OAAO;IACX;IACA;;MAEE,GACF,KAAK,SAAS,CAAC,gBAAgB,GAAG,SAAU,OAAO;QAC/C,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK;QAC3B,OAAQ,IAAI,CAAC,KAAK,CAAC,YAAY;YAC3B,KAAK,EAAE,gBAAgB;gBACnB,IAAI;oBACA,IAAK,IAAI,KAAK,SAAS,IAAI,CAAC,wBAAwB,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;wBAC7F,IAAI,WAAW,GAAG,KAAK;wBACvB,IAAI,YAAY,SAAS;4BACrB,OAAO;wBACX;oBACJ;gBACJ,EACA,OAAO,OAAO;oBAAE,MAAM;wBAAE,OAAO;oBAAM;gBAAG,SAChC;oBACJ,IAAI;wBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;oBACpD,SACQ;wBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;oBAAE;gBACxC;gBACA;YACJ,KAAK,EAAE,mBAAmB;gBACtB,IAAI;oBACA,IAAK,IAAI,KAAK,SAAS,IAAI,CAAC,6BAA6B,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;wBAClG,IAAI,WAAW,GAAG,KAAK;wBACvB,IAAI,YAAY,SAAS;4BACrB,OAAO;wBACX;oBACJ;gBACJ,EACA,OAAO,OAAO;oBAAE,MAAM;wBAAE,OAAO;oBAAM;gBAAG,SAChC;oBACJ,IAAI;wBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;oBACpD,SACQ;wBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;oBAAE;gBACxC;gBACA;QACR;QACA,IAAI;YACA,IAAK,IAAI,KAAK,SAAS,IAAI,CAAC,qBAAqB,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,GAAI;gBAC1F,IAAI,WAAW,GAAG,KAAK;gBACvB,IAAI,YAAY,SAAS;oBACrB,OAAO;gBACX;YACJ;QACJ,EACA,OAAO,OAAO;YAAE,MAAM;gBAAE,OAAO;YAAM;QAAG,SAChC;YACJ,IAAI;gBACA,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,CAAC;YACpD,SACQ;gBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;YAAE;QACxC;QACA,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,GAAG,EAAE;IAC5E;IACA;;MAEE,GACF,KAAK,SAAS,CAAC,qBAAqB,GAAG,SAAU,OAAO;QACpD,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW;IAC5C;IACA,KAAK,QAAQ,GAAG,SAAU,EAAE,EAAE,EAAE;QAC5B,IAAI,SAAS,IAAI,WAAW,KAAK,IAAI;QACrC,MAAM,CAAC,EAAE,GAAG;QACZ,IAAI,IAAI;YACJ,MAAM,CAAC,EAAE,GAAG;QAChB;QACA,OAAO;IACX;IACA,KAAK,SAAS,CAAC,UAAU,GAAG,SAAU,KAAK,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;QAC3D,IAAI,QAAQ,OAAO,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI;QAClE,KAAK,CAAC,OAAO,GAAG,QAAQ;QACxB,KAAK,CAAC,SAAS,EAAE,GAAG,QAAQ;IAChC;IACA,KAAK,SAAS,CAAC,WAAW,GAAG,SAAU,CAAC;QACpC,OAAO,MAAM,KACP,IACA,MAAM,KACF,IACA,MAAM,KACF,IACA,MAAM,KACF,IACA,KAAK,MAAM,KAAK,KACZ,IAAI,KACJ,KAAK,MAAM,KAAK,KACZ,IAAI,KACJ;IAC9B;IACA,KAAK,SAAS,CAAC,WAAW,GAAG;QACzB,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,GAAG;YACnC,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,SAAS,IAAI,WAAW,AAAC,IAAI,CAAC,eAAe,GAAG,IAAK;QACzD,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,KAAK,EAAG;YACvC,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,GAAG,AAAC,IAAI,IAAK,KAAK,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,GAAG,AAAC,IAAI,IAAK,IAAI,KAAK,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,GAAG,AAAC,IAAI,IAAK,IAAI;QACpP;QACA,OAAO;IACX;IACA,KAAK,SAAS,CAAC,aAAa,GAAG,SAAU,CAAC,EAAE,GAAG,EAAE,IAAI;QACjD,OAAO,AAAC,OAAO,eAAe,gBAAgB,CAAC,MAC1C,CAAC,OAAO,eAAe,iBAAiB,CAAC,KACxC,IACA,AAAC,OAAO,eAAe,gBAAgB,CAAC,GAAG,SACxC,CAAC,OAAO,eAAe,iBAAiB,CAAC,GAAG,QAC3C,IACA;IACd;IACA,KAAK,SAAS,CAAC,WAAW,GAAG,SAAU,GAAG,EAAE,QAAQ,EAAE,CAAC,EAAE,IAAI;QACzD,IAAI,MAAM,MAAM;YACZ,IAAI,CAAC,CAAC,aAAa,CAAC,GAAG;gBACnB,MAAM,IAAI,MAAM;YACpB;YACA,OAAO;QACX;QACA,IAAI,KAAK;YACL,OAAO,KAAK,KACN,IACA,MAAM,KACF,IACA,KAAK,KACD,IAAI,KACJ,KAAK,KACD,IAAI,KACJ,KAAK,KACD,IAAI,KACJ,KAAK,KACD,IAAI,KACJ,KAAK,KACD,IAAI,KACJ,KAAK,MACD,IAAI,KACJ;QACtC,OACK;YACD,OAAO,MAAM,IACP,IACA,aAAa,KAAK,KAAK,IACnB,IAAI,EAAE,6BAA6B;eACnC,aAAa,KAAK,KAAK,KACnB,IACA,MAAM,KACF,IACA,KAAK,MAAM,KAAK,KACZ,IAAI,KACJ,KAAK,MAAM,KAAK,KACZ,IAAI,KACJ,KAAK,MAAM,KAAK,KACZ,IAAI,KACJ,KAAK,MAAM,KAAK,KACZ,IAAI,KACJ,KAAK,MAAM,KAAK,KACZ,IAAI,KACJ,MAAM,KACF,IACA,KAAK,MAAM,KAAK,MACZ,IAAI,KACJ,KAAK,OAAO,KAAK,MACb,IAAI,KACJ;QACtD;IACJ;IACA,KAAK,SAAS,CAAC,WAAW,GAAG,SAAU,GAAG,EAAE,IAAI;QAC5C,IAAI,YAAY,EAAE;QAClB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,eAAe,EAAE,IAAK;YAC3C,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,GAAG;YAC/C,IAAI,AAAC,OAAO,iQAAA,CAAA,UAAgB,CAAC,WAAW,CAAC,OACpC,CAAC,OAAO,iQAAA,CAAA,UAAgB,CAAC,YAAY,CAAC,KAAM;gBAC7C,UAAU,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,IAAI;YAChD,OACK,IAAI,CAAC,eAAe,eAAe,CAAC,IAAI,OAAO;gBAChD,IAAI,aAAa,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK;gBAC7C,UAAU,IAAI,CAAC,aAAa,aAAa;gBACzC,UAAU,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,YAAY,IAAI;YACzD,OACK;gBACD,IAAI,aAAa,CAAC,KAAK,IAAI,IAAI;gBAC/B,IAAI,AAAC,OAAO,iQAAA,CAAA,UAAgB,CAAC,WAAW,CAAC,eACpC,CAAC,OAAO,iQAAA,CAAA,UAAgB,CAAC,YAAY,CAAC,aAAc;oBACrD,UAAU,IAAI,CAAC,IAAI,UAAU;oBAC7B,UAAU,IAAI,CAAC,KAAK,cAAc;oBAClC,UAAU,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,YAAY;gBACxD,OACK;oBACD,UAAU,IAAI,CAAC,IAAI,UAAU;oBAC7B,UAAU,IAAI,CAAC,KAAK,cAAc;oBAClC,IAAI,aAAa,IAAI,CAAC,aAAa,CAAC,YAAY,KAAK;oBACrD,UAAU,IAAI,CAAC,aAAa,aAAa;oBACzC,UAAU,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,YAAY,YAAY;gBACjE;YACJ;QACJ;QACA,IAAI,UAAU,MAAM,GAAG,MAAM,GAAG;YAC5B,IAAI,CAAC,CAAC,CAAC,UAAU,MAAM,GAAG,CAAC,IAAI,MAAM,KACjC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG;gBACnE,MAAM,IAAI,MAAM;YACpB;YACA,UAAU,IAAI,CAAC,IAAI,uBAAuB;QAC9C;QACA,IAAI,SAAS,IAAI,WAAW,AAAC,UAAU,MAAM,GAAG,IAAK;QACrD,IAAI,YAAY;QAChB,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,KAAK,EAAG;YAC1C,IAAI,CAAC,UAAU,CAAC,QAAQ,WAAW,SAAS,CAAC,EAAE,GAAG,MAAM,SAAS,CAAC,IAAI,EAAE,GAAG,MAAM,SAAS,CAAC,IAAI,EAAE,GAAG;YACpG,aAAa;QACjB;QACA,OAAO;IACX;IACA,KAAK,SAAS,CAAC,WAAW,GAAG;QACzB,IAAI,iBAAiB,KAAK,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG;QACtD,IAAI,SAAS,IAAI,WAAW,iBAAiB;QAC7C,IAAI,MAAM,IAAI,CAAC,YAAY;QAC3B,IAAI,SAAS,KAAK,GAAG,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK;QAC1F,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,KAAK,EAAG;YACxC,IAAI,YAAY,EAAE;YAClB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;gBACxB,IAAI,OAAO,QAAQ;oBACf,SAAS,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS;gBAC9C,OACK;oBACD,SAAS,CAAC,EAAE,GAAG,QAAQ,SAAS,IAAI,OAAO;gBAC/C;YACJ;YACA,IAAI,QAAQ,SAAS,CAAC,EAAE,IAAI;YAC5B,SAAS,SAAS,CAAC,EAAE,IAAI;YACzB,SAAS,SAAS,CAAC,EAAE,IAAI;YACzB,SAAS,SAAS,CAAC,EAAE;YACrB,MAAM,CAAC,EAAE,GAAG,AAAC,SAAS,KAAM;YAC5B,MAAM,CAAC,IAAI,EAAE,GAAG,AAAC,SAAS,IAAK;YAC/B,MAAM,CAAC,IAAI,EAAE,GAAG,QAAQ;QAC5B;QACA,OAAO;IACX;IACA,KAAK,SAAS,CAAC,aAAa,GAAG;QAC3B,OAAQ,IAAI,CAAC,eAAe;YACxB,KAAK,KAAK,KAAK;YACf,KAAK,KAAK,IAAI;gBACV,OAAQ,IAAI,CAAC,IAAI;oBACb,KAAK,KAAK,IAAI;wBACV,OAAO,KAAK,QAAQ,CAAC;oBACzB,KAAK,KAAK,GAAG;wBACT,OAAO,KAAK,QAAQ,CAAC;oBACzB,KAAK,KAAK,IAAI;wBACV,OAAO,KAAK,QAAQ,CAAC;oBACzB,KAAK,KAAK,GAAG;wBACT,OAAO,KAAK,QAAQ,CAAC;oBACzB,KAAK,KAAK,GAAG;wBACT,OAAO,KAAK,QAAQ,CAAC;gBAC7B;gBACA;YACJ,KAAK,KAAK,GAAG;YACb,KAAK,KAAK,IAAI;YACd,KAAK,KAAK,GAAG;gBACT,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,eAAe,IAAI;oBACtC,OAAQ,IAAI,CAAC,IAAI;wBACb,KAAK,KAAK,KAAK;4BACX,OAAO,KAAK,QAAQ,CAAC;wBACzB,KAAK,KAAK,IAAI;4BACV,OAAO,KAAK,QAAQ,CAAC,KAAK;wBAC9B,KAAK,KAAK,GAAG;4BACT,OAAO,KAAK,QAAQ,CAAC,KAAK;wBAC9B,KAAK,KAAK,IAAI;4BACV,OAAO,KAAK,QAAQ,CAAC,KAAK;wBAC9B,KAAK,KAAK,GAAG;4BACT,OAAO,KAAK,QAAQ,CAAC,KAAK;wBAC9B,KAAK,KAAK,GAAG;4BACT,OAAO,KAAK,QAAQ,CAAC,KAAK;oBAClC;gBACJ;gBACA;YACJ,KAAK,KAAK,GAAG;gBACT,kEAAkE;gBAClE,IAAI,IAAI,CAAC,IAAI,KAAK,KAAK,GAAG,EAAE;oBACxB,MAAM,IAAI,MAAM,+BAA+B,IAAI,CAAC,IAAI;gBAC5D;gBACA;QACR;QACA,OAAO,IAAI,WAAW;IAC1B;IACA,iGAAiG;IACjG,KAAK,SAAS,CAAC,YAAY,GAAG;QAC1B,OAAQ,IAAI,CAAC,IAAI;YACb,KAAK,KAAK,KAAK;gBACX,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,GAAG;oBACrC,OAAO,KAAK,QAAQ,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,IAAI;gBAC1E,OACK,IAAI,eAAe,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,KAAK;oBAC1G,OAAO,KAAK,QAAQ,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,IAAI;gBACrE,OACK,IAAI,IAAI,CAAC,eAAe,KAAK,GAAG;oBACjC,OAAO,KAAK,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,IAAI,KACxD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,GAAG,KACtC;gBACR,OACK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,GAAG;oBAC3C,OAAO,KAAK,QAAQ,CAAC;gBACzB,OACK;oBACD,OAAO,KAAK,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,IAAI;gBAChE;YACJ,KAAK,KAAK,IAAI;gBACV,OAAO,KAAK,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY;YAC5D,KAAK,KAAK,GAAG;gBACT,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,gBAAgB;YAC7D,KAAK,KAAK,IAAI;gBACV,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB;YAC9D,KAAK,KAAK,GAAG;gBACT,OAAO,IAAI,CAAC,WAAW;YAC3B,KAAK,KAAK,GAAG;gBACT,OAAO,IAAI,CAAC,WAAW;QAC/B;IACJ;IACA,OAAO;AACX;AACA,IAAI,QAAuB,SAAU,MAAM;IACvC,UAAU,OAAO;IACjB,SAAS,MAAM,cAAc,EAAE,eAAe,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO;QAChE,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,gBAAgB,iBAAiB,SAAS,IAAI;QAC5E,MAAM,KAAK,GAAG;QACd,MAAM,OAAO,GAAG;QAChB,OAAO;IACX;IACA,MAAM,SAAS,CAAC,UAAU,GAAG;QACzB,OAAO,IAAI,CAAC,OAAO;IACvB;IACA,MAAM,SAAS,CAAC,YAAY,GAAG;QAC3B,OAAO,IAAI,CAAC,KAAK;IACrB;IACA,OAAO;AACX,EAAE,iPAAA,CAAA,kBAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5267, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/core/datamatrix/encoder/index.js"], "sourcesContent": ["import { ASCIIEncoder } from './ASCIIEncoder';\nimport { Base256Encoder } from './Base256Encoder';\nimport { C40Encoder } from './C40Encoder';\nimport DefaultPlacement from './DefaultPlacement';\nimport { EdifactEncoder } from './EdifactEncoder';\nimport { EncoderContext } from './EncoderContext';\nimport ErrorCorrection from './ErrorCorrection';\nimport HighLevelEncoder from './HighLevelEncoder';\nimport { MinimalEncoder } from './MinimalEncoder';\nimport SymbolInfo from './SymbolInfo';\nimport { TextEncoder } from './TextEncoder';\nimport { X12Encoder } from './X12Encoder';\nexport { ASCIIEncoder, Base256Encoder, C40Encoder, EdifactEncoder, EncoderContext, ErrorCorrection, DefaultPlacement, HighLevelEncoder, MinimalEncoder, SymbolInfo, TextEncoder, X12Encoder, };\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5347, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/core/datamatrix/DataMatrixWriter.js"], "sourcesContent": ["import BarcodeFormat from '../BarcodeFormat';\nimport BitMatrix from '../common/BitMatrix';\nimport EncodeHintType from '../EncodeHintType';\nimport ByteMatrix from '../qrcode/encoder/ByteMatrix';\nimport Charset from '../util/Charset';\nimport { DefaultPlacement, ErrorCorrection, HighLevelEncoder, MinimalEncoder, SymbolInfo, } from './encoder';\nvar DataMatrixWriter = /** @class */ (function () {\n    function DataMatrixWriter() {\n    }\n    DataMatrixWriter.prototype.encode = function (contents, format, width, height, hints) {\n        if (hints === void 0) { hints = null; }\n        if (contents.trim() === '') {\n            throw new Error('Found empty contents');\n        }\n        if (format !== BarcodeFormat.DATA_MATRIX) {\n            throw new Error('Can only encode DATA_MATRIX, but got ' + format);\n        }\n        if (width < 0 || height < 0) {\n            throw new Error('Requested dimensions can\\'t be negative: ' + width + 'x' + height);\n        }\n        // Try to get force shape & min / max size\n        var shape = 0 /* FORCE_NONE */;\n        var minSize = null;\n        var maxSize = null;\n        if (hints != null) {\n            var requestedShape = hints.get(EncodeHintType.DATA_MATRIX_SHAPE);\n            if (requestedShape != null) {\n                shape = requestedShape;\n            }\n            var requestedMinSize = hints.get(EncodeHintType.MIN_SIZE);\n            if (requestedMinSize != null) {\n                minSize = requestedMinSize;\n            }\n            var requestedMaxSize = hints.get(EncodeHintType.MAX_SIZE);\n            if (requestedMaxSize != null) {\n                maxSize = requestedMaxSize;\n            }\n        }\n        // 1. step: Data encodation\n        var encoded;\n        var hasCompactionHint = hints != null &&\n            hints.has(EncodeHintType.DATA_MATRIX_COMPACT) &&\n            Boolean(hints.get(EncodeHintType.DATA_MATRIX_COMPACT).toString());\n        if (hasCompactionHint) {\n            var hasGS1FormatHint = hints.has(EncodeHintType.GS1_FORMAT) &&\n                Boolean(hints.get(EncodeHintType.GS1_FORMAT).toString());\n            var charset = null;\n            var hasEncodingHint = hints.has(EncodeHintType.CHARACTER_SET);\n            if (hasEncodingHint) {\n                charset = Charset.forName(hints.get(EncodeHintType.CHARACTER_SET).toString());\n            }\n            encoded = MinimalEncoder.encodeHighLevel(contents, charset, hasGS1FormatHint ? 0x1d : -1, shape);\n        }\n        else {\n            var hasForceC40Hint = hints != null &&\n                hints.has(EncodeHintType.FORCE_C40) &&\n                Boolean(hints.get(EncodeHintType.FORCE_C40).toString());\n            encoded = HighLevelEncoder.encodeHighLevel(contents, shape, minSize, maxSize, hasForceC40Hint);\n        }\n        var symbolInfo = SymbolInfo.lookup(encoded.length, shape, minSize, maxSize, true);\n        // 2. step: ECC generation\n        var codewords = ErrorCorrection.encodeECC200(encoded, symbolInfo);\n        // 3. step: Module placement in Matrix\n        var placement = new DefaultPlacement(codewords, symbolInfo.getSymbolDataWidth(), symbolInfo.getSymbolDataHeight());\n        placement.place();\n        // 4. step: low-level encoding\n        return this.encodeLowLevel(placement, symbolInfo, width, height);\n    };\n    /**\n     * Encode the given symbol info to a bit matrix.\n     *\n     * @param placement  The DataMatrix placement.\n     * @param symbolInfo The symbol info to encode.\n     * @return The bit matrix generated.\n     */\n    DataMatrixWriter.prototype.encodeLowLevel = function (placement, symbolInfo, width, height) {\n        var symbolWidth = symbolInfo.getSymbolDataWidth();\n        var symbolHeight = symbolInfo.getSymbolDataHeight();\n        var matrix = new ByteMatrix(symbolInfo.getSymbolWidth(), symbolInfo.getSymbolHeight());\n        var matrixY = 0;\n        for (var y = 0; y < symbolHeight; y++) {\n            // Fill the top edge with alternate 0 / 1\n            var matrixX = void 0;\n            if (y % symbolInfo.matrixHeight === 0) {\n                matrixX = 0;\n                for (var x = 0; x < symbolInfo.getSymbolWidth(); x++) {\n                    matrix.setBoolean(matrixX, matrixY, x % 2 === 0);\n                    matrixX++;\n                }\n                matrixY++;\n            }\n            matrixX = 0;\n            for (var x = 0; x < symbolWidth; x++) {\n                // Fill the right edge with full 1\n                if (x % symbolInfo.matrixWidth === 0) {\n                    matrix.setBoolean(matrixX, matrixY, true);\n                    matrixX++;\n                }\n                matrix.setBoolean(matrixX, matrixY, placement.getBit(x, y));\n                matrixX++;\n                // Fill the right edge with alternate 0 / 1\n                if (x % symbolInfo.matrixWidth === symbolInfo.matrixWidth - 1) {\n                    matrix.setBoolean(matrixX, matrixY, y % 2 === 0);\n                    matrixX++;\n                }\n            }\n            matrixY++;\n            // Fill the bottom edge with full 1\n            if (y % symbolInfo.matrixHeight === symbolInfo.matrixHeight - 1) {\n                matrixX = 0;\n                for (var x = 0; x < symbolInfo.getSymbolWidth(); x++) {\n                    matrix.setBoolean(matrixX, matrixY, true);\n                    matrixX++;\n                }\n                matrixY++;\n            }\n        }\n        return this.convertByteMatrixToBitMatrix(matrix, width, height);\n    };\n    /**\n     * Convert the ByteMatrix to BitMatrix.\n     *\n     * @param reqHeight The requested height of the image (in pixels) with the Datamatrix code\n     * @param reqWidth The requested width of the image (in pixels) with the Datamatrix code\n     * @param matrix The input matrix.\n     * @return The output matrix.\n     */\n    DataMatrixWriter.prototype.convertByteMatrixToBitMatrix = function (matrix, reqWidth, reqHeight) {\n        var matrixWidth = matrix.getWidth();\n        var matrixHeight = matrix.getHeight();\n        var outputWidth = Math.max(reqWidth, matrixWidth);\n        var outputHeight = Math.max(reqHeight, matrixHeight);\n        var multiple = Math.min(outputWidth / matrixWidth, outputHeight / matrixHeight);\n        var leftPadding = (outputWidth - matrixWidth * multiple) / 2;\n        var topPadding = (outputHeight - matrixHeight * multiple) / 2;\n        var output;\n        // remove padding if requested width and height are too small\n        if (reqHeight < matrixHeight || reqWidth < matrixWidth) {\n            leftPadding = 0;\n            topPadding = 0;\n            output = new BitMatrix(matrixWidth, matrixHeight);\n        }\n        else {\n            output = new BitMatrix(reqWidth, reqHeight);\n        }\n        output.clear();\n        for (var inputY = 0, outputY = topPadding; inputY < matrixHeight; inputY++, outputY += multiple) {\n            // Write the contents of this row of the bytematrix\n            for (var inputX = 0, outputX = leftPadding; inputX < matrixWidth; inputX++, outputX += multiple) {\n                if (matrix.get(inputX, inputY) === 1) {\n                    output.setRegion(outputX, outputY, multiple, multiple);\n                }\n            }\n        }\n        return output;\n    };\n    return DataMatrixWriter;\n}());\nexport default DataMatrixWriter;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;AACA,IAAI,mBAAkC;IAClC,SAAS,oBACT;IACA,iBAAiB,SAAS,CAAC,MAAM,GAAG,SAAU,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK;QAChF,IAAI,UAAU,KAAK,GAAG;YAAE,QAAQ;QAAM;QACtC,IAAI,SAAS,IAAI,OAAO,IAAI;YACxB,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,WAAW,qOAAA,CAAA,UAAa,CAAC,WAAW,EAAE;YACtC,MAAM,IAAI,MAAM,0CAA0C;QAC9D;QACA,IAAI,QAAQ,KAAK,SAAS,GAAG;YACzB,MAAM,IAAI,MAAM,8CAA8C,QAAQ,MAAM;QAChF;QACA,0CAA0C;QAC1C,IAAI,QAAQ,EAAE,cAAc;QAC5B,IAAI,UAAU;QACd,IAAI,UAAU;QACd,IAAI,SAAS,MAAM;YACf,IAAI,iBAAiB,MAAM,GAAG,CAAC,sOAAA,CAAA,UAAc,CAAC,iBAAiB;YAC/D,IAAI,kBAAkB,MAAM;gBACxB,QAAQ;YACZ;YACA,IAAI,mBAAmB,MAAM,GAAG,CAAC,sOAAA,CAAA,UAAc,CAAC,QAAQ;YACxD,IAAI,oBAAoB,MAAM;gBAC1B,UAAU;YACd;YACA,IAAI,mBAAmB,MAAM,GAAG,CAAC,sOAAA,CAAA,UAAc,CAAC,QAAQ;YACxD,IAAI,oBAAoB,MAAM;gBAC1B,UAAU;YACd;QACJ;QACA,2BAA2B;QAC3B,IAAI;QACJ,IAAI,oBAAoB,SAAS,QAC7B,MAAM,GAAG,CAAC,sOAAA,CAAA,UAAc,CAAC,mBAAmB,KAC5C,QAAQ,MAAM,GAAG,CAAC,sOAAA,CAAA,UAAc,CAAC,mBAAmB,EAAE,QAAQ;QAClE,IAAI,mBAAmB;YACnB,IAAI,mBAAmB,MAAM,GAAG,CAAC,sOAAA,CAAA,UAAc,CAAC,UAAU,KACtD,QAAQ,MAAM,GAAG,CAAC,sOAAA,CAAA,UAAc,CAAC,UAAU,EAAE,QAAQ;YACzD,IAAI,UAAU;YACd,IAAI,kBAAkB,MAAM,GAAG,CAAC,sOAAA,CAAA,UAAc,CAAC,aAAa;YAC5D,IAAI,iBAAiB;gBACjB,UAAU,uOAAA,CAAA,UAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,sOAAA,CAAA,UAAc,CAAC,aAAa,EAAE,QAAQ;YAC9E;YACA,UAAU,+PAAA,CAAA,iBAAc,CAAC,eAAe,CAAC,UAAU,SAAS,mBAAmB,OAAO,CAAC,GAAG;QAC9F,OACK;YACD,IAAI,kBAAkB,SAAS,QAC3B,MAAM,GAAG,CAAC,sOAAA,CAAA,UAAc,CAAC,SAAS,KAClC,QAAQ,MAAM,GAAG,CAAC,sOAAA,CAAA,UAAc,CAAC,SAAS,EAAE,QAAQ;YACxD,UAAU,gTAAA,CAAA,mBAAgB,CAAC,eAAe,CAAC,UAAU,OAAO,SAAS,SAAS;QAClF;QACA,IAAI,aAAa,oSAAA,CAAA,aAAU,CAAC,MAAM,CAAC,QAAQ,MAAM,EAAE,OAAO,SAAS,SAAS;QAC5E,0BAA0B;QAC1B,IAAI,YAAY,8SAAA,CAAA,kBAAe,CAAC,YAAY,CAAC,SAAS;QACtD,sCAAsC;QACtC,IAAI,YAAY,IAAI,gTAAA,CAAA,mBAAgB,CAAC,WAAW,WAAW,kBAAkB,IAAI,WAAW,mBAAmB;QAC/G,UAAU,KAAK;QACf,8BAA8B;QAC9B,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,YAAY,OAAO;IAC7D;IACA;;;;;;KAMC,GACD,iBAAiB,SAAS,CAAC,cAAc,GAAG,SAAU,SAAS,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM;QACtF,IAAI,cAAc,WAAW,kBAAkB;QAC/C,IAAI,eAAe,WAAW,mBAAmB;QACjD,IAAI,SAAS,IAAI,uPAAA,CAAA,UAAU,CAAC,WAAW,cAAc,IAAI,WAAW,eAAe;QACnF,IAAI,UAAU;QACd,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,IAAK;YACnC,yCAAyC;YACzC,IAAI,UAAU,KAAK;YACnB,IAAI,IAAI,WAAW,YAAY,KAAK,GAAG;gBACnC,UAAU;gBACV,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,cAAc,IAAI,IAAK;oBAClD,OAAO,UAAU,CAAC,SAAS,SAAS,IAAI,MAAM;oBAC9C;gBACJ;gBACA;YACJ;YACA,UAAU;YACV,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;gBAClC,kCAAkC;gBAClC,IAAI,IAAI,WAAW,WAAW,KAAK,GAAG;oBAClC,OAAO,UAAU,CAAC,SAAS,SAAS;oBACpC;gBACJ;gBACA,OAAO,UAAU,CAAC,SAAS,SAAS,UAAU,MAAM,CAAC,GAAG;gBACxD;gBACA,2CAA2C;gBAC3C,IAAI,IAAI,WAAW,WAAW,KAAK,WAAW,WAAW,GAAG,GAAG;oBAC3D,OAAO,UAAU,CAAC,SAAS,SAAS,IAAI,MAAM;oBAC9C;gBACJ;YACJ;YACA;YACA,mCAAmC;YACnC,IAAI,IAAI,WAAW,YAAY,KAAK,WAAW,YAAY,GAAG,GAAG;gBAC7D,UAAU;gBACV,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,cAAc,IAAI,IAAK;oBAClD,OAAO,UAAU,CAAC,SAAS,SAAS;oBACpC;gBACJ;gBACA;YACJ;QACJ;QACA,OAAO,IAAI,CAAC,4BAA4B,CAAC,QAAQ,OAAO;IAC5D;IACA;;;;;;;KAOC,GACD,iBAAiB,SAAS,CAAC,4BAA4B,GAAG,SAAU,MAAM,EAAE,QAAQ,EAAE,SAAS;QAC3F,IAAI,cAAc,OAAO,QAAQ;QACjC,IAAI,eAAe,OAAO,SAAS;QACnC,IAAI,cAAc,KAAK,GAAG,CAAC,UAAU;QACrC,IAAI,eAAe,KAAK,GAAG,CAAC,WAAW;QACvC,IAAI,WAAW,KAAK,GAAG,CAAC,cAAc,aAAa,eAAe;QAClE,IAAI,cAAc,CAAC,cAAc,cAAc,QAAQ,IAAI;QAC3D,IAAI,aAAa,CAAC,eAAe,eAAe,QAAQ,IAAI;QAC5D,IAAI;QACJ,6DAA6D;QAC7D,IAAI,YAAY,gBAAgB,WAAW,aAAa;YACpD,cAAc;YACd,aAAa;YACb,SAAS,IAAI,2OAAA,CAAA,UAAS,CAAC,aAAa;QACxC,OACK;YACD,SAAS,IAAI,2OAAA,CAAA,UAAS,CAAC,UAAU;QACrC;QACA,OAAO,KAAK;QACZ,IAAK,IAAI,SAAS,GAAG,UAAU,YAAY,SAAS,cAAc,UAAU,WAAW,SAAU;YAC7F,mDAAmD;YACnD,IAAK,IAAI,SAAS,GAAG,UAAU,aAAa,SAAS,aAAa,UAAU,WAAW,SAAU;gBAC7F,IAAI,OAAO,GAAG,CAAC,QAAQ,YAAY,GAAG;oBAClC,OAAO,SAAS,CAAC,SAAS,SAAS,UAAU;gBACjD;YACJ;QACJ;QACA,OAAO;IACX;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}]}