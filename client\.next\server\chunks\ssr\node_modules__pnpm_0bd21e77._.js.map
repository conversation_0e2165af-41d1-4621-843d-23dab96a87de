{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40swc%2Bhelpers%400.5.15/node_modules/%40swc/helpers/cjs/_interop_require_wildcard.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) return obj;\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") return { default: obj };\n\n    var cache = _getRequireWildcardCache(nodeInterop);\n\n    if (cache && cache.has(obj)) return cache.get(obj);\n\n    var newObj = { __proto__: null };\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n\n    for (var key in obj) {\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) Object.defineProperty(newObj, key, desc);\n            else newObj[key] = obj[key];\n        }\n    }\n\n    newObj.default = obj;\n\n    if (cache) cache.set(obj, newObj);\n\n    return newObj;\n}\nexports._ = _interop_require_wildcard;\n"], "names": [], "mappings": "AAEA,SAAS,yBAAyB,WAAW;IACzC,IAAI,OAAO,YAAY,YAAY,OAAO;IAE1C,IAAI,oBAAoB,IAAI;IAC5B,IAAI,mBAAmB,IAAI;IAE3B,OAAO,CAAC,2BAA2B,SAAS,WAAW;QACnD,OAAO,cAAc,mBAAmB;IAC5C,CAAC,EAAE;AACP;AACA,SAAS,0BAA0B,GAAG,EAAE,WAAW;IAC/C,IAAI,CAAC,eAAe,OAAO,IAAI,UAAU,EAAE,OAAO;IAClD,IAAI,QAAQ,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY,OAAO;QAAE,SAAS;IAAI;IAEhG,IAAI,QAAQ,yBAAyB;IAErC,IAAI,SAAS,MAAM,GAAG,CAAC,MAAM,OAAO,MAAM,GAAG,CAAC;IAE9C,IAAI,SAAS;QAAE,WAAW;IAAK;IAC/B,IAAI,wBAAwB,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAEpF,IAAK,IAAI,OAAO,IAAK;QACjB,IAAI,QAAQ,aAAa,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,MAAM;YACrE,IAAI,OAAO,wBAAwB,OAAO,wBAAwB,CAAC,KAAK,OAAO;YAC/E,IAAI,QAAQ,CAAC,KAAK,GAAG,IAAI,KAAK,GAAG,GAAG,OAAO,cAAc,CAAC,QAAQ,KAAK;iBAClE,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;QAC/B;IACJ;IAEA,OAAO,OAAO,GAAG;IAEjB,IAAI,OAAO,MAAM,GAAG,CAAC,KAAK;IAE1B,OAAO;AACX;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40swc%2Bhelpers%400.5.15/node_modules/%40swc/helpers/cjs/_class_private_field_loose_base.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _class_private_field_loose_base(receiver, privateKey) {\n    if (!Object.prototype.hasOwnProperty.call(receiver, privateKey)) {\n        throw new TypeError(\"attempted to use private field on non-instance\");\n    }\n\n    return receiver;\n}\nexports._ = _class_private_field_loose_base;\n"], "names": [], "mappings": "AAEA,SAAS,gCAAgC,QAAQ,EAAE,UAAU;IACzD,IAAI,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,aAAa;QAC7D,MAAM,IAAI,UAAU;IACxB;IAEA,OAAO;AACX;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40swc%2Bhelpers%400.5.15/node_modules/%40swc/helpers/cjs/_class_private_field_loose_key.cjs"], "sourcesContent": ["\"use strict\";\n\nvar id = 0;\n\nfunction _class_private_field_loose_key(name) {\n    return \"__private_\" + id++ + \"_\" + name;\n}\nexports._ = _class_private_field_loose_key;\n"], "names": [], "mappings": "AAEA,IAAI,KAAK;AAET,SAAS,+BAA+B,IAAI;IACxC,OAAO,eAAe,OAAO,MAAM;AACvC;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40swc%2Bhelpers%400.5.15/node_modules/%40swc/helpers/cjs/_interop_require_default.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : { default: obj };\n}\nexports._ = _interop_require_default;\n"], "names": [], "mappings": "AAEA,SAAS,yBAAyB,GAAG;IACjC,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,SAAS;IAAI;AACxD;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/styled-jsx%405.1.6_react%4019.1.0/node_modules/styled-jsx/dist/index/index.js"], "sourcesContent": ["require('client-only');\nvar React = require('react');\n\nfunction _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }\n\nvar React__default = /*#__PURE__*/_interopDefaultLegacy(React);\n\n/*\nBased on Glamor's sheet\nhttps://github.com/threepointone/glamor/blob/667b480d31b3721a905021b26e1290ce92ca2879/src/sheet.js\n*/ function _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n}\nvar isProd = typeof process !== \"undefined\" && process.env && process.env.NODE_ENV === \"production\";\nvar isString = function(o) {\n    return Object.prototype.toString.call(o) === \"[object String]\";\n};\nvar StyleSheet = /*#__PURE__*/ function() {\n    function StyleSheet(param) {\n        var ref = param === void 0 ? {} : param, _name = ref.name, name = _name === void 0 ? \"stylesheet\" : _name, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? isProd : _optimizeForSpeed;\n        invariant$1(isString(name), \"`name` must be a string\");\n        this._name = name;\n        this._deletedRulePlaceholder = \"#\" + name + \"-deleted-rule____{}\";\n        invariant$1(typeof optimizeForSpeed === \"boolean\", \"`optimizeForSpeed` must be a boolean\");\n        this._optimizeForSpeed = optimizeForSpeed;\n        this._serverSheet = undefined;\n        this._tags = [];\n        this._injected = false;\n        this._rulesCount = 0;\n        var node = typeof window !== \"undefined\" && document.querySelector('meta[property=\"csp-nonce\"]');\n        this._nonce = node ? node.getAttribute(\"content\") : null;\n    }\n    var _proto = StyleSheet.prototype;\n    _proto.setOptimizeForSpeed = function setOptimizeForSpeed(bool) {\n        invariant$1(typeof bool === \"boolean\", \"`setOptimizeForSpeed` accepts a boolean\");\n        invariant$1(this._rulesCount === 0, \"optimizeForSpeed cannot be when rules have already been inserted\");\n        this.flush();\n        this._optimizeForSpeed = bool;\n        this.inject();\n    };\n    _proto.isOptimizeForSpeed = function isOptimizeForSpeed() {\n        return this._optimizeForSpeed;\n    };\n    _proto.inject = function inject() {\n        var _this = this;\n        invariant$1(!this._injected, \"sheet already injected\");\n        this._injected = true;\n        if (typeof window !== \"undefined\" && this._optimizeForSpeed) {\n            this._tags[0] = this.makeStyleTag(this._name);\n            this._optimizeForSpeed = \"insertRule\" in this.getSheet();\n            if (!this._optimizeForSpeed) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: optimizeForSpeed mode not supported falling back to standard mode.\");\n                }\n                this.flush();\n                this._injected = true;\n            }\n            return;\n        }\n        this._serverSheet = {\n            cssRules: [],\n            insertRule: function(rule, index) {\n                if (typeof index === \"number\") {\n                    _this._serverSheet.cssRules[index] = {\n                        cssText: rule\n                    };\n                } else {\n                    _this._serverSheet.cssRules.push({\n                        cssText: rule\n                    });\n                }\n                return index;\n            },\n            deleteRule: function(index) {\n                _this._serverSheet.cssRules[index] = null;\n            }\n        };\n    };\n    _proto.getSheetForTag = function getSheetForTag(tag) {\n        if (tag.sheet) {\n            return tag.sheet;\n        }\n        // this weirdness brought to you by firefox\n        for(var i = 0; i < document.styleSheets.length; i++){\n            if (document.styleSheets[i].ownerNode === tag) {\n                return document.styleSheets[i];\n            }\n        }\n    };\n    _proto.getSheet = function getSheet() {\n        return this.getSheetForTag(this._tags[this._tags.length - 1]);\n    };\n    _proto.insertRule = function insertRule(rule, index) {\n        invariant$1(isString(rule), \"`insertRule` accepts only strings\");\n        if (typeof window === \"undefined\") {\n            if (typeof index !== \"number\") {\n                index = this._serverSheet.cssRules.length;\n            }\n            this._serverSheet.insertRule(rule, index);\n            return this._rulesCount++;\n        }\n        if (this._optimizeForSpeed) {\n            var sheet = this.getSheet();\n            if (typeof index !== \"number\") {\n                index = sheet.cssRules.length;\n            }\n            // this weirdness for perf, and chrome's weird bug\n            // https://stackoverflow.com/questions/20007992/chrome-suddenly-stopped-accepting-insertrule\n            try {\n                sheet.insertRule(rule, index);\n            } catch (error) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: illegal rule: \\n\\n\" + rule + \"\\n\\nSee https://stackoverflow.com/q/20007992 for more info\");\n                }\n                return -1;\n            }\n        } else {\n            var insertionPoint = this._tags[index];\n            this._tags.push(this.makeStyleTag(this._name, rule, insertionPoint));\n        }\n        return this._rulesCount++;\n    };\n    _proto.replaceRule = function replaceRule(index, rule) {\n        if (this._optimizeForSpeed || typeof window === \"undefined\") {\n            var sheet = typeof window !== \"undefined\" ? this.getSheet() : this._serverSheet;\n            if (!rule.trim()) {\n                rule = this._deletedRulePlaceholder;\n            }\n            if (!sheet.cssRules[index]) {\n                // @TBD Should we throw an error?\n                return index;\n            }\n            sheet.deleteRule(index);\n            try {\n                sheet.insertRule(rule, index);\n            } catch (error) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: illegal rule: \\n\\n\" + rule + \"\\n\\nSee https://stackoverflow.com/q/20007992 for more info\");\n                }\n                // In order to preserve the indices we insert a deleteRulePlaceholder\n                sheet.insertRule(this._deletedRulePlaceholder, index);\n            }\n        } else {\n            var tag = this._tags[index];\n            invariant$1(tag, \"old rule at index `\" + index + \"` not found\");\n            tag.textContent = rule;\n        }\n        return index;\n    };\n    _proto.deleteRule = function deleteRule(index) {\n        if (typeof window === \"undefined\") {\n            this._serverSheet.deleteRule(index);\n            return;\n        }\n        if (this._optimizeForSpeed) {\n            this.replaceRule(index, \"\");\n        } else {\n            var tag = this._tags[index];\n            invariant$1(tag, \"rule at index `\" + index + \"` not found\");\n            tag.parentNode.removeChild(tag);\n            this._tags[index] = null;\n        }\n    };\n    _proto.flush = function flush() {\n        this._injected = false;\n        this._rulesCount = 0;\n        if (typeof window !== \"undefined\") {\n            this._tags.forEach(function(tag) {\n                return tag && tag.parentNode.removeChild(tag);\n            });\n            this._tags = [];\n        } else {\n            // simpler on server\n            this._serverSheet.cssRules = [];\n        }\n    };\n    _proto.cssRules = function cssRules() {\n        var _this = this;\n        if (typeof window === \"undefined\") {\n            return this._serverSheet.cssRules;\n        }\n        return this._tags.reduce(function(rules, tag) {\n            if (tag) {\n                rules = rules.concat(Array.prototype.map.call(_this.getSheetForTag(tag).cssRules, function(rule) {\n                    return rule.cssText === _this._deletedRulePlaceholder ? null : rule;\n                }));\n            } else {\n                rules.push(null);\n            }\n            return rules;\n        }, []);\n    };\n    _proto.makeStyleTag = function makeStyleTag(name, cssString, relativeToTag) {\n        if (cssString) {\n            invariant$1(isString(cssString), \"makeStyleTag accepts only strings as second parameter\");\n        }\n        var tag = document.createElement(\"style\");\n        if (this._nonce) tag.setAttribute(\"nonce\", this._nonce);\n        tag.type = \"text/css\";\n        tag.setAttribute(\"data-\" + name, \"\");\n        if (cssString) {\n            tag.appendChild(document.createTextNode(cssString));\n        }\n        var head = document.head || document.getElementsByTagName(\"head\")[0];\n        if (relativeToTag) {\n            head.insertBefore(tag, relativeToTag);\n        } else {\n            head.appendChild(tag);\n        }\n        return tag;\n    };\n    _createClass(StyleSheet, [\n        {\n            key: \"length\",\n            get: function get() {\n                return this._rulesCount;\n            }\n        }\n    ]);\n    return StyleSheet;\n}();\nfunction invariant$1(condition, message) {\n    if (!condition) {\n        throw new Error(\"StyleSheet: \" + message + \".\");\n    }\n}\n\nfunction hash(str) {\n    var _$hash = 5381, i = str.length;\n    while(i){\n        _$hash = _$hash * 33 ^ str.charCodeAt(--i);\n    }\n    /* JavaScript does bitwise operations (like XOR, above) on 32-bit signed\n   * integers. Since we want the results to be always positive, convert the\n   * signed int to an unsigned by doing an unsigned bitshift. */ return _$hash >>> 0;\n}\nvar stringHash = hash;\n\nvar sanitize = function(rule) {\n    return rule.replace(/\\/style/gi, \"\\\\/style\");\n};\nvar cache = {};\n/**\n * computeId\n *\n * Compute and memoize a jsx id from a basedId and optionally props.\n */ function computeId(baseId, props) {\n    if (!props) {\n        return \"jsx-\" + baseId;\n    }\n    var propsToString = String(props);\n    var key = baseId + propsToString;\n    if (!cache[key]) {\n        cache[key] = \"jsx-\" + stringHash(baseId + \"-\" + propsToString);\n    }\n    return cache[key];\n}\n/**\n * computeSelector\n *\n * Compute and memoize dynamic selectors.\n */ function computeSelector(id, css) {\n    var selectoPlaceholderRegexp = /__jsx-style-dynamic-selector/g;\n    // Sanitize SSR-ed CSS.\n    // Client side code doesn't need to be sanitized since we use\n    // document.createTextNode (dev) and the CSSOM api sheet.insertRule (prod).\n    if (typeof window === \"undefined\") {\n        css = sanitize(css);\n    }\n    var idcss = id + css;\n    if (!cache[idcss]) {\n        cache[idcss] = css.replace(selectoPlaceholderRegexp, id);\n    }\n    return cache[idcss];\n}\n\nfunction mapRulesToStyle(cssRules, options) {\n    if (options === void 0) options = {};\n    return cssRules.map(function(args) {\n        var id = args[0];\n        var css = args[1];\n        return /*#__PURE__*/ React__default[\"default\"].createElement(\"style\", {\n            id: \"__\" + id,\n            // Avoid warnings upon render with a key\n            key: \"__\" + id,\n            nonce: options.nonce ? options.nonce : undefined,\n            dangerouslySetInnerHTML: {\n                __html: css\n            }\n        });\n    });\n}\nvar StyleSheetRegistry = /*#__PURE__*/ function() {\n    function StyleSheetRegistry(param) {\n        var ref = param === void 0 ? {} : param, _styleSheet = ref.styleSheet, styleSheet = _styleSheet === void 0 ? null : _styleSheet, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? false : _optimizeForSpeed;\n        this._sheet = styleSheet || new StyleSheet({\n            name: \"styled-jsx\",\n            optimizeForSpeed: optimizeForSpeed\n        });\n        this._sheet.inject();\n        if (styleSheet && typeof optimizeForSpeed === \"boolean\") {\n            this._sheet.setOptimizeForSpeed(optimizeForSpeed);\n            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();\n        }\n        this._fromServer = undefined;\n        this._indices = {};\n        this._instancesCounts = {};\n    }\n    var _proto = StyleSheetRegistry.prototype;\n    _proto.add = function add(props) {\n        var _this = this;\n        if (undefined === this._optimizeForSpeed) {\n            this._optimizeForSpeed = Array.isArray(props.children);\n            this._sheet.setOptimizeForSpeed(this._optimizeForSpeed);\n            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();\n        }\n        if (typeof window !== \"undefined\" && !this._fromServer) {\n            this._fromServer = this.selectFromServer();\n            this._instancesCounts = Object.keys(this._fromServer).reduce(function(acc, tagName) {\n                acc[tagName] = 0;\n                return acc;\n            }, {});\n        }\n        var ref = this.getIdAndRules(props), styleId = ref.styleId, rules = ref.rules;\n        // Deduping: just increase the instances count.\n        if (styleId in this._instancesCounts) {\n            this._instancesCounts[styleId] += 1;\n            return;\n        }\n        var indices = rules.map(function(rule) {\n            return _this._sheet.insertRule(rule);\n        })// Filter out invalid rules\n        .filter(function(index) {\n            return index !== -1;\n        });\n        this._indices[styleId] = indices;\n        this._instancesCounts[styleId] = 1;\n    };\n    _proto.remove = function remove(props) {\n        var _this = this;\n        var styleId = this.getIdAndRules(props).styleId;\n        invariant(styleId in this._instancesCounts, \"styleId: `\" + styleId + \"` not found\");\n        this._instancesCounts[styleId] -= 1;\n        if (this._instancesCounts[styleId] < 1) {\n            var tagFromServer = this._fromServer && this._fromServer[styleId];\n            if (tagFromServer) {\n                tagFromServer.parentNode.removeChild(tagFromServer);\n                delete this._fromServer[styleId];\n            } else {\n                this._indices[styleId].forEach(function(index) {\n                    return _this._sheet.deleteRule(index);\n                });\n                delete this._indices[styleId];\n            }\n            delete this._instancesCounts[styleId];\n        }\n    };\n    _proto.update = function update(props, nextProps) {\n        this.add(nextProps);\n        this.remove(props);\n    };\n    _proto.flush = function flush() {\n        this._sheet.flush();\n        this._sheet.inject();\n        this._fromServer = undefined;\n        this._indices = {};\n        this._instancesCounts = {};\n    };\n    _proto.cssRules = function cssRules() {\n        var _this = this;\n        var fromServer = this._fromServer ? Object.keys(this._fromServer).map(function(styleId) {\n            return [\n                styleId,\n                _this._fromServer[styleId]\n            ];\n        }) : [];\n        var cssRules = this._sheet.cssRules();\n        return fromServer.concat(Object.keys(this._indices).map(function(styleId) {\n            return [\n                styleId,\n                _this._indices[styleId].map(function(index) {\n                    return cssRules[index].cssText;\n                }).join(_this._optimizeForSpeed ? \"\" : \"\\n\")\n            ];\n        })// filter out empty rules\n        .filter(function(rule) {\n            return Boolean(rule[1]);\n        }));\n    };\n    _proto.styles = function styles(options) {\n        return mapRulesToStyle(this.cssRules(), options);\n    };\n    _proto.getIdAndRules = function getIdAndRules(props) {\n        var css = props.children, dynamic = props.dynamic, id = props.id;\n        if (dynamic) {\n            var styleId = computeId(id, dynamic);\n            return {\n                styleId: styleId,\n                rules: Array.isArray(css) ? css.map(function(rule) {\n                    return computeSelector(styleId, rule);\n                }) : [\n                    computeSelector(styleId, css)\n                ]\n            };\n        }\n        return {\n            styleId: computeId(id),\n            rules: Array.isArray(css) ? css : [\n                css\n            ]\n        };\n    };\n    /**\n   * selectFromServer\n   *\n   * Collects style tags from the document with id __jsx-XXX\n   */ _proto.selectFromServer = function selectFromServer() {\n        var elements = Array.prototype.slice.call(document.querySelectorAll('[id^=\"__jsx-\"]'));\n        return elements.reduce(function(acc, element) {\n            var id = element.id.slice(2);\n            acc[id] = element;\n            return acc;\n        }, {});\n    };\n    return StyleSheetRegistry;\n}();\nfunction invariant(condition, message) {\n    if (!condition) {\n        throw new Error(\"StyleSheetRegistry: \" + message + \".\");\n    }\n}\nvar StyleSheetContext = /*#__PURE__*/ React.createContext(null);\nStyleSheetContext.displayName = \"StyleSheetContext\";\nfunction createStyleRegistry() {\n    return new StyleSheetRegistry();\n}\nfunction StyleRegistry(param) {\n    var configuredRegistry = param.registry, children = param.children;\n    var rootRegistry = React.useContext(StyleSheetContext);\n    var ref = React.useState(function() {\n        return rootRegistry || configuredRegistry || createStyleRegistry();\n    }), registry = ref[0];\n    return /*#__PURE__*/ React__default[\"default\"].createElement(StyleSheetContext.Provider, {\n        value: registry\n    }, children);\n}\nfunction useStyleRegistry() {\n    return React.useContext(StyleSheetContext);\n}\n\n// Opt-into the new `useInsertionEffect` API in React 18, fallback to `useLayoutEffect`.\n// https://github.com/reactwg/react-18/discussions/110\nvar useInsertionEffect = React__default[\"default\"].useInsertionEffect || React__default[\"default\"].useLayoutEffect;\nvar defaultRegistry = typeof window !== \"undefined\" ? createStyleRegistry() : undefined;\nfunction JSXStyle(props) {\n    var registry = defaultRegistry ? defaultRegistry : useStyleRegistry();\n    // If `registry` does not exist, we do nothing here.\n    if (!registry) {\n        return null;\n    }\n    if (typeof window === \"undefined\") {\n        registry.add(props);\n        return null;\n    }\n    useInsertionEffect(function() {\n        registry.add(props);\n        return function() {\n            registry.remove(props);\n        };\n    // props.children can be string[], will be striped since id is identical\n    }, [\n        props.id,\n        String(props.dynamic)\n    ]);\n    return null;\n}\nJSXStyle.dynamic = function(info) {\n    return info.map(function(tagInfo) {\n        var baseId = tagInfo[0];\n        var props = tagInfo[1];\n        return computeId(baseId, props);\n    }).join(\" \");\n};\n\nexports.StyleRegistry = StyleRegistry;\nexports.createStyleRegistry = createStyleRegistry;\nexports.style = JSXStyle;\nexports.useStyleRegistry = useStyleRegistry;\n"], "names": [], "mappings": ";AACA,IAAI;AAEJ,SAAS,sBAAuB,CAAC;IAAI,OAAO,KAAK,OAAO,MAAM,YAAY,aAAa,IAAI,IAAI;QAAE,WAAW;IAAE;AAAG;AAEjH,IAAI,iBAAiB,WAAW,GAAE,sBAAsB;AAExD;;;AAGA,GAAG,SAAS,kBAAkB,MAAM,EAAE,KAAK;IACvC,IAAI,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAI;QACjC,IAAI,aAAa,KAAK,CAAC,EAAE;QACzB,WAAW,UAAU,GAAG,WAAW,UAAU,IAAI;QACjD,WAAW,YAAY,GAAG;QAC1B,IAAI,WAAW,YAAY,WAAW,QAAQ,GAAG;QACjD,OAAO,cAAc,CAAC,QAAQ,WAAW,GAAG,EAAE;IAClD;AACJ;AACA,SAAS,aAAa,WAAW,EAAE,UAAU,EAAE,WAAW;IACtD,IAAI,YAAY,kBAAkB,YAAY,SAAS,EAAE;IACzD,IAAI,aAAa,kBAAkB,aAAa;IAChD,OAAO;AACX;AACA,IAAI,SAAS,OAAO,YAAY,eAAe,QAAQ,GAAG,IAAI,oDAAyB;AACvF,IAAI,WAAW,SAAS,CAAC;IACrB,OAAO,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO;AACjD;AACA,IAAI,aAAa,WAAW,GAAG;IAC3B,SAAS,WAAW,KAAK;QACrB,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,IAAI,OAAO,QAAQ,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,IAAI,eAAe,OAAO,oBAAoB,IAAI,gBAAgB,EAAE,mBAAmB,sBAAsB,KAAK,IAAI,SAAS;QAChN,YAAY,SAAS,OAAO;QAC5B,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,uBAAuB,GAAG,MAAM,OAAO;QAC5C,YAAY,OAAO,qBAAqB,WAAW;QACnD,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,KAAK,GAAG,EAAE;QACf,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,OAAO,gBAAkB,eAAe,SAAS,aAAa,CAAC;QACnE,IAAI,CAAC,MAAM,GAAG,sCAAO,0BAA+B;IACxD;IACA,IAAI,SAAS,WAAW,SAAS;IACjC,OAAO,mBAAmB,GAAG,SAAS,oBAAoB,IAAI;QAC1D,YAAY,OAAO,SAAS,WAAW;QACvC,YAAY,IAAI,CAAC,WAAW,KAAK,GAAG;QACpC,IAAI,CAAC,KAAK;QACV,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,MAAM;IACf;IACA,OAAO,kBAAkB,GAAG,SAAS;QACjC,OAAO,IAAI,CAAC,iBAAiB;IACjC;IACA,OAAO,MAAM,GAAG,SAAS;QACrB,IAAI,QAAQ,IAAI;QAChB,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE;QAC7B,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,gBAAkB,eAAe,IAAI,CAAC,iBAAiB;;QAY3D,IAAI,CAAC,YAAY,GAAG;YAChB,UAAU,EAAE;YACZ,YAAY,SAAS,IAAI,EAAE,KAAK;gBAC5B,IAAI,OAAO,UAAU,UAAU;oBAC3B,MAAM,YAAY,CAAC,QAAQ,CAAC,MAAM,GAAG;wBACjC,SAAS;oBACb;gBACJ,OAAO;oBACH,MAAM,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;wBAC7B,SAAS;oBACb;gBACJ;gBACA,OAAO;YACX;YACA,YAAY,SAAS,KAAK;gBACtB,MAAM,YAAY,CAAC,QAAQ,CAAC,MAAM,GAAG;YACzC;QACJ;IACJ;IACA,OAAO,cAAc,GAAG,SAAS,eAAe,GAAG;QAC/C,IAAI,IAAI,KAAK,EAAE;YACX,OAAO,IAAI,KAAK;QACpB;QACA,2CAA2C;QAC3C,IAAI,IAAI,IAAI,GAAG,IAAI,SAAS,WAAW,CAAC,MAAM,EAAE,IAAI;YAChD,IAAI,SAAS,WAAW,CAAC,EAAE,CAAC,SAAS,KAAK,KAAK;gBAC3C,OAAO,SAAS,WAAW,CAAC,EAAE;YAClC;QACJ;IACJ;IACA,OAAO,QAAQ,GAAG,SAAS;QACvB,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;IAChE;IACA,OAAO,UAAU,GAAG,SAAS,WAAW,IAAI,EAAE,KAAK;QAC/C,YAAY,SAAS,OAAO;QAC5B,wCAAmC;YAC/B,IAAI,OAAO,UAAU,UAAU;gBAC3B,QAAQ,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM;YAC7C;YACA,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM;YACnC,OAAO,IAAI,CAAC,WAAW;QAC3B;;;QAEI,IAAI;QAeJ,IAAI;IAIZ;IACA,OAAO,WAAW,GAAG,SAAS,YAAY,KAAK,EAAE,IAAI;QACjD,IAAI,IAAI,CAAC,iBAAiB,IAAI,gBAAkB,aAAa;YACzD,IAAI,QAAQ,sCAAgC,0BAAkB,IAAI,CAAC,YAAY;YAC/E,IAAI,CAAC,KAAK,IAAI,IAAI;gBACd,OAAO,IAAI,CAAC,uBAAuB;YACvC;YACA,IAAI,CAAC,MAAM,QAAQ,CAAC,MAAM,EAAE;gBACxB,iCAAiC;gBACjC,OAAO;YACX;YACA,MAAM,UAAU,CAAC;YACjB,IAAI;gBACA,MAAM,UAAU,CAAC,MAAM;YAC3B,EAAE,OAAO,OAAO;gBACZ,wCAAa;oBACT,QAAQ,IAAI,CAAC,mCAAmC,OAAO;gBAC3D;gBACA,qEAAqE;gBACrE,MAAM,UAAU,CAAC,IAAI,CAAC,uBAAuB,EAAE;YACnD;QACJ;;YACI,IAAI;;QAIR,OAAO;IACX;IACA,OAAO,UAAU,GAAG,SAAS,WAAW,KAAK;QACzC,wCAAmC;YAC/B,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC;YAC7B;QACJ;;;QAII,IAAI;IAKZ;IACA,OAAO,KAAK,GAAG,SAAS;QACpB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,WAAW,GAAG;QACnB;;aAKO;YACH,oBAAoB;YACpB,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,EAAE;QACnC;IACJ;IACA,OAAO,QAAQ,GAAG,SAAS;QACvB,IAAI,QAAQ,IAAI;QAChB,wCAAmC;YAC/B,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ;QACrC;;;IAWJ;IACA,OAAO,YAAY,GAAG,SAAS,aAAa,IAAI,EAAE,SAAS,EAAE,aAAa;QACtE,IAAI,WAAW;YACX,YAAY,SAAS,YAAY;QACrC;QACA,IAAI,MAAM,SAAS,aAAa,CAAC;QACjC,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,YAAY,CAAC,SAAS,IAAI,CAAC,MAAM;QACtD,IAAI,IAAI,GAAG;QACX,IAAI,YAAY,CAAC,UAAU,MAAM;QACjC,IAAI,WAAW;YACX,IAAI,WAAW,CAAC,SAAS,cAAc,CAAC;QAC5C;QACA,IAAI,OAAO,SAAS,IAAI,IAAI,SAAS,oBAAoB,CAAC,OAAO,CAAC,EAAE;QACpE,IAAI,eAAe;YACf,KAAK,YAAY,CAAC,KAAK;QAC3B,OAAO;YACH,KAAK,WAAW,CAAC;QACrB;QACA,OAAO;IACX;IACA,aAAa,YAAY;QACrB;YACI,KAAK;YACL,KAAK,SAAS;gBACV,OAAO,IAAI,CAAC,WAAW;YAC3B;QACJ;KACH;IACD,OAAO;AACX;AACA,SAAS,YAAY,SAAS,EAAE,OAAO;IACnC,IAAI,CAAC,WAAW;QACZ,MAAM,IAAI,MAAM,iBAAiB,UAAU;IAC/C;AACJ;AAEA,SAAS,KAAK,GAAG;IACb,IAAI,SAAS,MAAM,IAAI,IAAI,MAAM;IACjC,MAAM,EAAE;QACJ,SAAS,SAAS,KAAK,IAAI,UAAU,CAAC,EAAE;IAC5C;IACA;;8DAE0D,GAAG,OAAO,WAAW;AACnF;AACA,IAAI,aAAa;AAEjB,IAAI,WAAW,SAAS,IAAI;IACxB,OAAO,KAAK,OAAO,CAAC,aAAa;AACrC;AACA,IAAI,QAAQ,CAAC;AACb;;;;CAIC,GAAG,SAAS,UAAU,MAAM,EAAE,KAAK;IAChC,IAAI,CAAC,OAAO;QACR,OAAO,SAAS;IACpB;IACA,IAAI,gBAAgB,OAAO;IAC3B,IAAI,MAAM,SAAS;IACnB,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;QACb,KAAK,CAAC,IAAI,GAAG,SAAS,WAAW,SAAS,MAAM;IACpD;IACA,OAAO,KAAK,CAAC,IAAI;AACrB;AACA;;;;CAIC,GAAG,SAAS,gBAAgB,EAAE,EAAE,GAAG;IAChC,IAAI,2BAA2B;IAC/B,uBAAuB;IACvB,6DAA6D;IAC7D,2EAA2E;IAC3E,wCAAmC;QAC/B,MAAM,SAAS;IACnB;IACA,IAAI,QAAQ,KAAK;IACjB,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;QACf,KAAK,CAAC,MAAM,GAAG,IAAI,OAAO,CAAC,0BAA0B;IACzD;IACA,OAAO,KAAK,CAAC,MAAM;AACvB;AAEA,SAAS,gBAAgB,QAAQ,EAAE,OAAO;IACtC,IAAI,YAAY,KAAK,GAAG,UAAU,CAAC;IACnC,OAAO,SAAS,GAAG,CAAC,SAAS,IAAI;QAC7B,IAAI,KAAK,IAAI,CAAC,EAAE;QAChB,IAAI,MAAM,IAAI,CAAC,EAAE;QACjB,OAAO,WAAW,GAAG,cAAc,CAAC,UAAU,CAAC,aAAa,CAAC,SAAS;YAClE,IAAI,OAAO;YACX,wCAAwC;YACxC,KAAK,OAAO;YACZ,OAAO,QAAQ,KAAK,GAAG,QAAQ,KAAK,GAAG;YACvC,yBAAyB;gBACrB,QAAQ;YACZ;QACJ;IACJ;AACJ;AACA,IAAI,qBAAqB,WAAW,GAAG;IACnC,SAAS,mBAAmB,KAAK;QAC7B,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,IAAI,OAAO,cAAc,IAAI,UAAU,EAAE,aAAa,gBAAgB,KAAK,IAAI,OAAO,aAAa,oBAAoB,IAAI,gBAAgB,EAAE,mBAAmB,sBAAsB,KAAK,IAAI,QAAQ;QACrO,IAAI,CAAC,MAAM,GAAG,cAAc,IAAI,WAAW;YACvC,MAAM;YACN,kBAAkB;QACtB;QACA,IAAI,CAAC,MAAM,CAAC,MAAM;QAClB,IAAI,cAAc,OAAO,qBAAqB,WAAW;YACrD,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC;YAChC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB;QAC3D;QACA,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,QAAQ,GAAG,CAAC;QACjB,IAAI,CAAC,gBAAgB,GAAG,CAAC;IAC7B;IACA,IAAI,SAAS,mBAAmB,SAAS;IACzC,OAAO,GAAG,GAAG,SAAS,IAAI,KAAK;QAC3B,IAAI,QAAQ,IAAI;QAChB,IAAI,cAAc,IAAI,CAAC,iBAAiB,EAAE;YACtC,IAAI,CAAC,iBAAiB,GAAG,MAAM,OAAO,CAAC,MAAM,QAAQ;YACrD,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,iBAAiB;YACtD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB;QAC3D;QACA,IAAI,gBAAkB,eAAe,CAAC,IAAI,CAAC,WAAW;;QAOtD,IAAI,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,UAAU,IAAI,OAAO,EAAE,QAAQ,IAAI,KAAK;QAC7E,+CAA+C;QAC/C,IAAI,WAAW,IAAI,CAAC,gBAAgB,EAAE;YAClC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,IAAI;YAClC;QACJ;QACA,IAAI,UAAU,MAAM,GAAG,CAAC,SAAS,IAAI;YACjC,OAAO,MAAM,MAAM,CAAC,UAAU,CAAC;QACnC,GAAE,2BAA2B;SAC5B,MAAM,CAAC,SAAS,KAAK;YAClB,OAAO,UAAU,CAAC;QACtB;QACA,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG;QACzB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,GAAG;IACrC;IACA,OAAO,MAAM,GAAG,SAAS,OAAO,KAAK;QACjC,IAAI,QAAQ,IAAI;QAChB,IAAI,UAAU,IAAI,CAAC,aAAa,CAAC,OAAO,OAAO;QAC/C,UAAU,WAAW,IAAI,CAAC,gBAAgB,EAAE,eAAe,UAAU;QACrE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,IAAI;QAClC,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,GAAG,GAAG;YACpC,IAAI,gBAAgB,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ;YACjE,IAAI,eAAe;gBACf,cAAc,UAAU,CAAC,WAAW,CAAC;gBACrC,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;YACpC,OAAO;gBACH,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,KAAK;oBACzC,OAAO,MAAM,MAAM,CAAC,UAAU,CAAC;gBACnC;gBACA,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ;YACjC;YACA,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ;QACzC;IACJ;IACA,OAAO,MAAM,GAAG,SAAS,OAAO,KAAK,EAAE,SAAS;QAC5C,IAAI,CAAC,GAAG,CAAC;QACT,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,OAAO,KAAK,GAAG,SAAS;QACpB,IAAI,CAAC,MAAM,CAAC,KAAK;QACjB,IAAI,CAAC,MAAM,CAAC,MAAM;QAClB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,QAAQ,GAAG,CAAC;QACjB,IAAI,CAAC,gBAAgB,GAAG,CAAC;IAC7B;IACA,OAAO,QAAQ,GAAG,SAAS;QACvB,IAAI,QAAQ,IAAI;QAChB,IAAI,aAAa,IAAI,CAAC,WAAW,GAAG,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,SAAS,OAAO;YAClF,OAAO;gBACH;gBACA,MAAM,WAAW,CAAC,QAAQ;aAC7B;QACL,KAAK,EAAE;QACP,IAAI,WAAW,IAAI,CAAC,MAAM,CAAC,QAAQ;QACnC,OAAO,WAAW,MAAM,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,SAAS,OAAO;YACpE,OAAO;gBACH;gBACA,MAAM,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,KAAK;oBACtC,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO;gBAClC,GAAG,IAAI,CAAC,MAAM,iBAAiB,GAAG,KAAK;aAC1C;QACL,GAAE,yBAAyB;SAC1B,MAAM,CAAC,SAAS,IAAI;YACjB,OAAO,QAAQ,IAAI,CAAC,EAAE;QAC1B;IACJ;IACA,OAAO,MAAM,GAAG,SAAS,OAAO,OAAO;QACnC,OAAO,gBAAgB,IAAI,CAAC,QAAQ,IAAI;IAC5C;IACA,OAAO,aAAa,GAAG,SAAS,cAAc,KAAK;QAC/C,IAAI,MAAM,MAAM,QAAQ,EAAE,UAAU,MAAM,OAAO,EAAE,KAAK,MAAM,EAAE;QAChE,IAAI,SAAS;YACT,IAAI,UAAU,UAAU,IAAI;YAC5B,OAAO;gBACH,SAAS;gBACT,OAAO,MAAM,OAAO,CAAC,OAAO,IAAI,GAAG,CAAC,SAAS,IAAI;oBAC7C,OAAO,gBAAgB,SAAS;gBACpC,KAAK;oBACD,gBAAgB,SAAS;iBAC5B;YACL;QACJ;QACA,OAAO;YACH,SAAS,UAAU;YACnB,OAAO,MAAM,OAAO,CAAC,OAAO,MAAM;gBAC9B;aACH;QACL;IACJ;IACA;;;;GAID,GAAG,OAAO,gBAAgB,GAAG,SAAS;QACjC,IAAI,WAAW,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,gBAAgB,CAAC;QACpE,OAAO,SAAS,MAAM,CAAC,SAAS,GAAG,EAAE,OAAO;YACxC,IAAI,KAAK,QAAQ,EAAE,CAAC,KAAK,CAAC;YAC1B,GAAG,CAAC,GAAG,GAAG;YACV,OAAO;QACX,GAAG,CAAC;IACR;IACA,OAAO;AACX;AACA,SAAS,UAAU,SAAS,EAAE,OAAO;IACjC,IAAI,CAAC,WAAW;QACZ,MAAM,IAAI,MAAM,yBAAyB,UAAU;IACvD;AACJ;AACA,IAAI,oBAAoB,WAAW,GAAG,MAAM,aAAa,CAAC;AAC1D,kBAAkB,WAAW,GAAG;AAChC,SAAS;IACL,OAAO,IAAI;AACf;AACA,SAAS,cAAc,KAAK;IACxB,IAAI,qBAAqB,MAAM,QAAQ,EAAE,WAAW,MAAM,QAAQ;IAClE,IAAI,eAAe,MAAM,UAAU,CAAC;IACpC,IAAI,MAAM,MAAM,QAAQ,CAAC;QACrB,OAAO,gBAAgB,sBAAsB;IACjD,IAAI,WAAW,GAAG,CAAC,EAAE;IACrB,OAAO,WAAW,GAAG,cAAc,CAAC,UAAU,CAAC,aAAa,CAAC,kBAAkB,QAAQ,EAAE;QACrF,OAAO;IACX,GAAG;AACP;AACA,SAAS;IACL,OAAO,MAAM,UAAU,CAAC;AAC5B;AAEA,wFAAwF;AACxF,sDAAsD;AACtD,IAAI,qBAAqB,cAAc,CAAC,UAAU,CAAC,kBAAkB,IAAI,cAAc,CAAC,UAAU,CAAC,eAAe;AAClH,IAAI,kBAAkB,sCAAgC,0BAAwB;AAC9E,SAAS,SAAS,KAAK;IACnB,IAAI,WAAW,kBAAkB,kBAAkB;IACnD,oDAAoD;IACpD,IAAI,CAAC,UAAU;QACX,OAAO;IACX;IACA,wCAAmC;QAC/B,SAAS,GAAG,CAAC;QACb,OAAO;IACX;;;AAYJ;AACA,SAAS,OAAO,GAAG,SAAS,IAAI;IAC5B,OAAO,KAAK,GAAG,CAAC,SAAS,OAAO;QAC5B,IAAI,SAAS,OAAO,CAAC,EAAE;QACvB,IAAI,QAAQ,OAAO,CAAC,EAAE;QACtB,OAAO,UAAU,QAAQ;IAC7B,GAAG,IAAI,CAAC;AACZ;AAEA,QAAQ,aAAa,GAAG;AACxB,QAAQ,mBAAmB,GAAG;AAC9B,QAAQ,KAAK,GAAG;AAChB,QAAQ,gBAAgB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 522, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/styled-jsx%405.1.6_react%4019.1.0/node_modules/styled-jsx/style.js"], "sourcesContent": ["module.exports = require('./dist/index').style\n"], "names": [], "mappings": "AAAA,OAAO,OAAO,GAAG,yJAAwB,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 527, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/qrcode.react%404.2.0_react%4019.1.0/node_modules/qrcode.react/lib/esm/index.js"], "sourcesContent": ["var __defProp = Object.defineProperty;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\n\n// src/index.tsx\nimport React from \"react\";\n\n// src/third-party/qrcodegen/index.ts\n/**\n * @license QR Code generator library (TypeScript)\n * Copyright (c) Project Nayuki.\n * SPDX-License-Identifier: MIT\n */\nvar qrcodegen;\n((qrcodegen2) => {\n  const _QrCode = class _QrCode {\n    /*-- Constructor (low level) and fields --*/\n    // Creates a new QR Code with the given version number,\n    // error correction level, data codeword bytes, and mask number.\n    // This is a low-level API that most users should not use directly.\n    // A mid-level API is the encodeSegments() function.\n    constructor(version, errorCorrectionLevel, dataCodewords, msk) {\n      this.version = version;\n      this.errorCorrectionLevel = errorCorrectionLevel;\n      // The modules of this QR Code (false = light, true = dark).\n      // Immutable after constructor finishes. Accessed through getModule().\n      this.modules = [];\n      // Indicates function modules that are not subjected to masking. Discarded when constructor finishes.\n      this.isFunction = [];\n      if (version < _QrCode.MIN_VERSION || version > _QrCode.MAX_VERSION)\n        throw new RangeError(\"Version value out of range\");\n      if (msk < -1 || msk > 7)\n        throw new RangeError(\"Mask value out of range\");\n      this.size = version * 4 + 17;\n      let row = [];\n      for (let i = 0; i < this.size; i++)\n        row.push(false);\n      for (let i = 0; i < this.size; i++) {\n        this.modules.push(row.slice());\n        this.isFunction.push(row.slice());\n      }\n      this.drawFunctionPatterns();\n      const allCodewords = this.addEccAndInterleave(dataCodewords);\n      this.drawCodewords(allCodewords);\n      if (msk == -1) {\n        let minPenalty = 1e9;\n        for (let i = 0; i < 8; i++) {\n          this.applyMask(i);\n          this.drawFormatBits(i);\n          const penalty = this.getPenaltyScore();\n          if (penalty < minPenalty) {\n            msk = i;\n            minPenalty = penalty;\n          }\n          this.applyMask(i);\n        }\n      }\n      assert(0 <= msk && msk <= 7);\n      this.mask = msk;\n      this.applyMask(msk);\n      this.drawFormatBits(msk);\n      this.isFunction = [];\n    }\n    /*-- Static factory functions (high level) --*/\n    // Returns a QR Code representing the given Unicode text string at the given error correction level.\n    // As a conservative upper bound, this function is guaranteed to succeed for strings that have 738 or fewer\n    // Unicode code points (not UTF-16 code units) if the low error correction level is used. The smallest possible\n    // QR Code version is automatically chosen for the output. The ECC level of the result may be higher than the\n    // ecl argument if it can be done without increasing the version.\n    static encodeText(text, ecl) {\n      const segs = qrcodegen2.QrSegment.makeSegments(text);\n      return _QrCode.encodeSegments(segs, ecl);\n    }\n    // Returns a QR Code representing the given binary data at the given error correction level.\n    // This function always encodes using the binary segment mode, not any text mode. The maximum number of\n    // bytes allowed is 2953. The smallest possible QR Code version is automatically chosen for the output.\n    // The ECC level of the result may be higher than the ecl argument if it can be done without increasing the version.\n    static encodeBinary(data, ecl) {\n      const seg = qrcodegen2.QrSegment.makeBytes(data);\n      return _QrCode.encodeSegments([seg], ecl);\n    }\n    /*-- Static factory functions (mid level) --*/\n    // Returns a QR Code representing the given segments with the given encoding parameters.\n    // The smallest possible QR Code version within the given range is automatically\n    // chosen for the output. Iff boostEcl is true, then the ECC level of the result\n    // may be higher than the ecl argument if it can be done without increasing the\n    // version. The mask number is either between 0 to 7 (inclusive) to force that\n    // mask, or -1 to automatically choose an appropriate mask (which may be slow).\n    // This function allows the user to create a custom sequence of segments that switches\n    // between modes (such as alphanumeric and byte) to encode text in less space.\n    // This is a mid-level API; the high-level API is encodeText() and encodeBinary().\n    static encodeSegments(segs, ecl, minVersion = 1, maxVersion = 40, mask = -1, boostEcl = true) {\n      if (!(_QrCode.MIN_VERSION <= minVersion && minVersion <= maxVersion && maxVersion <= _QrCode.MAX_VERSION) || mask < -1 || mask > 7)\n        throw new RangeError(\"Invalid value\");\n      let version;\n      let dataUsedBits;\n      for (version = minVersion; ; version++) {\n        const dataCapacityBits2 = _QrCode.getNumDataCodewords(version, ecl) * 8;\n        const usedBits = QrSegment.getTotalBits(segs, version);\n        if (usedBits <= dataCapacityBits2) {\n          dataUsedBits = usedBits;\n          break;\n        }\n        if (version >= maxVersion)\n          throw new RangeError(\"Data too long\");\n      }\n      for (const newEcl of [_QrCode.Ecc.MEDIUM, _QrCode.Ecc.QUARTILE, _QrCode.Ecc.HIGH]) {\n        if (boostEcl && dataUsedBits <= _QrCode.getNumDataCodewords(version, newEcl) * 8)\n          ecl = newEcl;\n      }\n      let bb = [];\n      for (const seg of segs) {\n        appendBits(seg.mode.modeBits, 4, bb);\n        appendBits(seg.numChars, seg.mode.numCharCountBits(version), bb);\n        for (const b of seg.getData())\n          bb.push(b);\n      }\n      assert(bb.length == dataUsedBits);\n      const dataCapacityBits = _QrCode.getNumDataCodewords(version, ecl) * 8;\n      assert(bb.length <= dataCapacityBits);\n      appendBits(0, Math.min(4, dataCapacityBits - bb.length), bb);\n      appendBits(0, (8 - bb.length % 8) % 8, bb);\n      assert(bb.length % 8 == 0);\n      for (let padByte = 236; bb.length < dataCapacityBits; padByte ^= 236 ^ 17)\n        appendBits(padByte, 8, bb);\n      let dataCodewords = [];\n      while (dataCodewords.length * 8 < bb.length)\n        dataCodewords.push(0);\n      bb.forEach((b, i) => dataCodewords[i >>> 3] |= b << 7 - (i & 7));\n      return new _QrCode(version, ecl, dataCodewords, mask);\n    }\n    /*-- Accessor methods --*/\n    // Returns the color of the module (pixel) at the given coordinates, which is false\n    // for light or true for dark. The top left corner has the coordinates (x=0, y=0).\n    // If the given coordinates are out of bounds, then false (light) is returned.\n    getModule(x, y) {\n      return 0 <= x && x < this.size && 0 <= y && y < this.size && this.modules[y][x];\n    }\n    // Modified to expose modules for easy access\n    getModules() {\n      return this.modules;\n    }\n    /*-- Private helper methods for constructor: Drawing function modules --*/\n    // Reads this object's version field, and draws and marks all function modules.\n    drawFunctionPatterns() {\n      for (let i = 0; i < this.size; i++) {\n        this.setFunctionModule(6, i, i % 2 == 0);\n        this.setFunctionModule(i, 6, i % 2 == 0);\n      }\n      this.drawFinderPattern(3, 3);\n      this.drawFinderPattern(this.size - 4, 3);\n      this.drawFinderPattern(3, this.size - 4);\n      const alignPatPos = this.getAlignmentPatternPositions();\n      const numAlign = alignPatPos.length;\n      for (let i = 0; i < numAlign; i++) {\n        for (let j = 0; j < numAlign; j++) {\n          if (!(i == 0 && j == 0 || i == 0 && j == numAlign - 1 || i == numAlign - 1 && j == 0))\n            this.drawAlignmentPattern(alignPatPos[i], alignPatPos[j]);\n        }\n      }\n      this.drawFormatBits(0);\n      this.drawVersion();\n    }\n    // Draws two copies of the format bits (with its own error correction code)\n    // based on the given mask and this object's error correction level field.\n    drawFormatBits(mask) {\n      const data = this.errorCorrectionLevel.formatBits << 3 | mask;\n      let rem = data;\n      for (let i = 0; i < 10; i++)\n        rem = rem << 1 ^ (rem >>> 9) * 1335;\n      const bits = (data << 10 | rem) ^ 21522;\n      assert(bits >>> 15 == 0);\n      for (let i = 0; i <= 5; i++)\n        this.setFunctionModule(8, i, getBit(bits, i));\n      this.setFunctionModule(8, 7, getBit(bits, 6));\n      this.setFunctionModule(8, 8, getBit(bits, 7));\n      this.setFunctionModule(7, 8, getBit(bits, 8));\n      for (let i = 9; i < 15; i++)\n        this.setFunctionModule(14 - i, 8, getBit(bits, i));\n      for (let i = 0; i < 8; i++)\n        this.setFunctionModule(this.size - 1 - i, 8, getBit(bits, i));\n      for (let i = 8; i < 15; i++)\n        this.setFunctionModule(8, this.size - 15 + i, getBit(bits, i));\n      this.setFunctionModule(8, this.size - 8, true);\n    }\n    // Draws two copies of the version bits (with its own error correction code),\n    // based on this object's version field, iff 7 <= version <= 40.\n    drawVersion() {\n      if (this.version < 7)\n        return;\n      let rem = this.version;\n      for (let i = 0; i < 12; i++)\n        rem = rem << 1 ^ (rem >>> 11) * 7973;\n      const bits = this.version << 12 | rem;\n      assert(bits >>> 18 == 0);\n      for (let i = 0; i < 18; i++) {\n        const color = getBit(bits, i);\n        const a = this.size - 11 + i % 3;\n        const b = Math.floor(i / 3);\n        this.setFunctionModule(a, b, color);\n        this.setFunctionModule(b, a, color);\n      }\n    }\n    // Draws a 9*9 finder pattern including the border separator,\n    // with the center module at (x, y). Modules can be out of bounds.\n    drawFinderPattern(x, y) {\n      for (let dy = -4; dy <= 4; dy++) {\n        for (let dx = -4; dx <= 4; dx++) {\n          const dist = Math.max(Math.abs(dx), Math.abs(dy));\n          const xx = x + dx;\n          const yy = y + dy;\n          if (0 <= xx && xx < this.size && 0 <= yy && yy < this.size)\n            this.setFunctionModule(xx, yy, dist != 2 && dist != 4);\n        }\n      }\n    }\n    // Draws a 5*5 alignment pattern, with the center module\n    // at (x, y). All modules must be in bounds.\n    drawAlignmentPattern(x, y) {\n      for (let dy = -2; dy <= 2; dy++) {\n        for (let dx = -2; dx <= 2; dx++)\n          this.setFunctionModule(x + dx, y + dy, Math.max(Math.abs(dx), Math.abs(dy)) != 1);\n      }\n    }\n    // Sets the color of a module and marks it as a function module.\n    // Only used by the constructor. Coordinates must be in bounds.\n    setFunctionModule(x, y, isDark) {\n      this.modules[y][x] = isDark;\n      this.isFunction[y][x] = true;\n    }\n    /*-- Private helper methods for constructor: Codewords and masking --*/\n    // Returns a new byte string representing the given data with the appropriate error correction\n    // codewords appended to it, based on this object's version and error correction level.\n    addEccAndInterleave(data) {\n      const ver = this.version;\n      const ecl = this.errorCorrectionLevel;\n      if (data.length != _QrCode.getNumDataCodewords(ver, ecl))\n        throw new RangeError(\"Invalid argument\");\n      const numBlocks = _QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n      const blockEccLen = _QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver];\n      const rawCodewords = Math.floor(_QrCode.getNumRawDataModules(ver) / 8);\n      const numShortBlocks = numBlocks - rawCodewords % numBlocks;\n      const shortBlockLen = Math.floor(rawCodewords / numBlocks);\n      let blocks = [];\n      const rsDiv = _QrCode.reedSolomonComputeDivisor(blockEccLen);\n      for (let i = 0, k = 0; i < numBlocks; i++) {\n        let dat = data.slice(k, k + shortBlockLen - blockEccLen + (i < numShortBlocks ? 0 : 1));\n        k += dat.length;\n        const ecc = _QrCode.reedSolomonComputeRemainder(dat, rsDiv);\n        if (i < numShortBlocks)\n          dat.push(0);\n        blocks.push(dat.concat(ecc));\n      }\n      let result = [];\n      for (let i = 0; i < blocks[0].length; i++) {\n        blocks.forEach((block, j) => {\n          if (i != shortBlockLen - blockEccLen || j >= numShortBlocks)\n            result.push(block[i]);\n        });\n      }\n      assert(result.length == rawCodewords);\n      return result;\n    }\n    // Draws the given sequence of 8-bit codewords (data and error correction) onto the entire\n    // data area of this QR Code. Function modules need to be marked off before this is called.\n    drawCodewords(data) {\n      if (data.length != Math.floor(_QrCode.getNumRawDataModules(this.version) / 8))\n        throw new RangeError(\"Invalid argument\");\n      let i = 0;\n      for (let right = this.size - 1; right >= 1; right -= 2) {\n        if (right == 6)\n          right = 5;\n        for (let vert = 0; vert < this.size; vert++) {\n          for (let j = 0; j < 2; j++) {\n            const x = right - j;\n            const upward = (right + 1 & 2) == 0;\n            const y = upward ? this.size - 1 - vert : vert;\n            if (!this.isFunction[y][x] && i < data.length * 8) {\n              this.modules[y][x] = getBit(data[i >>> 3], 7 - (i & 7));\n              i++;\n            }\n          }\n        }\n      }\n      assert(i == data.length * 8);\n    }\n    // XORs the codeword modules in this QR Code with the given mask pattern.\n    // The function modules must be marked and the codeword bits must be drawn\n    // before masking. Due to the arithmetic of XOR, calling applyMask() with\n    // the same mask value a second time will undo the mask. A final well-formed\n    // QR Code needs exactly one (not zero, two, etc.) mask applied.\n    applyMask(mask) {\n      if (mask < 0 || mask > 7)\n        throw new RangeError(\"Mask value out of range\");\n      for (let y = 0; y < this.size; y++) {\n        for (let x = 0; x < this.size; x++) {\n          let invert;\n          switch (mask) {\n            case 0:\n              invert = (x + y) % 2 == 0;\n              break;\n            case 1:\n              invert = y % 2 == 0;\n              break;\n            case 2:\n              invert = x % 3 == 0;\n              break;\n            case 3:\n              invert = (x + y) % 3 == 0;\n              break;\n            case 4:\n              invert = (Math.floor(x / 3) + Math.floor(y / 2)) % 2 == 0;\n              break;\n            case 5:\n              invert = x * y % 2 + x * y % 3 == 0;\n              break;\n            case 6:\n              invert = (x * y % 2 + x * y % 3) % 2 == 0;\n              break;\n            case 7:\n              invert = ((x + y) % 2 + x * y % 3) % 2 == 0;\n              break;\n            default:\n              throw new Error(\"Unreachable\");\n          }\n          if (!this.isFunction[y][x] && invert)\n            this.modules[y][x] = !this.modules[y][x];\n        }\n      }\n    }\n    // Calculates and returns the penalty score based on state of this QR Code's current modules.\n    // This is used by the automatic mask choice algorithm to find the mask pattern that yields the lowest score.\n    getPenaltyScore() {\n      let result = 0;\n      for (let y = 0; y < this.size; y++) {\n        let runColor = false;\n        let runX = 0;\n        let runHistory = [0, 0, 0, 0, 0, 0, 0];\n        for (let x = 0; x < this.size; x++) {\n          if (this.modules[y][x] == runColor) {\n            runX++;\n            if (runX == 5)\n              result += _QrCode.PENALTY_N1;\n            else if (runX > 5)\n              result++;\n          } else {\n            this.finderPenaltyAddHistory(runX, runHistory);\n            if (!runColor)\n              result += this.finderPenaltyCountPatterns(runHistory) * _QrCode.PENALTY_N3;\n            runColor = this.modules[y][x];\n            runX = 1;\n          }\n        }\n        result += this.finderPenaltyTerminateAndCount(runColor, runX, runHistory) * _QrCode.PENALTY_N3;\n      }\n      for (let x = 0; x < this.size; x++) {\n        let runColor = false;\n        let runY = 0;\n        let runHistory = [0, 0, 0, 0, 0, 0, 0];\n        for (let y = 0; y < this.size; y++) {\n          if (this.modules[y][x] == runColor) {\n            runY++;\n            if (runY == 5)\n              result += _QrCode.PENALTY_N1;\n            else if (runY > 5)\n              result++;\n          } else {\n            this.finderPenaltyAddHistory(runY, runHistory);\n            if (!runColor)\n              result += this.finderPenaltyCountPatterns(runHistory) * _QrCode.PENALTY_N3;\n            runColor = this.modules[y][x];\n            runY = 1;\n          }\n        }\n        result += this.finderPenaltyTerminateAndCount(runColor, runY, runHistory) * _QrCode.PENALTY_N3;\n      }\n      for (let y = 0; y < this.size - 1; y++) {\n        for (let x = 0; x < this.size - 1; x++) {\n          const color = this.modules[y][x];\n          if (color == this.modules[y][x + 1] && color == this.modules[y + 1][x] && color == this.modules[y + 1][x + 1])\n            result += _QrCode.PENALTY_N2;\n        }\n      }\n      let dark = 0;\n      for (const row of this.modules)\n        dark = row.reduce((sum, color) => sum + (color ? 1 : 0), dark);\n      const total = this.size * this.size;\n      const k = Math.ceil(Math.abs(dark * 20 - total * 10) / total) - 1;\n      assert(0 <= k && k <= 9);\n      result += k * _QrCode.PENALTY_N4;\n      assert(0 <= result && result <= 2568888);\n      return result;\n    }\n    /*-- Private helper functions --*/\n    // Returns an ascending list of positions of alignment patterns for this version number.\n    // Each position is in the range [0,177), and are used on both the x and y axes.\n    // This could be implemented as lookup table of 40 variable-length lists of integers.\n    getAlignmentPatternPositions() {\n      if (this.version == 1)\n        return [];\n      else {\n        const numAlign = Math.floor(this.version / 7) + 2;\n        const step = this.version == 32 ? 26 : Math.ceil((this.version * 4 + 4) / (numAlign * 2 - 2)) * 2;\n        let result = [6];\n        for (let pos = this.size - 7; result.length < numAlign; pos -= step)\n          result.splice(1, 0, pos);\n        return result;\n      }\n    }\n    // Returns the number of data bits that can be stored in a QR Code of the given version number, after\n    // all function modules are excluded. This includes remainder bits, so it might not be a multiple of 8.\n    // The result is in the range [208, 29648]. This could be implemented as a 40-entry lookup table.\n    static getNumRawDataModules(ver) {\n      if (ver < _QrCode.MIN_VERSION || ver > _QrCode.MAX_VERSION)\n        throw new RangeError(\"Version number out of range\");\n      let result = (16 * ver + 128) * ver + 64;\n      if (ver >= 2) {\n        const numAlign = Math.floor(ver / 7) + 2;\n        result -= (25 * numAlign - 10) * numAlign - 55;\n        if (ver >= 7)\n          result -= 36;\n      }\n      assert(208 <= result && result <= 29648);\n      return result;\n    }\n    // Returns the number of 8-bit data (i.e. not error correction) codewords contained in any\n    // QR Code of the given version number and error correction level, with remainder bits discarded.\n    // This stateless pure function could be implemented as a (40*4)-cell lookup table.\n    static getNumDataCodewords(ver, ecl) {\n      return Math.floor(_QrCode.getNumRawDataModules(ver) / 8) - _QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver] * _QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n    }\n    // Returns a Reed-Solomon ECC generator polynomial for the given degree. This could be\n    // implemented as a lookup table over all possible parameter values, instead of as an algorithm.\n    static reedSolomonComputeDivisor(degree) {\n      if (degree < 1 || degree > 255)\n        throw new RangeError(\"Degree out of range\");\n      let result = [];\n      for (let i = 0; i < degree - 1; i++)\n        result.push(0);\n      result.push(1);\n      let root = 1;\n      for (let i = 0; i < degree; i++) {\n        for (let j = 0; j < result.length; j++) {\n          result[j] = _QrCode.reedSolomonMultiply(result[j], root);\n          if (j + 1 < result.length)\n            result[j] ^= result[j + 1];\n        }\n        root = _QrCode.reedSolomonMultiply(root, 2);\n      }\n      return result;\n    }\n    // Returns the Reed-Solomon error correction codeword for the given data and divisor polynomials.\n    static reedSolomonComputeRemainder(data, divisor) {\n      let result = divisor.map((_) => 0);\n      for (const b of data) {\n        const factor = b ^ result.shift();\n        result.push(0);\n        divisor.forEach((coef, i) => result[i] ^= _QrCode.reedSolomonMultiply(coef, factor));\n      }\n      return result;\n    }\n    // Returns the product of the two given field elements modulo GF(2^8/0x11D). The arguments and result\n    // are unsigned 8-bit integers. This could be implemented as a lookup table of 256*256 entries of uint8.\n    static reedSolomonMultiply(x, y) {\n      if (x >>> 8 != 0 || y >>> 8 != 0)\n        throw new RangeError(\"Byte out of range\");\n      let z = 0;\n      for (let i = 7; i >= 0; i--) {\n        z = z << 1 ^ (z >>> 7) * 285;\n        z ^= (y >>> i & 1) * x;\n      }\n      assert(z >>> 8 == 0);\n      return z;\n    }\n    // Can only be called immediately after a light run is added, and\n    // returns either 0, 1, or 2. A helper function for getPenaltyScore().\n    finderPenaltyCountPatterns(runHistory) {\n      const n = runHistory[1];\n      assert(n <= this.size * 3);\n      const core = n > 0 && runHistory[2] == n && runHistory[3] == n * 3 && runHistory[4] == n && runHistory[5] == n;\n      return (core && runHistory[0] >= n * 4 && runHistory[6] >= n ? 1 : 0) + (core && runHistory[6] >= n * 4 && runHistory[0] >= n ? 1 : 0);\n    }\n    // Must be called at the end of a line (row or column) of modules. A helper function for getPenaltyScore().\n    finderPenaltyTerminateAndCount(currentRunColor, currentRunLength, runHistory) {\n      if (currentRunColor) {\n        this.finderPenaltyAddHistory(currentRunLength, runHistory);\n        currentRunLength = 0;\n      }\n      currentRunLength += this.size;\n      this.finderPenaltyAddHistory(currentRunLength, runHistory);\n      return this.finderPenaltyCountPatterns(runHistory);\n    }\n    // Pushes the given value to the front and drops the last value. A helper function for getPenaltyScore().\n    finderPenaltyAddHistory(currentRunLength, runHistory) {\n      if (runHistory[0] == 0)\n        currentRunLength += this.size;\n      runHistory.pop();\n      runHistory.unshift(currentRunLength);\n    }\n  };\n  /*-- Constants and tables --*/\n  // The minimum version number supported in the QR Code Model 2 standard.\n  _QrCode.MIN_VERSION = 1;\n  // The maximum version number supported in the QR Code Model 2 standard.\n  _QrCode.MAX_VERSION = 40;\n  // For use in getPenaltyScore(), when evaluating which mask is best.\n  _QrCode.PENALTY_N1 = 3;\n  _QrCode.PENALTY_N2 = 3;\n  _QrCode.PENALTY_N3 = 40;\n  _QrCode.PENALTY_N4 = 10;\n  _QrCode.ECC_CODEWORDS_PER_BLOCK = [\n    // Version: (note that index 0 is for padding, and is set to an illegal value)\n    //0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level\n    [-1, 7, 10, 15, 20, 26, 18, 20, 24, 30, 18, 20, 24, 26, 30, 22, 24, 28, 30, 28, 28, 28, 28, 30, 30, 26, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n    // Low\n    [-1, 10, 16, 26, 18, 24, 16, 18, 22, 22, 26, 30, 22, 22, 24, 24, 28, 28, 26, 26, 26, 26, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28],\n    // Medium\n    [-1, 13, 22, 18, 26, 18, 24, 18, 22, 20, 24, 28, 26, 24, 20, 30, 24, 28, 28, 26, 30, 28, 30, 30, 30, 30, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n    // Quartile\n    [-1, 17, 28, 22, 16, 22, 28, 26, 26, 24, 28, 24, 28, 22, 24, 24, 30, 28, 28, 26, 28, 30, 24, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30]\n    // High\n  ];\n  _QrCode.NUM_ERROR_CORRECTION_BLOCKS = [\n    // Version: (note that index 0 is for padding, and is set to an illegal value)\n    //0, 1, 2, 3, 4, 5, 6, 7, 8, 9,10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level\n    [-1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 4, 4, 4, 4, 4, 6, 6, 6, 6, 7, 8, 8, 9, 9, 10, 12, 12, 12, 13, 14, 15, 16, 17, 18, 19, 19, 20, 21, 22, 24, 25],\n    // Low\n    [-1, 1, 1, 1, 2, 2, 4, 4, 4, 5, 5, 5, 8, 9, 9, 10, 10, 11, 13, 14, 16, 17, 17, 18, 20, 21, 23, 25, 26, 28, 29, 31, 33, 35, 37, 38, 40, 43, 45, 47, 49],\n    // Medium\n    [-1, 1, 1, 2, 2, 4, 4, 6, 6, 8, 8, 8, 10, 12, 16, 12, 17, 16, 18, 21, 20, 23, 23, 25, 27, 29, 34, 34, 35, 38, 40, 43, 45, 48, 51, 53, 56, 59, 62, 65, 68],\n    // Quartile\n    [-1, 1, 1, 2, 4, 4, 4, 5, 6, 8, 8, 11, 11, 16, 16, 18, 16, 19, 21, 25, 25, 25, 34, 30, 32, 35, 37, 40, 42, 45, 48, 51, 54, 57, 60, 63, 66, 70, 74, 77, 81]\n    // High\n  ];\n  let QrCode = _QrCode;\n  qrcodegen2.QrCode = _QrCode;\n  function appendBits(val, len, bb) {\n    if (len < 0 || len > 31 || val >>> len != 0)\n      throw new RangeError(\"Value out of range\");\n    for (let i = len - 1; i >= 0; i--)\n      bb.push(val >>> i & 1);\n  }\n  function getBit(x, i) {\n    return (x >>> i & 1) != 0;\n  }\n  function assert(cond) {\n    if (!cond)\n      throw new Error(\"Assertion error\");\n  }\n  const _QrSegment = class _QrSegment {\n    /*-- Constructor (low level) and fields --*/\n    // Creates a new QR Code segment with the given attributes and data.\n    // The character count (numChars) must agree with the mode and the bit buffer length,\n    // but the constraint isn't checked. The given bit buffer is cloned and stored.\n    constructor(mode, numChars, bitData) {\n      this.mode = mode;\n      this.numChars = numChars;\n      this.bitData = bitData;\n      if (numChars < 0)\n        throw new RangeError(\"Invalid argument\");\n      this.bitData = bitData.slice();\n    }\n    /*-- Static factory functions (mid level) --*/\n    // Returns a segment representing the given binary data encoded in\n    // byte mode. All input byte arrays are acceptable. Any text string\n    // can be converted to UTF-8 bytes and encoded as a byte mode segment.\n    static makeBytes(data) {\n      let bb = [];\n      for (const b of data)\n        appendBits(b, 8, bb);\n      return new _QrSegment(_QrSegment.Mode.BYTE, data.length, bb);\n    }\n    // Returns a segment representing the given string of decimal digits encoded in numeric mode.\n    static makeNumeric(digits) {\n      if (!_QrSegment.isNumeric(digits))\n        throw new RangeError(\"String contains non-numeric characters\");\n      let bb = [];\n      for (let i = 0; i < digits.length; ) {\n        const n = Math.min(digits.length - i, 3);\n        appendBits(parseInt(digits.substring(i, i + n), 10), n * 3 + 1, bb);\n        i += n;\n      }\n      return new _QrSegment(_QrSegment.Mode.NUMERIC, digits.length, bb);\n    }\n    // Returns a segment representing the given text string encoded in alphanumeric mode.\n    // The characters allowed are: 0 to 9, A to Z (uppercase only), space,\n    // dollar, percent, asterisk, plus, hyphen, period, slash, colon.\n    static makeAlphanumeric(text) {\n      if (!_QrSegment.isAlphanumeric(text))\n        throw new RangeError(\"String contains unencodable characters in alphanumeric mode\");\n      let bb = [];\n      let i;\n      for (i = 0; i + 2 <= text.length; i += 2) {\n        let temp = _QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)) * 45;\n        temp += _QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i + 1));\n        appendBits(temp, 11, bb);\n      }\n      if (i < text.length)\n        appendBits(_QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)), 6, bb);\n      return new _QrSegment(_QrSegment.Mode.ALPHANUMERIC, text.length, bb);\n    }\n    // Returns a new mutable list of zero or more segments to represent the given Unicode text string.\n    // The result may use various segment modes and switch modes to optimize the length of the bit stream.\n    static makeSegments(text) {\n      if (text == \"\")\n        return [];\n      else if (_QrSegment.isNumeric(text))\n        return [_QrSegment.makeNumeric(text)];\n      else if (_QrSegment.isAlphanumeric(text))\n        return [_QrSegment.makeAlphanumeric(text)];\n      else\n        return [_QrSegment.makeBytes(_QrSegment.toUtf8ByteArray(text))];\n    }\n    // Returns a segment representing an Extended Channel Interpretation\n    // (ECI) designator with the given assignment value.\n    static makeEci(assignVal) {\n      let bb = [];\n      if (assignVal < 0)\n        throw new RangeError(\"ECI assignment value out of range\");\n      else if (assignVal < 1 << 7)\n        appendBits(assignVal, 8, bb);\n      else if (assignVal < 1 << 14) {\n        appendBits(2, 2, bb);\n        appendBits(assignVal, 14, bb);\n      } else if (assignVal < 1e6) {\n        appendBits(6, 3, bb);\n        appendBits(assignVal, 21, bb);\n      } else\n        throw new RangeError(\"ECI assignment value out of range\");\n      return new _QrSegment(_QrSegment.Mode.ECI, 0, bb);\n    }\n    // Tests whether the given string can be encoded as a segment in numeric mode.\n    // A string is encodable iff each character is in the range 0 to 9.\n    static isNumeric(text) {\n      return _QrSegment.NUMERIC_REGEX.test(text);\n    }\n    // Tests whether the given string can be encoded as a segment in alphanumeric mode.\n    // A string is encodable iff each character is in the following set: 0 to 9, A to Z\n    // (uppercase only), space, dollar, percent, asterisk, plus, hyphen, period, slash, colon.\n    static isAlphanumeric(text) {\n      return _QrSegment.ALPHANUMERIC_REGEX.test(text);\n    }\n    /*-- Methods --*/\n    // Returns a new copy of the data bits of this segment.\n    getData() {\n      return this.bitData.slice();\n    }\n    // (Package-private) Calculates and returns the number of bits needed to encode the given segments at\n    // the given version. The result is infinity if a segment has too many characters to fit its length field.\n    static getTotalBits(segs, version) {\n      let result = 0;\n      for (const seg of segs) {\n        const ccbits = seg.mode.numCharCountBits(version);\n        if (seg.numChars >= 1 << ccbits)\n          return Infinity;\n        result += 4 + ccbits + seg.bitData.length;\n      }\n      return result;\n    }\n    // Returns a new array of bytes representing the given string encoded in UTF-8.\n    static toUtf8ByteArray(str) {\n      str = encodeURI(str);\n      let result = [];\n      for (let i = 0; i < str.length; i++) {\n        if (str.charAt(i) != \"%\")\n          result.push(str.charCodeAt(i));\n        else {\n          result.push(parseInt(str.substring(i + 1, i + 3), 16));\n          i += 2;\n        }\n      }\n      return result;\n    }\n  };\n  /*-- Constants --*/\n  // Describes precisely all strings that are encodable in numeric mode.\n  _QrSegment.NUMERIC_REGEX = /^[0-9]*$/;\n  // Describes precisely all strings that are encodable in alphanumeric mode.\n  _QrSegment.ALPHANUMERIC_REGEX = /^[A-Z0-9 $%*+.\\/:-]*$/;\n  // The set of all legal characters in alphanumeric mode,\n  // where each character value maps to the index in the string.\n  _QrSegment.ALPHANUMERIC_CHARSET = \"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:\";\n  let QrSegment = _QrSegment;\n  qrcodegen2.QrSegment = _QrSegment;\n})(qrcodegen || (qrcodegen = {}));\n((qrcodegen2) => {\n  let QrCode;\n  ((QrCode2) => {\n    const _Ecc = class _Ecc {\n      // The QR Code can tolerate about 30% erroneous codewords\n      /*-- Constructor and fields --*/\n      constructor(ordinal, formatBits) {\n        this.ordinal = ordinal;\n        this.formatBits = formatBits;\n      }\n    };\n    /*-- Constants --*/\n    _Ecc.LOW = new _Ecc(0, 1);\n    // The QR Code can tolerate about  7% erroneous codewords\n    _Ecc.MEDIUM = new _Ecc(1, 0);\n    // The QR Code can tolerate about 15% erroneous codewords\n    _Ecc.QUARTILE = new _Ecc(2, 3);\n    // The QR Code can tolerate about 25% erroneous codewords\n    _Ecc.HIGH = new _Ecc(3, 2);\n    let Ecc = _Ecc;\n    QrCode2.Ecc = _Ecc;\n  })(QrCode = qrcodegen2.QrCode || (qrcodegen2.QrCode = {}));\n})(qrcodegen || (qrcodegen = {}));\n((qrcodegen2) => {\n  let QrSegment;\n  ((QrSegment2) => {\n    const _Mode = class _Mode {\n      /*-- Constructor and fields --*/\n      constructor(modeBits, numBitsCharCount) {\n        this.modeBits = modeBits;\n        this.numBitsCharCount = numBitsCharCount;\n      }\n      /*-- Method --*/\n      // (Package-private) Returns the bit width of the character count field for a segment in\n      // this mode in a QR Code at the given version number. The result is in the range [0, 16].\n      numCharCountBits(ver) {\n        return this.numBitsCharCount[Math.floor((ver + 7) / 17)];\n      }\n    };\n    /*-- Constants --*/\n    _Mode.NUMERIC = new _Mode(1, [10, 12, 14]);\n    _Mode.ALPHANUMERIC = new _Mode(2, [9, 11, 13]);\n    _Mode.BYTE = new _Mode(4, [8, 16, 16]);\n    _Mode.KANJI = new _Mode(8, [8, 10, 12]);\n    _Mode.ECI = new _Mode(7, [0, 0, 0]);\n    let Mode = _Mode;\n    QrSegment2.Mode = _Mode;\n  })(QrSegment = qrcodegen2.QrSegment || (qrcodegen2.QrSegment = {}));\n})(qrcodegen || (qrcodegen = {}));\nvar qrcodegen_default = qrcodegen;\n\n// src/index.tsx\n/**\n * @license qrcode.react\n * Copyright (c) Paul O'Shannessy\n * SPDX-License-Identifier: ISC\n */\nvar ERROR_LEVEL_MAP = {\n  L: qrcodegen_default.QrCode.Ecc.LOW,\n  M: qrcodegen_default.QrCode.Ecc.MEDIUM,\n  Q: qrcodegen_default.QrCode.Ecc.QUARTILE,\n  H: qrcodegen_default.QrCode.Ecc.HIGH\n};\nvar DEFAULT_SIZE = 128;\nvar DEFAULT_LEVEL = \"L\";\nvar DEFAULT_BGCOLOR = \"#FFFFFF\";\nvar DEFAULT_FGCOLOR = \"#000000\";\nvar DEFAULT_INCLUDEMARGIN = false;\nvar DEFAULT_MINVERSION = 1;\nvar SPEC_MARGIN_SIZE = 4;\nvar DEFAULT_MARGIN_SIZE = 0;\nvar DEFAULT_IMG_SCALE = 0.1;\nfunction generatePath(modules, margin = 0) {\n  const ops = [];\n  modules.forEach(function(row, y) {\n    let start = null;\n    row.forEach(function(cell, x) {\n      if (!cell && start !== null) {\n        ops.push(\n          `M${start + margin} ${y + margin}h${x - start}v1H${start + margin}z`\n        );\n        start = null;\n        return;\n      }\n      if (x === row.length - 1) {\n        if (!cell) {\n          return;\n        }\n        if (start === null) {\n          ops.push(`M${x + margin},${y + margin} h1v1H${x + margin}z`);\n        } else {\n          ops.push(\n            `M${start + margin},${y + margin} h${x + 1 - start}v1H${start + margin}z`\n          );\n        }\n        return;\n      }\n      if (cell && start === null) {\n        start = x;\n      }\n    });\n  });\n  return ops.join(\"\");\n}\nfunction excavateModules(modules, excavation) {\n  return modules.slice().map((row, y) => {\n    if (y < excavation.y || y >= excavation.y + excavation.h) {\n      return row;\n    }\n    return row.map((cell, x) => {\n      if (x < excavation.x || x >= excavation.x + excavation.w) {\n        return cell;\n      }\n      return false;\n    });\n  });\n}\nfunction getImageSettings(cells, size, margin, imageSettings) {\n  if (imageSettings == null) {\n    return null;\n  }\n  const numCells = cells.length + margin * 2;\n  const defaultSize = Math.floor(size * DEFAULT_IMG_SCALE);\n  const scale = numCells / size;\n  const w = (imageSettings.width || defaultSize) * scale;\n  const h = (imageSettings.height || defaultSize) * scale;\n  const x = imageSettings.x == null ? cells.length / 2 - w / 2 : imageSettings.x * scale;\n  const y = imageSettings.y == null ? cells.length / 2 - h / 2 : imageSettings.y * scale;\n  const opacity = imageSettings.opacity == null ? 1 : imageSettings.opacity;\n  let excavation = null;\n  if (imageSettings.excavate) {\n    let floorX = Math.floor(x);\n    let floorY = Math.floor(y);\n    let ceilW = Math.ceil(w + x - floorX);\n    let ceilH = Math.ceil(h + y - floorY);\n    excavation = { x: floorX, y: floorY, w: ceilW, h: ceilH };\n  }\n  const crossOrigin = imageSettings.crossOrigin;\n  return { x, y, h, w, excavation, opacity, crossOrigin };\n}\nfunction getMarginSize(includeMargin, marginSize) {\n  if (marginSize != null) {\n    return Math.max(Math.floor(marginSize), 0);\n  }\n  return includeMargin ? SPEC_MARGIN_SIZE : DEFAULT_MARGIN_SIZE;\n}\nfunction useQRCode({\n  value,\n  level,\n  minVersion,\n  includeMargin,\n  marginSize,\n  imageSettings,\n  size,\n  boostLevel\n}) {\n  let qrcode = React.useMemo(() => {\n    const values = Array.isArray(value) ? value : [value];\n    const segments = values.reduce((accum, v) => {\n      accum.push(...qrcodegen_default.QrSegment.makeSegments(v));\n      return accum;\n    }, []);\n    return qrcodegen_default.QrCode.encodeSegments(\n      segments,\n      ERROR_LEVEL_MAP[level],\n      minVersion,\n      void 0,\n      void 0,\n      boostLevel\n    );\n  }, [value, level, minVersion, boostLevel]);\n  const { cells, margin, numCells, calculatedImageSettings } = React.useMemo(() => {\n    let cells2 = qrcode.getModules();\n    const margin2 = getMarginSize(includeMargin, marginSize);\n    const numCells2 = cells2.length + margin2 * 2;\n    const calculatedImageSettings2 = getImageSettings(\n      cells2,\n      size,\n      margin2,\n      imageSettings\n    );\n    return {\n      cells: cells2,\n      margin: margin2,\n      numCells: numCells2,\n      calculatedImageSettings: calculatedImageSettings2\n    };\n  }, [qrcode, size, imageSettings, includeMargin, marginSize]);\n  return {\n    qrcode,\n    margin,\n    cells,\n    numCells,\n    calculatedImageSettings\n  };\n}\nvar SUPPORTS_PATH2D = function() {\n  try {\n    new Path2D().addPath(new Path2D());\n  } catch (e) {\n    return false;\n  }\n  return true;\n}();\nvar QRCodeCanvas = React.forwardRef(\n  function QRCodeCanvas2(props, forwardedRef) {\n    const _a = props, {\n      value,\n      size = DEFAULT_SIZE,\n      level = DEFAULT_LEVEL,\n      bgColor = DEFAULT_BGCOLOR,\n      fgColor = DEFAULT_FGCOLOR,\n      includeMargin = DEFAULT_INCLUDEMARGIN,\n      minVersion = DEFAULT_MINVERSION,\n      boostLevel,\n      marginSize,\n      imageSettings\n    } = _a, extraProps = __objRest(_a, [\n      \"value\",\n      \"size\",\n      \"level\",\n      \"bgColor\",\n      \"fgColor\",\n      \"includeMargin\",\n      \"minVersion\",\n      \"boostLevel\",\n      \"marginSize\",\n      \"imageSettings\"\n    ]);\n    const _b = extraProps, { style } = _b, otherProps = __objRest(_b, [\"style\"]);\n    const imgSrc = imageSettings == null ? void 0 : imageSettings.src;\n    const _canvas = React.useRef(null);\n    const _image = React.useRef(null);\n    const setCanvasRef = React.useCallback(\n      (node) => {\n        _canvas.current = node;\n        if (typeof forwardedRef === \"function\") {\n          forwardedRef(node);\n        } else if (forwardedRef) {\n          forwardedRef.current = node;\n        }\n      },\n      [forwardedRef]\n    );\n    const [isImgLoaded, setIsImageLoaded] = React.useState(false);\n    const { margin, cells, numCells, calculatedImageSettings } = useQRCode({\n      value,\n      level,\n      minVersion,\n      boostLevel,\n      includeMargin,\n      marginSize,\n      imageSettings,\n      size\n    });\n    React.useEffect(() => {\n      if (_canvas.current != null) {\n        const canvas = _canvas.current;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) {\n          return;\n        }\n        let cellsToDraw = cells;\n        const image = _image.current;\n        const haveImageToRender = calculatedImageSettings != null && image !== null && image.complete && image.naturalHeight !== 0 && image.naturalWidth !== 0;\n        if (haveImageToRender) {\n          if (calculatedImageSettings.excavation != null) {\n            cellsToDraw = excavateModules(\n              cells,\n              calculatedImageSettings.excavation\n            );\n          }\n        }\n        const pixelRatio = window.devicePixelRatio || 1;\n        canvas.height = canvas.width = size * pixelRatio;\n        const scale = size / numCells * pixelRatio;\n        ctx.scale(scale, scale);\n        ctx.fillStyle = bgColor;\n        ctx.fillRect(0, 0, numCells, numCells);\n        ctx.fillStyle = fgColor;\n        if (SUPPORTS_PATH2D) {\n          ctx.fill(new Path2D(generatePath(cellsToDraw, margin)));\n        } else {\n          cells.forEach(function(row, rdx) {\n            row.forEach(function(cell, cdx) {\n              if (cell) {\n                ctx.fillRect(cdx + margin, rdx + margin, 1, 1);\n              }\n            });\n          });\n        }\n        if (calculatedImageSettings) {\n          ctx.globalAlpha = calculatedImageSettings.opacity;\n        }\n        if (haveImageToRender) {\n          ctx.drawImage(\n            image,\n            calculatedImageSettings.x + margin,\n            calculatedImageSettings.y + margin,\n            calculatedImageSettings.w,\n            calculatedImageSettings.h\n          );\n        }\n      }\n    });\n    React.useEffect(() => {\n      setIsImageLoaded(false);\n    }, [imgSrc]);\n    const canvasStyle = __spreadValues({ height: size, width: size }, style);\n    let img = null;\n    if (imgSrc != null) {\n      img = /* @__PURE__ */ React.createElement(\n        \"img\",\n        {\n          src: imgSrc,\n          key: imgSrc,\n          style: { display: \"none\" },\n          onLoad: () => {\n            setIsImageLoaded(true);\n          },\n          ref: _image,\n          crossOrigin: calculatedImageSettings == null ? void 0 : calculatedImageSettings.crossOrigin\n        }\n      );\n    }\n    return /* @__PURE__ */ React.createElement(React.Fragment, null, /* @__PURE__ */ React.createElement(\n      \"canvas\",\n      __spreadValues({\n        style: canvasStyle,\n        height: size,\n        width: size,\n        ref: setCanvasRef,\n        role: \"img\"\n      }, otherProps)\n    ), img);\n  }\n);\nQRCodeCanvas.displayName = \"QRCodeCanvas\";\nvar QRCodeSVG = React.forwardRef(\n  function QRCodeSVG2(props, forwardedRef) {\n    const _a = props, {\n      value,\n      size = DEFAULT_SIZE,\n      level = DEFAULT_LEVEL,\n      bgColor = DEFAULT_BGCOLOR,\n      fgColor = DEFAULT_FGCOLOR,\n      includeMargin = DEFAULT_INCLUDEMARGIN,\n      minVersion = DEFAULT_MINVERSION,\n      boostLevel,\n      title,\n      marginSize,\n      imageSettings\n    } = _a, otherProps = __objRest(_a, [\n      \"value\",\n      \"size\",\n      \"level\",\n      \"bgColor\",\n      \"fgColor\",\n      \"includeMargin\",\n      \"minVersion\",\n      \"boostLevel\",\n      \"title\",\n      \"marginSize\",\n      \"imageSettings\"\n    ]);\n    const { margin, cells, numCells, calculatedImageSettings } = useQRCode({\n      value,\n      level,\n      minVersion,\n      boostLevel,\n      includeMargin,\n      marginSize,\n      imageSettings,\n      size\n    });\n    let cellsToDraw = cells;\n    let image = null;\n    if (imageSettings != null && calculatedImageSettings != null) {\n      if (calculatedImageSettings.excavation != null) {\n        cellsToDraw = excavateModules(\n          cells,\n          calculatedImageSettings.excavation\n        );\n      }\n      image = /* @__PURE__ */ React.createElement(\n        \"image\",\n        {\n          href: imageSettings.src,\n          height: calculatedImageSettings.h,\n          width: calculatedImageSettings.w,\n          x: calculatedImageSettings.x + margin,\n          y: calculatedImageSettings.y + margin,\n          preserveAspectRatio: \"none\",\n          opacity: calculatedImageSettings.opacity,\n          crossOrigin: calculatedImageSettings.crossOrigin\n        }\n      );\n    }\n    const fgPath = generatePath(cellsToDraw, margin);\n    return /* @__PURE__ */ React.createElement(\n      \"svg\",\n      __spreadValues({\n        height: size,\n        width: size,\n        viewBox: `0 0 ${numCells} ${numCells}`,\n        ref: forwardedRef,\n        role: \"img\"\n      }, otherProps),\n      !!title && /* @__PURE__ */ React.createElement(\"title\", null, title),\n      /* @__PURE__ */ React.createElement(\n        \"path\",\n        {\n          fill: bgColor,\n          d: `M0,0 h${numCells}v${numCells}H0z`,\n          shapeRendering: \"crispEdges\"\n        }\n      ),\n      /* @__PURE__ */ React.createElement(\"path\", { fill: fgColor, d: fgPath, shapeRendering: \"crispEdges\" }),\n      image\n    );\n  }\n);\nQRCodeSVG.displayName = \"QRCodeSVG\";\nexport {\n  QRCodeCanvas,\n  QRCodeSVG\n};\n"], "names": [], "mappings": ";;;;AA6BA,gBAAgB;AAChB;AA9BA,IAAI,YAAY,OAAO,cAAc;AACrC,IAAI,sBAAsB,OAAO,qBAAqB;AACtD,IAAI,eAAe,OAAO,SAAS,CAAC,cAAc;AAClD,IAAI,eAAe,OAAO,SAAS,CAAC,oBAAoB;AACxD,IAAI,kBAAkB,CAAC,KAAK,KAAK,QAAU,OAAO,MAAM,UAAU,KAAK,KAAK;QAAE,YAAY;QAAM,cAAc;QAAM,UAAU;QAAM;IAAM,KAAK,GAAG,CAAC,IAAI,GAAG;AAC1J,IAAI,iBAAiB,CAAC,GAAG;IACvB,IAAK,IAAI,QAAQ,KAAK,CAAC,IAAI,CAAC,CAAC,EAC3B,IAAI,aAAa,IAAI,CAAC,GAAG,OACvB,gBAAgB,GAAG,MAAM,CAAC,CAAC,KAAK;IACpC,IAAI,qBACF,KAAK,IAAI,QAAQ,oBAAoB,GAAI;QACvC,IAAI,aAAa,IAAI,CAAC,GAAG,OACvB,gBAAgB,GAAG,MAAM,CAAC,CAAC,KAAK;IACpC;IACF,OAAO;AACT;AACA,IAAI,YAAY,CAAC,QAAQ;IACvB,IAAI,SAAS,CAAC;IACd,IAAK,IAAI,QAAQ,OACf,IAAI,aAAa,IAAI,CAAC,QAAQ,SAAS,QAAQ,OAAO,CAAC,QAAQ,GAC7D,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK;IAC/B,IAAI,UAAU,QAAQ,qBACpB,KAAK,IAAI,QAAQ,oBAAoB,QAAS;QAC5C,IAAI,QAAQ,OAAO,CAAC,QAAQ,KAAK,aAAa,IAAI,CAAC,QAAQ,OACzD,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK;IAC/B;IACF,OAAO;AACT;;AAKA,qCAAqC;AACrC;;;;CAIC,GACD,IAAI;AACJ,CAAC,CAAC;IACA,MAAM,UAAU,MAAM;QACpB,0CAA0C,GAC1C,uDAAuD;QACvD,gEAAgE;QAChE,mEAAmE;QACnE,oDAAoD;QACpD,YAAY,OAAO,EAAE,oBAAoB,EAAE,aAAa,EAAE,GAAG,CAAE;YAC7D,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,oBAAoB,GAAG;YAC5B,4DAA4D;YAC5D,sEAAsE;YACtE,IAAI,CAAC,OAAO,GAAG,EAAE;YACjB,qGAAqG;YACrG,IAAI,CAAC,UAAU,GAAG,EAAE;YACpB,IAAI,UAAU,QAAQ,WAAW,IAAI,UAAU,QAAQ,WAAW,EAChE,MAAM,IAAI,WAAW;YACvB,IAAI,MAAM,CAAC,KAAK,MAAM,GACpB,MAAM,IAAI,WAAW;YACvB,IAAI,CAAC,IAAI,GAAG,UAAU,IAAI;YAC1B,IAAI,MAAM,EAAE;YACZ,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,IAC7B,IAAI,IAAI,CAAC;YACX,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,IAAK;gBAClC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK;gBAC3B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK;YAChC;YACA,IAAI,CAAC,oBAAoB;YACzB,MAAM,eAAe,IAAI,CAAC,mBAAmB,CAAC;YAC9C,IAAI,CAAC,aAAa,CAAC;YACnB,IAAI,OAAO,CAAC,GAAG;gBACb,IAAI,aAAa;gBACjB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;oBAC1B,IAAI,CAAC,SAAS,CAAC;oBACf,IAAI,CAAC,cAAc,CAAC;oBACpB,MAAM,UAAU,IAAI,CAAC,eAAe;oBACpC,IAAI,UAAU,YAAY;wBACxB,MAAM;wBACN,aAAa;oBACf;oBACA,IAAI,CAAC,SAAS,CAAC;gBACjB;YACF;YACA,OAAO,KAAK,OAAO,OAAO;YAC1B,IAAI,CAAC,IAAI,GAAG;YACZ,IAAI,CAAC,SAAS,CAAC;YACf,IAAI,CAAC,cAAc,CAAC;YACpB,IAAI,CAAC,UAAU,GAAG,EAAE;QACtB;QACA,6CAA6C,GAC7C,oGAAoG;QACpG,2GAA2G;QAC3G,+GAA+G;QAC/G,6GAA6G;QAC7G,iEAAiE;QACjE,OAAO,WAAW,IAAI,EAAE,GAAG,EAAE;YAC3B,MAAM,OAAO,WAAW,SAAS,CAAC,YAAY,CAAC;YAC/C,OAAO,QAAQ,cAAc,CAAC,MAAM;QACtC;QACA,4FAA4F;QAC5F,uGAAuG;QACvG,uGAAuG;QACvG,oHAAoH;QACpH,OAAO,aAAa,IAAI,EAAE,GAAG,EAAE;YAC7B,MAAM,MAAM,WAAW,SAAS,CAAC,SAAS,CAAC;YAC3C,OAAO,QAAQ,cAAc,CAAC;gBAAC;aAAI,EAAE;QACvC;QACA,4CAA4C,GAC5C,wFAAwF;QACxF,gFAAgF;QAChF,gFAAgF;QAChF,+EAA+E;QAC/E,8EAA8E;QAC9E,+EAA+E;QAC/E,sFAAsF;QACtF,8EAA8E;QAC9E,kFAAkF;QAClF,OAAO,eAAe,IAAI,EAAE,GAAG,EAAE,aAAa,CAAC,EAAE,aAAa,EAAE,EAAE,OAAO,CAAC,CAAC,EAAE,WAAW,IAAI,EAAE;YAC5F,IAAI,CAAC,CAAC,QAAQ,WAAW,IAAI,cAAc,cAAc,cAAc,cAAc,QAAQ,WAAW,KAAK,OAAO,CAAC,KAAK,OAAO,GAC/H,MAAM,IAAI,WAAW;YACvB,IAAI;YACJ,IAAI;YACJ,IAAK,UAAU,aAAc,UAAW;gBACtC,MAAM,oBAAoB,QAAQ,mBAAmB,CAAC,SAAS,OAAO;gBACtE,MAAM,WAAW,UAAU,YAAY,CAAC,MAAM;gBAC9C,IAAI,YAAY,mBAAmB;oBACjC,eAAe;oBACf;gBACF;gBACA,IAAI,WAAW,YACb,MAAM,IAAI,WAAW;YACzB;YACA,KAAK,MAAM,UAAU;gBAAC,QAAQ,GAAG,CAAC,MAAM;gBAAE,QAAQ,GAAG,CAAC,QAAQ;gBAAE,QAAQ,GAAG,CAAC,IAAI;aAAC,CAAE;gBACjF,IAAI,YAAY,gBAAgB,QAAQ,mBAAmB,CAAC,SAAS,UAAU,GAC7E,MAAM;YACV;YACA,IAAI,KAAK,EAAE;YACX,KAAK,MAAM,OAAO,KAAM;gBACtB,WAAW,IAAI,IAAI,CAAC,QAAQ,EAAE,GAAG;gBACjC,WAAW,IAAI,QAAQ,EAAE,IAAI,IAAI,CAAC,gBAAgB,CAAC,UAAU;gBAC7D,KAAK,MAAM,KAAK,IAAI,OAAO,GACzB,GAAG,IAAI,CAAC;YACZ;YACA,OAAO,GAAG,MAAM,IAAI;YACpB,MAAM,mBAAmB,QAAQ,mBAAmB,CAAC,SAAS,OAAO;YACrE,OAAO,GAAG,MAAM,IAAI;YACpB,WAAW,GAAG,KAAK,GAAG,CAAC,GAAG,mBAAmB,GAAG,MAAM,GAAG;YACzD,WAAW,GAAG,CAAC,IAAI,GAAG,MAAM,GAAG,CAAC,IAAI,GAAG;YACvC,OAAO,GAAG,MAAM,GAAG,KAAK;YACxB,IAAK,IAAI,UAAU,KAAK,GAAG,MAAM,GAAG,kBAAkB,WAAW,MAAM,GACrE,WAAW,SAAS,GAAG;YACzB,IAAI,gBAAgB,EAAE;YACtB,MAAO,cAAc,MAAM,GAAG,IAAI,GAAG,MAAM,CACzC,cAAc,IAAI,CAAC;YACrB,GAAG,OAAO,CAAC,CAAC,GAAG,IAAM,aAAa,CAAC,MAAM,EAAE,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC;YAC9D,OAAO,IAAI,QAAQ,SAAS,KAAK,eAAe;QAClD;QACA,wBAAwB,GACxB,mFAAmF;QACnF,kFAAkF;QAClF,8EAA8E;QAC9E,UAAU,CAAC,EAAE,CAAC,EAAE;YACd,OAAO,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;QACjF;QACA,6CAA6C;QAC7C,aAAa;YACX,OAAO,IAAI,CAAC,OAAO;QACrB;QACA,wEAAwE,GACxE,+EAA+E;QAC/E,uBAAuB;YACrB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,IAAK;gBAClC,IAAI,CAAC,iBAAiB,CAAC,GAAG,GAAG,IAAI,KAAK;gBACtC,IAAI,CAAC,iBAAiB,CAAC,GAAG,GAAG,IAAI,KAAK;YACxC;YACA,IAAI,CAAC,iBAAiB,CAAC,GAAG;YAC1B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG;YACtC,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG;YACtC,MAAM,cAAc,IAAI,CAAC,4BAA4B;YACrD,MAAM,WAAW,YAAY,MAAM;YACnC,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,IAAK;gBACjC,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,IAAK;oBACjC,IAAI,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,WAAW,KAAK,KAAK,WAAW,KAAK,KAAK,CAAC,GAClF,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,EAAE,EAAE,WAAW,CAAC,EAAE;gBAC5D;YACF;YACA,IAAI,CAAC,cAAc,CAAC;YACpB,IAAI,CAAC,WAAW;QAClB;QACA,2EAA2E;QAC3E,0EAA0E;QAC1E,eAAe,IAAI,EAAE;YACnB,MAAM,OAAO,IAAI,CAAC,oBAAoB,CAAC,UAAU,IAAI,IAAI;YACzD,IAAI,MAAM;YACV,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IACtB,MAAM,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI;YACjC,MAAM,OAAO,CAAC,QAAQ,KAAK,GAAG,IAAI;YAClC,OAAO,SAAS,MAAM;YACtB,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IACtB,IAAI,CAAC,iBAAiB,CAAC,GAAG,GAAG,OAAO,MAAM;YAC5C,IAAI,CAAC,iBAAiB,CAAC,GAAG,GAAG,OAAO,MAAM;YAC1C,IAAI,CAAC,iBAAiB,CAAC,GAAG,GAAG,OAAO,MAAM;YAC1C,IAAI,CAAC,iBAAiB,CAAC,GAAG,GAAG,OAAO,MAAM;YAC1C,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IACtB,IAAI,CAAC,iBAAiB,CAAC,KAAK,GAAG,GAAG,OAAO,MAAM;YACjD,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IACrB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,GAAG,OAAO,MAAM;YAC5D,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IACtB,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,KAAK,GAAG,OAAO,MAAM;YAC7D,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,GAAG;QAC3C;QACA,6EAA6E;QAC7E,gEAAgE;QAChE,cAAc;YACZ,IAAI,IAAI,CAAC,OAAO,GAAG,GACjB;YACF,IAAI,MAAM,IAAI,CAAC,OAAO;YACtB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IACtB,MAAM,OAAO,IAAI,CAAC,QAAQ,EAAE,IAAI;YAClC,MAAM,OAAO,IAAI,CAAC,OAAO,IAAI,KAAK;YAClC,OAAO,SAAS,MAAM;YACtB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;gBAC3B,MAAM,QAAQ,OAAO,MAAM;gBAC3B,MAAM,IAAI,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI;gBAC/B,MAAM,IAAI,KAAK,KAAK,CAAC,IAAI;gBACzB,IAAI,CAAC,iBAAiB,CAAC,GAAG,GAAG;gBAC7B,IAAI,CAAC,iBAAiB,CAAC,GAAG,GAAG;YAC/B;QACF;QACA,6DAA6D;QAC7D,kEAAkE;QAClE,kBAAkB,CAAC,EAAE,CAAC,EAAE;YACtB,IAAK,IAAI,KAAK,CAAC,GAAG,MAAM,GAAG,KAAM;gBAC/B,IAAK,IAAI,KAAK,CAAC,GAAG,MAAM,GAAG,KAAM;oBAC/B,MAAM,OAAO,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC;oBAC7C,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,IAAI,KAAK,MAAM,KAAK,IAAI,CAAC,IAAI,IAAI,KAAK,MAAM,KAAK,IAAI,CAAC,IAAI,EACxD,IAAI,CAAC,iBAAiB,CAAC,IAAI,IAAI,QAAQ,KAAK,QAAQ;gBACxD;YACF;QACF;QACA,wDAAwD;QACxD,4CAA4C;QAC5C,qBAAqB,CAAC,EAAE,CAAC,EAAE;YACzB,IAAK,IAAI,KAAK,CAAC,GAAG,MAAM,GAAG,KAAM;gBAC/B,IAAK,IAAI,KAAK,CAAC,GAAG,MAAM,GAAG,KACzB,IAAI,CAAC,iBAAiB,CAAC,IAAI,IAAI,IAAI,IAAI,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,QAAQ;YACnF;QACF;QACA,gEAAgE;QAChE,+DAA+D;QAC/D,kBAAkB,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE;YAC9B,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,GAAG;YACrB,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG;QAC1B;QACA,qEAAqE,GACrE,8FAA8F;QAC9F,uFAAuF;QACvF,oBAAoB,IAAI,EAAE;YACxB,MAAM,MAAM,IAAI,CAAC,OAAO;YACxB,MAAM,MAAM,IAAI,CAAC,oBAAoB;YACrC,IAAI,KAAK,MAAM,IAAI,QAAQ,mBAAmB,CAAC,KAAK,MAClD,MAAM,IAAI,WAAW;YACvB,MAAM,YAAY,QAAQ,2BAA2B,CAAC,IAAI,OAAO,CAAC,CAAC,IAAI;YACvE,MAAM,cAAc,QAAQ,uBAAuB,CAAC,IAAI,OAAO,CAAC,CAAC,IAAI;YACrE,MAAM,eAAe,KAAK,KAAK,CAAC,QAAQ,oBAAoB,CAAC,OAAO;YACpE,MAAM,iBAAiB,YAAY,eAAe;YAClD,MAAM,gBAAgB,KAAK,KAAK,CAAC,eAAe;YAChD,IAAI,SAAS,EAAE;YACf,MAAM,QAAQ,QAAQ,yBAAyB,CAAC;YAChD,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,WAAW,IAAK;gBACzC,IAAI,MAAM,KAAK,KAAK,CAAC,GAAG,IAAI,gBAAgB,cAAc,CAAC,IAAI,iBAAiB,IAAI,CAAC;gBACrF,KAAK,IAAI,MAAM;gBACf,MAAM,MAAM,QAAQ,2BAA2B,CAAC,KAAK;gBACrD,IAAI,IAAI,gBACN,IAAI,IAAI,CAAC;gBACX,OAAO,IAAI,CAAC,IAAI,MAAM,CAAC;YACzB;YACA,IAAI,SAAS,EAAE;YACf,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,IAAK;gBACzC,OAAO,OAAO,CAAC,CAAC,OAAO;oBACrB,IAAI,KAAK,gBAAgB,eAAe,KAAK,gBAC3C,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE;gBACxB;YACF;YACA,OAAO,OAAO,MAAM,IAAI;YACxB,OAAO;QACT;QACA,0FAA0F;QAC1F,2FAA2F;QAC3F,cAAc,IAAI,EAAE;YAClB,IAAI,KAAK,MAAM,IAAI,KAAK,KAAK,CAAC,QAAQ,oBAAoB,CAAC,IAAI,CAAC,OAAO,IAAI,IACzE,MAAM,IAAI,WAAW;YACvB,IAAI,IAAI;YACR,IAAK,IAAI,QAAQ,IAAI,CAAC,IAAI,GAAG,GAAG,SAAS,GAAG,SAAS,EAAG;gBACtD,IAAI,SAAS,GACX,QAAQ;gBACV,IAAK,IAAI,OAAO,GAAG,OAAO,IAAI,CAAC,IAAI,EAAE,OAAQ;oBAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;wBAC1B,MAAM,IAAI,QAAQ;wBAClB,MAAM,SAAS,CAAC,QAAQ,IAAI,CAAC,KAAK;wBAClC,MAAM,IAAI,SAAS,IAAI,CAAC,IAAI,GAAG,IAAI,OAAO;wBAC1C,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,IAAI,KAAK,MAAM,GAAG,GAAG;4BACjD,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,GAAG,OAAO,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC;4BACrD;wBACF;oBACF;gBACF;YACF;YACA,OAAO,KAAK,KAAK,MAAM,GAAG;QAC5B;QACA,yEAAyE;QACzE,0EAA0E;QAC1E,yEAAyE;QACzE,4EAA4E;QAC5E,gEAAgE;QAChE,UAAU,IAAI,EAAE;YACd,IAAI,OAAO,KAAK,OAAO,GACrB,MAAM,IAAI,WAAW;YACvB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,IAAK;gBAClC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,IAAK;oBAClC,IAAI;oBACJ,OAAQ;wBACN,KAAK;4BACH,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK;4BACxB;wBACF,KAAK;4BACH,SAAS,IAAI,KAAK;4BAClB;wBACF,KAAK;4BACH,SAAS,IAAI,KAAK;4BAClB;wBACF,KAAK;4BACH,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK;4BACxB;wBACF,KAAK;4BACH,SAAS,CAAC,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,KAAK,CAAC,IAAI,EAAE,IAAI,KAAK;4BACxD;wBACF,KAAK;4BACH,SAAS,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK;4BAClC;wBACF,KAAK;4BACH,SAAS,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK;4BACxC;wBACF,KAAK;4BACH,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK;4BAC1C;wBACF;4BACE,MAAM,IAAI,MAAM;oBACpB;oBACA,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,QAC5B,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;gBAC5C;YACF;QACF;QACA,6FAA6F;QAC7F,6GAA6G;QAC7G,kBAAkB;YAChB,IAAI,SAAS;YACb,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,IAAK;gBAClC,IAAI,WAAW;gBACf,IAAI,OAAO;gBACX,IAAI,aAAa;oBAAC;oBAAG;oBAAG;oBAAG;oBAAG;oBAAG;oBAAG;iBAAE;gBACtC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,IAAK;oBAClC,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,IAAI,UAAU;wBAClC;wBACA,IAAI,QAAQ,GACV,UAAU,QAAQ,UAAU;6BACzB,IAAI,OAAO,GACd;oBACJ,OAAO;wBACL,IAAI,CAAC,uBAAuB,CAAC,MAAM;wBACnC,IAAI,CAAC,UACH,UAAU,IAAI,CAAC,0BAA0B,CAAC,cAAc,QAAQ,UAAU;wBAC5E,WAAW,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;wBAC7B,OAAO;oBACT;gBACF;gBACA,UAAU,IAAI,CAAC,8BAA8B,CAAC,UAAU,MAAM,cAAc,QAAQ,UAAU;YAChG;YACA,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,IAAK;gBAClC,IAAI,WAAW;gBACf,IAAI,OAAO;gBACX,IAAI,aAAa;oBAAC;oBAAG;oBAAG;oBAAG;oBAAG;oBAAG;oBAAG;iBAAE;gBACtC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,IAAK;oBAClC,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,IAAI,UAAU;wBAClC;wBACA,IAAI,QAAQ,GACV,UAAU,QAAQ,UAAU;6BACzB,IAAI,OAAO,GACd;oBACJ,OAAO;wBACL,IAAI,CAAC,uBAAuB,CAAC,MAAM;wBACnC,IAAI,CAAC,UACH,UAAU,IAAI,CAAC,0BAA0B,CAAC,cAAc,QAAQ,UAAU;wBAC5E,WAAW,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;wBAC7B,OAAO;oBACT;gBACF;gBACA,UAAU,IAAI,CAAC,8BAA8B,CAAC,UAAU,MAAM,cAAc,QAAQ,UAAU;YAChG;YACA,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,IAAK;gBACtC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,IAAK;oBACtC,MAAM,QAAQ,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;oBAChC,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,EAC3G,UAAU,QAAQ,UAAU;gBAChC;YACF;YACA,IAAI,OAAO;YACX,KAAK,MAAM,OAAO,IAAI,CAAC,OAAO,CAC5B,OAAO,IAAI,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,CAAC,QAAQ,IAAI,CAAC,GAAG;YAC3D,MAAM,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;YACnC,MAAM,IAAI,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC,OAAO,KAAK,QAAQ,MAAM,SAAS;YAChE,OAAO,KAAK,KAAK,KAAK;YACtB,UAAU,IAAI,QAAQ,UAAU;YAChC,OAAO,KAAK,UAAU,UAAU;YAChC,OAAO;QACT;QACA,gCAAgC,GAChC,wFAAwF;QACxF,gFAAgF;QAChF,qFAAqF;QACrF,+BAA+B;YAC7B,IAAI,IAAI,CAAC,OAAO,IAAI,GAClB,OAAO,EAAE;iBACN;gBACH,MAAM,WAAW,KAAK,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,KAAK;gBAChD,MAAM,OAAO,IAAI,CAAC,OAAO,IAAI,KAAK,KAAK,KAAK,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,KAAK;gBAChG,IAAI,SAAS;oBAAC;iBAAE;gBAChB,IAAK,IAAI,MAAM,IAAI,CAAC,IAAI,GAAG,GAAG,OAAO,MAAM,GAAG,UAAU,OAAO,KAC7D,OAAO,MAAM,CAAC,GAAG,GAAG;gBACtB,OAAO;YACT;QACF;QACA,qGAAqG;QACrG,uGAAuG;QACvG,iGAAiG;QACjG,OAAO,qBAAqB,GAAG,EAAE;YAC/B,IAAI,MAAM,QAAQ,WAAW,IAAI,MAAM,QAAQ,WAAW,EACxD,MAAM,IAAI,WAAW;YACvB,IAAI,SAAS,CAAC,KAAK,MAAM,GAAG,IAAI,MAAM;YACtC,IAAI,OAAO,GAAG;gBACZ,MAAM,WAAW,KAAK,KAAK,CAAC,MAAM,KAAK;gBACvC,UAAU,CAAC,KAAK,WAAW,EAAE,IAAI,WAAW;gBAC5C,IAAI,OAAO,GACT,UAAU;YACd;YACA,OAAO,OAAO,UAAU,UAAU;YAClC,OAAO;QACT;QACA,0FAA0F;QAC1F,iGAAiG;QACjG,mFAAmF;QACnF,OAAO,oBAAoB,GAAG,EAAE,GAAG,EAAE;YACnC,OAAO,KAAK,KAAK,CAAC,QAAQ,oBAAoB,CAAC,OAAO,KAAK,QAAQ,uBAAuB,CAAC,IAAI,OAAO,CAAC,CAAC,IAAI,GAAG,QAAQ,2BAA2B,CAAC,IAAI,OAAO,CAAC,CAAC,IAAI;QACtK;QACA,sFAAsF;QACtF,gGAAgG;QAChG,OAAO,0BAA0B,MAAM,EAAE;YACvC,IAAI,SAAS,KAAK,SAAS,KACzB,MAAM,IAAI,WAAW;YACvB,IAAI,SAAS,EAAE;YACf,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,GAAG,IAC9B,OAAO,IAAI,CAAC;YACd,OAAO,IAAI,CAAC;YACZ,IAAI,OAAO;YACX,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;gBAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;oBACtC,MAAM,CAAC,EAAE,GAAG,QAAQ,mBAAmB,CAAC,MAAM,CAAC,EAAE,EAAE;oBACnD,IAAI,IAAI,IAAI,OAAO,MAAM,EACvB,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,IAAI,EAAE;gBAC9B;gBACA,OAAO,QAAQ,mBAAmB,CAAC,MAAM;YAC3C;YACA,OAAO;QACT;QACA,iGAAiG;QACjG,OAAO,4BAA4B,IAAI,EAAE,OAAO,EAAE;YAChD,IAAI,SAAS,QAAQ,GAAG,CAAC,CAAC,IAAM;YAChC,KAAK,MAAM,KAAK,KAAM;gBACpB,MAAM,SAAS,IAAI,OAAO,KAAK;gBAC/B,OAAO,IAAI,CAAC;gBACZ,QAAQ,OAAO,CAAC,CAAC,MAAM,IAAM,MAAM,CAAC,EAAE,IAAI,QAAQ,mBAAmB,CAAC,MAAM;YAC9E;YACA,OAAO;QACT;QACA,qGAAqG;QACrG,wGAAwG;QACxG,OAAO,oBAAoB,CAAC,EAAE,CAAC,EAAE;YAC/B,IAAI,MAAM,KAAK,KAAK,MAAM,KAAK,GAC7B,MAAM,IAAI,WAAW;YACvB,IAAI,IAAI;YACR,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAK;gBAC3B,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,IAAI;gBACzB,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI;YACvB;YACA,OAAO,MAAM,KAAK;YAClB,OAAO;QACT;QACA,iEAAiE;QACjE,sEAAsE;QACtE,2BAA2B,UAAU,EAAE;YACrC,MAAM,IAAI,UAAU,CAAC,EAAE;YACvB,OAAO,KAAK,IAAI,CAAC,IAAI,GAAG;YACxB,MAAM,OAAO,IAAI,KAAK,UAAU,CAAC,EAAE,IAAI,KAAK,UAAU,CAAC,EAAE,IAAI,IAAI,KAAK,UAAU,CAAC,EAAE,IAAI,KAAK,UAAU,CAAC,EAAE,IAAI;YAC7G,OAAO,CAAC,QAAQ,UAAU,CAAC,EAAE,IAAI,IAAI,KAAK,UAAU,CAAC,EAAE,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,UAAU,CAAC,EAAE,IAAI,IAAI,KAAK,UAAU,CAAC,EAAE,IAAI,IAAI,IAAI,CAAC;QACvI;QACA,2GAA2G;QAC3G,+BAA+B,eAAe,EAAE,gBAAgB,EAAE,UAAU,EAAE;YAC5E,IAAI,iBAAiB;gBACnB,IAAI,CAAC,uBAAuB,CAAC,kBAAkB;gBAC/C,mBAAmB;YACrB;YACA,oBAAoB,IAAI,CAAC,IAAI;YAC7B,IAAI,CAAC,uBAAuB,CAAC,kBAAkB;YAC/C,OAAO,IAAI,CAAC,0BAA0B,CAAC;QACzC;QACA,yGAAyG;QACzG,wBAAwB,gBAAgB,EAAE,UAAU,EAAE;YACpD,IAAI,UAAU,CAAC,EAAE,IAAI,GACnB,oBAAoB,IAAI,CAAC,IAAI;YAC/B,WAAW,GAAG;YACd,WAAW,OAAO,CAAC;QACrB;IACF;IACA,4BAA4B,GAC5B,wEAAwE;IACxE,QAAQ,WAAW,GAAG;IACtB,wEAAwE;IACxE,QAAQ,WAAW,GAAG;IACtB,oEAAoE;IACpE,QAAQ,UAAU,GAAG;IACrB,QAAQ,UAAU,GAAG;IACrB,QAAQ,UAAU,GAAG;IACrB,QAAQ,UAAU,GAAG;IACrB,QAAQ,uBAAuB,GAAG;QAChC,8EAA8E;QAC9E,6LAA6L;QAC7L;YAAC,CAAC;YAAG;YAAG;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;SAAG;QACnK,MAAM;QACN;YAAC,CAAC;YAAG;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;SAAG;QACpK,SAAS;QACT;YAAC,CAAC;YAAG;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;SAAG;QACpK,WAAW;QACX;YAAC,CAAC;YAAG;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;SAAG;KAErK;IACD,QAAQ,2BAA2B,GAAG;QACpC,8EAA8E;QAC9E,mLAAmL;QACnL;YAAC,CAAC;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;SAAG;QAC7I,MAAM;QACN;YAAC,CAAC;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;SAAG;QACtJ,SAAS;QACT;YAAC,CAAC;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;SAAG;QACzJ,WAAW;QACX;YAAC,CAAC;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;YAAI;SAAG;KAE3J;IACD,IAAI,SAAS;IACb,WAAW,MAAM,GAAG;IACpB,SAAS,WAAW,GAAG,EAAE,GAAG,EAAE,EAAE;QAC9B,IAAI,MAAM,KAAK,MAAM,MAAM,QAAQ,OAAO,GACxC,MAAM,IAAI,WAAW;QACvB,IAAK,IAAI,IAAI,MAAM,GAAG,KAAK,GAAG,IAC5B,GAAG,IAAI,CAAC,QAAQ,IAAI;IACxB;IACA,SAAS,OAAO,CAAC,EAAE,CAAC;QAClB,OAAO,CAAC,MAAM,IAAI,CAAC,KAAK;IAC1B;IACA,SAAS,OAAO,IAAI;QAClB,IAAI,CAAC,MACH,MAAM,IAAI,MAAM;IACpB;IACA,MAAM,aAAa,MAAM;QACvB,0CAA0C,GAC1C,oEAAoE;QACpE,qFAAqF;QACrF,+EAA+E;QAC/E,YAAY,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAE;YACnC,IAAI,CAAC,IAAI,GAAG;YACZ,IAAI,CAAC,QAAQ,GAAG;YAChB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,WAAW,GACb,MAAM,IAAI,WAAW;YACvB,IAAI,CAAC,OAAO,GAAG,QAAQ,KAAK;QAC9B;QACA,4CAA4C,GAC5C,kEAAkE;QAClE,mEAAmE;QACnE,sEAAsE;QACtE,OAAO,UAAU,IAAI,EAAE;YACrB,IAAI,KAAK,EAAE;YACX,KAAK,MAAM,KAAK,KACd,WAAW,GAAG,GAAG;YACnB,OAAO,IAAI,WAAW,WAAW,IAAI,CAAC,IAAI,EAAE,KAAK,MAAM,EAAE;QAC3D;QACA,6FAA6F;QAC7F,OAAO,YAAY,MAAM,EAAE;YACzB,IAAI,CAAC,WAAW,SAAS,CAAC,SACxB,MAAM,IAAI,WAAW;YACvB,IAAI,KAAK,EAAE;YACX,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAI;gBACnC,MAAM,IAAI,KAAK,GAAG,CAAC,OAAO,MAAM,GAAG,GAAG;gBACtC,WAAW,SAAS,OAAO,SAAS,CAAC,GAAG,IAAI,IAAI,KAAK,IAAI,IAAI,GAAG;gBAChE,KAAK;YACP;YACA,OAAO,IAAI,WAAW,WAAW,IAAI,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE;QAChE;QACA,qFAAqF;QACrF,sEAAsE;QACtE,iEAAiE;QACjE,OAAO,iBAAiB,IAAI,EAAE;YAC5B,IAAI,CAAC,WAAW,cAAc,CAAC,OAC7B,MAAM,IAAI,WAAW;YACvB,IAAI,KAAK,EAAE;YACX,IAAI;YACJ,IAAK,IAAI,GAAG,IAAI,KAAK,KAAK,MAAM,EAAE,KAAK,EAAG;gBACxC,IAAI,OAAO,WAAW,oBAAoB,CAAC,OAAO,CAAC,KAAK,MAAM,CAAC,MAAM;gBACrE,QAAQ,WAAW,oBAAoB,CAAC,OAAO,CAAC,KAAK,MAAM,CAAC,IAAI;gBAChE,WAAW,MAAM,IAAI;YACvB;YACA,IAAI,IAAI,KAAK,MAAM,EACjB,WAAW,WAAW,oBAAoB,CAAC,OAAO,CAAC,KAAK,MAAM,CAAC,KAAK,GAAG;YACzE,OAAO,IAAI,WAAW,WAAW,IAAI,CAAC,YAAY,EAAE,KAAK,MAAM,EAAE;QACnE;QACA,kGAAkG;QAClG,sGAAsG;QACtG,OAAO,aAAa,IAAI,EAAE;YACxB,IAAI,QAAQ,IACV,OAAO,EAAE;iBACN,IAAI,WAAW,SAAS,CAAC,OAC5B,OAAO;gBAAC,WAAW,WAAW,CAAC;aAAM;iBAClC,IAAI,WAAW,cAAc,CAAC,OACjC,OAAO;gBAAC,WAAW,gBAAgB,CAAC;aAAM;iBAE1C,OAAO;gBAAC,WAAW,SAAS,CAAC,WAAW,eAAe,CAAC;aAAO;QACnE;QACA,oEAAoE;QACpE,oDAAoD;QACpD,OAAO,QAAQ,SAAS,EAAE;YACxB,IAAI,KAAK,EAAE;YACX,IAAI,YAAY,GACd,MAAM,IAAI,WAAW;iBAClB,IAAI,YAAY,KAAK,GACxB,WAAW,WAAW,GAAG;iBACtB,IAAI,YAAY,KAAK,IAAI;gBAC5B,WAAW,GAAG,GAAG;gBACjB,WAAW,WAAW,IAAI;YAC5B,OAAO,IAAI,YAAY,KAAK;gBAC1B,WAAW,GAAG,GAAG;gBACjB,WAAW,WAAW,IAAI;YAC5B,OACE,MAAM,IAAI,WAAW;YACvB,OAAO,IAAI,WAAW,WAAW,IAAI,CAAC,GAAG,EAAE,GAAG;QAChD;QACA,8EAA8E;QAC9E,mEAAmE;QACnE,OAAO,UAAU,IAAI,EAAE;YACrB,OAAO,WAAW,aAAa,CAAC,IAAI,CAAC;QACvC;QACA,mFAAmF;QACnF,mFAAmF;QACnF,0FAA0F;QAC1F,OAAO,eAAe,IAAI,EAAE;YAC1B,OAAO,WAAW,kBAAkB,CAAC,IAAI,CAAC;QAC5C;QACA,eAAe,GACf,uDAAuD;QACvD,UAAU;YACR,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK;QAC3B;QACA,qGAAqG;QACrG,0GAA0G;QAC1G,OAAO,aAAa,IAAI,EAAE,OAAO,EAAE;YACjC,IAAI,SAAS;YACb,KAAK,MAAM,OAAO,KAAM;gBACtB,MAAM,SAAS,IAAI,IAAI,CAAC,gBAAgB,CAAC;gBACzC,IAAI,IAAI,QAAQ,IAAI,KAAK,QACvB,OAAO;gBACT,UAAU,IAAI,SAAS,IAAI,OAAO,CAAC,MAAM;YAC3C;YACA,OAAO;QACT;QACA,+EAA+E;QAC/E,OAAO,gBAAgB,GAAG,EAAE;YAC1B,MAAM,UAAU;YAChB,IAAI,SAAS,EAAE;YACf,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;gBACnC,IAAI,IAAI,MAAM,CAAC,MAAM,KACnB,OAAO,IAAI,CAAC,IAAI,UAAU,CAAC;qBACxB;oBACH,OAAO,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,IAAI,GAAG,IAAI,IAAI;oBAClD,KAAK;gBACP;YACF;YACA,OAAO;QACT;IACF;IACA,iBAAiB,GACjB,sEAAsE;IACtE,WAAW,aAAa,GAAG;IAC3B,2EAA2E;IAC3E,WAAW,kBAAkB,GAAG;IAChC,wDAAwD;IACxD,8DAA8D;IAC9D,WAAW,oBAAoB,GAAG;IAClC,IAAI,YAAY;IAChB,WAAW,SAAS,GAAG;AACzB,CAAC,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC;AAC/B,CAAC,CAAC;IACA,IAAI;IACJ,CAAC,CAAC;QACA,MAAM,OAAO,MAAM;YACjB,yDAAyD;YACzD,8BAA8B,GAC9B,YAAY,OAAO,EAAE,UAAU,CAAE;gBAC/B,IAAI,CAAC,OAAO,GAAG;gBACf,IAAI,CAAC,UAAU,GAAG;YACpB;QACF;QACA,iBAAiB,GACjB,KAAK,GAAG,GAAG,IAAI,KAAK,GAAG;QACvB,yDAAyD;QACzD,KAAK,MAAM,GAAG,IAAI,KAAK,GAAG;QAC1B,yDAAyD;QACzD,KAAK,QAAQ,GAAG,IAAI,KAAK,GAAG;QAC5B,yDAAyD;QACzD,KAAK,IAAI,GAAG,IAAI,KAAK,GAAG;QACxB,IAAI,MAAM;QACV,QAAQ,GAAG,GAAG;IAChB,CAAC,EAAE,SAAS,WAAW,MAAM,IAAI,CAAC,WAAW,MAAM,GAAG,CAAC,CAAC;AAC1D,CAAC,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC;AAC/B,CAAC,CAAC;IACA,IAAI;IACJ,CAAC,CAAC;QACA,MAAM,QAAQ,MAAM;YAClB,8BAA8B,GAC9B,YAAY,QAAQ,EAAE,gBAAgB,CAAE;gBACtC,IAAI,CAAC,QAAQ,GAAG;gBAChB,IAAI,CAAC,gBAAgB,GAAG;YAC1B;YACA,cAAc,GACd,wFAAwF;YACxF,0FAA0F;YAC1F,iBAAiB,GAAG,EAAE;gBACpB,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,IAAI;YAC1D;QACF;QACA,iBAAiB,GACjB,MAAM,OAAO,GAAG,IAAI,MAAM,GAAG;YAAC;YAAI;YAAI;SAAG;QACzC,MAAM,YAAY,GAAG,IAAI,MAAM,GAAG;YAAC;YAAG;YAAI;SAAG;QAC7C,MAAM,IAAI,GAAG,IAAI,MAAM,GAAG;YAAC;YAAG;YAAI;SAAG;QACrC,MAAM,KAAK,GAAG,IAAI,MAAM,GAAG;YAAC;YAAG;YAAI;SAAG;QACtC,MAAM,GAAG,GAAG,IAAI,MAAM,GAAG;YAAC;YAAG;YAAG;SAAE;QAClC,IAAI,OAAO;QACX,WAAW,IAAI,GAAG;IACpB,CAAC,EAAE,YAAY,WAAW,SAAS,IAAI,CAAC,WAAW,SAAS,GAAG,CAAC,CAAC;AACnE,CAAC,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC;AAC/B,IAAI,oBAAoB;AAExB,gBAAgB;AAChB;;;;CAIC,GACD,IAAI,kBAAkB;IACpB,GAAG,kBAAkB,MAAM,CAAC,GAAG,CAAC,GAAG;IACnC,GAAG,kBAAkB,MAAM,CAAC,GAAG,CAAC,MAAM;IACtC,GAAG,kBAAkB,MAAM,CAAC,GAAG,CAAC,QAAQ;IACxC,GAAG,kBAAkB,MAAM,CAAC,GAAG,CAAC,IAAI;AACtC;AACA,IAAI,eAAe;AACnB,IAAI,gBAAgB;AACpB,IAAI,kBAAkB;AACtB,IAAI,kBAAkB;AACtB,IAAI,wBAAwB;AAC5B,IAAI,qBAAqB;AACzB,IAAI,mBAAmB;AACvB,IAAI,sBAAsB;AAC1B,IAAI,oBAAoB;AACxB,SAAS,aAAa,OAAO,EAAE,SAAS,CAAC;IACvC,MAAM,MAAM,EAAE;IACd,QAAQ,OAAO,CAAC,SAAS,GAAG,EAAE,CAAC;QAC7B,IAAI,QAAQ;QACZ,IAAI,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC;YAC1B,IAAI,CAAC,QAAQ,UAAU,MAAM;gBAC3B,IAAI,IAAI,CACN,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE,IAAI,MAAM,GAAG,EAAE,QAAQ,OAAO,CAAC,CAAC;gBAEtE,QAAQ;gBACR;YACF;YACA,IAAI,MAAM,IAAI,MAAM,GAAG,GAAG;gBACxB,IAAI,CAAC,MAAM;oBACT;gBACF;gBACA,IAAI,UAAU,MAAM;oBAClB,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE,IAAI,OAAO,MAAM,EAAE,IAAI,OAAO,CAAC,CAAC;gBAC7D,OAAO;oBACL,IAAI,IAAI,CACN,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,EAAE,IAAI,OAAO,EAAE,EAAE,IAAI,IAAI,MAAM,GAAG,EAAE,QAAQ,OAAO,CAAC,CAAC;gBAE7E;gBACA;YACF;YACA,IAAI,QAAQ,UAAU,MAAM;gBAC1B,QAAQ;YACV;QACF;IACF;IACA,OAAO,IAAI,IAAI,CAAC;AAClB;AACA,SAAS,gBAAgB,OAAO,EAAE,UAAU;IAC1C,OAAO,QAAQ,KAAK,GAAG,GAAG,CAAC,CAAC,KAAK;QAC/B,IAAI,IAAI,WAAW,CAAC,IAAI,KAAK,WAAW,CAAC,GAAG,WAAW,CAAC,EAAE;YACxD,OAAO;QACT;QACA,OAAO,IAAI,GAAG,CAAC,CAAC,MAAM;YACpB,IAAI,IAAI,WAAW,CAAC,IAAI,KAAK,WAAW,CAAC,GAAG,WAAW,CAAC,EAAE;gBACxD,OAAO;YACT;YACA,OAAO;QACT;IACF;AACF;AACA,SAAS,iBAAiB,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,aAAa;IAC1D,IAAI,iBAAiB,MAAM;QACzB,OAAO;IACT;IACA,MAAM,WAAW,MAAM,MAAM,GAAG,SAAS;IACzC,MAAM,cAAc,KAAK,KAAK,CAAC,OAAO;IACtC,MAAM,QAAQ,WAAW;IACzB,MAAM,IAAI,CAAC,cAAc,KAAK,IAAI,WAAW,IAAI;IACjD,MAAM,IAAI,CAAC,cAAc,MAAM,IAAI,WAAW,IAAI;IAClD,MAAM,IAAI,cAAc,CAAC,IAAI,OAAO,MAAM,MAAM,GAAG,IAAI,IAAI,IAAI,cAAc,CAAC,GAAG;IACjF,MAAM,IAAI,cAAc,CAAC,IAAI,OAAO,MAAM,MAAM,GAAG,IAAI,IAAI,IAAI,cAAc,CAAC,GAAG;IACjF,MAAM,UAAU,cAAc,OAAO,IAAI,OAAO,IAAI,cAAc,OAAO;IACzE,IAAI,aAAa;IACjB,IAAI,cAAc,QAAQ,EAAE;QAC1B,IAAI,SAAS,KAAK,KAAK,CAAC;QACxB,IAAI,SAAS,KAAK,KAAK,CAAC;QACxB,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,IAAI;QAC9B,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,IAAI;QAC9B,aAAa;YAAE,GAAG;YAAQ,GAAG;YAAQ,GAAG;YAAO,GAAG;QAAM;IAC1D;IACA,MAAM,cAAc,cAAc,WAAW;IAC7C,OAAO;QAAE;QAAG;QAAG;QAAG;QAAG;QAAY;QAAS;IAAY;AACxD;AACA,SAAS,cAAc,aAAa,EAAE,UAAU;IAC9C,IAAI,cAAc,MAAM;QACtB,OAAO,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,aAAa;IAC1C;IACA,OAAO,gBAAgB,mBAAmB;AAC5C;AACA,SAAS,UAAU,EACjB,KAAK,EACL,KAAK,EACL,UAAU,EACV,aAAa,EACb,UAAU,EACV,aAAa,EACb,IAAI,EACJ,UAAU,EACX;IACC,IAAI,SAAS,oUAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QACzB,MAAM,SAAS,MAAM,OAAO,CAAC,SAAS,QAAQ;YAAC;SAAM;QACrD,MAAM,WAAW,OAAO,MAAM,CAAC,CAAC,OAAO;YACrC,MAAM,IAAI,IAAI,kBAAkB,SAAS,CAAC,YAAY,CAAC;YACvD,OAAO;QACT,GAAG,EAAE;QACL,OAAO,kBAAkB,MAAM,CAAC,cAAc,CAC5C,UACA,eAAe,CAAC,MAAM,EACtB,YACA,KAAK,GACL,KAAK,GACL;IAEJ,GAAG;QAAC;QAAO;QAAO;QAAY;KAAW;IACzC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,uBAAuB,EAAE,GAAG,oUAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QACzE,IAAI,SAAS,OAAO,UAAU;QAC9B,MAAM,UAAU,cAAc,eAAe;QAC7C,MAAM,YAAY,OAAO,MAAM,GAAG,UAAU;QAC5C,MAAM,2BAA2B,iBAC/B,QACA,MACA,SACA;QAEF,OAAO;YACL,OAAO;YACP,QAAQ;YACR,UAAU;YACV,yBAAyB;QAC3B;IACF,GAAG;QAAC;QAAQ;QAAM;QAAe;QAAe;KAAW;IAC3D,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AACA,IAAI,kBAAkB;IACpB,IAAI;QACF,IAAI,SAAS,OAAO,CAAC,IAAI;IAC3B,EAAE,OAAO,GAAG;QACV,OAAO;IACT;IACA,OAAO;AACT;AACA,IAAI,eAAe,oUAAA,CAAA,UAAK,CAAC,UAAU,CACjC,SAAS,cAAc,KAAK,EAAE,YAAY;IACxC,MAAM,KAAK,OAAO,EAChB,KAAK,EACL,OAAO,YAAY,EACnB,QAAQ,aAAa,EACrB,UAAU,eAAe,EACzB,UAAU,eAAe,EACzB,gBAAgB,qBAAqB,EACrC,aAAa,kBAAkB,EAC/B,UAAU,EACV,UAAU,EACV,aAAa,EACd,GAAG,IAAI,aAAa,UAAU,IAAI;QACjC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,KAAK,YAAY,EAAE,KAAK,EAAE,GAAG,IAAI,aAAa,UAAU,IAAI;QAAC;KAAQ;IAC3E,MAAM,SAAS,iBAAiB,OAAO,KAAK,IAAI,cAAc,GAAG;IACjE,MAAM,UAAU,oUAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC7B,MAAM,SAAS,oUAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC5B,MAAM,eAAe,oUAAA,CAAA,UAAK,CAAC,WAAW,CACpC,CAAC;QACC,QAAQ,OAAO,GAAG;QAClB,IAAI,OAAO,iBAAiB,YAAY;YACtC,aAAa;QACf,OAAO,IAAI,cAAc;YACvB,aAAa,OAAO,GAAG;QACzB;IACF,GACA;QAAC;KAAa;IAEhB,MAAM,CAAC,aAAa,iBAAiB,GAAG,oUAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACvD,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,uBAAuB,EAAE,GAAG,UAAU;QACrE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IACA,oUAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,QAAQ,OAAO,IAAI,MAAM;YAC3B,MAAM,SAAS,QAAQ,OAAO;YAC9B,MAAM,MAAM,OAAO,UAAU,CAAC;YAC9B,IAAI,CAAC,KAAK;gBACR;YACF;YACA,IAAI,cAAc;YAClB,MAAM,QAAQ,OAAO,OAAO;YAC5B,MAAM,oBAAoB,2BAA2B,QAAQ,UAAU,QAAQ,MAAM,QAAQ,IAAI,MAAM,aAAa,KAAK,KAAK,MAAM,YAAY,KAAK;YACrJ,IAAI,mBAAmB;gBACrB,IAAI,wBAAwB,UAAU,IAAI,MAAM;oBAC9C,cAAc,gBACZ,OACA,wBAAwB,UAAU;gBAEtC;YACF;YACA,MAAM,aAAa,OAAO,gBAAgB,IAAI;YAC9C,OAAO,MAAM,GAAG,OAAO,KAAK,GAAG,OAAO;YACtC,MAAM,QAAQ,OAAO,WAAW;YAChC,IAAI,KAAK,CAAC,OAAO;YACjB,IAAI,SAAS,GAAG;YAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,UAAU;YAC7B,IAAI,SAAS,GAAG;YAChB,IAAI,iBAAiB;gBACnB,IAAI,IAAI,CAAC,IAAI,OAAO,aAAa,aAAa;YAChD,OAAO;gBACL,MAAM,OAAO,CAAC,SAAS,GAAG,EAAE,GAAG;oBAC7B,IAAI,OAAO,CAAC,SAAS,IAAI,EAAE,GAAG;wBAC5B,IAAI,MAAM;4BACR,IAAI,QAAQ,CAAC,MAAM,QAAQ,MAAM,QAAQ,GAAG;wBAC9C;oBACF;gBACF;YACF;YACA,IAAI,yBAAyB;gBAC3B,IAAI,WAAW,GAAG,wBAAwB,OAAO;YACnD;YACA,IAAI,mBAAmB;gBACrB,IAAI,SAAS,CACX,OACA,wBAAwB,CAAC,GAAG,QAC5B,wBAAwB,CAAC,GAAG,QAC5B,wBAAwB,CAAC,EACzB,wBAAwB,CAAC;YAE7B;QACF;IACF;IACA,oUAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,iBAAiB;IACnB,GAAG;QAAC;KAAO;IACX,MAAM,cAAc,eAAe;QAAE,QAAQ;QAAM,OAAO;IAAK,GAAG;IAClE,IAAI,MAAM;IACV,IAAI,UAAU,MAAM;QAClB,MAAM,aAAa,GAAG,oUAAA,CAAA,UAAK,CAAC,aAAa,CACvC,OACA;YACE,KAAK;YACL,KAAK;YACL,OAAO;gBAAE,SAAS;YAAO;YACzB,QAAQ;gBACN,iBAAiB;YACnB;YACA,KAAK;YACL,aAAa,2BAA2B,OAAO,KAAK,IAAI,wBAAwB,WAAW;QAC7F;IAEJ;IACA,OAAO,aAAa,GAAG,oUAAA,CAAA,UAAK,CAAC,aAAa,CAAC,oUAAA,CAAA,UAAK,CAAC,QAAQ,EAAE,MAAM,aAAa,GAAG,oUAAA,CAAA,UAAK,CAAC,aAAa,CAClG,UACA,eAAe;QACb,OAAO;QACP,QAAQ;QACR,OAAO;QACP,KAAK;QACL,MAAM;IACR,GAAG,cACF;AACL;AAEF,aAAa,WAAW,GAAG;AAC3B,IAAI,YAAY,oUAAA,CAAA,UAAK,CAAC,UAAU,CAC9B,SAAS,WAAW,KAAK,EAAE,YAAY;IACrC,MAAM,KAAK,OAAO,EAChB,KAAK,EACL,OAAO,YAAY,EACnB,QAAQ,aAAa,EACrB,UAAU,eAAe,EACzB,UAAU,eAAe,EACzB,gBAAgB,qBAAqB,EACrC,aAAa,kBAAkB,EAC/B,UAAU,EACV,KAAK,EACL,UAAU,EACV,aAAa,EACd,GAAG,IAAI,aAAa,UAAU,IAAI;QACjC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,uBAAuB,EAAE,GAAG,UAAU;QACrE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IACA,IAAI,cAAc;IAClB,IAAI,QAAQ;IACZ,IAAI,iBAAiB,QAAQ,2BAA2B,MAAM;QAC5D,IAAI,wBAAwB,UAAU,IAAI,MAAM;YAC9C,cAAc,gBACZ,OACA,wBAAwB,UAAU;QAEtC;QACA,QAAQ,aAAa,GAAG,oUAAA,CAAA,UAAK,CAAC,aAAa,CACzC,SACA;YACE,MAAM,cAAc,GAAG;YACvB,QAAQ,wBAAwB,CAAC;YACjC,OAAO,wBAAwB,CAAC;YAChC,GAAG,wBAAwB,CAAC,GAAG;YAC/B,GAAG,wBAAwB,CAAC,GAAG;YAC/B,qBAAqB;YACrB,SAAS,wBAAwB,OAAO;YACxC,aAAa,wBAAwB,WAAW;QAClD;IAEJ;IACA,MAAM,SAAS,aAAa,aAAa;IACzC,OAAO,aAAa,GAAG,oUAAA,CAAA,UAAK,CAAC,aAAa,CACxC,OACA,eAAe;QACb,QAAQ;QACR,OAAO;QACP,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,UAAU;QACtC,KAAK;QACL,MAAM;IACR,GAAG,aACH,CAAC,CAAC,SAAS,aAAa,GAAG,oUAAA,CAAA,UAAK,CAAC,aAAa,CAAC,SAAS,MAAM,QAC9D,aAAa,GAAG,oUAAA,CAAA,UAAK,CAAC,aAAa,CACjC,QACA;QACE,MAAM;QACN,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE,SAAS,GAAG,CAAC;QACrC,gBAAgB;IAClB,IAEF,aAAa,GAAG,oUAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;QAAE,MAAM;QAAS,GAAG;QAAQ,gBAAgB;IAAa,IACrG;AAEJ;AAEF,UAAU,WAAW,GAAG", "ignoreList": [0], "debugId": null}}]}