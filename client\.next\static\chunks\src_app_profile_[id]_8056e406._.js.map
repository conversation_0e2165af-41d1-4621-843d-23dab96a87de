{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/src/app/profile/%5Bid%5D/QRCodeDisplay.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { QRCodeSVG } from 'qrcode.react';\n\ninterface QRCodeDisplayProps {\n  profileId: number;\n}\n\nexport default function QRCodeDisplay({ profileId }: QRCodeDisplayProps) {\n  const [showPrintView, setShowPrintView] = useState(false);\n  \n  // URL yang akan di-encode dalam QR Code\n  const profileUrl = `${window.location.origin}/profile/${profileId}`;\n\n  const handlePrint = () => {\n    setShowPrintView(true);\n    setTimeout(() => {\n      window.print();\n      setShowPrintView(false);\n    }, 100);\n  };\n\n  const handleDownload = () => {\n    const canvas = document.querySelector('#qr-code canvas') as HTMLCanvasElement;\n    if (canvas) {\n      const link = document.createElement('a');\n      link.download = `qr-code-lansia-${profileId}.png`;\n      link.href = canvas.toDataURL();\n      link.click();\n    }\n  };\n\n  const handleCopyUrl = () => {\n    navigator.clipboard.writeText(profileUrl).then(() => {\n      alert('URL berhasil disalin ke clipboard!');\n    }).catch(() => {\n      alert('Gagal menyalin URL');\n    });\n  };\n\n  return (\n    <>\n      <div className=\"text-center space-y-4\">\n        {/* QR Code */}\n        <div id=\"qr-code\" className=\"flex justify-center\">\n          <div className=\"bg-white p-4 rounded-lg border-2 border-gray-200\">\n            <QRCodeSVG\n              value={profileUrl}\n              size={200}\n              level=\"M\"\n            />\n          </div>\n        </div>\n\n        {/* Profile Info */}\n        <div className=\"text-sm text-gray-600\">\n          <p>ID Profil: <span className=\"font-mono font-semibold\">{profileId}</span></p>\n          <p className=\"mt-1\">Scan untuk akses cepat</p>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex flex-col space-y-2\">\n          <button\n            onClick={handlePrint}\n            className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 btn-hover\"\n          >\n            <span className=\"flex items-center justify-center\">\n              <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z\" />\n              </svg>\n              Cetak QR Code\n            </span>\n          </button>\n\n          <button\n            onClick={handleDownload}\n            className=\"w-full bg-green-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all duration-200 btn-hover\"\n          >\n            <span className=\"flex items-center justify-center\">\n              <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n              </svg>\n              Download PNG\n            </span>\n          </button>\n\n          <button\n            onClick={handleCopyUrl}\n            className=\"w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-lg font-medium hover:bg-gray-300 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-200 btn-hover\"\n          >\n            <span className=\"flex items-center justify-center\">\n              <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\" />\n              </svg>\n              Salin URL\n            </span>\n          </button>\n        </div>\n      </div>\n\n      {/* Print Styles */}\n      {showPrintView && (\n        <style jsx global>{`\n          @media print {\n            body * {\n              visibility: hidden;\n            }\n            #print-qr-code, #print-qr-code * {\n              visibility: visible;\n            }\n            #print-qr-code {\n              position: absolute;\n              left: 0;\n              top: 0;\n              width: 100%;\n              text-align: center;\n              padding: 20px;\n            }\n          }\n        `}</style>\n      )}\n\n      {/* Hidden Print Content */}\n      <div id=\"print-qr-code\" className=\"hidden print:block\">\n        <div className=\"text-center\">\n          <h1 className=\"text-2xl font-bold mb-4\">QR Code Kesehatan Lansia</h1>\n          <div className=\"flex justify-center mb-4\">\n            <QRCodeSVG\n              value={profileUrl}\n              size={300}\n              level=\"M\"\n            />\n          </div>\n          <div className=\"text-lg\">\n            <p><strong>ID Profil:</strong> {profileId}</p>\n            <p className=\"mt-2\">Scan QR Code ini untuk mengakses profil kesehatan</p>\n            <p className=\"mt-4 text-sm text-gray-600\">\n              Aplikasi Kesehatan Lansia - Posyandu Digital\n            </p>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;;AASe,SAAS,cAAc,KAAiC;QAAjC,EAAE,SAAS,EAAsB,GAAjC;;IACpC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,wCAAwC;IACxC,MAAM,aAAa,AAAC,GAAoC,OAAlC,OAAO,QAAQ,CAAC,MAAM,EAAC,aAAqB,OAAV;IAExD,MAAM,cAAc;QAClB,iBAAiB;QACjB,WAAW;YACT,OAAO,KAAK;YACZ,iBAAiB;QACnB,GAAG;IACL;IAEA,MAAM,iBAAiB;QACrB,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,IAAI,QAAQ;YACV,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,QAAQ,GAAG,AAAC,kBAA2B,OAAV,WAAU;YAC5C,KAAK,IAAI,GAAG,OAAO,SAAS;YAC5B,KAAK,KAAK;QACZ;IACF;IAEA,MAAM,gBAAgB;QACpB,UAAU,SAAS,CAAC,SAAS,CAAC,YAAY,IAAI,CAAC;YAC7C,MAAM;QACR,GAAG,KAAK,CAAC;YACP,MAAM;QACR;IACF;IAEA,qBACE;;0BACE,4TAAC;gBAAI,WAAU;;kCAEb,4TAAC;wBAAI,IAAG;wBAAU,WAAU;kCAC1B,cAAA,4TAAC;4BAAI,WAAU;sCACb,cAAA,4TAAC,2OAAA,CAAA,YAAS;gCACR,OAAO;gCACP,MAAM;gCACN,OAAM;;;;;;;;;;;;;;;;kCAMZ,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;;oCAAE;kDAAW,4TAAC;wCAAK,WAAU;kDAA2B;;;;;;;;;;;;0CACzD,4TAAC;gCAAE,WAAU;0CAAO;;;;;;;;;;;;kCAItB,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,4TAAC;oCAAK,WAAU;;sDACd,4TAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACtE,cAAA,4TAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCACjE;;;;;;;;;;;;0CAKV,4TAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,4TAAC;oCAAK,WAAU;;sDACd,4TAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACtE,cAAA,4TAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCACjE;;;;;;;;;;;;0CAKV,4TAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,4TAAC;oCAAK,WAAU;;sDACd,4TAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACtE,cAAA,4TAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCACjE;;;;;;;;;;;;;;;;;;;;;;;;YAQb;;;;0BAsBD,4TAAC;gBAAI,IAAG;gBAAgB,WAAU;0BAChC,cAAA,4TAAC;oBAAI,WAAU;;sCACb,4TAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,4TAAC;4BAAI,WAAU;sCACb,cAAA,4TAAC,2OAAA,CAAA,YAAS;gCACR,OAAO;gCACP,MAAM;gCACN,OAAM;;;;;;;;;;;sCAGV,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;;sDAAE,4TAAC;sDAAO;;;;;;wCAAmB;wCAAE;;;;;;;8CAChC,4TAAC;oCAAE,WAAU;8CAAO;;;;;;8CACpB,4TAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;AAQtD;GAxIwB;KAAA", "debugId": null}}, {"offset": {"line": 344, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/src/app/profile/%5Bid%5D/AddCheckupForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\n\ninterface AddCheckupFormProps {\n  profileId: number;\n}\n\ninterface CheckupData {\n  tekanan_darah: string;\n  gula_darah: string;\n  catatan: string;\n}\n\nexport default function AddCheckupForm({ profileId }: AddCheckupFormProps) {\n  const router = useRouter();\n  const [isLoading, setIsLoading] = useState(false);\n  const [showForm, setShowForm] = useState(false);\n  const [formData, setFormData] = useState<CheckupData>({\n    tekanan_darah: '',\n    gula_darah: '',\n    catatan: ''\n  });\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsLoading(true);\n\n    try {\n      const response = await fetch('http://localhost:5000/api/checkups', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          profile_id: profileId,\n          ...formData\n        })\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        // Reset form\n        setFormData({\n          tekanan_darah: '',\n          gula_darah: '',\n          catatan: ''\n        });\n        setShowForm(false);\n        \n        // Refresh the page to show new data\n        router.refresh();\n        \n        alert('Pemeriksaan berhasil ditambahkan!');\n      } else {\n        alert(`Gagal menambah pemeriksaan: ${data.error || 'Unknown error'}`);\n      }\n    } catch (error) {\n      console.error('Error adding checkup:', error);\n      alert('Gagal menghubungi server. Pastikan server backend berjalan.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleCancel = () => {\n    setFormData({\n      tekanan_darah: '',\n      gula_darah: '',\n      catatan: ''\n    });\n    setShowForm(false);\n  };\n\n  if (!showForm) {\n    return (\n      <div className=\"text-center\">\n        <button\n          onClick={() => setShowForm(true)}\n          className=\"bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 btn-hover\"\n        >\n          <span className=\"flex items-center justify-center\">\n            <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n            </svg>\n            Tambah Pemeriksaan Baru\n          </span>\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-4\">\n      <div className=\"grid md:grid-cols-2 gap-4\">\n        <div>\n          <label htmlFor=\"tekanan_darah\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Tekanan Darah *\n          </label>\n          <input\n            type=\"text\"\n            id=\"tekanan_darah\"\n            name=\"tekanan_darah\"\n            value={formData.tekanan_darah}\n            onChange={handleChange}\n            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\n            placeholder=\"Contoh: 120/80\"\n            pattern=\"[0-9]{2,3}/[0-9]{2,3}\"\n            title=\"Format: sistol/diastol (contoh: 120/80)\"\n            required\n          />\n        </div>\n\n        <div>\n          <label htmlFor=\"gula_darah\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Gula Darah (mg/dL) *\n          </label>\n          <input\n            type=\"number\"\n            id=\"gula_darah\"\n            name=\"gula_darah\"\n            value={formData.gula_darah}\n            onChange={handleChange}\n            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\n            placeholder=\"Masukkan kadar gula darah\"\n            min=\"50\"\n            max=\"500\"\n            required\n          />\n        </div>\n      </div>\n\n      <div>\n        <label htmlFor=\"catatan\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Catatan Pemeriksaan\n        </label>\n        <textarea\n          id=\"catatan\"\n          name=\"catatan\"\n          value={formData.catatan}\n          onChange={handleChange}\n          rows={3}\n          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\n          placeholder=\"Catatan tambahan dari pemeriksaan (opsional)\"\n        />\n      </div>\n\n      <div className=\"flex flex-col sm:flex-row gap-3\">\n        <button\n          type=\"submit\"\n          disabled={isLoading}\n          className=\"flex-1 bg-green-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed btn-hover\"\n        >\n          {isLoading ? (\n            <span className=\"flex items-center justify-center\">\n              <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n              </svg>\n              Menyimpan...\n            </span>\n          ) : (\n            <span className=\"flex items-center justify-center\">\n              <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n              </svg>\n              Simpan Pemeriksaan\n            </span>\n          )}\n        </button>\n        \n        <button\n          type=\"button\"\n          onClick={handleCancel}\n          className=\"flex-1 bg-gray-200 text-gray-800 py-3 px-6 rounded-lg font-medium hover:bg-gray-300 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-200 btn-hover\"\n        >\n          Batal\n        </button>\n      </div>\n    </form>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAee,SAAS,eAAe,KAAkC;QAAlC,EAAE,SAAS,EAAuB,GAAlC;;IACrC,MAAM,SAAS,CAAA,GAAA,oQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAe;QACpD,eAAe;QACf,YAAY;QACZ,SAAS;IACX;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sCAAsC;gBACjE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,YAAY;oBACZ,GAAG,QAAQ;gBACb;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,aAAa;gBACb,YAAY;oBACV,eAAe;oBACf,YAAY;oBACZ,SAAS;gBACX;gBACA,YAAY;gBAEZ,oCAAoC;gBACpC,OAAO,OAAO;gBAEd,MAAM;YACR,OAAO;gBACL,MAAM,AAAC,+BAA4D,OAA9B,KAAK,KAAK,IAAI;YACrD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,YAAY;YACV,eAAe;YACf,YAAY;YACZ,SAAS;QACX;QACA,YAAY;IACd;IAEA,IAAI,CAAC,UAAU;QACb,qBACE,4TAAC;YAAI,WAAU;sBACb,cAAA,4TAAC;gBACC,SAAS,IAAM,YAAY;gBAC3B,WAAU;0BAEV,cAAA,4TAAC;oBAAK,WAAU;;sCACd,4TAAC;4BAAI,WAAU;4BAAe,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACtE,cAAA,4TAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;wBACjE;;;;;;;;;;;;;;;;;IAMhB;IAEA,qBACE,4TAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;;0CACC,4TAAC;gCAAM,SAAQ;gCAAgB,WAAU;0CAA+C;;;;;;0CAGxF,4TAAC;gCACC,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,aAAa;gCAC7B,UAAU;gCACV,WAAU;gCACV,aAAY;gCACZ,SAAQ;gCACR,OAAM;gCACN,QAAQ;;;;;;;;;;;;kCAIZ,4TAAC;;0CACC,4TAAC;gCAAM,SAAQ;gCAAa,WAAU;0CAA+C;;;;;;0CAGrF,4TAAC;gCACC,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,UAAU;gCAC1B,UAAU;gCACV,WAAU;gCACV,aAAY;gCACZ,KAAI;gCACJ,KAAI;gCACJ,QAAQ;;;;;;;;;;;;;;;;;;0BAKd,4TAAC;;kCACC,4TAAC;wBAAM,SAAQ;wBAAU,WAAU;kCAA+C;;;;;;kCAGlF,4TAAC;wBACC,IAAG;wBACH,MAAK;wBACL,OAAO,SAAS,OAAO;wBACvB,UAAU;wBACV,MAAM;wBACN,WAAU;wBACV,aAAY;;;;;;;;;;;;0BAIhB,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBACC,MAAK;wBACL,UAAU;wBACV,WAAU;kCAET,0BACC,4TAAC;4BAAK,WAAU;;8CACd,4TAAC;oCAAI,WAAU;oCAA6C,OAAM;oCAA6B,MAAK;oCAAO,SAAQ;;sDACjH,4TAAC;4CAAO,WAAU;4CAAa,IAAG;4CAAK,IAAG;4CAAK,GAAE;4CAAK,QAAO;4CAAe,aAAY;;;;;;sDACxF,4TAAC;4CAAK,WAAU;4CAAa,MAAK;4CAAe,GAAE;;;;;;;;;;;;gCAC/C;;;;;;iDAIR,4TAAC;4BAAK,WAAU;;8CACd,4TAAC;oCAAI,WAAU;oCAAe,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACtE,cAAA,4TAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;gCACjE;;;;;;;;;;;;kCAMZ,4TAAC;wBACC,MAAK;wBACL,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT;GAhLwB;;QACP,oQAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}