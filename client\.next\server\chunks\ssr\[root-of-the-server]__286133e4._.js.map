{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/src/app/form/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\n\ninterface FormData {\n  nama: string;\n  usia: string;\n  alamat: string;\n  riwayat_medis: string;\n  tekanan_darah: string;\n  gula_darah: string;\n  catatan: string;\n}\n\nexport default function FormPage() {\n  const router = useRouter();\n  const [isLoading, setIsLoading] = useState(false);\n  const [formData, setFormData] = useState<FormData>({\n    nama: '',\n    usia: '',\n    alamat: '',\n    riwayat_medis: '',\n    tekanan_darah: '',\n    gula_darah: '',\n    catatan: ''\n  });\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsLoading(true);\n\n    try {\n      const response = await fetch('http://localhost:5000/api/profiles', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(formData)\n      });\n\n      const data = await response.json();\n\n      if (response.ok && data.id) {\n        // Redirect ke halaman profil dengan ID yang baru dibuat\n        router.push(`/profile/${data.id}`);\n      } else {\n        alert(`Gagal menyimpan data: ${data.error || 'Unknown error'}`);\n      }\n    } catch (error) {\n      console.error('Error submitting form:', error);\n      alert('Gagal menghubungi server. Pastikan server backend berjalan.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div className=\"flex items-center space-x-4\">\n              <Link href=\"/\" className=\"flex items-center space-x-3\">\n                <div className=\"w-12 h-12 bg-blue-600 rounded-2xl flex items-center justify-center shadow-sm\">\n                  <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n                  </svg>\n                </div>\n                <div>\n                  <h1 className=\"text-2xl font-bold text-gray-900\">Kesehatan Lansia</h1>\n                  <p className=\"text-sm text-gray-600\">Form Pendaftaran</p>\n                </div>\n              </Link>\n            </div>\n            <Link\n              href=\"/\"\n              className=\"btn-secondary text-sm\"\n            >\n              ← Kembali ke Beranda\n            </Link>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n        <div className=\"fade-in\">\n          {/* Progress Indicator */}\n          <div className=\"mb-8\">\n            <div className=\"flex items-center justify-center space-x-4 mb-4\">\n              <div className=\"flex items-center\">\n                <div className=\"w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold\">1</div>\n                <span className=\"ml-2 text-sm font-medium text-gray-900\">Data Pribadi</span>\n              </div>\n              <div className=\"w-16 h-1 bg-gray-300 rounded\"></div>\n              <div className=\"flex items-center\">\n                <div className=\"w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold\">2</div>\n                <span className=\"ml-2 text-sm font-medium text-gray-900\">Pemeriksaan</span>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"card\">\n            <div className=\"px-8 py-8 border-b border-gray-200\">\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4\">\n                  <svg className=\"w-8 h-8 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                  </svg>\n                </div>\n                <h2 className=\"text-3xl font-bold text-gray-900 mb-2\">Form Pendaftaran Lansia</h2>\n                <p className=\"text-gray-600 max-w-md mx-auto\">\n                  Isi data pribadi dan pemeriksaan kesehatan pertama untuk lansia baru dengan lengkap dan akurat\n                </p>\n              </div>\n            </div>\n\n          <form onSubmit={handleSubmit} className=\"px-8 py-6 space-y-6\">\n            {/* Data Pribadi */}\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Data Pribadi</h3>\n              <div className=\"grid md:grid-cols-2 gap-4\">\n                <div>\n                  <label htmlFor=\"nama\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Nama Lengkap *\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"nama\"\n                    name=\"nama\"\n                    value={formData.nama}\n                    onChange={handleChange}\n                    className=\"input-field\"\n                    placeholder=\"Masukkan nama lengkap\"\n                    required\n                  />\n                </div>\n\n                <div>\n                  <label htmlFor=\"usia\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Usia *\n                  </label>\n                  <input\n                    type=\"number\"\n                    id=\"usia\"\n                    name=\"usia\"\n                    value={formData.usia}\n                    onChange={handleChange}\n                    className=\"input-field\"\n                    placeholder=\"Masukkan usia\"\n                    min=\"50\"\n                    max=\"120\"\n                    required\n                  />\n                </div>\n              </div>\n\n              <div className=\"mt-4\">\n                <label htmlFor=\"alamat\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Alamat Lengkap *\n                </label>\n                <textarea\n                  id=\"alamat\"\n                  name=\"alamat\"\n                  value={formData.alamat}\n                  onChange={handleChange}\n                  rows={3}\n                  className=\"textarea-field\"\n                  placeholder=\"Masukkan alamat lengkap\"\n                  required\n                />\n              </div>\n\n              <div className=\"mt-4\">\n                <label htmlFor=\"riwayat_medis\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Riwayat Medis\n                </label>\n                <textarea\n                  id=\"riwayat_medis\"\n                  name=\"riwayat_medis\"\n                  value={formData.riwayat_medis}\n                  onChange={handleChange}\n                  rows={3}\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\n                  placeholder=\"Masukkan riwayat penyakit atau kondisi medis (opsional)\"\n                />\n              </div>\n            </div>\n\n            {/* Pemeriksaan Kesehatan */}\n            <div className=\"border-t border-gray-200 pt-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Pemeriksaan Kesehatan Pertama</h3>\n              <div className=\"grid md:grid-cols-2 gap-4\">\n                <div>\n                  <label htmlFor=\"tekanan_darah\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Tekanan Darah *\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"tekanan_darah\"\n                    name=\"tekanan_darah\"\n                    value={formData.tekanan_darah}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\n                    placeholder=\"Contoh: 120/80\"\n                    pattern=\"[0-9]{2,3}/[0-9]{2,3}\"\n                    title=\"Format: sistol/diastol (contoh: 120/80)\"\n                    required\n                  />\n                </div>\n\n                <div>\n                  <label htmlFor=\"gula_darah\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Gula Darah (mg/dL) *\n                  </label>\n                  <input\n                    type=\"number\"\n                    id=\"gula_darah\"\n                    name=\"gula_darah\"\n                    value={formData.gula_darah}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\n                    placeholder=\"Masukkan kadar gula darah\"\n                    min=\"50\"\n                    max=\"500\"\n                    required\n                  />\n                </div>\n              </div>\n\n              <div className=\"mt-4\">\n                <label htmlFor=\"catatan\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Catatan Pemeriksaan\n                </label>\n                <textarea\n                  id=\"catatan\"\n                  name=\"catatan\"\n                  value={formData.catatan}\n                  onChange={handleChange}\n                  rows={3}\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\n                  placeholder=\"Catatan tambahan dari pemeriksaan (opsional)\"\n                />\n              </div>\n            </div>\n\n            {/* Submit Button */}\n            <div className=\"border-t border-gray-200 pt-6\">\n              <div className=\"flex flex-col sm:flex-row gap-4\">\n                <button\n                  type=\"submit\"\n                  disabled={isLoading}\n                  className=\"btn-primary flex-1 disabled:opacity-50 disabled:cursor-not-allowed\"\n                >\n                  {isLoading ? (\n                    <span className=\"flex items-center justify-center\">\n                      <div className=\"spinner mr-3\"></div>\n                      Menyimpan...\n                    </span>\n                  ) : (\n                    <span className=\"flex items-center justify-center\">\n                      <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                      </svg>\n                      Simpan Data\n                    </span>\n                  )}\n                </button>\n                <Link\n                  href=\"/\"\n                  className=\"btn-secondary flex-1 text-center\"\n                >\n                  Batal\n                </Link>\n              </div>\n            </div>\n          </form>\n        </div>\n\n        {/* Info Box */}\n        <div className=\"mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4\">\n          <div className=\"flex\">\n            <div className=\"flex-shrink-0\">\n              <svg className=\"h-5 w-5 text-blue-400\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\n              </svg>\n            </div>\n            <div className=\"ml-3\">\n              <h3 className=\"text-sm font-medium text-blue-800\">Informasi Penting</h3>\n              <div className=\"mt-2 text-sm text-blue-700\">\n                <ul className=\"list-disc list-inside space-y-1\">\n                  <li>Pastikan semua data yang dimasukkan sudah benar</li>\n                  <li>Setelah disimpan, Anda akan mendapatkan QR Code untuk lansia</li>\n                  <li>QR Code dapat dicetak atau disimpan untuk pemeriksaan selanjutnya</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAgBe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,MAAM;QACN,MAAM;QACN,QAAQ;QACR,eAAe;QACf,eAAe;QACf,YAAY;QACZ,SAAS;IACX;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sCAAsC;gBACjE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,IAAI,KAAK,EAAE,EAAE;gBAC1B,wDAAwD;gBACxD,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,KAAK,EAAE,EAAE;YACnC,OAAO;gBACL,MAAM,CAAC,sBAAsB,EAAE,KAAK,KAAK,IAAI,iBAAiB;YAChE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAO,WAAU;0BAChB,cAAA,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC,2RAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,6WAAC;4CAAI,WAAU;sDACb,cAAA,6WAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC5E,cAAA,6WAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6WAAC;;8DACC,6WAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,6WAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;0CAI3C,6WAAC,2RAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,6WAAC;gBAAK,WAAU;0BACd,cAAA,6WAAC;oBAAI,WAAU;;sCAEb,6WAAC;4BAAI,WAAU;sCACb,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;0DAAiG;;;;;;0DAChH,6WAAC;gDAAK,WAAU;0DAAyC;;;;;;;;;;;;kDAE3D,6WAAC;wCAAI,WAAU;;;;;;kDACf,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;0DAAiG;;;;;;0DAChH,6WAAC;gDAAK,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;;;;;;sCAK/D,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAI,WAAU;8CACb,cAAA,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC/E,cAAA,6WAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,6WAAC;gDAAG,WAAU;0DAAwC;;;;;;0DACtD,6WAAC;gDAAE,WAAU;0DAAiC;;;;;;;;;;;;;;;;;8CAMpD,6WAAC;oCAAK,UAAU;oCAAc,WAAU;;sDAEtC,6WAAC;;8DACC,6WAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;;8EACC,6WAAC;oEAAM,SAAQ;oEAAO,WAAU;8EAA+C;;;;;;8EAG/E,6WAAC;oEACC,MAAK;oEACL,IAAG;oEACH,MAAK;oEACL,OAAO,SAAS,IAAI;oEACpB,UAAU;oEACV,WAAU;oEACV,aAAY;oEACZ,QAAQ;;;;;;;;;;;;sEAIZ,6WAAC;;8EACC,6WAAC;oEAAM,SAAQ;oEAAO,WAAU;8EAA+C;;;;;;8EAG/E,6WAAC;oEACC,MAAK;oEACL,IAAG;oEACH,MAAK;oEACL,OAAO,SAAS,IAAI;oEACpB,UAAU;oEACV,WAAU;oEACV,aAAY;oEACZ,KAAI;oEACJ,KAAI;oEACJ,QAAQ;;;;;;;;;;;;;;;;;;8DAKd,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;4DAAM,SAAQ;4DAAS,WAAU;sEAA+C;;;;;;sEAGjF,6WAAC;4DACC,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,MAAM;4DACtB,UAAU;4DACV,MAAM;4DACN,WAAU;4DACV,aAAY;4DACZ,QAAQ;;;;;;;;;;;;8DAIZ,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;4DAAM,SAAQ;4DAAgB,WAAU;sEAA+C;;;;;;sEAGxF,6WAAC;4DACC,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,aAAa;4DAC7B,UAAU;4DACV,MAAM;4DACN,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;sDAMlB,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;;8EACC,6WAAC;oEAAM,SAAQ;oEAAgB,WAAU;8EAA+C;;;;;;8EAGxF,6WAAC;oEACC,MAAK;oEACL,IAAG;oEACH,MAAK;oEACL,OAAO,SAAS,aAAa;oEAC7B,UAAU;oEACV,WAAU;oEACV,aAAY;oEACZ,SAAQ;oEACR,OAAM;oEACN,QAAQ;;;;;;;;;;;;sEAIZ,6WAAC;;8EACC,6WAAC;oEAAM,SAAQ;oEAAa,WAAU;8EAA+C;;;;;;8EAGrF,6WAAC;oEACC,MAAK;oEACL,IAAG;oEACH,MAAK;oEACL,OAAO,SAAS,UAAU;oEAC1B,UAAU;oEACV,WAAU;oEACV,aAAY;oEACZ,KAAI;oEACJ,KAAI;oEACJ,QAAQ;;;;;;;;;;;;;;;;;;8DAKd,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;4DAAM,SAAQ;4DAAU,WAAU;sEAA+C;;;;;;sEAGlF,6WAAC;4DACC,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,OAAO;4DACvB,UAAU;4DACV,MAAM;4DACN,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;sDAMlB,6WAAC;4CAAI,WAAU;sDACb,cAAA,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDACC,MAAK;wDACL,UAAU;wDACV,WAAU;kEAET,0BACC,6WAAC;4DAAK,WAAU;;8EACd,6WAAC;oEAAI,WAAU;;;;;;gEAAqB;;;;;;iFAItC,6WAAC;4DAAK,WAAU;;8EACd,6WAAC;oEAAI,WAAU;oEAAe,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EACtE,cAAA,6WAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;gEACjE;;;;;;;;;;;;kEAKZ,6WAAC,2RAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAST,6WAAC;4BAAI,WAAU;sCACb,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC;4CAAI,WAAU;4CAAwB,OAAM;4CAA6B,SAAQ;4CAAY,MAAK;sDACjG,cAAA,6WAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAmI,UAAS;;;;;;;;;;;;;;;;kDAG3K,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC;oDAAG,WAAU;;sEACZ,6WAAC;sEAAG;;;;;;sEACJ,6WAAC;sEAAG;;;;;;sEACJ,6WAAC;sEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUtB", "debugId": null}}]}