{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/browser/HTMLCanvasElementLuminanceSource.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport InvertedLuminanceSource from '../core/InvertedLuminanceSource';\nimport LuminanceSource from '../core/LuminanceSource';\nimport IllegalArgumentException from '../core/IllegalArgumentException';\n/**\n * @deprecated Moving to @zxing/browser\n */\nvar HTMLCanvasElementLuminanceSource = /** @class */ (function (_super) {\n    __extends(HTMLCanvasElementLuminanceSource, _super);\n    function HTMLCanvasElementLuminanceSource(canvas, doAutoInvert) {\n        if (doAutoInvert === void 0) { doAutoInvert = false; }\n        var _this = _super.call(this, canvas.width, canvas.height) || this;\n        _this.canvas = canvas;\n        _this.tempCanvasElement = null;\n        _this.buffer = HTMLCanvasElementLuminanceSource.makeBufferFromCanvasImageData(canvas, doAutoInvert);\n        return _this;\n    }\n    HTMLCanvasElementLuminanceSource.makeBufferFromCanvasImageData = function (canvas, doAutoInvert) {\n        if (doAutoInvert === void 0) { doAutoInvert = false; }\n        var imageData = canvas.getContext('2d').getImageData(0, 0, canvas.width, canvas.height);\n        return HTMLCanvasElementLuminanceSource.toGrayscaleBuffer(imageData.data, canvas.width, canvas.height, doAutoInvert);\n    };\n    HTMLCanvasElementLuminanceSource.toGrayscaleBuffer = function (imageBuffer, width, height, doAutoInvert) {\n        if (doAutoInvert === void 0) { doAutoInvert = false; }\n        var grayscaleBuffer = new Uint8ClampedArray(width * height);\n        HTMLCanvasElementLuminanceSource.FRAME_INDEX = !HTMLCanvasElementLuminanceSource.FRAME_INDEX;\n        if (HTMLCanvasElementLuminanceSource.FRAME_INDEX || !doAutoInvert) {\n            for (var i = 0, j = 0, length_1 = imageBuffer.length; i < length_1; i += 4, j++) {\n                var gray = void 0;\n                var alpha = imageBuffer[i + 3];\n                // The color of fully-transparent pixels is irrelevant. They are often, technically, fully-transparent\n                // black (0 alpha, and then 0 RGB). They are often used, of course as the \"white\" area in a\n                // barcode image. Force any such pixel to be white:\n                if (alpha === 0) {\n                    gray = 0xFF;\n                }\n                else {\n                    var pixelR = imageBuffer[i];\n                    var pixelG = imageBuffer[i + 1];\n                    var pixelB = imageBuffer[i + 2];\n                    // .299R + 0.587G + 0.114B (YUV/YIQ for PAL and NTSC),\n                    // (306*R) >> 10 is approximately equal to R*0.299, and so on.\n                    // 0x200 >> 10 is 0.5, it implements rounding.\n                    gray = (306 * pixelR +\n                        601 * pixelG +\n                        117 * pixelB +\n                        0x200) >> 10;\n                }\n                grayscaleBuffer[j] = gray;\n            }\n        }\n        else {\n            for (var i = 0, j = 0, length_2 = imageBuffer.length; i < length_2; i += 4, j++) {\n                var gray = void 0;\n                var alpha = imageBuffer[i + 3];\n                // The color of fully-transparent pixels is irrelevant. They are often, technically, fully-transparent\n                // black (0 alpha, and then 0 RGB). They are often used, of course as the \"white\" area in a\n                // barcode image. Force any such pixel to be white:\n                if (alpha === 0) {\n                    gray = 0xFF;\n                }\n                else {\n                    var pixelR = imageBuffer[i];\n                    var pixelG = imageBuffer[i + 1];\n                    var pixelB = imageBuffer[i + 2];\n                    // .299R + 0.587G + 0.114B (YUV/YIQ for PAL and NTSC),\n                    // (306*R) >> 10 is approximately equal to R*0.299, and so on.\n                    // 0x200 >> 10 is 0.5, it implements rounding.\n                    gray = (306 * pixelR +\n                        601 * pixelG +\n                        117 * pixelB +\n                        0x200) >> 10;\n                }\n                grayscaleBuffer[j] = 0xFF - gray;\n            }\n        }\n        return grayscaleBuffer;\n    };\n    HTMLCanvasElementLuminanceSource.prototype.getRow = function (y /*int*/, row) {\n        if (y < 0 || y >= this.getHeight()) {\n            throw new IllegalArgumentException('Requested row is outside the image: ' + y);\n        }\n        var width = this.getWidth();\n        var start = y * width;\n        if (row === null) {\n            row = this.buffer.slice(start, start + width);\n        }\n        else {\n            if (row.length < width) {\n                row = new Uint8ClampedArray(width);\n            }\n            // The underlying raster of image consists of bytes with the luminance values\n            // TODO: can avoid set/slice?\n            row.set(this.buffer.slice(start, start + width));\n        }\n        return row;\n    };\n    HTMLCanvasElementLuminanceSource.prototype.getMatrix = function () {\n        return this.buffer;\n    };\n    HTMLCanvasElementLuminanceSource.prototype.isCropSupported = function () {\n        return true;\n    };\n    HTMLCanvasElementLuminanceSource.prototype.crop = function (left /*int*/, top /*int*/, width /*int*/, height /*int*/) {\n        _super.prototype.crop.call(this, left, top, width, height);\n        return this;\n    };\n    /**\n     * This is always true, since the image is a gray-scale image.\n     *\n     * @return true\n     */\n    HTMLCanvasElementLuminanceSource.prototype.isRotateSupported = function () {\n        return true;\n    };\n    HTMLCanvasElementLuminanceSource.prototype.rotateCounterClockwise = function () {\n        this.rotate(-90);\n        return this;\n    };\n    HTMLCanvasElementLuminanceSource.prototype.rotateCounterClockwise45 = function () {\n        this.rotate(-45);\n        return this;\n    };\n    HTMLCanvasElementLuminanceSource.prototype.getTempCanvasElement = function () {\n        if (null === this.tempCanvasElement) {\n            var tempCanvasElement = this.canvas.ownerDocument.createElement('canvas');\n            tempCanvasElement.width = this.canvas.width;\n            tempCanvasElement.height = this.canvas.height;\n            this.tempCanvasElement = tempCanvasElement;\n        }\n        return this.tempCanvasElement;\n    };\n    HTMLCanvasElementLuminanceSource.prototype.rotate = function (angle) {\n        var tempCanvasElement = this.getTempCanvasElement();\n        var tempContext = tempCanvasElement.getContext('2d');\n        var angleRadians = angle * HTMLCanvasElementLuminanceSource.DEGREE_TO_RADIANS;\n        // Calculate and set new dimensions for temp canvas\n        var width = this.canvas.width;\n        var height = this.canvas.height;\n        var newWidth = Math.ceil(Math.abs(Math.cos(angleRadians)) * width + Math.abs(Math.sin(angleRadians)) * height);\n        var newHeight = Math.ceil(Math.abs(Math.sin(angleRadians)) * width + Math.abs(Math.cos(angleRadians)) * height);\n        tempCanvasElement.width = newWidth;\n        tempCanvasElement.height = newHeight;\n        // Draw at center of temp canvas to prevent clipping of image data\n        tempContext.translate(newWidth / 2, newHeight / 2);\n        tempContext.rotate(angleRadians);\n        tempContext.drawImage(this.canvas, width / -2, height / -2);\n        this.buffer = HTMLCanvasElementLuminanceSource.makeBufferFromCanvasImageData(tempCanvasElement);\n        return this;\n    };\n    HTMLCanvasElementLuminanceSource.prototype.invert = function () {\n        return new InvertedLuminanceSource(this);\n    };\n    HTMLCanvasElementLuminanceSource.DEGREE_TO_RADIANS = Math.PI / 180;\n    HTMLCanvasElementLuminanceSource.FRAME_INDEX = true;\n    return HTMLCanvasElementLuminanceSource;\n}(LuminanceSource));\nexport { HTMLCanvasElementLuminanceSource };\n"], "names": [], "mappings": ";;;AAaA;AACA;AACA;AAfA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;;AAIA;;CAEC,GACD,IAAI,mCAAkD,SAAU,MAAM;IAClE,UAAU,kCAAkC;IAC5C,SAAS,iCAAiC,MAAM,EAAE,YAAY;QAC1D,IAAI,iBAAiB,KAAK,GAAG;YAAE,eAAe;QAAO;QACrD,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,OAAO,KAAK,EAAE,OAAO,MAAM,KAAK,IAAI;QAClE,MAAM,MAAM,GAAG;QACf,MAAM,iBAAiB,GAAG;QAC1B,MAAM,MAAM,GAAG,iCAAiC,6BAA6B,CAAC,QAAQ;QACtF,OAAO;IACX;IACA,iCAAiC,6BAA6B,GAAG,SAAU,MAAM,EAAE,YAAY;QAC3F,IAAI,iBAAiB,KAAK,GAAG;YAAE,eAAe;QAAO;QACrD,IAAI,YAAY,OAAO,UAAU,CAAC,MAAM,YAAY,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;QACtF,OAAO,iCAAiC,iBAAiB,CAAC,UAAU,IAAI,EAAE,OAAO,KAAK,EAAE,OAAO,MAAM,EAAE;IAC3G;IACA,iCAAiC,iBAAiB,GAAG,SAAU,WAAW,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY;QACnG,IAAI,iBAAiB,KAAK,GAAG;YAAE,eAAe;QAAO;QACrD,IAAI,kBAAkB,IAAI,kBAAkB,QAAQ;QACpD,iCAAiC,WAAW,GAAG,CAAC,iCAAiC,WAAW;QAC5F,IAAI,iCAAiC,WAAW,IAAI,CAAC,cAAc;YAC/D,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,WAAW,YAAY,MAAM,EAAE,IAAI,UAAU,KAAK,GAAG,IAAK;gBAC7E,IAAI,OAAO,KAAK;gBAChB,IAAI,QAAQ,WAAW,CAAC,IAAI,EAAE;gBAC9B,sGAAsG;gBACtG,2FAA2F;gBAC3F,mDAAmD;gBACnD,IAAI,UAAU,GAAG;oBACb,OAAO;gBACX,OACK;oBACD,IAAI,SAAS,WAAW,CAAC,EAAE;oBAC3B,IAAI,SAAS,WAAW,CAAC,IAAI,EAAE;oBAC/B,IAAI,SAAS,WAAW,CAAC,IAAI,EAAE;oBAC/B,sDAAsD;oBACtD,8DAA8D;oBAC9D,8CAA8C;oBAC9C,OAAO,AAAC,MAAM,SACV,MAAM,SACN,MAAM,SACN,SAAU;gBAClB;gBACA,eAAe,CAAC,EAAE,GAAG;YACzB;QACJ,OACK;YACD,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,WAAW,YAAY,MAAM,EAAE,IAAI,UAAU,KAAK,GAAG,IAAK;gBAC7E,IAAI,OAAO,KAAK;gBAChB,IAAI,QAAQ,WAAW,CAAC,IAAI,EAAE;gBAC9B,sGAAsG;gBACtG,2FAA2F;gBAC3F,mDAAmD;gBACnD,IAAI,UAAU,GAAG;oBACb,OAAO;gBACX,OACK;oBACD,IAAI,SAAS,WAAW,CAAC,EAAE;oBAC3B,IAAI,SAAS,WAAW,CAAC,IAAI,EAAE;oBAC/B,IAAI,SAAS,WAAW,CAAC,IAAI,EAAE;oBAC/B,sDAAsD;oBACtD,8DAA8D;oBAC9D,8CAA8C;oBAC9C,OAAO,AAAC,MAAM,SACV,MAAM,SACN,MAAM,SACN,SAAU;gBAClB;gBACA,eAAe,CAAC,EAAE,GAAG,OAAO;YAChC;QACJ;QACA,OAAO;IACX;IACA,iCAAiC,SAAS,CAAC,MAAM,GAAG,SAAU,EAAE,KAAK,GAAN,EAAU,GAAG;QACxE,IAAI,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,IAAI;YAChC,MAAM,IAAI,gPAAA,CAAA,UAAwB,CAAC,yCAAyC;QAChF;QACA,IAAI,QAAQ,IAAI,CAAC,QAAQ;QACzB,IAAI,QAAQ,IAAI;QAChB,IAAI,QAAQ,MAAM;YACd,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,QAAQ;QAC3C,OACK;YACD,IAAI,IAAI,MAAM,GAAG,OAAO;gBACpB,MAAM,IAAI,kBAAkB;YAChC;YACA,6EAA6E;YAC7E,6BAA6B;YAC7B,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,QAAQ;QAC7C;QACA,OAAO;IACX;IACA,iCAAiC,SAAS,CAAC,SAAS,GAAG;QACnD,OAAO,IAAI,CAAC,MAAM;IACtB;IACA,iCAAiC,SAAS,CAAC,eAAe,GAAG;QACzD,OAAO;IACX;IACA,iCAAiC,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,KAAK,GAAN,EAAU,IAAI,KAAK,GAAN,EAAU,MAAM,KAAK,GAAN,EAAU,OAAO,KAAK,GAAN;QACxG,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,KAAK,OAAO;QACnD,OAAO,IAAI;IACf;IACA;;;;KAIC,GACD,iCAAiC,SAAS,CAAC,iBAAiB,GAAG;QAC3D,OAAO;IACX;IACA,iCAAiC,SAAS,CAAC,sBAAsB,GAAG;QAChE,IAAI,CAAC,MAAM,CAAC,CAAC;QACb,OAAO,IAAI;IACf;IACA,iCAAiC,SAAS,CAAC,wBAAwB,GAAG;QAClE,IAAI,CAAC,MAAM,CAAC,CAAC;QACb,OAAO,IAAI;IACf;IACA,iCAAiC,SAAS,CAAC,oBAAoB,GAAG;QAC9D,IAAI,SAAS,IAAI,CAAC,iBAAiB,EAAE;YACjC,IAAI,oBAAoB,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC;YAChE,kBAAkB,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK;YAC3C,kBAAkB,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM;YAC7C,IAAI,CAAC,iBAAiB,GAAG;QAC7B;QACA,OAAO,IAAI,CAAC,iBAAiB;IACjC;IACA,iCAAiC,SAAS,CAAC,MAAM,GAAG,SAAU,KAAK;QAC/D,IAAI,oBAAoB,IAAI,CAAC,oBAAoB;QACjD,IAAI,cAAc,kBAAkB,UAAU,CAAC;QAC/C,IAAI,eAAe,QAAQ,iCAAiC,iBAAiB;QAC7E,mDAAmD;QACnD,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK;QAC7B,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC,MAAM;QAC/B,IAAI,WAAW,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,iBAAiB,QAAQ,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,iBAAiB;QACvG,IAAI,YAAY,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,iBAAiB,QAAQ,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,iBAAiB;QACxG,kBAAkB,KAAK,GAAG;QAC1B,kBAAkB,MAAM,GAAG;QAC3B,kEAAkE;QAClE,YAAY,SAAS,CAAC,WAAW,GAAG,YAAY;QAChD,YAAY,MAAM,CAAC;QACnB,YAAY,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,GAAG,SAAS,CAAC;QACzD,IAAI,CAAC,MAAM,GAAG,iCAAiC,6BAA6B,CAAC;QAC7E,OAAO,IAAI;IACf;IACA,iCAAiC,SAAS,CAAC,MAAM,GAAG;QAChD,OAAO,IAAI,+OAAA,CAAA,UAAuB,CAAC,IAAI;IAC3C;IACA,iCAAiC,iBAAiB,GAAG,KAAK,EAAE,GAAG;IAC/D,iCAAiC,WAAW,GAAG;IAC/C,OAAO;AACX,EAAE,uOAAA,CAAA,UAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/browser/VideoInputDevice.js"], "sourcesContent": ["/**\n * @deprecated Moving to @zxing/browser\n *\n * Video input device metadata containing the id and label of the device if available.\n */\nvar VideoInputDevice = /** @class */ (function () {\n    /**\n     * Creates an instance of VideoInputDevice.\n     *\n     * @param {string} deviceId the video input device id\n     * @param {string} label the label of the device if available\n     */\n    function VideoInputDevice(deviceId, label, groupId) {\n        this.deviceId = deviceId;\n        this.label = label;\n        /** @inheritdoc */\n        this.kind = 'videoinput';\n        this.groupId = groupId || undefined;\n    }\n    /** @inheritdoc */\n    VideoInputDevice.prototype.toJSON = function () {\n        return {\n            kind: this.kind,\n            groupId: this.groupId,\n            deviceId: this.deviceId,\n            label: this.label,\n        };\n    };\n    return VideoInputDevice;\n}());\nexport { VideoInputDevice };\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AACD,IAAI,mBAAkC;IAClC;;;;;KAKC,GACD,SAAS,iBAAiB,QAAQ,EAAE,KAAK,EAAE,OAAO;QAC9C,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,KAAK,GAAG;QACb,gBAAgB,GAChB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,OAAO,GAAG,WAAW;IAC9B;IACA,gBAAgB,GAChB,iBAAiB,SAAS,CAAC,MAAM,GAAG;QAChC,OAAO;YACH,MAAM,IAAI,CAAC,IAAI;YACf,SAAS,IAAI,CAAC,OAAO;YACrB,UAAU,IAAI,CAAC,QAAQ;YACvB,OAAO,IAAI,CAAC,KAAK;QACrB;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/browser/BrowserCodeReader.js"], "sourcesContent": ["var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (_) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport ArgumentException from '../core/ArgumentException';\nimport BinaryBitmap from '../core/BinaryBitmap';\nimport ChecksumException from '../core/ChecksumException';\nimport HybridBinarizer from '../core/common/HybridBinarizer';\nimport FormatException from '../core/FormatException';\nimport NotFoundException from '../core/NotFoundException';\nimport { HTMLCanvasElementLuminanceSource } from './HTMLCanvasElementLuminanceSource';\nimport { VideoInputDevice } from './VideoInputDevice';\n/**\n * @deprecated Moving to @zxing/browser\n *\n * Base class for browser code reader.\n */\nvar BrowserCodeReader = /** @class */ (function () {\n    /**\n     * Creates an instance of BrowserCodeReader.\n     * @param {Reader} reader The reader instance to decode the barcode\n     * @param {number} [timeBetweenScansMillis=500] the time delay between subsequent successful decode tries\n     *\n     * @memberOf BrowserCodeReader\n     */\n    function BrowserCodeReader(reader, timeBetweenScansMillis, _hints) {\n        if (timeBetweenScansMillis === void 0) { timeBetweenScansMillis = 500; }\n        this.reader = reader;\n        this.timeBetweenScansMillis = timeBetweenScansMillis;\n        this._hints = _hints;\n        /**\n         * This will break the loop.\n         */\n        this._stopContinuousDecode = false;\n        /**\n         * This will break the loop.\n         */\n        this._stopAsyncDecode = false;\n        /**\n         * Delay time between decode attempts made by the scanner.\n         */\n        this._timeBetweenDecodingAttempts = 0;\n    }\n    Object.defineProperty(BrowserCodeReader.prototype, \"hasNavigator\", {\n        /**\n         * If navigator is present.\n         */\n        get: function () {\n            return typeof navigator !== 'undefined';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(BrowserCodeReader.prototype, \"isMediaDevicesSuported\", {\n        /**\n         * If mediaDevices under navigator is supported.\n         */\n        get: function () {\n            return this.hasNavigator && !!navigator.mediaDevices;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(BrowserCodeReader.prototype, \"canEnumerateDevices\", {\n        /**\n         * If enumerateDevices under navigator is supported.\n         */\n        get: function () {\n            return !!(this.isMediaDevicesSuported && navigator.mediaDevices.enumerateDevices);\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(BrowserCodeReader.prototype, \"timeBetweenDecodingAttempts\", {\n        /** Time between two decoding tries in milli seconds. */\n        get: function () {\n            return this._timeBetweenDecodingAttempts;\n        },\n        /**\n         * Change the time span the decoder waits between two decoding tries.\n         *\n         * @param {number} millis Time between two decoding tries in milli seconds.\n         */\n        set: function (millis) {\n            this._timeBetweenDecodingAttempts = millis < 0 ? 0 : millis;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(BrowserCodeReader.prototype, \"hints\", {\n        /**\n         * Sets the hints.\n         */\n        get: function () {\n            return this._hints;\n        },\n        /**\n         * Sets the hints.\n         */\n        set: function (hints) {\n            this._hints = hints || null;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * Lists all the available video input devices.\n     */\n    BrowserCodeReader.prototype.listVideoInputDevices = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            var devices, videoDevices, devices_1, devices_1_1, device, kind, deviceId, label, groupId, videoDevice;\n            var e_1, _a;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        if (!this.hasNavigator) {\n                            throw new Error(\"Can't enumerate devices, navigator is not present.\");\n                        }\n                        if (!this.canEnumerateDevices) {\n                            throw new Error(\"Can't enumerate devices, method not supported.\");\n                        }\n                        return [4 /*yield*/, navigator.mediaDevices.enumerateDevices()];\n                    case 1:\n                        devices = _b.sent();\n                        videoDevices = [];\n                        try {\n                            for (devices_1 = __values(devices), devices_1_1 = devices_1.next(); !devices_1_1.done; devices_1_1 = devices_1.next()) {\n                                device = devices_1_1.value;\n                                kind = device.kind === 'video' ? 'videoinput' : device.kind;\n                                if (kind !== 'videoinput') {\n                                    continue;\n                                }\n                                deviceId = device.deviceId || device.id;\n                                label = device.label || \"Video device \" + (videoDevices.length + 1);\n                                groupId = device.groupId;\n                                videoDevice = { deviceId: deviceId, label: label, kind: kind, groupId: groupId };\n                                videoDevices.push(videoDevice);\n                            }\n                        }\n                        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n                        finally {\n                            try {\n                                if (devices_1_1 && !devices_1_1.done && (_a = devices_1.return)) _a.call(devices_1);\n                            }\n                            finally { if (e_1) throw e_1.error; }\n                        }\n                        return [2 /*return*/, videoDevices];\n                }\n            });\n        });\n    };\n    /**\n     * Obtain the list of available devices with type 'videoinput'.\n     *\n     * @returns {Promise<VideoInputDevice[]>} an array of available video input devices\n     *\n     * @memberOf BrowserCodeReader\n     *\n     * @deprecated Use `listVideoInputDevices` instead.\n     */\n    BrowserCodeReader.prototype.getVideoInputDevices = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            var devices;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, this.listVideoInputDevices()];\n                    case 1:\n                        devices = _a.sent();\n                        return [2 /*return*/, devices.map(function (d) { return new VideoInputDevice(d.deviceId, d.label); })];\n                }\n            });\n        });\n    };\n    /**\n     * Let's you find a device using it's Id.\n     */\n    BrowserCodeReader.prototype.findDeviceById = function (deviceId) {\n        return __awaiter(this, void 0, void 0, function () {\n            var devices;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, this.listVideoInputDevices()];\n                    case 1:\n                        devices = _a.sent();\n                        if (!devices) {\n                            return [2 /*return*/, null];\n                        }\n                        return [2 /*return*/, devices.find(function (x) { return x.deviceId === deviceId; })];\n                }\n            });\n        });\n    };\n    /**\n     * Decodes the barcode from the device specified by deviceId while showing the video in the specified video element.\n     *\n     * @param deviceId the id of one of the devices obtained after calling getVideoInputDevices. Can be undefined, in this case it will decode from one of the available devices, preffering the main camera (environment facing) if available.\n     * @param video the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.\n     * @returns The decoding result.\n     *\n     * @memberOf BrowserCodeReader\n     *\n     * @deprecated Use `decodeOnceFromVideoDevice` instead.\n     */\n    BrowserCodeReader.prototype.decodeFromInputVideoDevice = function (deviceId, videoSource) {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, this.decodeOnceFromVideoDevice(deviceId, videoSource)];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    /**\n     * In one attempt, tries to decode the barcode from the device specified by deviceId while showing the video in the specified video element.\n     *\n     * @param deviceId the id of one of the devices obtained after calling getVideoInputDevices. Can be undefined, in this case it will decode from one of the available devices, preffering the main camera (environment facing) if available.\n     * @param video the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.\n     * @returns The decoding result.\n     *\n     * @memberOf BrowserCodeReader\n     */\n    BrowserCodeReader.prototype.decodeOnceFromVideoDevice = function (deviceId, videoSource) {\n        return __awaiter(this, void 0, void 0, function () {\n            var videoConstraints, constraints;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        this.reset();\n                        if (!deviceId) {\n                            videoConstraints = { facingMode: 'environment' };\n                        }\n                        else {\n                            videoConstraints = { deviceId: { exact: deviceId } };\n                        }\n                        constraints = { video: videoConstraints };\n                        return [4 /*yield*/, this.decodeOnceFromConstraints(constraints, videoSource)];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    /**\n     * In one attempt, tries to decode the barcode from a stream obtained from the given constraints while showing the video in the specified video element.\n     *\n     * @param constraints the media stream constraints to get s valid media stream to decode from\n     * @param video the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.\n     * @returns The decoding result.\n     *\n     * @memberOf BrowserCodeReader\n     */\n    BrowserCodeReader.prototype.decodeOnceFromConstraints = function (constraints, videoSource) {\n        return __awaiter(this, void 0, void 0, function () {\n            var stream;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, navigator.mediaDevices.getUserMedia(constraints)];\n                    case 1:\n                        stream = _a.sent();\n                        return [4 /*yield*/, this.decodeOnceFromStream(stream, videoSource)];\n                    case 2: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    /**\n     * In one attempt, tries to decode the barcode from a stream obtained from the given constraints while showing the video in the specified video element.\n     *\n     * @param {MediaStream} [constraints] the media stream constraints to get s valid media stream to decode from\n     * @param {string|HTMLVideoElement} [video] the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.\n     * @returns {Promise<Result>} The decoding result.\n     *\n     * @memberOf BrowserCodeReader\n     */\n    BrowserCodeReader.prototype.decodeOnceFromStream = function (stream, videoSource) {\n        return __awaiter(this, void 0, void 0, function () {\n            var video, result;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        this.reset();\n                        return [4 /*yield*/, this.attachStreamToVideo(stream, videoSource)];\n                    case 1:\n                        video = _a.sent();\n                        return [4 /*yield*/, this.decodeOnce(video)];\n                    case 2:\n                        result = _a.sent();\n                        return [2 /*return*/, result];\n                }\n            });\n        });\n    };\n    /**\n     * Continuously decodes the barcode from the device specified by device while showing the video in the specified video element.\n     *\n     * @param {string|null} [deviceId] the id of one of the devices obtained after calling getVideoInputDevices. Can be undefined, in this case it will decode from one of the available devices, preffering the main camera (environment facing) if available.\n     * @param {string|HTMLVideoElement|null} [video] the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.\n     * @returns {Promise<void>}\n     *\n     * @memberOf BrowserCodeReader\n     *\n     * @deprecated Use `decodeFromVideoDevice` instead.\n     */\n    BrowserCodeReader.prototype.decodeFromInputVideoDeviceContinuously = function (deviceId, videoSource, callbackFn) {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, this.decodeFromVideoDevice(deviceId, videoSource, callbackFn)];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    /**\n     * Continuously tries to decode the barcode from the device specified by device while showing the video in the specified video element.\n     *\n     * @param {string|null} [deviceId] the id of one of the devices obtained after calling getVideoInputDevices. Can be undefined, in this case it will decode from one of the available devices, preffering the main camera (environment facing) if available.\n     * @param {string|HTMLVideoElement|null} [video] the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.\n     * @returns {Promise<void>}\n     *\n     * @memberOf BrowserCodeReader\n     */\n    BrowserCodeReader.prototype.decodeFromVideoDevice = function (deviceId, videoSource, callbackFn) {\n        return __awaiter(this, void 0, void 0, function () {\n            var videoConstraints, constraints;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!deviceId) {\n                            videoConstraints = { facingMode: 'environment' };\n                        }\n                        else {\n                            videoConstraints = { deviceId: { exact: deviceId } };\n                        }\n                        constraints = { video: videoConstraints };\n                        return [4 /*yield*/, this.decodeFromConstraints(constraints, videoSource, callbackFn)];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    /**\n     * Continuously tries to decode the barcode from a stream obtained from the given constraints while showing the video in the specified video element.\n     *\n     * @param {MediaStream} [constraints] the media stream constraints to get s valid media stream to decode from\n     * @param {string|HTMLVideoElement} [video] the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.\n     * @returns {Promise<Result>} The decoding result.\n     *\n     * @memberOf BrowserCodeReader\n     */\n    BrowserCodeReader.prototype.decodeFromConstraints = function (constraints, videoSource, callbackFn) {\n        return __awaiter(this, void 0, void 0, function () {\n            var stream;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, navigator.mediaDevices.getUserMedia(constraints)];\n                    case 1:\n                        stream = _a.sent();\n                        return [4 /*yield*/, this.decodeFromStream(stream, videoSource, callbackFn)];\n                    case 2: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    /**\n     * In one attempt, tries to decode the barcode from a stream obtained from the given constraints while showing the video in the specified video element.\n     *\n     * @param {MediaStream} [constraints] the media stream constraints to get s valid media stream to decode from\n     * @param {string|HTMLVideoElement} [video] the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.\n     * @returns {Promise<Result>} The decoding result.\n     *\n     * @memberOf BrowserCodeReader\n     */\n    BrowserCodeReader.prototype.decodeFromStream = function (stream, videoSource, callbackFn) {\n        return __awaiter(this, void 0, void 0, function () {\n            var video;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        this.reset();\n                        return [4 /*yield*/, this.attachStreamToVideo(stream, videoSource)];\n                    case 1:\n                        video = _a.sent();\n                        return [4 /*yield*/, this.decodeContinuously(video, callbackFn)];\n                    case 2: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    /**\n     * Breaks the decoding loop.\n     */\n    BrowserCodeReader.prototype.stopAsyncDecode = function () {\n        this._stopAsyncDecode = true;\n    };\n    /**\n     * Breaks the decoding loop.\n     */\n    BrowserCodeReader.prototype.stopContinuousDecode = function () {\n        this._stopContinuousDecode = true;\n    };\n    /**\n     * Sets the new stream and request a new decoding-with-delay.\n     *\n     * @param stream The stream to be shown in the video element.\n     * @param decodeFn A callback for the decode method.\n     */\n    BrowserCodeReader.prototype.attachStreamToVideo = function (stream, videoSource) {\n        return __awaiter(this, void 0, void 0, function () {\n            var videoElement;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        videoElement = this.prepareVideoElement(videoSource);\n                        this.addVideoSource(videoElement, stream);\n                        this.videoElement = videoElement;\n                        this.stream = stream;\n                        return [4 /*yield*/, this.playVideoOnLoadAsync(videoElement)];\n                    case 1:\n                        _a.sent();\n                        return [2 /*return*/, videoElement];\n                }\n            });\n        });\n    };\n    /**\n     *\n     * @param videoElement\n     */\n    BrowserCodeReader.prototype.playVideoOnLoadAsync = function (videoElement) {\n        var _this = this;\n        return new Promise(function (resolve, reject) {\n            return _this.playVideoOnLoad(videoElement, function () { return resolve(); });\n        });\n    };\n    /**\n     * Binds listeners and callbacks to the videoElement.\n     *\n     * @param element\n     * @param callbackFn\n     */\n    BrowserCodeReader.prototype.playVideoOnLoad = function (element, callbackFn) {\n        var _this = this;\n        this.videoEndedListener = function () { return _this.stopStreams(); };\n        this.videoCanPlayListener = function () { return _this.tryPlayVideo(element); };\n        element.addEventListener('ended', this.videoEndedListener);\n        element.addEventListener('canplay', this.videoCanPlayListener);\n        element.addEventListener('playing', callbackFn);\n        // if canplay was already fired, we won't know when to play, so just give it a try\n        this.tryPlayVideo(element);\n    };\n    /**\n     * Checks if the given video element is currently playing.\n     */\n    BrowserCodeReader.prototype.isVideoPlaying = function (video) {\n        return (video.currentTime > 0 &&\n            !video.paused &&\n            !video.ended &&\n            video.readyState > 2);\n    };\n    /**\n     * Just tries to play the video and logs any errors.\n     * The play call is only made is the video is not already playing.\n     */\n    BrowserCodeReader.prototype.tryPlayVideo = function (videoElement) {\n        return __awaiter(this, void 0, void 0, function () {\n            var _a;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        if (this.isVideoPlaying(videoElement)) {\n                            console.warn('Trying to play video that is already playing.');\n                            return [2 /*return*/];\n                        }\n                        _b.label = 1;\n                    case 1:\n                        _b.trys.push([1, 3, , 4]);\n                        return [4 /*yield*/, videoElement.play()];\n                    case 2:\n                        _b.sent();\n                        return [3 /*break*/, 4];\n                    case 3:\n                        _a = _b.sent();\n                        console.warn('It was not possible to play the video.');\n                        return [3 /*break*/, 4];\n                    case 4: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    /**\n     * Searches and validates a media element.\n     */\n    BrowserCodeReader.prototype.getMediaElement = function (mediaElementId, type) {\n        var mediaElement = document.getElementById(mediaElementId);\n        if (!mediaElement) {\n            throw new ArgumentException(\"element with id '\" + mediaElementId + \"' not found\");\n        }\n        if (mediaElement.nodeName.toLowerCase() !== type.toLowerCase()) {\n            throw new ArgumentException(\"element with id '\" + mediaElementId + \"' must be an \" + type + \" element\");\n        }\n        return mediaElement;\n    };\n    /**\n     * Decodes the barcode from an image.\n     *\n     * @param {(string|HTMLImageElement)} [source] The image element that can be either an element id or the element itself. Can be undefined in which case the decoding will be done from the imageUrl parameter.\n     * @param {string} [url]\n     * @returns {Promise<Result>} The decoding result.\n     *\n     * @memberOf BrowserCodeReader\n     */\n    BrowserCodeReader.prototype.decodeFromImage = function (source, url) {\n        if (!source && !url) {\n            throw new ArgumentException('either imageElement with a src set or an url must be provided');\n        }\n        if (url && !source) {\n            return this.decodeFromImageUrl(url);\n        }\n        return this.decodeFromImageElement(source);\n    };\n    /**\n     * Decodes the barcode from a video.\n     *\n     * @param {(string|HTMLImageElement)} [source] The image element that can be either an element id or the element itself. Can be undefined in which case the decoding will be done from the imageUrl parameter.\n     * @param {string} [url]\n     * @returns {Promise<Result>} The decoding result.\n     *\n     * @memberOf BrowserCodeReader\n     */\n    BrowserCodeReader.prototype.decodeFromVideo = function (source, url) {\n        if (!source && !url) {\n            throw new ArgumentException('Either an element with a src set or an URL must be provided');\n        }\n        if (url && !source) {\n            return this.decodeFromVideoUrl(url);\n        }\n        return this.decodeFromVideoElement(source);\n    };\n    /**\n     * Decodes continuously the barcode from a video.\n     *\n     * @param {(string|HTMLImageElement)} [source] The image element that can be either an element id or the element itself. Can be undefined in which case the decoding will be done from the imageUrl parameter.\n     * @param {string} [url]\n     * @returns {Promise<Result>} The decoding result.\n     *\n     * @memberOf BrowserCodeReader\n     *\n     * @experimental\n     */\n    BrowserCodeReader.prototype.decodeFromVideoContinuously = function (source, url, callbackFn) {\n        if (undefined === source && undefined === url) {\n            throw new ArgumentException('Either an element with a src set or an URL must be provided');\n        }\n        if (url && !source) {\n            return this.decodeFromVideoUrlContinuously(url, callbackFn);\n        }\n        return this.decodeFromVideoElementContinuously(source, callbackFn);\n    };\n    /**\n     * Decodes something from an image HTML element.\n     */\n    BrowserCodeReader.prototype.decodeFromImageElement = function (source) {\n        if (!source) {\n            throw new ArgumentException('An image element must be provided.');\n        }\n        this.reset();\n        var element = this.prepareImageElement(source);\n        this.imageElement = element;\n        var task;\n        if (this.isImageLoaded(element)) {\n            task = this.decodeOnce(element, false, true);\n        }\n        else {\n            task = this._decodeOnLoadImage(element);\n        }\n        return task;\n    };\n    /**\n     * Decodes something from an image HTML element.\n     */\n    BrowserCodeReader.prototype.decodeFromVideoElement = function (source) {\n        var element = this._decodeFromVideoElementSetup(source);\n        return this._decodeOnLoadVideo(element);\n    };\n    /**\n     * Decodes something from an image HTML element.\n     */\n    BrowserCodeReader.prototype.decodeFromVideoElementContinuously = function (source, callbackFn) {\n        var element = this._decodeFromVideoElementSetup(source);\n        return this._decodeOnLoadVideoContinuously(element, callbackFn);\n    };\n    /**\n     * Sets up the video source so it can be decoded when loaded.\n     *\n     * @param source The video source element.\n     */\n    BrowserCodeReader.prototype._decodeFromVideoElementSetup = function (source) {\n        if (!source) {\n            throw new ArgumentException('A video element must be provided.');\n        }\n        this.reset();\n        var element = this.prepareVideoElement(source);\n        // defines the video element before starts decoding\n        this.videoElement = element;\n        return element;\n    };\n    /**\n     * Decodes an image from a URL.\n     */\n    BrowserCodeReader.prototype.decodeFromImageUrl = function (url) {\n        if (!url) {\n            throw new ArgumentException('An URL must be provided.');\n        }\n        this.reset();\n        var element = this.prepareImageElement();\n        this.imageElement = element;\n        var decodeTask = this._decodeOnLoadImage(element);\n        element.src = url;\n        return decodeTask;\n    };\n    /**\n     * Decodes an image from a URL.\n     */\n    BrowserCodeReader.prototype.decodeFromVideoUrl = function (url) {\n        if (!url) {\n            throw new ArgumentException('An URL must be provided.');\n        }\n        this.reset();\n        // creates a new element\n        var element = this.prepareVideoElement();\n        var decodeTask = this.decodeFromVideoElement(element);\n        element.src = url;\n        return decodeTask;\n    };\n    /**\n     * Decodes an image from a URL.\n     *\n     * @experimental\n     */\n    BrowserCodeReader.prototype.decodeFromVideoUrlContinuously = function (url, callbackFn) {\n        if (!url) {\n            throw new ArgumentException('An URL must be provided.');\n        }\n        this.reset();\n        // creates a new element\n        var element = this.prepareVideoElement();\n        var decodeTask = this.decodeFromVideoElementContinuously(element, callbackFn);\n        element.src = url;\n        return decodeTask;\n    };\n    BrowserCodeReader.prototype._decodeOnLoadImage = function (element) {\n        var _this = this;\n        return new Promise(function (resolve, reject) {\n            _this.imageLoadedListener = function () {\n                return _this.decodeOnce(element, false, true).then(resolve, reject);\n            };\n            element.addEventListener('load', _this.imageLoadedListener);\n        });\n    };\n    BrowserCodeReader.prototype._decodeOnLoadVideo = function (videoElement) {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: \n                    // plays the video\n                    return [4 /*yield*/, this.playVideoOnLoadAsync(videoElement)];\n                    case 1:\n                        // plays the video\n                        _a.sent();\n                        return [4 /*yield*/, this.decodeOnce(videoElement)];\n                    case 2: \n                    // starts decoding after played the video\n                    return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    BrowserCodeReader.prototype._decodeOnLoadVideoContinuously = function (videoElement, callbackFn) {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: \n                    // plays the video\n                    return [4 /*yield*/, this.playVideoOnLoadAsync(videoElement)];\n                    case 1:\n                        // plays the video\n                        _a.sent();\n                        // starts decoding after played the video\n                        this.decodeContinuously(videoElement, callbackFn);\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    BrowserCodeReader.prototype.isImageLoaded = function (img) {\n        // During the onload event, IE correctly identifies any images that\n        // weren’t downloaded as not complete. Others should too. Gecko-based\n        // browsers act like NS4 in that they report this incorrectly.\n        if (!img.complete) {\n            return false;\n        }\n        // However, they do have two very useful properties: naturalWidth and\n        // naturalHeight. These give the true size of the image. If it failed\n        // to load, either of these should be zero.\n        if (img.naturalWidth === 0) {\n            return false;\n        }\n        // No other way of checking: assume it’s ok.\n        return true;\n    };\n    BrowserCodeReader.prototype.prepareImageElement = function (imageSource) {\n        var imageElement;\n        if (typeof imageSource === 'undefined') {\n            imageElement = document.createElement('img');\n            imageElement.width = 200;\n            imageElement.height = 200;\n        }\n        if (typeof imageSource === 'string') {\n            imageElement = this.getMediaElement(imageSource, 'img');\n        }\n        if (imageSource instanceof HTMLImageElement) {\n            imageElement = imageSource;\n        }\n        return imageElement;\n    };\n    /**\n     * Sets a HTMLVideoElement for scanning or creates a new one.\n     *\n     * @param videoSource The HTMLVideoElement to be set.\n     */\n    BrowserCodeReader.prototype.prepareVideoElement = function (videoSource) {\n        var videoElement;\n        if (!videoSource && typeof document !== 'undefined') {\n            videoElement = document.createElement('video');\n            videoElement.width = 200;\n            videoElement.height = 200;\n        }\n        if (typeof videoSource === 'string') {\n            videoElement = (this.getMediaElement(videoSource, 'video'));\n        }\n        if (videoSource instanceof HTMLVideoElement) {\n            videoElement = videoSource;\n        }\n        // Needed for iOS 11\n        videoElement.setAttribute('autoplay', 'true');\n        videoElement.setAttribute('muted', 'true');\n        videoElement.setAttribute('playsinline', 'true');\n        return videoElement;\n    };\n    /**\n     * Tries to decode from the video input until it finds some value.\n     */\n    BrowserCodeReader.prototype.decodeOnce = function (element, retryIfNotFound, retryIfChecksumOrFormatError) {\n        var _this = this;\n        if (retryIfNotFound === void 0) { retryIfNotFound = true; }\n        if (retryIfChecksumOrFormatError === void 0) { retryIfChecksumOrFormatError = true; }\n        this._stopAsyncDecode = false;\n        var loop = function (resolve, reject) {\n            if (_this._stopAsyncDecode) {\n                reject(new NotFoundException('Video stream has ended before any code could be detected.'));\n                _this._stopAsyncDecode = undefined;\n                return;\n            }\n            try {\n                var result = _this.decode(element);\n                resolve(result);\n            }\n            catch (e) {\n                var ifNotFound = retryIfNotFound && e instanceof NotFoundException;\n                var isChecksumOrFormatError = e instanceof ChecksumException || e instanceof FormatException;\n                var ifChecksumOrFormat = isChecksumOrFormatError && retryIfChecksumOrFormatError;\n                if (ifNotFound || ifChecksumOrFormat) {\n                    // trying again\n                    return setTimeout(loop, _this._timeBetweenDecodingAttempts, resolve, reject);\n                }\n                reject(e);\n            }\n        };\n        return new Promise(function (resolve, reject) { return loop(resolve, reject); });\n    };\n    /**\n     * Continuously decodes from video input.\n     */\n    BrowserCodeReader.prototype.decodeContinuously = function (element, callbackFn) {\n        var _this = this;\n        this._stopContinuousDecode = false;\n        var loop = function () {\n            if (_this._stopContinuousDecode) {\n                _this._stopContinuousDecode = undefined;\n                return;\n            }\n            try {\n                var result = _this.decode(element);\n                callbackFn(result, null);\n                setTimeout(loop, _this.timeBetweenScansMillis);\n            }\n            catch (e) {\n                callbackFn(null, e);\n                var isChecksumOrFormatError = e instanceof ChecksumException || e instanceof FormatException;\n                var isNotFound = e instanceof NotFoundException;\n                if (isChecksumOrFormatError || isNotFound) {\n                    // trying again\n                    setTimeout(loop, _this._timeBetweenDecodingAttempts);\n                }\n            }\n        };\n        loop();\n    };\n    /**\n     * Gets the BinaryBitmap for ya! (and decodes it)\n     */\n    BrowserCodeReader.prototype.decode = function (element) {\n        // get binary bitmap for decode function\n        var binaryBitmap = this.createBinaryBitmap(element);\n        return this.decodeBitmap(binaryBitmap);\n    };\n    /**\n     * Creates a binaryBitmap based in some image source.\n     *\n     * @param mediaElement HTML element containing drawable image source.\n     */\n    BrowserCodeReader.prototype.createBinaryBitmap = function (mediaElement) {\n        var ctx = this.getCaptureCanvasContext(mediaElement);\n        // doing a scan with inverted colors on the second scan should only happen for video elements\n        var doAutoInvert = false;\n        if (mediaElement instanceof HTMLVideoElement) {\n            this.drawFrameOnCanvas(mediaElement);\n            doAutoInvert = true;\n        }\n        else {\n            this.drawImageOnCanvas(mediaElement);\n        }\n        var canvas = this.getCaptureCanvas(mediaElement);\n        var luminanceSource = new HTMLCanvasElementLuminanceSource(canvas, doAutoInvert);\n        var hybridBinarizer = new HybridBinarizer(luminanceSource);\n        return new BinaryBitmap(hybridBinarizer);\n    };\n    /**\n     *\n     */\n    BrowserCodeReader.prototype.getCaptureCanvasContext = function (mediaElement) {\n        if (!this.captureCanvasContext) {\n            var elem = this.getCaptureCanvas(mediaElement);\n            var ctx = void 0;\n            try {\n                ctx = elem.getContext('2d', { willReadFrequently: true });\n            }\n            catch (e) {\n                ctx = elem.getContext('2d');\n            }\n            this.captureCanvasContext = ctx;\n        }\n        return this.captureCanvasContext;\n    };\n    /**\n     *\n     */\n    BrowserCodeReader.prototype.getCaptureCanvas = function (mediaElement) {\n        if (!this.captureCanvas) {\n            var elem = this.createCaptureCanvas(mediaElement);\n            this.captureCanvas = elem;\n        }\n        return this.captureCanvas;\n    };\n    /**\n     * Overwriting this allows you to manipulate the next frame in anyway you want before decode.\n     */\n    BrowserCodeReader.prototype.drawFrameOnCanvas = function (srcElement, dimensions, canvasElementContext) {\n        if (dimensions === void 0) { dimensions = {\n            sx: 0,\n            sy: 0,\n            sWidth: srcElement.videoWidth,\n            sHeight: srcElement.videoHeight,\n            dx: 0,\n            dy: 0,\n            dWidth: srcElement.videoWidth,\n            dHeight: srcElement.videoHeight,\n        }; }\n        if (canvasElementContext === void 0) { canvasElementContext = this.captureCanvasContext; }\n        canvasElementContext.drawImage(srcElement, dimensions.sx, dimensions.sy, dimensions.sWidth, dimensions.sHeight, dimensions.dx, dimensions.dy, dimensions.dWidth, dimensions.dHeight);\n    };\n    /**\n     * Ovewriting this allows you to manipulate the snapshot image in anyway you want before decode.\n     */\n    BrowserCodeReader.prototype.drawImageOnCanvas = function (srcElement, dimensions, canvasElementContext) {\n        if (dimensions === void 0) { dimensions = {\n            sx: 0,\n            sy: 0,\n            sWidth: srcElement.naturalWidth,\n            sHeight: srcElement.naturalHeight,\n            dx: 0,\n            dy: 0,\n            dWidth: srcElement.naturalWidth,\n            dHeight: srcElement.naturalHeight,\n        }; }\n        if (canvasElementContext === void 0) { canvasElementContext = this.captureCanvasContext; }\n        canvasElementContext.drawImage(srcElement, dimensions.sx, dimensions.sy, dimensions.sWidth, dimensions.sHeight, dimensions.dx, dimensions.dy, dimensions.dWidth, dimensions.dHeight);\n    };\n    /**\n     * Call the encapsulated readers decode\n     */\n    BrowserCodeReader.prototype.decodeBitmap = function (binaryBitmap) {\n        return this.reader.decode(binaryBitmap, this._hints);\n    };\n    /**\n     * 🖌 Prepares the canvas for capture and scan frames.\n     */\n    BrowserCodeReader.prototype.createCaptureCanvas = function (mediaElement) {\n        if (typeof document === 'undefined') {\n            this._destroyCaptureCanvas();\n            return null;\n        }\n        var canvasElement = document.createElement('canvas');\n        var width;\n        var height;\n        if (typeof mediaElement !== 'undefined') {\n            if (mediaElement instanceof HTMLVideoElement) {\n                width = mediaElement.videoWidth;\n                height = mediaElement.videoHeight;\n            }\n            else if (mediaElement instanceof HTMLImageElement) {\n                width = mediaElement.naturalWidth || mediaElement.width;\n                height = mediaElement.naturalHeight || mediaElement.height;\n            }\n        }\n        canvasElement.style.width = width + 'px';\n        canvasElement.style.height = height + 'px';\n        canvasElement.width = width;\n        canvasElement.height = height;\n        return canvasElement;\n    };\n    /**\n     * Stops the continuous scan and cleans the stream.\n     */\n    BrowserCodeReader.prototype.stopStreams = function () {\n        if (this.stream) {\n            this.stream.getVideoTracks().forEach(function (t) { return t.stop(); });\n            this.stream = undefined;\n        }\n        if (this._stopAsyncDecode === false) {\n            this.stopAsyncDecode();\n        }\n        if (this._stopContinuousDecode === false) {\n            this.stopContinuousDecode();\n        }\n    };\n    /**\n     * Resets the code reader to the initial state. Cancels any ongoing barcode scanning from video or camera.\n     *\n     * @memberOf BrowserCodeReader\n     */\n    BrowserCodeReader.prototype.reset = function () {\n        // stops the camera, preview and scan 🔴\n        this.stopStreams();\n        // clean and forget about HTML elements\n        this._destroyVideoElement();\n        this._destroyImageElement();\n        this._destroyCaptureCanvas();\n    };\n    BrowserCodeReader.prototype._destroyVideoElement = function () {\n        if (!this.videoElement) {\n            return;\n        }\n        // first gives freedon to the element 🕊\n        if (typeof this.videoEndedListener !== 'undefined') {\n            this.videoElement.removeEventListener('ended', this.videoEndedListener);\n        }\n        if (typeof this.videoPlayingEventListener !== 'undefined') {\n            this.videoElement.removeEventListener('playing', this.videoPlayingEventListener);\n        }\n        if (typeof this.videoCanPlayListener !== 'undefined') {\n            this.videoElement.removeEventListener('loadedmetadata', this.videoCanPlayListener);\n        }\n        // then forgets about that element 😢\n        this.cleanVideoSource(this.videoElement);\n        this.videoElement = undefined;\n    };\n    BrowserCodeReader.prototype._destroyImageElement = function () {\n        if (!this.imageElement) {\n            return;\n        }\n        // first gives freedon to the element 🕊\n        if (undefined !== this.imageLoadedListener) {\n            this.imageElement.removeEventListener('load', this.imageLoadedListener);\n        }\n        // then forget about that element 😢\n        this.imageElement.src = undefined;\n        this.imageElement.removeAttribute('src');\n        this.imageElement = undefined;\n    };\n    /**\n     * Cleans canvas references 🖌\n     */\n    BrowserCodeReader.prototype._destroyCaptureCanvas = function () {\n        // then forget about that element 😢\n        this.captureCanvasContext = undefined;\n        this.captureCanvas = undefined;\n    };\n    /**\n     * Defines what the videoElement src will be.\n     *\n     * @param videoElement\n     * @param stream\n     */\n    BrowserCodeReader.prototype.addVideoSource = function (videoElement, stream) {\n        // Older browsers may not have `srcObject`\n        try {\n            // @note Throws Exception if interrupted by a new loaded request\n            videoElement.srcObject = stream;\n        }\n        catch (err) {\n            // @note Avoid using this in new browsers, as it is going away.\n            // @ts-ignore\n            videoElement.src = URL.createObjectURL(stream);\n        }\n    };\n    /**\n     * Unbinds a HTML video src property.\n     *\n     * @param videoElement\n     */\n    BrowserCodeReader.prototype.cleanVideoSource = function (videoElement) {\n        try {\n            videoElement.srcObject = null;\n        }\n        catch (err) {\n            videoElement.src = '';\n        }\n        this.videoElement.removeAttribute('src');\n    };\n    return BrowserCodeReader;\n}());\nexport { BrowserCodeReader };\n"], "names": [], "mappings": ";;;AA+CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAtDA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,SAAU,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS;IACnF,SAAS,MAAM,KAAK;QAAI,OAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,OAAO;YAAI,QAAQ;QAAQ;IAAI;IAC3G,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,EAAE,SAAU,OAAO,EAAE,MAAM;QACrD,SAAS,UAAU,KAAK;YAAI,IAAI;gBAAE,KAAK,UAAU,IAAI,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC1F,SAAS,SAAS,KAAK;YAAI,IAAI;gBAAE,KAAK,SAAS,CAAC,QAAQ,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC7F,SAAS,KAAK,MAAM;YAAI,OAAO,IAAI,GAAG,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,IAAI,CAAC,WAAW;QAAW;QAC7G,KAAK,CAAC,YAAY,UAAU,KAAK,CAAC,SAAS,cAAc,EAAE,CAAC,EAAE,IAAI;IACtE;AACJ;AACA,IAAI,cAAc,4CAAS,yCAAK,WAAW,IAAK,SAAU,OAAO,EAAE,IAAI;IACnE,IAAI,IAAI;QAAE,OAAO;QAAG,MAAM;YAAa,IAAI,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,CAAC,EAAE;YAAE,OAAO,CAAC,CAAC,EAAE;QAAE;QAAG,MAAM,EAAE;QAAE,KAAK,EAAE;IAAC,GAAG,GAAG,GAAG,GAAG;IAC/G,OAAO,IAAI;QAAE,MAAM,KAAK;QAAI,SAAS,KAAK;QAAI,UAAU,KAAK;IAAG,GAAG,OAAO,WAAW,cAAc,CAAC,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAG;QAAa,OAAO,IAAI;IAAE,CAAC,GAAG;;;IACvJ,SAAS,KAAK,CAAC;QAAI,OAAO,SAAU,CAAC;YAAI,OAAO,KAAK;gBAAC;gBAAG;aAAE;QAAG;IAAG;IACjE,SAAS,KAAK,EAAE;QACZ,IAAI,GAAG,MAAM,IAAI,UAAU;QAC3B,MAAO,EAAG,IAAI;YACV,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,SAAS,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO;YAC3J,IAAI,IAAI,GAAG,GAAG,KAAK;gBAAC,EAAE,CAAC,EAAE,GAAG;gBAAG,EAAE,KAAK;aAAC;YACvC,OAAQ,EAAE,CAAC,EAAE;gBACT,KAAK;gBAAG,KAAK;oBAAG,IAAI;oBAAI;gBACxB,KAAK;oBAAG,EAAE,KAAK;oBAAI,OAAO;wBAAE,OAAO,EAAE,CAAC,EAAE;wBAAE,MAAM;oBAAM;gBACtD,KAAK;oBAAG,EAAE,KAAK;oBAAI,IAAI,EAAE,CAAC,EAAE;oBAAE,KAAK;wBAAC;qBAAE;oBAAE;gBACxC,KAAK;oBAAG,KAAK,EAAE,GAAG,CAAC,GAAG;oBAAI,EAAE,IAAI,CAAC,GAAG;oBAAI;gBACxC;oBACI,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG;wBAAE,IAAI;wBAAG;oBAAU;oBAC3G,IAAI,EAAE,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,KAAM,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,AAAC,GAAG;wBAAE,EAAE,KAAK,GAAG,EAAE,CAAC,EAAE;wBAAE;oBAAO;oBACrF,IAAI,EAAE,CAAC,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE;wBAAE,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE;wBAAE,IAAI;wBAAI;oBAAO;oBACpE,IAAI,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE;wBAAE,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE;wBAAE,EAAE,GAAG,CAAC,IAAI,CAAC;wBAAK;oBAAO;oBAClE,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,GAAG;oBACnB,EAAE,IAAI,CAAC,GAAG;oBAAI;YACtB;YACA,KAAK,KAAK,IAAI,CAAC,SAAS;QAC5B,EAAE,OAAO,GAAG;YAAE,KAAK;gBAAC;gBAAG;aAAE;YAAE,IAAI;QAAG,SAAU;YAAE,IAAI,IAAI;QAAG;QACzD,IAAI,EAAE,CAAC,EAAE,GAAG,GAAG,MAAM,EAAE,CAAC,EAAE;QAAE,OAAO;YAAE,OAAO,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,KAAK;YAAG,MAAM;QAAK;IACnF;AACJ;AACA,IAAI,WAAW,4CAAS,yCAAK,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;;;;;;;AASA;;;;CAIC,GACD,IAAI,oBAAmC;IACnC;;;;;;KAMC,GACD,SAAS,kBAAkB,MAAM,EAAE,sBAAsB,EAAE,MAAM;QAC7D,IAAI,2BAA2B,KAAK,GAAG;YAAE,yBAAyB;QAAK;QACvE,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,sBAAsB,GAAG;QAC9B,IAAI,CAAC,MAAM,GAAG;QACd;;SAEC,GACD,IAAI,CAAC,qBAAqB,GAAG;QAC7B;;SAEC,GACD,IAAI,CAAC,gBAAgB,GAAG;QACxB;;SAEC,GACD,IAAI,CAAC,4BAA4B,GAAG;IACxC;IACA,OAAO,cAAc,CAAC,kBAAkB,SAAS,EAAE,gBAAgB;QAC/D;;SAEC,GACD,KAAK;YACD,OAAO,OAAO,cAAc;QAChC;QACA,YAAY;QACZ,cAAc;IAClB;IACA,OAAO,cAAc,CAAC,kBAAkB,SAAS,EAAE,0BAA0B;QACzE;;SAEC,GACD,KAAK;YACD,OAAO,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC,UAAU,YAAY;QACxD;QACA,YAAY;QACZ,cAAc;IAClB;IACA,OAAO,cAAc,CAAC,kBAAkB,SAAS,EAAE,uBAAuB;QACtE;;SAEC,GACD,KAAK;YACD,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,IAAI,UAAU,YAAY,CAAC,gBAAgB;QACpF;QACA,YAAY;QACZ,cAAc;IAClB;IACA,OAAO,cAAc,CAAC,kBAAkB,SAAS,EAAE,+BAA+B;QAC9E,sDAAsD,GACtD,KAAK;YACD,OAAO,IAAI,CAAC,4BAA4B;QAC5C;QACA;;;;SAIC,GACD,KAAK,SAAU,MAAM;YACjB,IAAI,CAAC,4BAA4B,GAAG,SAAS,IAAI,IAAI;QACzD;QACA,YAAY;QACZ,cAAc;IAClB;IACA,OAAO,cAAc,CAAC,kBAAkB,SAAS,EAAE,SAAS;QACxD;;SAEC,GACD,KAAK;YACD,OAAO,IAAI,CAAC,MAAM;QACtB;QACA;;SAEC,GACD,KAAK,SAAU,KAAK;YAChB,IAAI,CAAC,MAAM,GAAG,SAAS;QAC3B;QACA,YAAY;QACZ,cAAc;IAClB;IACA;;KAEC,GACD,kBAAkB,SAAS,CAAC,qBAAqB,GAAG;QAChD,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,IAAI,SAAS,cAAc,WAAW,aAAa,QAAQ,MAAM,UAAU,OAAO,SAAS;YAC3F,IAAI,KAAK;YACT,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBACD,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;4BACpB,MAAM,IAAI,MAAM;wBACpB;wBACA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;4BAC3B,MAAM,IAAI,MAAM;wBACpB;wBACA,OAAO;4BAAC,EAAE,OAAO;4BAAI,UAAU,YAAY,CAAC,gBAAgB;yBAAG;oBACnE,KAAK;wBACD,UAAU,GAAG,IAAI;wBACjB,eAAe,EAAE;wBACjB,IAAI;4BACA,IAAK,YAAY,SAAS,UAAU,cAAc,UAAU,IAAI,IAAI,CAAC,YAAY,IAAI,EAAE,cAAc,UAAU,IAAI,GAAI;gCACnH,SAAS,YAAY,KAAK;gCAC1B,OAAO,OAAO,IAAI,KAAK,UAAU,eAAe,OAAO,IAAI;gCAC3D,IAAI,SAAS,cAAc;oCACvB;gCACJ;gCACA,WAAW,OAAO,QAAQ,IAAI,OAAO,EAAE;gCACvC,QAAQ,OAAO,KAAK,IAAI,kBAAkB,CAAC,aAAa,MAAM,GAAG,CAAC;gCAClE,UAAU,OAAO,OAAO;gCACxB,cAAc;oCAAE,UAAU;oCAAU,OAAO;oCAAO,MAAM;oCAAM,SAAS;gCAAQ;gCAC/E,aAAa,IAAI,CAAC;4BACtB;wBACJ,EACA,OAAO,OAAO;4BAAE,MAAM;gCAAE,OAAO;4BAAM;wBAAG,SAChC;4BACJ,IAAI;gCACA,IAAI,eAAe,CAAC,YAAY,IAAI,IAAI,CAAC,KAAK,UAAU,MAAM,GAAG,GAAG,IAAI,CAAC;4BAC7E,SACQ;gCAAE,IAAI,KAAK,MAAM,IAAI,KAAK;4BAAE;wBACxC;wBACA,OAAO;4BAAC,EAAE,QAAQ;4BAAI;yBAAa;gBAC3C;YACJ;QACJ;IACJ;IACA;;;;;;;;KAQC,GACD,kBAAkB,SAAS,CAAC,oBAAoB,GAAG;QAC/C,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,IAAI;YACJ,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBAAG,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,qBAAqB;yBAAG;oBAC1D,KAAK;wBACD,UAAU,GAAG,IAAI;wBACjB,OAAO;4BAAC,EAAE,QAAQ;4BAAI,QAAQ,GAAG,CAAC,SAAU,CAAC;gCAAI,OAAO,IAAI,2OAAA,CAAA,mBAAgB,CAAC,EAAE,QAAQ,EAAE,EAAE,KAAK;4BAAG;yBAAG;gBAC9G;YACJ;QACJ;IACJ;IACA;;KAEC,GACD,kBAAkB,SAAS,CAAC,cAAc,GAAG,SAAU,QAAQ;QAC3D,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,IAAI;YACJ,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBAAG,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,qBAAqB;yBAAG;oBAC1D,KAAK;wBACD,UAAU,GAAG,IAAI;wBACjB,IAAI,CAAC,SAAS;4BACV,OAAO;gCAAC,EAAE,QAAQ;gCAAI;6BAAK;wBAC/B;wBACA,OAAO;4BAAC,EAAE,QAAQ;4BAAI,QAAQ,IAAI,CAAC,SAAU,CAAC;gCAAI,OAAO,EAAE,QAAQ,KAAK;4BAAU;yBAAG;gBAC7F;YACJ;QACJ;IACJ;IACA;;;;;;;;;;KAUC,GACD,kBAAkB,SAAS,CAAC,0BAA0B,GAAG,SAAU,QAAQ,EAAE,WAAW;QACpF,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBAAG,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,yBAAyB,CAAC,UAAU;yBAAa;oBACnF,KAAK;wBAAG,OAAO;4BAAC,EAAE,QAAQ;4BAAI,GAAG,IAAI;yBAAG;gBAC5C;YACJ;QACJ;IACJ;IACA;;;;;;;;KAQC,GACD,kBAAkB,SAAS,CAAC,yBAAyB,GAAG,SAAU,QAAQ,EAAE,WAAW;QACnF,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,IAAI,kBAAkB;YACtB,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBACD,IAAI,CAAC,KAAK;wBACV,IAAI,CAAC,UAAU;4BACX,mBAAmB;gCAAE,YAAY;4BAAc;wBACnD,OACK;4BACD,mBAAmB;gCAAE,UAAU;oCAAE,OAAO;gCAAS;4BAAE;wBACvD;wBACA,cAAc;4BAAE,OAAO;wBAAiB;wBACxC,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,yBAAyB,CAAC,aAAa;yBAAa;oBAClF,KAAK;wBAAG,OAAO;4BAAC,EAAE,QAAQ;4BAAI,GAAG,IAAI;yBAAG;gBAC5C;YACJ;QACJ;IACJ;IACA;;;;;;;;KAQC,GACD,kBAAkB,SAAS,CAAC,yBAAyB,GAAG,SAAU,WAAW,EAAE,WAAW;QACtF,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,IAAI;YACJ,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBAAG,OAAO;4BAAC,EAAE,OAAO;4BAAI,UAAU,YAAY,CAAC,YAAY,CAAC;yBAAa;oBAC9E,KAAK;wBACD,SAAS,GAAG,IAAI;wBAChB,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,oBAAoB,CAAC,QAAQ;yBAAa;oBACxE,KAAK;wBAAG,OAAO;4BAAC,EAAE,QAAQ;4BAAI,GAAG,IAAI;yBAAG;gBAC5C;YACJ;QACJ;IACJ;IACA;;;;;;;;KAQC,GACD,kBAAkB,SAAS,CAAC,oBAAoB,GAAG,SAAU,MAAM,EAAE,WAAW;QAC5E,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,IAAI,OAAO;YACX,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBACD,IAAI,CAAC,KAAK;wBACV,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,mBAAmB,CAAC,QAAQ;yBAAa;oBACvE,KAAK;wBACD,QAAQ,GAAG,IAAI;wBACf,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,UAAU,CAAC;yBAAO;oBAChD,KAAK;wBACD,SAAS,GAAG,IAAI;wBAChB,OAAO;4BAAC,EAAE,QAAQ;4BAAI;yBAAO;gBACrC;YACJ;QACJ;IACJ;IACA;;;;;;;;;;KAUC,GACD,kBAAkB,SAAS,CAAC,sCAAsC,GAAG,SAAU,QAAQ,EAAE,WAAW,EAAE,UAAU;QAC5G,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBAAG,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,qBAAqB,CAAC,UAAU,aAAa;yBAAY;oBAC3F,KAAK;wBAAG,OAAO;4BAAC,EAAE,QAAQ;4BAAI,GAAG,IAAI;yBAAG;gBAC5C;YACJ;QACJ;IACJ;IACA;;;;;;;;KAQC,GACD,kBAAkB,SAAS,CAAC,qBAAqB,GAAG,SAAU,QAAQ,EAAE,WAAW,EAAE,UAAU;QAC3F,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,IAAI,kBAAkB;YACtB,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBACD,IAAI,CAAC,UAAU;4BACX,mBAAmB;gCAAE,YAAY;4BAAc;wBACnD,OACK;4BACD,mBAAmB;gCAAE,UAAU;oCAAE,OAAO;gCAAS;4BAAE;wBACvD;wBACA,cAAc;4BAAE,OAAO;wBAAiB;wBACxC,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,qBAAqB,CAAC,aAAa,aAAa;yBAAY;oBAC1F,KAAK;wBAAG,OAAO;4BAAC,EAAE,QAAQ;4BAAI,GAAG,IAAI;yBAAG;gBAC5C;YACJ;QACJ;IACJ;IACA;;;;;;;;KAQC,GACD,kBAAkB,SAAS,CAAC,qBAAqB,GAAG,SAAU,WAAW,EAAE,WAAW,EAAE,UAAU;QAC9F,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,IAAI;YACJ,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBAAG,OAAO;4BAAC,EAAE,OAAO;4BAAI,UAAU,YAAY,CAAC,YAAY,CAAC;yBAAa;oBAC9E,KAAK;wBACD,SAAS,GAAG,IAAI;wBAChB,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,aAAa;yBAAY;oBAChF,KAAK;wBAAG,OAAO;4BAAC,EAAE,QAAQ;4BAAI,GAAG,IAAI;yBAAG;gBAC5C;YACJ;QACJ;IACJ;IACA;;;;;;;;KAQC,GACD,kBAAkB,SAAS,CAAC,gBAAgB,GAAG,SAAU,MAAM,EAAE,WAAW,EAAE,UAAU;QACpF,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,IAAI;YACJ,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBACD,IAAI,CAAC,KAAK;wBACV,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,mBAAmB,CAAC,QAAQ;yBAAa;oBACvE,KAAK;wBACD,QAAQ,GAAG,IAAI;wBACf,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO;yBAAY;oBACpE,KAAK;wBAAG,OAAO;4BAAC,EAAE,QAAQ;4BAAI,GAAG,IAAI;yBAAG;gBAC5C;YACJ;QACJ;IACJ;IACA;;KAEC,GACD,kBAAkB,SAAS,CAAC,eAAe,GAAG;QAC1C,IAAI,CAAC,gBAAgB,GAAG;IAC5B;IACA;;KAEC,GACD,kBAAkB,SAAS,CAAC,oBAAoB,GAAG;QAC/C,IAAI,CAAC,qBAAqB,GAAG;IACjC;IACA;;;;;KAKC,GACD,kBAAkB,SAAS,CAAC,mBAAmB,GAAG,SAAU,MAAM,EAAE,WAAW;QAC3E,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,IAAI;YACJ,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBACD,eAAe,IAAI,CAAC,mBAAmB,CAAC;wBACxC,IAAI,CAAC,cAAc,CAAC,cAAc;wBAClC,IAAI,CAAC,YAAY,GAAG;wBACpB,IAAI,CAAC,MAAM,GAAG;wBACd,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,oBAAoB,CAAC;yBAAc;oBACjE,KAAK;wBACD,GAAG,IAAI;wBACP,OAAO;4BAAC,EAAE,QAAQ;4BAAI;yBAAa;gBAC3C;YACJ;QACJ;IACJ;IACA;;;KAGC,GACD,kBAAkB,SAAS,CAAC,oBAAoB,GAAG,SAAU,YAAY;QACrE,IAAI,QAAQ,IAAI;QAChB,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;YACxC,OAAO,MAAM,eAAe,CAAC,cAAc;gBAAc,OAAO;YAAW;QAC/E;IACJ;IACA;;;;;KAKC,GACD,kBAAkB,SAAS,CAAC,eAAe,GAAG,SAAU,OAAO,EAAE,UAAU;QACvE,IAAI,QAAQ,IAAI;QAChB,IAAI,CAAC,kBAAkB,GAAG;YAAc,OAAO,MAAM,WAAW;QAAI;QACpE,IAAI,CAAC,oBAAoB,GAAG;YAAc,OAAO,MAAM,YAAY,CAAC;QAAU;QAC9E,QAAQ,gBAAgB,CAAC,SAAS,IAAI,CAAC,kBAAkB;QACzD,QAAQ,gBAAgB,CAAC,WAAW,IAAI,CAAC,oBAAoB;QAC7D,QAAQ,gBAAgB,CAAC,WAAW;QACpC,kFAAkF;QAClF,IAAI,CAAC,YAAY,CAAC;IACtB;IACA;;KAEC,GACD,kBAAkB,SAAS,CAAC,cAAc,GAAG,SAAU,KAAK;QACxD,OAAQ,MAAM,WAAW,GAAG,KACxB,CAAC,MAAM,MAAM,IACb,CAAC,MAAM,KAAK,IACZ,MAAM,UAAU,GAAG;IAC3B;IACA;;;KAGC,GACD,kBAAkB,SAAS,CAAC,YAAY,GAAG,SAAU,YAAY;QAC7D,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,IAAI;YACJ,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBACD,IAAI,IAAI,CAAC,cAAc,CAAC,eAAe;4BACnC,QAAQ,IAAI,CAAC;4BACb,OAAO;gCAAC,EAAE,QAAQ;6BAAG;wBACzB;wBACA,GAAG,KAAK,GAAG;oBACf,KAAK;wBACD,GAAG,IAAI,CAAC,IAAI,CAAC;4BAAC;4BAAG;;4BAAK;yBAAE;wBACxB,OAAO;4BAAC,EAAE,OAAO;4BAAI,aAAa,IAAI;yBAAG;oBAC7C,KAAK;wBACD,GAAG,IAAI;wBACP,OAAO;4BAAC,EAAE,OAAO;4BAAI;yBAAE;oBAC3B,KAAK;wBACD,KAAK,GAAG,IAAI;wBACZ,QAAQ,IAAI,CAAC;wBACb,OAAO;4BAAC,EAAE,OAAO;4BAAI;yBAAE;oBAC3B,KAAK;wBAAG,OAAO;4BAAC,EAAE,QAAQ;yBAAG;gBACjC;YACJ;QACJ;IACJ;IACA;;KAEC,GACD,kBAAkB,SAAS,CAAC,eAAe,GAAG,SAAU,cAAc,EAAE,IAAI;QACxE,IAAI,eAAe,SAAS,cAAc,CAAC;QAC3C,IAAI,CAAC,cAAc;YACf,MAAM,IAAI,yOAAA,CAAA,UAAiB,CAAC,sBAAsB,iBAAiB;QACvE;QACA,IAAI,aAAa,QAAQ,CAAC,WAAW,OAAO,KAAK,WAAW,IAAI;YAC5D,MAAM,IAAI,yOAAA,CAAA,UAAiB,CAAC,sBAAsB,iBAAiB,kBAAkB,OAAO;QAChG;QACA,OAAO;IACX;IACA;;;;;;;;KAQC,GACD,kBAAkB,SAAS,CAAC,eAAe,GAAG,SAAU,MAAM,EAAE,GAAG;QAC/D,IAAI,CAAC,UAAU,CAAC,KAAK;YACjB,MAAM,IAAI,yOAAA,CAAA,UAAiB,CAAC;QAChC;QACA,IAAI,OAAO,CAAC,QAAQ;YAChB,OAAO,IAAI,CAAC,kBAAkB,CAAC;QACnC;QACA,OAAO,IAAI,CAAC,sBAAsB,CAAC;IACvC;IACA;;;;;;;;KAQC,GACD,kBAAkB,SAAS,CAAC,eAAe,GAAG,SAAU,MAAM,EAAE,GAAG;QAC/D,IAAI,CAAC,UAAU,CAAC,KAAK;YACjB,MAAM,IAAI,yOAAA,CAAA,UAAiB,CAAC;QAChC;QACA,IAAI,OAAO,CAAC,QAAQ;YAChB,OAAO,IAAI,CAAC,kBAAkB,CAAC;QACnC;QACA,OAAO,IAAI,CAAC,sBAAsB,CAAC;IACvC;IACA;;;;;;;;;;KAUC,GACD,kBAAkB,SAAS,CAAC,2BAA2B,GAAG,SAAU,MAAM,EAAE,GAAG,EAAE,UAAU;QACvF,IAAI,cAAc,UAAU,cAAc,KAAK;YAC3C,MAAM,IAAI,yOAAA,CAAA,UAAiB,CAAC;QAChC;QACA,IAAI,OAAO,CAAC,QAAQ;YAChB,OAAO,IAAI,CAAC,8BAA8B,CAAC,KAAK;QACpD;QACA,OAAO,IAAI,CAAC,kCAAkC,CAAC,QAAQ;IAC3D;IACA;;KAEC,GACD,kBAAkB,SAAS,CAAC,sBAAsB,GAAG,SAAU,MAAM;QACjE,IAAI,CAAC,QAAQ;YACT,MAAM,IAAI,yOAAA,CAAA,UAAiB,CAAC;QAChC;QACA,IAAI,CAAC,KAAK;QACV,IAAI,UAAU,IAAI,CAAC,mBAAmB,CAAC;QACvC,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI;QACJ,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU;YAC7B,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,OAAO;QAC3C,OACK;YACD,OAAO,IAAI,CAAC,kBAAkB,CAAC;QACnC;QACA,OAAO;IACX;IACA;;KAEC,GACD,kBAAkB,SAAS,CAAC,sBAAsB,GAAG,SAAU,MAAM;QACjE,IAAI,UAAU,IAAI,CAAC,4BAA4B,CAAC;QAChD,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC;IACA;;KAEC,GACD,kBAAkB,SAAS,CAAC,kCAAkC,GAAG,SAAU,MAAM,EAAE,UAAU;QACzF,IAAI,UAAU,IAAI,CAAC,4BAA4B,CAAC;QAChD,OAAO,IAAI,CAAC,8BAA8B,CAAC,SAAS;IACxD;IACA;;;;KAIC,GACD,kBAAkB,SAAS,CAAC,4BAA4B,GAAG,SAAU,MAAM;QACvE,IAAI,CAAC,QAAQ;YACT,MAAM,IAAI,yOAAA,CAAA,UAAiB,CAAC;QAChC;QACA,IAAI,CAAC,KAAK;QACV,IAAI,UAAU,IAAI,CAAC,mBAAmB,CAAC;QACvC,mDAAmD;QACnD,IAAI,CAAC,YAAY,GAAG;QACpB,OAAO;IACX;IACA;;KAEC,GACD,kBAAkB,SAAS,CAAC,kBAAkB,GAAG,SAAU,GAAG;QAC1D,IAAI,CAAC,KAAK;YACN,MAAM,IAAI,yOAAA,CAAA,UAAiB,CAAC;QAChC;QACA,IAAI,CAAC,KAAK;QACV,IAAI,UAAU,IAAI,CAAC,mBAAmB;QACtC,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,aAAa,IAAI,CAAC,kBAAkB,CAAC;QACzC,QAAQ,GAAG,GAAG;QACd,OAAO;IACX;IACA;;KAEC,GACD,kBAAkB,SAAS,CAAC,kBAAkB,GAAG,SAAU,GAAG;QAC1D,IAAI,CAAC,KAAK;YACN,MAAM,IAAI,yOAAA,CAAA,UAAiB,CAAC;QAChC;QACA,IAAI,CAAC,KAAK;QACV,wBAAwB;QACxB,IAAI,UAAU,IAAI,CAAC,mBAAmB;QACtC,IAAI,aAAa,IAAI,CAAC,sBAAsB,CAAC;QAC7C,QAAQ,GAAG,GAAG;QACd,OAAO;IACX;IACA;;;;KAIC,GACD,kBAAkB,SAAS,CAAC,8BAA8B,GAAG,SAAU,GAAG,EAAE,UAAU;QAClF,IAAI,CAAC,KAAK;YACN,MAAM,IAAI,yOAAA,CAAA,UAAiB,CAAC;QAChC;QACA,IAAI,CAAC,KAAK;QACV,wBAAwB;QACxB,IAAI,UAAU,IAAI,CAAC,mBAAmB;QACtC,IAAI,aAAa,IAAI,CAAC,kCAAkC,CAAC,SAAS;QAClE,QAAQ,GAAG,GAAG;QACd,OAAO;IACX;IACA,kBAAkB,SAAS,CAAC,kBAAkB,GAAG,SAAU,OAAO;QAC9D,IAAI,QAAQ,IAAI;QAChB,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;YACxC,MAAM,mBAAmB,GAAG;gBACxB,OAAO,MAAM,UAAU,CAAC,SAAS,OAAO,MAAM,IAAI,CAAC,SAAS;YAChE;YACA,QAAQ,gBAAgB,CAAC,QAAQ,MAAM,mBAAmB;QAC9D;IACJ;IACA,kBAAkB,SAAS,CAAC,kBAAkB,GAAG,SAAU,YAAY;QACnE,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBACL,kBAAkB;wBAClB,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,oBAAoB,CAAC;yBAAc;oBAC7D,KAAK;wBACD,kBAAkB;wBAClB,GAAG,IAAI;wBACP,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,UAAU,CAAC;yBAAc;oBACvD,KAAK;wBACL,yCAAyC;wBACzC,OAAO;4BAAC,EAAE,QAAQ;4BAAI,GAAG,IAAI;yBAAG;gBACpC;YACJ;QACJ;IACJ;IACA,kBAAkB,SAAS,CAAC,8BAA8B,GAAG,SAAU,YAAY,EAAE,UAAU;QAC3F,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBACL,kBAAkB;wBAClB,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,oBAAoB,CAAC;yBAAc;oBAC7D,KAAK;wBACD,kBAAkB;wBAClB,GAAG,IAAI;wBACP,yCAAyC;wBACzC,IAAI,CAAC,kBAAkB,CAAC,cAAc;wBACtC,OAAO;4BAAC,EAAE,QAAQ;yBAAG;gBAC7B;YACJ;QACJ;IACJ;IACA,kBAAkB,SAAS,CAAC,aAAa,GAAG,SAAU,GAAG;QACrD,mEAAmE;QACnE,qEAAqE;QACrE,8DAA8D;QAC9D,IAAI,CAAC,IAAI,QAAQ,EAAE;YACf,OAAO;QACX;QACA,qEAAqE;QACrE,qEAAqE;QACrE,2CAA2C;QAC3C,IAAI,IAAI,YAAY,KAAK,GAAG;YACxB,OAAO;QACX;QACA,4CAA4C;QAC5C,OAAO;IACX;IACA,kBAAkB,SAAS,CAAC,mBAAmB,GAAG,SAAU,WAAW;QACnE,IAAI;QACJ,IAAI,OAAO,gBAAgB,aAAa;YACpC,eAAe,SAAS,aAAa,CAAC;YACtC,aAAa,KAAK,GAAG;YACrB,aAAa,MAAM,GAAG;QAC1B;QACA,IAAI,OAAO,gBAAgB,UAAU;YACjC,eAAe,IAAI,CAAC,eAAe,CAAC,aAAa;QACrD;QACA,IAAI,uBAAuB,kBAAkB;YACzC,eAAe;QACnB;QACA,OAAO;IACX;IACA;;;;KAIC,GACD,kBAAkB,SAAS,CAAC,mBAAmB,GAAG,SAAU,WAAW;QACnE,IAAI;QACJ,IAAI,CAAC,eAAe,OAAO,aAAa,aAAa;YACjD,eAAe,SAAS,aAAa,CAAC;YACtC,aAAa,KAAK,GAAG;YACrB,aAAa,MAAM,GAAG;QAC1B;QACA,IAAI,OAAO,gBAAgB,UAAU;YACjC,eAAgB,IAAI,CAAC,eAAe,CAAC,aAAa;QACtD;QACA,IAAI,uBAAuB,kBAAkB;YACzC,eAAe;QACnB;QACA,oBAAoB;QACpB,aAAa,YAAY,CAAC,YAAY;QACtC,aAAa,YAAY,CAAC,SAAS;QACnC,aAAa,YAAY,CAAC,eAAe;QACzC,OAAO;IACX;IACA;;KAEC,GACD,kBAAkB,SAAS,CAAC,UAAU,GAAG,SAAU,OAAO,EAAE,eAAe,EAAE,4BAA4B;QACrG,IAAI,QAAQ,IAAI;QAChB,IAAI,oBAAoB,KAAK,GAAG;YAAE,kBAAkB;QAAM;QAC1D,IAAI,iCAAiC,KAAK,GAAG;YAAE,+BAA+B;QAAM;QACpF,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,OAAO,SAAU,OAAO,EAAE,MAAM;YAChC,IAAI,MAAM,gBAAgB,EAAE;gBACxB,OAAO,IAAI,yOAAA,CAAA,UAAiB,CAAC;gBAC7B,MAAM,gBAAgB,GAAG;gBACzB;YACJ;YACA,IAAI;gBACA,IAAI,SAAS,MAAM,MAAM,CAAC;gBAC1B,QAAQ;YACZ,EACA,OAAO,GAAG;gBACN,IAAI,aAAa,mBAAmB,aAAa,yOAAA,CAAA,UAAiB;gBAClE,IAAI,0BAA0B,aAAa,yOAAA,CAAA,UAAiB,IAAI,aAAa,uOAAA,CAAA,UAAe;gBAC5F,IAAI,qBAAqB,2BAA2B;gBACpD,IAAI,cAAc,oBAAoB;oBAClC,eAAe;oBACf,OAAO,WAAW,MAAM,MAAM,4BAA4B,EAAE,SAAS;gBACzE;gBACA,OAAO;YACX;QACJ;QACA,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;YAAI,OAAO,KAAK,SAAS;QAAS;IAClF;IACA;;KAEC,GACD,kBAAkB,SAAS,CAAC,kBAAkB,GAAG,SAAU,OAAO,EAAE,UAAU;QAC1E,IAAI,QAAQ,IAAI;QAChB,IAAI,CAAC,qBAAqB,GAAG;QAC7B,IAAI,OAAO;YACP,IAAI,MAAM,qBAAqB,EAAE;gBAC7B,MAAM,qBAAqB,GAAG;gBAC9B;YACJ;YACA,IAAI;gBACA,IAAI,SAAS,MAAM,MAAM,CAAC;gBAC1B,WAAW,QAAQ;gBACnB,WAAW,MAAM,MAAM,sBAAsB;YACjD,EACA,OAAO,GAAG;gBACN,WAAW,MAAM;gBACjB,IAAI,0BAA0B,aAAa,yOAAA,CAAA,UAAiB,IAAI,aAAa,uOAAA,CAAA,UAAe;gBAC5F,IAAI,aAAa,aAAa,yOAAA,CAAA,UAAiB;gBAC/C,IAAI,2BAA2B,YAAY;oBACvC,eAAe;oBACf,WAAW,MAAM,MAAM,4BAA4B;gBACvD;YACJ;QACJ;QACA;IACJ;IACA;;KAEC,GACD,kBAAkB,SAAS,CAAC,MAAM,GAAG,SAAU,OAAO;QAClD,wCAAwC;QACxC,IAAI,eAAe,IAAI,CAAC,kBAAkB,CAAC;QAC3C,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B;IACA;;;;KAIC,GACD,kBAAkB,SAAS,CAAC,kBAAkB,GAAG,SAAU,YAAY;QACnE,IAAI,MAAM,IAAI,CAAC,uBAAuB,CAAC;QACvC,6FAA6F;QAC7F,IAAI,eAAe;QACnB,IAAI,wBAAwB,kBAAkB;YAC1C,IAAI,CAAC,iBAAiB,CAAC;YACvB,eAAe;QACnB,OACK;YACD,IAAI,CAAC,iBAAiB,CAAC;QAC3B;QACA,IAAI,SAAS,IAAI,CAAC,gBAAgB,CAAC;QACnC,IAAI,kBAAkB,IAAI,2PAAA,CAAA,mCAAgC,CAAC,QAAQ;QACnE,IAAI,kBAAkB,IAAI,iPAAA,CAAA,UAAe,CAAC;QAC1C,OAAO,IAAI,oOAAA,CAAA,UAAY,CAAC;IAC5B;IACA;;KAEC,GACD,kBAAkB,SAAS,CAAC,uBAAuB,GAAG,SAAU,YAAY;QACxE,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC5B,IAAI,OAAO,IAAI,CAAC,gBAAgB,CAAC;YACjC,IAAI,MAAM,KAAK;YACf,IAAI;gBACA,MAAM,KAAK,UAAU,CAAC,MAAM;oBAAE,oBAAoB;gBAAK;YAC3D,EACA,OAAO,GAAG;gBACN,MAAM,KAAK,UAAU,CAAC;YAC1B;YACA,IAAI,CAAC,oBAAoB,GAAG;QAChC;QACA,OAAO,IAAI,CAAC,oBAAoB;IACpC;IACA;;KAEC,GACD,kBAAkB,SAAS,CAAC,gBAAgB,GAAG,SAAU,YAAY;QACjE,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,IAAI,OAAO,IAAI,CAAC,mBAAmB,CAAC;YACpC,IAAI,CAAC,aAAa,GAAG;QACzB;QACA,OAAO,IAAI,CAAC,aAAa;IAC7B;IACA;;KAEC,GACD,kBAAkB,SAAS,CAAC,iBAAiB,GAAG,SAAU,UAAU,EAAE,UAAU,EAAE,oBAAoB;QAClG,IAAI,eAAe,KAAK,GAAG;YAAE,aAAa;gBACtC,IAAI;gBACJ,IAAI;gBACJ,QAAQ,WAAW,UAAU;gBAC7B,SAAS,WAAW,WAAW;gBAC/B,IAAI;gBACJ,IAAI;gBACJ,QAAQ,WAAW,UAAU;gBAC7B,SAAS,WAAW,WAAW;YACnC;QAAG;QACH,IAAI,yBAAyB,KAAK,GAAG;YAAE,uBAAuB,IAAI,CAAC,oBAAoB;QAAE;QACzF,qBAAqB,SAAS,CAAC,YAAY,WAAW,EAAE,EAAE,WAAW,EAAE,EAAE,WAAW,MAAM,EAAE,WAAW,OAAO,EAAE,WAAW,EAAE,EAAE,WAAW,EAAE,EAAE,WAAW,MAAM,EAAE,WAAW,OAAO;IACvL;IACA;;KAEC,GACD,kBAAkB,SAAS,CAAC,iBAAiB,GAAG,SAAU,UAAU,EAAE,UAAU,EAAE,oBAAoB;QAClG,IAAI,eAAe,KAAK,GAAG;YAAE,aAAa;gBACtC,IAAI;gBACJ,IAAI;gBACJ,QAAQ,WAAW,YAAY;gBAC/B,SAAS,WAAW,aAAa;gBACjC,IAAI;gBACJ,IAAI;gBACJ,QAAQ,WAAW,YAAY;gBAC/B,SAAS,WAAW,aAAa;YACrC;QAAG;QACH,IAAI,yBAAyB,KAAK,GAAG;YAAE,uBAAuB,IAAI,CAAC,oBAAoB;QAAE;QACzF,qBAAqB,SAAS,CAAC,YAAY,WAAW,EAAE,EAAE,WAAW,EAAE,EAAE,WAAW,MAAM,EAAE,WAAW,OAAO,EAAE,WAAW,EAAE,EAAE,WAAW,EAAE,EAAE,WAAW,MAAM,EAAE,WAAW,OAAO;IACvL;IACA;;KAEC,GACD,kBAAkB,SAAS,CAAC,YAAY,GAAG,SAAU,YAAY;QAC7D,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,IAAI,CAAC,MAAM;IACvD;IACA;;KAEC,GACD,kBAAkB,SAAS,CAAC,mBAAmB,GAAG,SAAU,YAAY;QACpE,IAAI,OAAO,aAAa,aAAa;YACjC,IAAI,CAAC,qBAAqB;YAC1B,OAAO;QACX;QACA,IAAI,gBAAgB,SAAS,aAAa,CAAC;QAC3C,IAAI;QACJ,IAAI;QACJ,IAAI,OAAO,iBAAiB,aAAa;YACrC,IAAI,wBAAwB,kBAAkB;gBAC1C,QAAQ,aAAa,UAAU;gBAC/B,SAAS,aAAa,WAAW;YACrC,OACK,IAAI,wBAAwB,kBAAkB;gBAC/C,QAAQ,aAAa,YAAY,IAAI,aAAa,KAAK;gBACvD,SAAS,aAAa,aAAa,IAAI,aAAa,MAAM;YAC9D;QACJ;QACA,cAAc,KAAK,CAAC,KAAK,GAAG,QAAQ;QACpC,cAAc,KAAK,CAAC,MAAM,GAAG,SAAS;QACtC,cAAc,KAAK,GAAG;QACtB,cAAc,MAAM,GAAG;QACvB,OAAO;IACX;IACA;;KAEC,GACD,kBAAkB,SAAS,CAAC,WAAW,GAAG;QACtC,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC,cAAc,GAAG,OAAO,CAAC,SAAU,CAAC;gBAAI,OAAO,EAAE,IAAI;YAAI;YACrE,IAAI,CAAC,MAAM,GAAG;QAClB;QACA,IAAI,IAAI,CAAC,gBAAgB,KAAK,OAAO;YACjC,IAAI,CAAC,eAAe;QACxB;QACA,IAAI,IAAI,CAAC,qBAAqB,KAAK,OAAO;YACtC,IAAI,CAAC,oBAAoB;QAC7B;IACJ;IACA;;;;KAIC,GACD,kBAAkB,SAAS,CAAC,KAAK,GAAG;QAChC,wCAAwC;QACxC,IAAI,CAAC,WAAW;QAChB,uCAAuC;QACvC,IAAI,CAAC,oBAAoB;QACzB,IAAI,CAAC,oBAAoB;QACzB,IAAI,CAAC,qBAAqB;IAC9B;IACA,kBAAkB,SAAS,CAAC,oBAAoB,GAAG;QAC/C,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB;QACJ;QACA,wCAAwC;QACxC,IAAI,OAAO,IAAI,CAAC,kBAAkB,KAAK,aAAa;YAChD,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,SAAS,IAAI,CAAC,kBAAkB;QAC1E;QACA,IAAI,OAAO,IAAI,CAAC,yBAAyB,KAAK,aAAa;YACvD,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,WAAW,IAAI,CAAC,yBAAyB;QACnF;QACA,IAAI,OAAO,IAAI,CAAC,oBAAoB,KAAK,aAAa;YAClD,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,kBAAkB,IAAI,CAAC,oBAAoB;QACrF;QACA,qCAAqC;QACrC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY;QACvC,IAAI,CAAC,YAAY,GAAG;IACxB;IACA,kBAAkB,SAAS,CAAC,oBAAoB,GAAG;QAC/C,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB;QACJ;QACA,wCAAwC;QACxC,IAAI,cAAc,IAAI,CAAC,mBAAmB,EAAE;YACxC,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,QAAQ,IAAI,CAAC,mBAAmB;QAC1E;QACA,oCAAoC;QACpC,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG;QACxB,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC;QAClC,IAAI,CAAC,YAAY,GAAG;IACxB;IACA;;KAEC,GACD,kBAAkB,SAAS,CAAC,qBAAqB,GAAG;QAChD,oCAAoC;QACpC,IAAI,CAAC,oBAAoB,GAAG;QAC5B,IAAI,CAAC,aAAa,GAAG;IACzB;IACA;;;;;KAKC,GACD,kBAAkB,SAAS,CAAC,cAAc,GAAG,SAAU,YAAY,EAAE,MAAM;QACvE,0CAA0C;QAC1C,IAAI;YACA,gEAAgE;YAChE,aAAa,SAAS,GAAG;QAC7B,EACA,OAAO,KAAK;YACR,+DAA+D;YAC/D,aAAa;YACb,aAAa,GAAG,GAAG,IAAI,eAAe,CAAC;QAC3C;IACJ;IACA;;;;KAIC,GACD,kBAAkB,SAAS,CAAC,gBAAgB,GAAG,SAAU,YAAY;QACjE,IAAI;YACA,aAAa,SAAS,GAAG;QAC7B,EACA,OAAO,KAAK;YACR,aAAa,GAAG,GAAG;QACvB;QACA,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC;IACtC;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1519, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/browser/BrowserAztecCodeReader.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport { BrowserCodeReader } from './BrowserCodeReader';\nimport AztecReader from '../core/aztec/AztecReader';\n/**\n * Aztec Code reader to use from browser.\n *\n * @class BrowserAztecCodeReader\n * @extends {BrowserCodeReader}\n */\nvar BrowserAztecCodeReader = /** @class */ (function (_super) {\n    __extends(BrowserAztecCodeReader, _super);\n    /**\n     * Creates an instance of BrowserAztecCodeReader.\n     * @param {number} [timeBetweenScansMillis=500] the time delay between subsequent decode tries\n     *\n     * @memberOf BrowserAztecCodeReader\n     */\n    function BrowserAztecCodeReader(timeBetweenScansMillis) {\n        if (timeBetweenScansMillis === void 0) { timeBetweenScansMillis = 500; }\n        return _super.call(this, new AztecReader(), timeBetweenScansMillis) || this;\n    }\n    return BrowserAztecCodeReader;\n}(BrowserCodeReader));\nexport { BrowserAztecCodeReader };\n"], "names": [], "mappings": ";;;AAaA;AACA;AAdA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;AAGA;;;;;CAKC,GACD,IAAI,yBAAwC,SAAU,MAAM;IACxD,UAAU,wBAAwB;IAClC;;;;;KAKC,GACD,SAAS,uBAAuB,sBAAsB;QAClD,IAAI,2BAA2B,KAAK,GAAG;YAAE,yBAAyB;QAAK;QACvE,OAAO,OAAO,IAAI,CAAC,IAAI,EAAE,IAAI,4OAAA,CAAA,UAAW,IAAI,2BAA2B,IAAI;IAC/E;IACA,OAAO;AACX,EAAE,4OAAA,CAAA,oBAAiB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1570, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/browser/BrowserBarcodeReader.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport { BrowserCodeReader } from './BrowserCodeReader';\nimport MultiFormatOneDReader from '../core/oned/MultiFormatOneDReader';\n/**\n * @deprecated Moving to @zxing/browser\n *\n * Barcode reader reader to use from browser.\n */\nvar BrowserBarcodeReader = /** @class */ (function (_super) {\n    __extends(BrowserBarcodeReader, _super);\n    /**\n     * Creates an instance of BrowserBarcodeReader.\n     * @param {number} [timeBetweenScansMillis=500] the time delay between subsequent decode tries\n     * @param {Map<DecodeHintType, any>} hints\n     */\n    function BrowserBarcodeReader(timeBetweenScansMillis, hints) {\n        if (timeBetweenScansMillis === void 0) { timeBetweenScansMillis = 500; }\n        return _super.call(this, new MultiFormatOneDReader(hints), timeBetweenScansMillis, hints) || this;\n    }\n    return BrowserBarcodeReader;\n}(BrowserCodeReader));\nexport { BrowserBarcodeReader };\n"], "names": [], "mappings": ";;;AAaA;AACA;AAdA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;AAGA;;;;CAIC,GACD,IAAI,uBAAsC,SAAU,MAAM;IACtD,UAAU,sBAAsB;IAChC;;;;KAIC,GACD,SAAS,qBAAqB,sBAAsB,EAAE,KAAK;QACvD,IAAI,2BAA2B,KAAK,GAAG;YAAE,yBAAyB;QAAK;QACvE,OAAO,OAAO,IAAI,CAAC,IAAI,EAAE,IAAI,qPAAA,CAAA,UAAqB,CAAC,QAAQ,wBAAwB,UAAU,IAAI;IACrG;IACA,OAAO;AACX,EAAE,4OAAA,CAAA,oBAAiB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1619, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/browser/BrowserDatamatrixCodeReader.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport { BrowserCodeReader } from './BrowserCodeReader';\nimport DataMatrixReader from '../core/datamatrix/DataMatrixReader';\n/**\n * @deprecated Moving to @zxing/browser\n *\n * QR Code reader to use from browser.\n */\nvar BrowserDatamatrixCodeReader = /** @class */ (function (_super) {\n    __extends(BrowserDatamatrixCodeReader, _super);\n    /**\n     * Creates an instance of BrowserQRCodeReader.\n     * @param {number} [timeBetweenScansMillis=500] the time delay between subsequent decode tries\n     */\n    function BrowserDatamatrixCodeReader(timeBetweenScansMillis) {\n        if (timeBetweenScansMillis === void 0) { timeBetweenScansMillis = 500; }\n        return _super.call(this, new DataMatrixReader(), timeBetweenScansMillis) || this;\n    }\n    return BrowserDatamatrixCodeReader;\n}(BrowserCodeReader));\nexport { BrowserDatamatrixCodeReader };\n"], "names": [], "mappings": ";;;AAaA;AACA;AAdA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;AAGA;;;;CAIC,GACD,IAAI,8BAA6C,SAAU,MAAM;IAC7D,UAAU,6BAA6B;IACvC;;;KAGC,GACD,SAAS,4BAA4B,sBAAsB;QACvD,IAAI,2BAA2B,KAAK,GAAG;YAAE,yBAAyB;QAAK;QACvE,OAAO,OAAO,IAAI,CAAC,IAAI,EAAE,IAAI,sPAAA,CAAA,UAAgB,IAAI,2BAA2B,IAAI;IACpF;IACA,OAAO;AACX,EAAE,4OAAA,CAAA,oBAAiB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1667, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/browser/BrowserMultiFormatReader.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport { BrowserCodeReader } from './BrowserCodeReader';\nimport MultiFormatReader from '../core/MultiFormatReader';\nvar BrowserMultiFormatReader = /** @class */ (function (_super) {\n    __extends(BrowserMultiFormatReader, _super);\n    function BrowserMultiFormatReader(hints, timeBetweenScansMillis) {\n        if (hints === void 0) { hints = null; }\n        if (timeBetweenScansMillis === void 0) { timeBetweenScansMillis = 500; }\n        var _this = this;\n        var reader = new MultiFormatReader();\n        reader.setHints(hints);\n        _this = _super.call(this, reader, timeBetweenScansMillis) || this;\n        return _this;\n    }\n    /**\n     * Overwrite decodeBitmap to call decodeWithState, which will pay\n     * attention to the hints set in the constructor function\n     */\n    BrowserMultiFormatReader.prototype.decodeBitmap = function (binaryBitmap) {\n        return this.reader.decodeWithState(binaryBitmap);\n    };\n    return BrowserMultiFormatReader;\n}(BrowserCodeReader));\nexport { BrowserMultiFormatReader };\n"], "names": [], "mappings": ";;;AAaA;AACA;AAdA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;AAGA,IAAI,2BAA0C,SAAU,MAAM;IAC1D,UAAU,0BAA0B;IACpC,SAAS,yBAAyB,KAAK,EAAE,sBAAsB;QAC3D,IAAI,UAAU,KAAK,GAAG;YAAE,QAAQ;QAAM;QACtC,IAAI,2BAA2B,KAAK,GAAG;YAAE,yBAAyB;QAAK;QACvE,IAAI,QAAQ,IAAI;QAChB,IAAI,SAAS,IAAI,yOAAA,CAAA,UAAiB;QAClC,OAAO,QAAQ,CAAC;QAChB,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,QAAQ,2BAA2B,IAAI;QACjE,OAAO;IACX;IACA;;;KAGC,GACD,yBAAyB,SAAS,CAAC,YAAY,GAAG,SAAU,YAAY;QACpE,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC;IACvC;IACA,OAAO;AACX,EAAE,4OAAA,CAAA,oBAAiB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1721, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/browser/BrowserPDF417Reader.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport { BrowserCodeReader } from './BrowserCodeReader';\nimport PDF417Reader from '../core/pdf417/PDF417Reader';\n/**\n * @deprecated Moving to @zxing/browser\n *\n * QR Code reader to use from browser.\n */\nvar BrowserPDF417Reader = /** @class */ (function (_super) {\n    __extends(BrowserPDF417Reader, _super);\n    /**\n     * Creates an instance of BrowserPDF417Reader.\n     * @param {number} [timeBetweenScansMillis=500] the time delay between subsequent decode tries\n     */\n    function BrowserPDF417Reader(timeBetweenScansMillis) {\n        if (timeBetweenScansMillis === void 0) { timeBetweenScansMillis = 500; }\n        return _super.call(this, new PDF417Reader(), timeBetweenScansMillis) || this;\n    }\n    return BrowserPDF417Reader;\n}(BrowserCodeReader));\nexport { BrowserPDF417Reader };\n"], "names": [], "mappings": ";;;AAaA;AACA;AAdA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;AAGA;;;;CAIC,GACD,IAAI,sBAAqC,SAAU,MAAM;IACrD,UAAU,qBAAqB;IAC/B;;;KAGC,GACD,SAAS,oBAAoB,sBAAsB;QAC/C,IAAI,2BAA2B,KAAK,GAAG;YAAE,yBAAyB;QAAK;QACvE,OAAO,OAAO,IAAI,CAAC,IAAI,EAAE,IAAI,8OAAA,CAAA,UAAY,IAAI,2BAA2B,IAAI;IAChF;IACA,OAAO;AACX,EAAE,4OAAA,CAAA,oBAAiB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1769, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/browser/BrowserQRCodeReader.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport { BrowserCodeReader } from './BrowserCodeReader';\nimport QRCodeReader from '../core/qrcode/QRCodeReader';\n/**\n * @deprecated Moving to @zxing/browser\n *\n * QR Code reader to use from browser.\n */\nvar BrowserQRCodeReader = /** @class */ (function (_super) {\n    __extends(BrowserQRCodeReader, _super);\n    /**\n     * Creates an instance of BrowserQRCodeReader.\n     * @param {number} [timeBetweenScansMillis=500] the time delay between subsequent decode tries\n     */\n    function BrowserQRCodeReader(timeBetweenScansMillis) {\n        if (timeBetweenScansMillis === void 0) { timeBetweenScansMillis = 500; }\n        return _super.call(this, new QRCodeReader(), timeBetweenScansMillis) || this;\n    }\n    return BrowserQRCodeReader;\n}(BrowserCodeReader));\nexport { BrowserQRCodeReader };\n"], "names": [], "mappings": ";;;AAaA;AACA;AAdA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QAC7E,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;;AAGA;;;;CAIC,GACD,IAAI,sBAAqC,SAAU,MAAM;IACrD,UAAU,qBAAqB;IAC/B;;;KAGC,GACD,SAAS,oBAAoB,sBAAsB;QAC/C,IAAI,2BAA2B,KAAK,GAAG;YAAE,yBAAyB;QAAK;QACvE,OAAO,OAAO,IAAI,CAAC,IAAI,EAAE,IAAI,8OAAA,CAAA,UAAY,IAAI,2BAA2B,IAAI;IAChF;IACA,OAAO;AACX,EAAE,4OAAA,CAAA,oBAAiB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1817, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/browser/BrowserQRCodeSvgWriter.js"], "sourcesContent": ["import EncodeHintType from '../core/EncodeHintType';\nimport Encoder from '../core/qrcode/encoder/Encoder';\nimport ErrorCorrectionLevel from '../core/qrcode/decoder/ErrorCorrectionLevel';\nimport IllegalArgumentException from '../core/IllegalArgumentException';\nimport IllegalStateException from '../core/IllegalStateException';\n/**\n * @deprecated Moving to @zxing/browser\n */\nvar BrowserQRCodeSvgWriter = /** @class */ (function () {\n    function BrowserQRCodeSvgWriter() {\n    }\n    /**\n     * Writes and renders a QRCode SVG element.\n     *\n     * @param contents\n     * @param width\n     * @param height\n     * @param hints\n     */\n    BrowserQRCodeSvgWriter.prototype.write = function (contents, width, height, hints) {\n        if (hints === void 0) { hints = null; }\n        if (contents.length === 0) {\n            throw new IllegalArgumentException('Found empty contents');\n        }\n        // if (format != BarcodeFormat.QR_CODE) {\n        //   throw new IllegalArgumentException(\"Can only encode QR_CODE, but got \" + format)\n        // }\n        if (width < 0 || height < 0) {\n            throw new IllegalArgumentException('Requested dimensions are too small: ' + width + 'x' + height);\n        }\n        var errorCorrectionLevel = ErrorCorrectionLevel.L;\n        var quietZone = BrowserQRCodeSvgWriter.QUIET_ZONE_SIZE;\n        if (hints !== null) {\n            if (undefined !== hints.get(EncodeHintType.ERROR_CORRECTION)) {\n                errorCorrectionLevel = ErrorCorrectionLevel.fromString(hints.get(EncodeHintType.ERROR_CORRECTION).toString());\n            }\n            if (undefined !== hints.get(EncodeHintType.MARGIN)) {\n                quietZone = Number.parseInt(hints.get(EncodeHintType.MARGIN).toString(), 10);\n            }\n        }\n        var code = Encoder.encode(contents, errorCorrectionLevel, hints);\n        return this.renderResult(code, width, height, quietZone);\n    };\n    /**\n     * Renders the result and then appends it to the DOM.\n     */\n    BrowserQRCodeSvgWriter.prototype.writeToDom = function (containerElement, contents, width, height, hints) {\n        if (hints === void 0) { hints = null; }\n        if (typeof containerElement === 'string') {\n            containerElement = document.querySelector(containerElement);\n        }\n        var svgElement = this.write(contents, width, height, hints);\n        if (containerElement)\n            containerElement.appendChild(svgElement);\n    };\n    /**\n     * Note that the input matrix uses 0 == white, 1 == black.\n     * The output matrix uses 0 == black, 255 == white (i.e. an 8 bit greyscale bitmap).\n     */\n    BrowserQRCodeSvgWriter.prototype.renderResult = function (code, width /*int*/, height /*int*/, quietZone /*int*/) {\n        var input = code.getMatrix();\n        if (input === null) {\n            throw new IllegalStateException();\n        }\n        var inputWidth = input.getWidth();\n        var inputHeight = input.getHeight();\n        var qrWidth = inputWidth + (quietZone * 2);\n        var qrHeight = inputHeight + (quietZone * 2);\n        var outputWidth = Math.max(width, qrWidth);\n        var outputHeight = Math.max(height, qrHeight);\n        var multiple = Math.min(Math.floor(outputWidth / qrWidth), Math.floor(outputHeight / qrHeight));\n        // Padding includes both the quiet zone and the extra white pixels to accommodate the requested\n        // dimensions. For example, if input is 25x25 the QR will be 33x33 including the quiet zone.\n        // If the requested size is 200x160, the multiple will be 4, for a QR of 132x132. These will\n        // handle all the padding from 100x100 (the actual QR) up to 200x160.\n        var leftPadding = Math.floor((outputWidth - (inputWidth * multiple)) / 2);\n        var topPadding = Math.floor((outputHeight - (inputHeight * multiple)) / 2);\n        var svgElement = this.createSVGElement(outputWidth, outputHeight);\n        for (var inputY = 0, outputY = topPadding; inputY < inputHeight; inputY++, outputY += multiple) {\n            // Write the contents of this row of the barcode\n            for (var inputX = 0, outputX = leftPadding; inputX < inputWidth; inputX++, outputX += multiple) {\n                if (input.get(inputX, inputY) === 1) {\n                    var svgRectElement = this.createSvgRectElement(outputX, outputY, multiple, multiple);\n                    svgElement.appendChild(svgRectElement);\n                }\n            }\n        }\n        return svgElement;\n    };\n    /**\n     * Creates a SVG element.\n     *\n     * @param w SVG's width attribute\n     * @param h SVG's height attribute\n     */\n    BrowserQRCodeSvgWriter.prototype.createSVGElement = function (w, h) {\n        var svgElement = document.createElementNS(BrowserQRCodeSvgWriter.SVG_NS, 'svg');\n        svgElement.setAttributeNS(null, 'height', w.toString());\n        svgElement.setAttributeNS(null, 'width', h.toString());\n        return svgElement;\n    };\n    /**\n     * Creates a SVG rect element.\n     *\n     * @param x Element's x coordinate\n     * @param y Element's y coordinate\n     * @param w Element's width attribute\n     * @param h Element's height attribute\n     */\n    BrowserQRCodeSvgWriter.prototype.createSvgRectElement = function (x, y, w, h) {\n        var rect = document.createElementNS(BrowserQRCodeSvgWriter.SVG_NS, 'rect');\n        rect.setAttributeNS(null, 'x', x.toString());\n        rect.setAttributeNS(null, 'y', y.toString());\n        rect.setAttributeNS(null, 'height', w.toString());\n        rect.setAttributeNS(null, 'width', h.toString());\n        rect.setAttributeNS(null, 'fill', '#000000');\n        return rect;\n    };\n    BrowserQRCodeSvgWriter.QUIET_ZONE_SIZE = 4;\n    /**\n     * SVG markup NameSpace\n     */\n    BrowserQRCodeSvgWriter.SVG_NS = 'http://www.w3.org/2000/svg';\n    return BrowserQRCodeSvgWriter;\n}());\nexport { BrowserQRCodeSvgWriter };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AACA;;CAEC,GACD,IAAI,yBAAwC;IACxC,SAAS,0BACT;IACA;;;;;;;KAOC,GACD,uBAAuB,SAAS,CAAC,KAAK,GAAG,SAAU,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK;QAC7E,IAAI,UAAU,KAAK,GAAG;YAAE,QAAQ;QAAM;QACtC,IAAI,SAAS,MAAM,KAAK,GAAG;YACvB,MAAM,IAAI,gPAAA,CAAA,UAAwB,CAAC;QACvC;QACA,yCAAyC;QACzC,qFAAqF;QACrF,IAAI;QACJ,IAAI,QAAQ,KAAK,SAAS,GAAG;YACzB,MAAM,IAAI,gPAAA,CAAA,UAAwB,CAAC,yCAAyC,QAAQ,MAAM;QAC9F;QACA,IAAI,uBAAuB,iQAAA,CAAA,UAAoB,CAAC,CAAC;QACjD,IAAI,YAAY,uBAAuB,eAAe;QACtD,IAAI,UAAU,MAAM;YAChB,IAAI,cAAc,MAAM,GAAG,CAAC,sOAAA,CAAA,UAAc,CAAC,gBAAgB,GAAG;gBAC1D,uBAAuB,iQAAA,CAAA,UAAoB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,sOAAA,CAAA,UAAc,CAAC,gBAAgB,EAAE,QAAQ;YAC9G;YACA,IAAI,cAAc,MAAM,GAAG,CAAC,sOAAA,CAAA,UAAc,CAAC,MAAM,GAAG;gBAChD,YAAY,OAAO,QAAQ,CAAC,MAAM,GAAG,CAAC,sOAAA,CAAA,UAAc,CAAC,MAAM,EAAE,QAAQ,IAAI;YAC7E;QACJ;QACA,IAAI,OAAO,oPAAA,CAAA,UAAO,CAAC,MAAM,CAAC,UAAU,sBAAsB;QAC1D,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,OAAO,QAAQ;IAClD;IACA;;KAEC,GACD,uBAAuB,SAAS,CAAC,UAAU,GAAG,SAAU,gBAAgB,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK;QACpG,IAAI,UAAU,KAAK,GAAG;YAAE,QAAQ;QAAM;QACtC,IAAI,OAAO,qBAAqB,UAAU;YACtC,mBAAmB,SAAS,aAAa,CAAC;QAC9C;QACA,IAAI,aAAa,IAAI,CAAC,KAAK,CAAC,UAAU,OAAO,QAAQ;QACrD,IAAI,kBACA,iBAAiB,WAAW,CAAC;IACrC;IACA;;;KAGC,GACD,uBAAuB,SAAS,CAAC,YAAY,GAAG,SAAU,IAAI,EAAE,MAAM,KAAK,GAAN,EAAU,OAAO,KAAK,GAAN,EAAU,UAAU,KAAK,GAAN;QACpG,IAAI,QAAQ,KAAK,SAAS;QAC1B,IAAI,UAAU,MAAM;YAChB,MAAM,IAAI,6OAAA,CAAA,UAAqB;QACnC;QACA,IAAI,aAAa,MAAM,QAAQ;QAC/B,IAAI,cAAc,MAAM,SAAS;QACjC,IAAI,UAAU,aAAc,YAAY;QACxC,IAAI,WAAW,cAAe,YAAY;QAC1C,IAAI,cAAc,KAAK,GAAG,CAAC,OAAO;QAClC,IAAI,eAAe,KAAK,GAAG,CAAC,QAAQ;QACpC,IAAI,WAAW,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,cAAc,UAAU,KAAK,KAAK,CAAC,eAAe;QACrF,+FAA+F;QAC/F,4FAA4F;QAC5F,4FAA4F;QAC5F,qEAAqE;QACrE,IAAI,cAAc,KAAK,KAAK,CAAC,CAAC,cAAe,aAAa,QAAS,IAAI;QACvE,IAAI,aAAa,KAAK,KAAK,CAAC,CAAC,eAAgB,cAAc,QAAS,IAAI;QACxE,IAAI,aAAa,IAAI,CAAC,gBAAgB,CAAC,aAAa;QACpD,IAAK,IAAI,SAAS,GAAG,UAAU,YAAY,SAAS,aAAa,UAAU,WAAW,SAAU;YAC5F,gDAAgD;YAChD,IAAK,IAAI,SAAS,GAAG,UAAU,aAAa,SAAS,YAAY,UAAU,WAAW,SAAU;gBAC5F,IAAI,MAAM,GAAG,CAAC,QAAQ,YAAY,GAAG;oBACjC,IAAI,iBAAiB,IAAI,CAAC,oBAAoB,CAAC,SAAS,SAAS,UAAU;oBAC3E,WAAW,WAAW,CAAC;gBAC3B;YACJ;QACJ;QACA,OAAO;IACX;IACA;;;;;KAKC,GACD,uBAAuB,SAAS,CAAC,gBAAgB,GAAG,SAAU,CAAC,EAAE,CAAC;QAC9D,IAAI,aAAa,SAAS,eAAe,CAAC,uBAAuB,MAAM,EAAE;QACzE,WAAW,cAAc,CAAC,MAAM,UAAU,EAAE,QAAQ;QACpD,WAAW,cAAc,CAAC,MAAM,SAAS,EAAE,QAAQ;QACnD,OAAO;IACX;IACA;;;;;;;KAOC,GACD,uBAAuB,SAAS,CAAC,oBAAoB,GAAG,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QACxE,IAAI,OAAO,SAAS,eAAe,CAAC,uBAAuB,MAAM,EAAE;QACnE,KAAK,cAAc,CAAC,MAAM,KAAK,EAAE,QAAQ;QACzC,KAAK,cAAc,CAAC,MAAM,KAAK,EAAE,QAAQ;QACzC,KAAK,cAAc,CAAC,MAAM,UAAU,EAAE,QAAQ;QAC9C,KAAK,cAAc,CAAC,MAAM,SAAS,EAAE,QAAQ;QAC7C,KAAK,cAAc,CAAC,MAAM,QAAQ;QAClC,OAAO;IACX;IACA,uBAAuB,eAAe,GAAG;IACzC;;KAEC,GACD,uBAAuB,MAAM,GAAG;IAChC,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1951, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1956, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1960, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/browser.js"], "sourcesContent": ["// browser\nexport * from './browser/BrowserAztecCodeReader';\nexport * from './browser/BrowserBarcodeReader';\nexport * from './browser/BrowserCodeReader';\nexport * from './browser/BrowserDatamatrixCodeReader';\nexport * from './browser/BrowserMultiFormatReader';\nexport * from './browser/BrowserPDF417Reader';\nexport * from './browser/BrowserQRCodeReader';\nexport * from './browser/BrowserQRCodeSvgWriter';\nexport * from './browser/DecodeContinuouslyCallback';\nexport * from './browser/HTMLCanvasElementLuminanceSource';\nexport * from './browser/HTMLVisualMediaElement';\nexport * from './browser/VideoInputDevice';\n"], "names": [], "mappings": "AAAA,UAAU;;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2008, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/lansia/client/node_modules/.pnpm/%40zxing%2Blibrary%400.21.3/node_modules/%40zxing/library/esm/index.js"], "sourcesContent": ["export * from './browser';\n// Exceptions\nexport { default as ArgumentException } from './core/ArgumentException';\nexport { default as ArithmeticException } from './core/ArithmeticException';\nexport { default as ChecksumException } from './core/ChecksumException';\nexport { default as Exception } from './core/Exception';\nexport { default as FormatException } from './core/FormatException';\nexport { default as IllegalArgumentException } from './core/IllegalArgumentException';\nexport { default as IllegalStateException } from './core/IllegalStateException';\nexport { default as NotFoundException } from './core/NotFoundException';\nexport { default as ReaderException } from './core/ReaderException';\nexport { default as ReedSolomonException } from './core/ReedSolomonException';\nexport { default as UnsupportedOperationException } from './core/UnsupportedOperationException';\nexport { default as WriterException } from './core/WriterException';\n// core\nexport { default as BarcodeFormat } from './core/BarcodeFormat';\nexport { default as Binarizer } from './core/Binarizer';\nexport { default as BinaryBitmap } from './core/BinaryBitmap';\nexport { default as DecodeHintType } from './core/DecodeHintType';\nexport { default as InvertedLuminanceSource } from './core/InvertedLuminanceSource';\nexport { default as LuminanceSource } from './core/LuminanceSource';\nexport { default as MultiFormatReader } from './core/MultiFormatReader';\nexport { default as MultiFormatWriter } from './core/MultiFormatWriter';\nexport { default as PlanarYUVLuminanceSource } from './core/PlanarYUVLuminanceSource';\nexport { default as Result } from './core/Result';\nexport { default as ResultMetadataType } from './core/ResultMetadataType';\nexport { default as RGBLuminanceSource } from './core/RGBLuminanceSource';\nexport { default as ResultPoint } from './core/ResultPoint';\n// core/util\nexport { default as ZXingSystem } from './core/util/System';\nexport { default as ZXingStringBuilder } from './core/util/StringBuilder';\nexport { default as ZXingStringEncoding } from './core/util/StringEncoding';\nexport { default as ZXingCharset } from './core/util/Charset';\nexport { default as ZXingArrays } from './core/util/Arrays';\nexport { default as ZXingStandardCharsets } from './core/util/StandardCharsets';\nexport { default as ZXingInteger } from './core/util/Integer';\n// core/common\nexport { default as BitArray } from './core/common/BitArray';\nexport { default as BitMatrix } from './core/common/BitMatrix';\nexport { default as BitSource } from './core/common/BitSource';\nexport { default as CharacterSetECI } from './core/common/CharacterSetECI';\nexport { default as DecoderResult } from './core/common/DecoderResult';\nexport { default as DefaultGridSampler } from './core/common/DefaultGridSampler';\nexport { default as DetectorResult } from './core/common/DetectorResult';\nexport { default as EncodeHintType } from './core/EncodeHintType';\nexport { default as GlobalHistogramBinarizer } from './core/common/GlobalHistogramBinarizer';\nexport { default as GridSampler } from './core/common/GridSampler';\nexport { default as GridSamplerInstance } from './core/common/GridSamplerInstance';\nexport { default as HybridBinarizer } from './core/common/HybridBinarizer';\nexport { default as PerspectiveTransform } from './core/common/PerspectiveTransform';\nexport { default as StringUtils } from './core/common/StringUtils';\n// core/common/detector\nexport { default as MathUtils } from './core/common/detector/MathUtils';\n// export { default as MonochromeRectangleDetector } from './core/common/detector/MonochromeRectangleDetector';\nexport { default as WhiteRectangleDetector } from './core/common/detector/WhiteRectangleDetector';\n// core/common/reedsolomon\nexport { default as GenericGF } from './core/common/reedsolomon/GenericGF';\nexport { default as GenericGFPoly } from './core/common/reedsolomon/GenericGFPoly';\nexport { default as ReedSolomonDecoder } from './core/common/reedsolomon/ReedSolomonDecoder';\nexport { default as ReedSolomonEncoder } from './core/common/reedsolomon/ReedSolomonEncoder';\n// core/datamatrix\nexport { default as DataMatrixReader } from './core/datamatrix/DataMatrixReader';\nexport { default as DataMatrixDecodedBitStreamParser } from './core/datamatrix/decoder/DecodedBitStreamParser';\nexport { default as DataMatrixDefaultPlacement } from './core/datamatrix/encoder/DefaultPlacement';\nexport { default as DataMatrixErrorCorrection } from './core/datamatrix/encoder/ErrorCorrection';\nexport { default as DataMatrixHighLevelEncoder } from './core/datamatrix/encoder/HighLevelEncoder';\nexport { default as DataMatrixSymbolInfo } from './core/datamatrix/encoder/SymbolInfo';\nexport { SymbolShapeHint as DataMatrixSymbolShapeHint } from './core/datamatrix/encoder/constants';\nexport { default as DataMatrixWriter } from './core/datamatrix/DataMatrixWriter';\n// core/pdf417\nexport { default as PDF417Reader } from './core/pdf417/PDF417Reader';\nexport { default as PDF417ResultMetadata } from './core/pdf417/PDF417ResultMetadata';\nexport { default as PDF417DecodedBitStreamParser } from './core/pdf417/decoder/DecodedBitStreamParser';\nexport { default as PDF417DecoderErrorCorrection } from './core/pdf417/decoder/ec/ErrorCorrection';\n// core/twod/qrcode\nexport { default as QRCodeReader } from './core/qrcode/QRCodeReader';\nexport { default as QRCodeWriter } from './core/qrcode/QRCodeWriter';\nexport { default as QRCodeDecoderErrorCorrectionLevel } from './core/qrcode/decoder/ErrorCorrectionLevel';\nexport { default as QRCodeDecoderFormatInformation } from './core/qrcode/decoder/FormatInformation';\nexport { default as QRCodeVersion } from './core/qrcode/decoder/Version';\nexport { default as QRCodeMode } from './core/qrcode/decoder/Mode';\nexport { default as QRCodeDecodedBitStreamParser } from './core/qrcode/decoder/DecodedBitStreamParser';\nexport { default as QRCodeDataMask } from './core/qrcode/decoder/DataMask';\nexport { default as QRCodeEncoder } from './core/qrcode/encoder/Encoder';\nexport { default as QRCodeEncoderQRCode } from './core/qrcode/encoder/QRCode';\nexport { default as QRCodeMatrixUtil } from './core/qrcode/encoder/MatrixUtil';\nexport { default as QRCodeByteMatrix } from './core/qrcode/encoder/ByteMatrix';\nexport { default as QRCodeMaskUtil } from './core/qrcode/encoder/MaskUtil';\n// core/twod/aztec\nexport { default as AztecCodeReader } from './core/aztec/AztecReader';\nexport { default as AztecCodeWriter } from './core/aztec/AztecWriter';\nexport { default as AztecDetectorResult } from './core/aztec/AztecDetectorResult';\nexport { default as AztecEncoder } from './core/aztec/encoder/Encoder';\nexport { default as AztecHighLevelEncoder } from './core/aztec/encoder/HighLevelEncoder';\nexport { default as AztecCode } from './core/aztec/encoder/AztecCode';\nexport { default as AztecDecoder } from './core/aztec/decoder/Decoder';\nexport { default as AztecDetector } from './core/aztec/detector/Detector';\nexport { Point as AztecPoint } from './core/aztec/detector/Detector';\n// core/oned\nexport { default as OneDReader } from './core/oned/OneDReader';\nexport { default as EAN13Reader } from './core/oned/EAN13Reader';\nexport { default as Code128Reader } from './core/oned/Code128Reader';\nexport { default as ITFReader } from './core/oned/ITFReader';\nexport { default as Code39Reader } from './core/oned/Code39Reader';\nexport { default as Code93Reader } from './core/oned/Code93Reader';\nexport { default as RSS14Reader } from './core/oned/rss/RSS14Reader';\nexport { default as RSSExpandedReader } from './core/oned/rss/expanded/RSSExpandedReader';\nexport { default as AbstractExpandedDecoder } from './core/oned/rss/expanded/decoders/AbstractExpandedDecoder';\nexport { createDecoder as createAbstractExpandedDecoder } from './core/oned/rss/expanded/decoders/AbstractExpandedDecoderComplement';\nexport { default as MultiFormatOneDReader } from './core/oned/MultiFormatOneDReader';\nexport { default as CodaBarReader } from './core/oned/CodaBarReader';\n"], "names": [], "mappings": ";AAAA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA,+GAA+G;AAC/G;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": [0], "debugId": null}}]}